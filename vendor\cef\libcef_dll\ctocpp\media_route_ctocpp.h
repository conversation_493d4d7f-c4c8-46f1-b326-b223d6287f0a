// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=b9525329d4366e71a8f3c9c46b787ba01073bc97$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_MEDIA_ROUTE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_MEDIA_ROUTE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_media_router_capi.h"
#include "include/cef_media_router.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefMediaRouteCToCpp : public CefCToCppRefCounted<CefMediaRouteCToCpp,
                                                       CefMediaRoute,
                                                       cef_media_route_t> {
 public:
  CefMediaRouteCToCpp();
  virtual ~CefMediaRouteCToCpp();

  // CefMediaRoute methods.
  CefString GetId() override;
  CefRefPtr<CefMediaSource> GetSource() override;
  CefRefPtr<CefMediaSink> GetSink() override;
  void SendRouteMessage(const void* message, size_t message_size) override;
  void Terminate() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_MEDIA_ROUTE_CTOCPP_H_
