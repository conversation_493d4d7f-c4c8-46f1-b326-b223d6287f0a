#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return { };
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(CreateEmptyObjectLiteral),
  /*   45 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return { name: 'string', val: 9.2 };
"
frame size: 0
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
  /*   70 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  var a = 1; return { name: 'string', val: a };
"
frame size: 2
parameter count: 1
bytecode array length: 17
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   75 E> */ B(DefineNamedOwnProperty), R(1), U8(1), U8(1),
                B(Ldar), R(1),
  /*   79 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  var a = 1; return { val: a, val: a + 1 };
"
frame size: 2
parameter count: 1
bytecode array length: 20
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   69 E> */ B(AddSmi), I8(1), U8(1),
                B(DefineNamedOwnProperty), R(1), U8(1), U8(2),
                B(Ldar), R(1),
  /*   75 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  return { func: function() { } };
"
frame size: 1
parameter count: 1
bytecode array length: 16
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star0),
  /*   49 E> */ B(CreateClosure), U8(1), U8(0), U8(2),
                B(DefineNamedOwnProperty), R(0), U8(2), U8(1),
                B(Ldar), R(0),
  /*   66 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  return { func(a) { return a; } };
"
frame size: 1
parameter count: 1
bytecode array length: 16
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star0),
  /*   43 E> */ B(CreateClosure), U8(1), U8(0), U8(2),
                B(DefineNamedOwnProperty), R(0), U8(2), U8(1),
                B(Ldar), R(0),
  /*   67 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  return { get a() { return 2; } };
"
frame size: 6
parameter count: 1
bytecode array length: 28
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star0),
                B(LdaConstant), U8(1),
                B(Star2),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaNull),
                B(Star4),
                B(LdaZero),
                B(Star5),
                B(Mov), R(0), R(1),
                B(CallRuntime), U16(Runtime::kDefineAccessorPropertyUnchecked), R(1), U8(5),
                B(Ldar), R(1),
  /*   67 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  return { get a() { return this.x; }, set a(val) { this.x = val } };
"
frame size: 6
parameter count: 1
bytecode array length: 31
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star0),
                B(LdaConstant), U8(1),
                B(Star2),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star4),
                B(LdaZero),
                B(Star5),
                B(Mov), R(0), R(1),
                B(CallRuntime), U16(Runtime::kDefineAccessorPropertyUnchecked), R(1), U8(5),
                B(Ldar), R(1),
  /*  101 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  return { set b(val) { this.y = val } };
"
frame size: 6
parameter count: 1
bytecode array length: 28
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star0),
                B(LdaConstant), U8(1),
                B(Star2),
                B(LdaNull),
                B(Star3),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star4),
                B(LdaZero),
                B(Star5),
                B(Mov), R(0), R(1),
                B(CallRuntime), U16(Runtime::kDefineAccessorPropertyUnchecked), R(1), U8(5),
                B(Ldar), R(1),
  /*   73 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  var a = 1; return { 1: a };
"
frame size: 3
parameter count: 1
bytecode array length: 21
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(Ldar), R(0),
  /*   57 E> */ B(DefineKeyedOwnProperty), R(1), R(2), U8(0), U8(1),
                B(Ldar), R(1),
  /*   61 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  return { __proto__: null };
"
frame size: 0
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(57),
  /*   61 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  var a = 'test'; return { [a]: 1 };
"
frame size: 3
parameter count: 1
bytecode array length: 22
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   50 S> */ B(CreateObjectLiteral), U8(1), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   60 E> */ B(ToName),
                B(Star2),
                B(LdaSmi), I8(1),
  /*   64 E> */ B(DefineKeyedOwnPropertyInLiteral), R(1), R(2), U8(0), U8(1),
                B(Ldar), R(1),
  /*   68 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["test"],
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  var a = 'test'; return { val: a, [a]: 1 };
"
frame size: 3
parameter count: 1
bytecode array length: 28
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   50 S> */ B(CreateObjectLiteral), U8(1), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   64 E> */ B(DefineNamedOwnProperty), R(1), U8(2), U8(1),
                B(Ldar), R(0),
  /*   68 E> */ B(ToName),
                B(Star2),
                B(LdaSmi), I8(1),
  /*   72 E> */ B(DefineKeyedOwnPropertyInLiteral), R(1), R(2), U8(0), U8(3),
                B(Ldar), R(1),
  /*   76 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["test"],
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  var a = 'test'; return { [a]: 1, __proto__: {} };
"
frame size: 4
parameter count: 1
bytecode array length: 32
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   50 S> */ B(CreateObjectLiteral), U8(1), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   60 E> */ B(ToName),
                B(Star2),
                B(LdaSmi), I8(1),
  /*   64 E> */ B(DefineKeyedOwnPropertyInLiteral), R(1), R(2), U8(0), U8(1),
  /*   78 E> */ B(CreateEmptyObjectLiteral),
                B(Star3),
                B(Mov), R(1), R(2),
                B(CallRuntime), U16(Runtime::kInternalSetPrototype), R(2), U8(2),
                B(Ldar), R(2),
  /*   83 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["test"],
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  var n = 'name'; return { [n]: 'val', get a() { }, set a(b) {} };
"
frame size: 6
parameter count: 1
bytecode array length: 55
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   50 S> */ B(CreateObjectLiteral), U8(1), U8(0), U8(41),
                B(Star1),
                B(Ldar), R(0),
  /*   60 E> */ B(ToName),
                B(Star2),
                B(LdaConstant), U8(2),
  /*   64 E> */ B(DefineKeyedOwnPropertyInLiteral), R(1), R(2), U8(0), U8(1),
                B(LdaConstant), U8(3),
                B(Star3),
  /*   71 E> */ B(CreateClosure), U8(4), U8(0), U8(2),
                B(Star4),
                B(LdaZero),
                B(Star5),
                B(Mov), R(1), R(2),
                B(CallRuntime), U16(Runtime::kDefineGetterPropertyUnchecked), R(2), U8(4),
                B(LdaConstant), U8(3),
                B(Star3),
  /*   84 E> */ B(CreateClosure), U8(5), U8(1), U8(2),
                B(Star4),
                B(LdaZero),
                B(Star5),
                B(CallRuntime), U16(Runtime::kDefineSetterPropertyUnchecked), R(2), U8(4),
                B(Ldar), R(2),
  /*   98 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

