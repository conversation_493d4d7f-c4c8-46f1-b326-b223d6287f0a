#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  var f;
  var x = 1;
  function f1() {
    eval(\"function t() { return x; }; f = t; f();\");
  }
  f1();
"
frame size: 0
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   15 S> */ B(LdaLookupGlobalSlot), U8(0), U8(0), U8(1),
  /*   24 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  var f;
  var x = 1;
  function f1() {
    eval(\"function t() { x = 10; }; f = t; f();\");
  }
  f1();
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   15 S> */ B(LdaSmi), I8(10),
  /*   17 E> */ B(StaLookupSlot), U8(0), U8(0),
                B(LdaUndefined),
  /*   23 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  var f;
  var x = 1;
  function f1() {
    eval(\"function t() { 'use strict'; x = 10; }; f = t; f();\");
  }
  f1();
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   29 S> */ B(LdaSmi), I8(10),
  /*   31 E> */ B(StaLookupSlot), U8(0), U8(1),
                B(LdaUndefined),
  /*   37 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  var f;
  var x = 1;
  function f1() {
    eval(\"function t() { return typeof x; }; f = t; f();\");
  }
  f1();
"
frame size: 0
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   15 S> */ B(LdaLookupGlobalSlotInsideTypeof), U8(0), U8(0), U8(1),
                B(TypeOf),
  /*   31 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

