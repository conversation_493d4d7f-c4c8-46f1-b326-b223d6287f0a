// Copyright 2012 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Flags: --allow-natives-syntax

var ordering = [];
function reset() {
  ordering = [];
}

function assertArrayValues(expected, actual) {
  assertEquals(expected.length, actual.length);
  for (var i = 0; i < expected.length; i++) {
    assertEquals(expected[i], actual[i]);
  }
}

function assertOrdering(expected) {
  %PerformMicrotaskCheckpoint();
  assertArrayValues(expected, ordering);
}

function newPromise(id, fn) {
  var r;
  var t = 1;
  var promise = new Promise(function(resolve) {
    r = resolve;
    if (fn) fn();
  });

  var next = promise.then(function(value) {
    ordering.push('p' + id);
    return value;
  });

  return {
    resolve: r,
    then: function(fn) {
      next = next.then(function(value) {
        ordering.push('p' + id + ':' + t++);
        return fn ? fn(value) : value;
      });

      return this;
    }
  };
}

(function PromiseThens() {
  reset();

  var p1 = newPromise(1).then();
  var p2 = newPromise(2).then();

  p1.resolve();
  p2.resolve();

  assertOrdering(['p1', 'p2', 'p1:1', 'p2:1']);
})();
