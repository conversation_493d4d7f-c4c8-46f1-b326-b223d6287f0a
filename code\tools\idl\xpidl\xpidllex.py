# xpidllex.py. This file automatically created by PL<PERSON> (version 3.11). Don't edit!
_tabversion   = '3.10'
_lextokens    = set(('ATTRIBUTE', 'CDATA', 'CONST', 'HEXNUM', 'IDENTIFIER', 'IID', 'IN', 'INCLUDE', 'INOUT', 'INTERFACE', 'LSHIFT', 'NATIVE', 'NATIVEID', 'NUMBER', 'OUT', 'RAISES', 'READONLY', 'RSHIFT', 'TYPEDEF'))
_lexreflags   = 64
_lexliterals  = '"(){}[],;:=|+-*'
_lexstateinfo = {'INITIAL': 'inclusive', 'nativeid': 'exclusive'}
_lexstatere   = {'INITIAL': [('(?P<t_multilinecomment>/\\*(\\n|.)*?\\*/)|(?P<t_singlelinecomment>//[^\\n]*)|(?P<t_IID>[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})|(?P<t_IDENTIFIER>(unsigned\\ long\\ long|unsigned\\ short|unsigned\\ long|long\\ long)(?!_?[A-Za-z][A-Za-z_0-9])|_?[A-Za-z][A-Za-z_0-9]*)|(?P<t_LCDATA>%\\{[ ]*C\\+\\+[ ]*\\n(?P<cdata>(\\n|.)*?\\n?)%\\}[ ]*(C\\+\\+)?)|(?P<t_INCLUDE>\\#include[ \\t]+"[^"\\n]+")|(?P<t_directive>\\#(?P<directive>[a-zA-Z]+)[^\\n]+)|(?P<t_newline>\\n+)|(?P<t_HEXNUM>0x[a-fA-F0-9]+)|(?P<t_NUMBER>-?\\d+)|(?P<t_LSHIFT><<)|(?P<t_RSHIFT>>>)', [None, ('t_multilinecomment', 'multilinecomment'), None, ('t_singlelinecomment', 'singlelinecomment'), ('t_IID', 'IID'), ('t_IDENTIFIER', 'IDENTIFIER'), None, ('t_LCDATA', 'LCDATA'), None, None, None, ('t_INCLUDE', 'INCLUDE'), ('t_directive', 'directive'), None, ('t_newline', 'newline'), (None, 'HEXNUM'), (None, 'NUMBER'), (None, 'LSHIFT'), (None, 'RSHIFT')])], 'nativeid': [('(?P<t_nativeid_NATIVEID>[^()\\n]+(?=\\)))', [None, ('t_nativeid_NATIVEID', 'NATIVEID')])]}
_lexstateignore = {'INITIAL': ' \t', 'nativeid': ''}
_lexstateerrorf = {'INITIAL': 't_ANY_error', 'nativeid': 't_ANY_error'}
_lexstateeoff = {}
