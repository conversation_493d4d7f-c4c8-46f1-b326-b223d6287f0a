// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=e9e56607998130280b093c45987aa294c06f1912$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_BINARY_VALUE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_BINARY_VALUE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_values_capi.h"
#include "include/cef_values.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefBinaryValueCToCpp : public CefCToCppRefCounted<CefBinaryValueCToCpp,
                                                        CefBinaryValue,
                                                        cef_binary_value_t> {
 public:
  CefBinaryValueCToCpp();
  virtual ~CefBinaryValueCToCpp();

  // CefBinaryValue methods.
  bool IsValid() override;
  bool IsOwned() override;
  bool IsSame(CefRefPtr<CefBinaryValue> that) override;
  bool IsEqual(CefRefPtr<CefBinaryValue> that) override;
  CefRefPtr<CefBinaryValue> Copy() override;
  size_t GetSize() override;
  size_t GetData(void* buffer, size_t buffer_size, size_t data_offset) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_BINARY_VALUE_CTOCPP_H_
