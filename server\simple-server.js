const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors());

// Path to FiveM release directory
const FIVEM_RELEASE_PATH = path.join(__dirname, '..', 'code', 'bin', 'five', 'release');

// Global variables
let fileList = [];
let manifestHash = '';
let manifestXml = '';

// Helper function to calculate SHA256 hash of a file
function calculateFileHash(filePath) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    return crypto.createHash('sha256').update(fileBuffer).digest('hex');
  } catch (error) {
    console.error(`Error calculating hash for ${filePath}:`, error.message);
    return null;
  }
}

// Helper function to check if file should be included
function shouldIncludeFile(fileName) {
  const allowedExtensions = ['.dll', '.exe', '.bin', '.json', '.xml', '.txt', '.cfg', '.com'];
  const ext = path.extname(fileName).toLowerCase();
  return allowedExtensions.includes(ext);
}

// Build file list from FiveM release directory
function buildFileList() {
  const files = [];
  
  function scanDirectory(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const fullPath = path.join(dirPath, item);
        const relativeFilePath = path.join(relativePath, item).replace(/\\/g, '/');
        
        try {
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            // Skip certain directories
            if (!['dbg', 'gen', 'mods'].includes(item)) {
              scanDirectory(fullPath, relativeFilePath);
            }
          } else if (stat.isFile() && shouldIncludeFile(item)) {
            const hash = calculateFileHash(fullPath);
            if (hash) {
              const sha1Hash = crypto.createHash('sha1').update(hash).digest('hex');
              
              files.push({
                name: relativeFilePath,
                fullPath: fullPath,
                size: stat.size,
                sha256Hash: hash,
                sha1Hash: sha1Hash
              });
            }
          }
        } catch (error) {
          console.warn(`Skipping ${item}: ${error.message}`);
        }
      });
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error.message);
    }
  }
  
  if (fs.existsSync(FIVEM_RELEASE_PATH)) {
    console.log(`Scanning directory: ${FIVEM_RELEASE_PATH}`);
    scanDirectory(FIVEM_RELEASE_PATH);
    console.log(`Found ${files.length} files to serve`);
  } else {
    console.error(`Release directory not found: ${FIVEM_RELEASE_PATH}`);
  }
  
  return files;
}

// Generate manifest XML
function generateManifest() {
  let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<Cache>\n';
  
  fileList.forEach(file => {
    xmlContent += `  <ContentFile name="${file.name}" ` +
                 `SHA1Hash="${file.sha1Hash}" ` +
                 `SHA256Hash="${file.sha256Hash}" ` +
                 `localSize="${file.size}" ` +
                 `downloadSize="${file.size}" ` +
                 `algorithm="None" />\n`;
  });
  
  xmlContent += '</Cache>';
  return xmlContent;
}

// Initialize server data
function initializeServer() {
  console.log('Initializing cache data...');
  fileList = buildFileList();
  manifestXml = generateManifest();
  manifestHash = crypto.createHash('sha256').update(manifestXml).digest('hex');
  console.log(`Manifest hash: ${manifestHash}`);
  console.log('Server ready to serve content!');
}

// API Routes

// Get cache heads (version info) - This is what CFX client calls first
app.get('/updates/heads/:cacheName/:channel', (req, res) => {
  const { cacheName, channel } = req.params;
  console.log(`Request for cache heads: ${cacheName}/${channel}`);
  
  // Set headers that CFX client expects
  res.set({
    'x-amz-meta-branch-version': '1.0.0',
    'x-amz-meta-branch-manifest': manifestHash
  });
  
  // Return version as plain text
  res.send('1.0.0');
});

// Get manifest or file by hash - CFX client uses format: /updates/ab/cd/abcd1234...
app.get('/updates/:dir1/:dir2/:hash', (req, res) => {
  const { dir1, dir2, hash } = req.params;
  const fullHash = dir1 + dir2 + hash;
  
  console.log(`Request for object with hash: ${fullHash}`);
  
  // Check if this is a manifest request
  if (fullHash === manifestHash) {
    console.log('Serving manifest file');
    res.set('Content-Type', 'application/xml');
    res.send(manifestXml);
    return;
  }
  
  // Find file by hash
  const file = fileList.find(f => 
    f.sha256Hash.toLowerCase() === fullHash.toLowerCase() || 
    f.sha1Hash.toLowerCase() === fullHash.toLowerCase()
  );
  
  if (file) {
    if (fs.existsSync(file.fullPath)) {
      console.log(`Serving file: ${file.name} (${file.size} bytes)`);
      
      res.set({
        'Content-Length': file.size,
        'Content-Type': 'application/octet-stream',
        'Cache-Control': 'public, max-age=31536000'
      });
      
      res.sendFile(file.fullPath);
    } else {
      console.error(`File not found: ${file.fullPath}`);
      res.status(404).send('File not found on disk');
    }
  } else {
    console.error(`Hash not found: ${fullHash}`);
    res.status(404).send('Hash not found');
  }
});

// Health check endpoint
app.get('/health', (_, res) => {
  res.json({
    status: 'ok',
    message: 'GangHaiCity Content Server is running',
    filesCount: fileList.length
  });
});

// Start server
app.listen(PORT, () => {
  console.log('Starting GangHaiCity Content Server...');
  console.log(`Starting server on http://localhost:${PORT}`);
  console.log('Press Ctrl+C to stop the server\n');
  
  // Initialize after server starts
  initializeServer();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  process.exit(0);
});
