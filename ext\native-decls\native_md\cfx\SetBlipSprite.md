---
ns: CFX
apiset: server
---
## SET_BLIP_SPRITE

```c
void SET_BLIP_SPRITE(Blip blip, int spriteId);
```

Sets the displayed sprite for a specific blip.
There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.

**This is the server-side RPC native equivalent of the client native [SET\_BLIP\_SPRITE](?_0xDF735600A4696DAF).**

## Parameters
* **blip**: The blip to change.
* **spriteId**: The sprite ID to set.

