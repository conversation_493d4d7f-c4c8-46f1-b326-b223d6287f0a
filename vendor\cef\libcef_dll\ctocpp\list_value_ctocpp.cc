// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=93f45c1e39dc2ba72a6cb44bc3d762f3870f2ef2$
//

#include "libcef_dll/ctocpp/list_value_ctocpp.h"
#include "libcef_dll/ctocpp/binary_value_ctocpp.h"
#include "libcef_dll/ctocpp/dictionary_value_ctocpp.h"
#include "libcef_dll/ctocpp/value_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

// STATIC METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") CefRefPtr<CefListValue> CefListValue::Create() {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_list_value_t* _retval = cef_list_value_create();

  // Return type: refptr_same
  return CefListValueCToCpp::Wrap(_retval);
}

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::IsValid() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_valid))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_valid(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::IsOwned() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_owned))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_owned(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::IsReadOnly() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_read_only))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_read_only(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::IsSame(CefRefPtr<CefListValue> that) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_same))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: that; type: refptr_same
  DCHECK(that.get());
  if (!that.get())
    return false;

  // Execute
  int _retval = _struct->is_same(_struct, CefListValueCToCpp::Unwrap(that));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::IsEqual(CefRefPtr<CefListValue> that) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_equal))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: that; type: refptr_same
  DCHECK(that.get());
  if (!that.get())
    return false;

  // Execute
  int _retval = _struct->is_equal(_struct, CefListValueCToCpp::Unwrap(that));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") CefRefPtr<CefListValue> CefListValueCToCpp::Copy() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, copy))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_list_value_t* _retval = _struct->copy(_struct);

  // Return type: refptr_same
  return CefListValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::SetSize(size_t size) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_size))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->set_size(_struct, size);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") size_t CefListValueCToCpp::GetSize() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_size))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  size_t _retval = _struct->get_size(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::Clear() {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, clear))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->clear(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::Remove(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, remove))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->remove(_struct, index);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
CefValueType CefListValueCToCpp::GetType(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_type))
    return VTYPE_INVALID;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_value_type_t _retval = _struct->get_type(_struct, index);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefValue> CefListValueCToCpp::GetValue(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_value))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_value_t* _retval = _struct->get_value(_struct, index);

  // Return type: refptr_same
  return CefValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::GetBool(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_bool))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_bool(_struct, index);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") int CefListValueCToCpp::GetInt(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_int))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_int(_struct, index);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") double CefListValueCToCpp::GetDouble(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_double))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  double _retval = _struct->get_double(_struct, index);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") CefString CefListValueCToCpp::GetString(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_string))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_string(_struct, index);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefBinaryValue> CefListValueCToCpp::GetBinary(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_binary))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_binary_value_t* _retval = _struct->get_binary(_struct, index);

  // Return type: refptr_same
  return CefBinaryValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefDictionaryValue> CefListValueCToCpp::GetDictionary(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_dictionary))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_dictionary_value_t* _retval = _struct->get_dictionary(_struct, index);

  // Return type: refptr_same
  return CefDictionaryValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefListValue> CefListValueCToCpp::GetList(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_list))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_list_value_t* _retval = _struct->get_list(_struct, index);

  // Return type: refptr_same
  return CefListValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetValue(size_t index, CefRefPtr<CefValue> value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_value))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: value; type: refptr_same
  DCHECK(value.get());
  if (!value.get())
    return false;

  // Execute
  int _retval =
      _struct->set_value(_struct, index, CefValueCToCpp::Unwrap(value));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefListValueCToCpp::SetNull(size_t index) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_null))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->set_null(_struct, index);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetBool(size_t index, bool value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_bool))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->set_bool(_struct, index, value);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetInt(size_t index, int value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_int))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->set_int(_struct, index, value);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetDouble(size_t index, double value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_double))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->set_double(_struct, index, value);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetString(size_t index, const CefString& value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_string))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: value

  // Execute
  int _retval = _struct->set_string(_struct, index, value.GetStruct());

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetBinary(size_t index,
                                   CefRefPtr<CefBinaryValue> value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_binary))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: value; type: refptr_same
  DCHECK(value.get());
  if (!value.get())
    return false;

  // Execute
  int _retval =
      _struct->set_binary(_struct, index, CefBinaryValueCToCpp::Unwrap(value));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetDictionary(size_t index,
                                       CefRefPtr<CefDictionaryValue> value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_dictionary))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: value; type: refptr_same
  DCHECK(value.get());
  if (!value.get())
    return false;

  // Execute
  int _retval = _struct->set_dictionary(
      _struct, index, CefDictionaryValueCToCpp::Unwrap(value));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefListValueCToCpp::SetList(size_t index, CefRefPtr<CefListValue> value) {
  shutdown_checker::AssertNotShutdown();

  cef_list_value_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_list))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: value; type: refptr_same
  DCHECK(value.get());
  if (!value.get())
    return false;

  // Execute
  int _retval =
      _struct->set_list(_struct, index, CefListValueCToCpp::Unwrap(value));

  // Return type: bool
  return _retval ? true : false;
}

// CONSTRUCTOR - Do not edit by hand.

CefListValueCToCpp::CefListValueCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefListValueCToCpp::~CefListValueCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_list_value_t*
CefCToCppRefCounted<CefListValueCToCpp, CefListValue, cef_list_value_t>::
    UnwrapDerived(CefWrapperType type, CefListValue* c) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefListValueCToCpp,
                                   CefListValue,
                                   cef_list_value_t>::kWrapperType =
    WT_LIST_VALUE;
