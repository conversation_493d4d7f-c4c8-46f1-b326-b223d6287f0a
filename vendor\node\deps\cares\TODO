TODO
====

ares_reinit()

- To allow an app to force a re-read of /etc/resolv.conf etc, pretty much
  like the res_init() resolver function offers

ares_gethostbyname

- When built to support IPv6, it needs to also support PF_UNSPEC or similar,
  so that an application can ask for any protocol and then c-ares would return
  all known resolves and not just explicitly IPv4 _or_ IPv6 resolves.

ares_process

- Upon next ABI breakage ares_process() should be changed to return 'int'
  and return ARES_ENOTINITIALIZED if ares_library_init() has not been called.

ares_process_fd

- Upon next ABI breakage ares_process_fd() should be changed to return
  'int' and return ARES_ENOTINITIALIZED if library has not been initialized.
