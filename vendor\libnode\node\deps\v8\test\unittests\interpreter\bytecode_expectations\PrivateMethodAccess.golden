#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  class A {
    #a() { return 1; }
    constructor() { return this.#a(); }
  }
  
  var test = A;
  new A;
"
frame size: 2
parameter count: 1
bytecode array length: 24
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   44 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   49 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   61 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
  /*   63 E> */ B(CallAnyReceiver), R(1), R(this), U8(1), U8(4),
  /*   66 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  class B {
    #b() { return 1; }
    constructor() { this.#b = 1; }
  }
  
  var test = B;
  new test;
"
frame size: 5
parameter count: 1
bytecode array length: 32
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   44 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   49 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star2),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   54 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(318),
                B(Star3),
                B(LdaConstant), U8(0),
                B(Star4),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(3), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#b"],
]
handlers: [
]

---
snippet: "
  class C {
    #c() { return 1; }
    constructor() { this.#c++; }
  }
  
  var test = C;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 29
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   44 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   49 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   54 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(318),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#c"],
]
handlers: [
]

---
snippet: "
  class D {
    #d() { return 1; }
    constructor() { (() => this)().#d(); }
  }
  
  var test = D;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 49
bytecodes: [
  /*   44 E> */ B(CreateFunctionContext), U8(0), U8(1),
                B(PushContext), R(0),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(2),
                B(LdaImmutableContextSlot), R(0), U8(3), U8(0),
                B(Star1),
                B(Ldar), R(0),
                B(DefineKeyedOwnProperty), R(this), R(1), U8(0), U8(0),
  /*   49 S> */ B(CreateClosure), U8(1), U8(0), U8(2),
                B(Star3),
  /*   61 E> */ B(CallUndefinedReceiver0), R(3), U8(2),
                B(Star3),
                B(LdaImmutableContextSlot), R(0), U8(3), U8(0),
  /*   63 E> */ B(GetKeyedProperty), R(3), U8(4),
                B(LdaImmutableContextSlot), R(0), U8(2), U8(0),
                B(Star2),
  /*   66 E> */ B(CallAnyReceiver), R(2), R(3), U8(1), U8(6),
                B(LdaUndefined),
  /*   70 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  var test;
  class F extends class {} {
    #method() { }
    constructor() {
      (test = () => super())();
      this.#method();
    }
  };
  new F;
"
frame size: 8
parameter count: 1
bytecode array length: 63
bytecodes: [
  /*   89 S> */ B(LdaImmutableCurrentContextSlot), U8(4),
                B(Star0),
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star2),
                B(Ldar), R(0),
  /*   89 E> */ B(GetSuperConstructor), R(1),
                B(ThrowIfNotSuperConstructor), R(1),
                B(Ldar), R(2),
  /*   89 E> */ B(Construct), R(1), R(0), U8(0), U8(0),
                B(Star1),
                B(LdaCurrentContextSlot), U8(2),
                B(ThrowSuperAlreadyCalledIfNotHole),
                B(Ldar), R(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaImmutableContextSlot), R(context), U8(3), U8(1),
                B(Star3),
                B(LdaSmi), I8(1),
                B(Star5),
                B(Mov), R(1), R(2),
                B(Mov), R(context), R(4),
                B(CallRuntime), U16(Runtime::kAddPrivateBrand), R(2), U8(4),
                B(GetNamedProperty), R(0), U8(0), U8(2),
                B(JumpIfUndefined), U8(10),
                B(Star7),
                B(CallProperty0), R(7), R(1), U8(4),
                B(Mov), R(1), R(6),
                B(Ldar), R(1),
  /*   96 S> */ B(Return),
]
constant pool: [
  SYMBOL_TYPE,
]
handlers: [
]

---
snippet: "
  var test;
  class G extends class {} {
    #method() { }
    constructor() {
      test = () => super();
      test();
      this.#method();
    }
  };
  new G();
"
frame size: 8
parameter count: 1
bytecode array length: 63
bytecodes: [
  /*   88 S> */ B(LdaImmutableCurrentContextSlot), U8(4),
                B(Star0),
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star2),
                B(Ldar), R(0),
  /*   88 E> */ B(GetSuperConstructor), R(1),
                B(ThrowIfNotSuperConstructor), R(1),
                B(Ldar), R(2),
  /*   88 E> */ B(Construct), R(1), R(0), U8(0), U8(0),
                B(Star1),
                B(LdaCurrentContextSlot), U8(2),
                B(ThrowSuperAlreadyCalledIfNotHole),
                B(Ldar), R(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaImmutableContextSlot), R(context), U8(3), U8(1),
                B(Star3),
                B(LdaSmi), I8(1),
                B(Star5),
                B(Mov), R(1), R(2),
                B(Mov), R(context), R(4),
                B(CallRuntime), U16(Runtime::kAddPrivateBrand), R(2), U8(4),
                B(GetNamedProperty), R(0), U8(0), U8(2),
                B(JumpIfUndefined), U8(10),
                B(Star7),
                B(CallProperty0), R(7), R(1), U8(4),
                B(Mov), R(1), R(6),
                B(Ldar), R(1),
  /*   95 S> */ B(Return),
]
constant pool: [
  SYMBOL_TYPE,
]
handlers: [
]

---
snippet: "
  var test;
  class H extends class {} {
    #method() { }
    constructor(str) {
      eval(str);
      this.#method();
    }
  };
  new test('test = () => super(); test()');
"
frame size: 8
parameter count: 1
bytecode array length: 63
bytecodes: [
  /*   88 S> */ B(LdaImmutableCurrentContextSlot), U8(4),
                B(Star0),
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star2),
                B(Ldar), R(0),
  /*   88 E> */ B(GetSuperConstructor), R(1),
                B(ThrowIfNotSuperConstructor), R(1),
                B(Ldar), R(2),
  /*   88 E> */ B(Construct), R(1), R(0), U8(0), U8(0),
                B(Star1),
                B(LdaCurrentContextSlot), U8(2),
                B(ThrowSuperAlreadyCalledIfNotHole),
                B(Ldar), R(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaImmutableContextSlot), R(context), U8(3), U8(1),
                B(Star3),
                B(LdaSmi), I8(1),
                B(Star5),
                B(Mov), R(1), R(2),
                B(Mov), R(context), R(4),
                B(CallRuntime), U16(Runtime::kAddPrivateBrand), R(2), U8(4),
                B(GetNamedProperty), R(0), U8(0), U8(2),
                B(JumpIfUndefined), U8(10),
                B(Star7),
                B(CallProperty0), R(7), R(1), U8(4),
                B(Mov), R(1), R(6),
                B(Ldar), R(1),
  /*   95 S> */ B(Return),
]
constant pool: [
  SYMBOL_TYPE,
]
handlers: [
]

