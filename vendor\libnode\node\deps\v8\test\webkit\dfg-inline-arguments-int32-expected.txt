# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves function.arguments functionality if the arguments were represented as unboxed int32.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 1, 2, 3"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 2, 3, 4"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 3, 4, 5"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 4, 5, 6"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 5, 6, 7"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 6, 7, 8"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 7, 8, 9"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 8, 9, 10"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 9, 10, 11"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 10, 11, 12"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 11, 12, 13"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 12, 13, 14"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 13, 14, 15"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 14, 15, 16"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 15, 16, 17"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 16, 17, 18"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 17, 18, 19"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 18, 19, 20"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 19, 20, 21"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 20, 21, 22"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 21, 22, 23"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 22, 23, 24"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 23, 24, 25"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 24, 25, 26"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 25, 26, 27"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 26, 27, 28"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 27, 28, 29"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 28, 29, 30"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 29, 30, 31"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 30, 31, 32"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 31, 32, 33"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 32, 33, 34"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 33, 34, 35"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 34, 35, 36"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 35, 36, 37"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 36, 37, 38"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 37, 38, 39"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 38, 39, 40"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 39, 40, 41"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 40, 41, 42"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 41, 42, 43"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 42, 43, 44"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 43, 44, 45"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 44, 45, 46"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 45, 46, 47"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 46, 47, 48"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 47, 48, 49"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 48, 49, 50"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 49, 50, 51"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 50, 51, 52"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 51, 52, 53"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 52, 53, 54"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 53, 54, 55"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 54, 55, 56"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 55, 56, 57"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 56, 57, 58"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 57, 58, 59"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 58, 59, 60"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 59, 60, 61"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 60, 61, 62"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 61, 62, 63"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 62, 63, 64"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 63, 64, 65"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 64, 65, 66"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 65, 66, 67"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 66, 67, 68"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 67, 68, 69"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 68, 69, 70"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 69, 70, 71"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 70, 71, 72"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 71, 72, 73"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 72, 73, 74"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 73, 74, 75"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 74, 75, 76"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 75, 76, 77"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 76, 77, 78"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 77, 78, 79"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 78, 79, 80"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 79, 80, 81"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 80, 81, 82"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 81, 82, 83"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 82, 83, 84"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 83, 84, 85"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 84, 85, 86"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 85, 86, 87"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 86, 87, 88"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 87, 88, 89"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 88, 89, 90"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 89, 90, 91"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 90, 91, 92"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 91, 92, 93"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 92, 93, 94"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 93, 94, 95"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 94, 95, 96"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 95, 96, 97"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 96, 97, 98"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 97, 98, 99"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 98, 99, 100"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 99, 100, 101"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 100, 101, 102"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 101, 102, 103"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 102, 103, 104"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 103, 104, 105"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 104, 105, 106"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 105, 106, 107"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 106, 107, 108"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 107, 108, 109"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 108, 109, 110"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 109, 110, 111"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 110, 111, 112"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 111, 112, 113"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 112, 113, 114"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 113, 114, 115"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 114, 115, 116"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 115, 116, 117"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 116, 117, 118"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 117, 118, 119"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 118, 119, 120"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 119, 120, 121"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 120, 121, 122"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 121, 122, 123"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 122, 123, 124"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 123, 124, 125"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 124, 125, 126"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 125, 126, 127"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 126, 127, 128"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 127, 128, 129"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 128, 129, 130"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 129, 130, 131"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 130, 131, 132"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 131, 132, 133"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 132, 133, 134"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 133, 134, 135"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 134, 135, 136"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 135, 136, 137"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 136, 137, 138"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 137, 138, 139"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 138, 139, 140"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 139, 140, 141"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 140, 141, 142"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 141, 142, 143"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 142, 143, 144"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 143, 144, 145"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 144, 145, 146"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 145, 146, 147"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 146, 147, 148"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 147, 148, 149"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 148, 149, 150"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 149, 150, 151"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 150, 151, 152"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 151, 152, 153"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 152, 153, 154"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 153, 154, 155"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 154, 155, 156"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 155, 156, 157"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 156, 157, 158"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 157, 158, 159"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 158, 159, 160"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 159, 160, 161"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 160, 161, 162"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 161, 162, 163"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 162, 163, 164"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 163, 164, 165"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 164, 165, 166"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 165, 166, 167"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 166, 167, 168"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 167, 168, 169"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 168, 169, 170"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 169, 170, 171"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 170, 171, 172"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 171, 172, 173"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 172, 173, 174"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 173, 174, 175"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 174, 175, 176"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 175, 176, 177"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 176, 177, 178"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 177, 178, 179"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 178, 179, 180"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 179, 180, 181"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 180, 181, 182"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 181, 182, 183"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 182, 183, 184"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 183, 184, 185"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 184, 185, 186"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 185, 186, 187"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 186, 187, 188"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 187, 188, 189"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 188, 189, 190"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 189, 190, 191"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 190, 191, 192"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 191, 192, 193"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 192, 193, 194"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 193, 194, 195"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 194, 195, 196"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 195, 196, 197"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 196, 197, 198"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 197, 198, 199"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 198, 199, 200"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 199, 200, 201"
PASS argsToStr(baz(__i + 1, __i + 2, __i + 3)) is "[object Arguments]: 200, 201, 202"
PASS successfullyParsed is true

TEST COMPLETE

