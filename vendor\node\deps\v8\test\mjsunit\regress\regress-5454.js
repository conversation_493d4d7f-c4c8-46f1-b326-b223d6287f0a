// Copyright 2016 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

assertThrows(function(...[b = !b]) { }, ReferenceError);
assertThrows(() => (function([b = !b]) { })([]), ReferenceError);
assertThrows(() => (function({b = !b}) { })({}), ReferenceError);

assertThrows((...[b = !b]) => { }, ReferenceError);
assertThrows(() => (([b = !b]) => { })([]), ReferenceError);
assertThrows(() => (({b = !b}) => { })({}), ReferenceError);
