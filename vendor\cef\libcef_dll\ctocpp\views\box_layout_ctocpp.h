// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=0d208d785b4fa84ba2e9f8245911d1a47f5e206c$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_VIEWS_BOX_LAYOUT_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_VIEWS_BOX_LAYOUT_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/views/cef_box_layout_capi.h"
#include "include/capi/views/cef_view_capi.h"
#include "include/views/cef_box_layout.h"
#include "include/views/cef_view.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefBoxLayoutCToCpp : public CefCToCppRefCounted<CefBoxLayoutCToCpp,
                                                      CefBoxLayout,
                                                      cef_box_layout_t> {
 public:
  CefBoxLayoutCToCpp();
  virtual ~CefBoxLayoutCToCpp();

  // CefBoxLayout methods.
  void SetFlexForView(CefRefPtr<CefView> view, int flex) override;
  void ClearFlexForView(CefRefPtr<CefView> view) override;

  // CefLayout methods.
  CefRefPtr<CefBoxLayout> AsBoxLayout() override;
  CefRefPtr<CefFillLayout> AsFillLayout() override;
  bool IsValid() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_VIEWS_BOX_LAYOUT_CTOCPP_H_
