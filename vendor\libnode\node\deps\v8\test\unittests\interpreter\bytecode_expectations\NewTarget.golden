#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return new.target;
"
frame size: 1
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(Ldar), R(0),
  /*   52 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  new.target;
"
frame size: 1
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaUndefined),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

