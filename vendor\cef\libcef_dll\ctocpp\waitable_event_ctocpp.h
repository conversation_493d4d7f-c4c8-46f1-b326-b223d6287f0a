// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=519f7aab2913629ad5ac8ebcf228abd14b816ade$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_WAITABLE_EVENT_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_WAITABLE_EVENT_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_waitable_event_capi.h"
#include "include/cef_waitable_event.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefWaitableEventCToCpp
    : public CefCToCppRefCounted<CefWaitableEventCToCpp,
                                 CefWaitableEvent,
                                 cef_waitable_event_t> {
 public:
  CefWaitableEventCToCpp();
  virtual ~CefWaitableEventCToCpp();

  // CefWaitableEvent methods.
  void Reset() override;
  void Signal() override;
  bool IsSignaled() override;
  void Wait() override;
  bool TimedWait(int64 max_ms) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_WAITABLE_EVENT_CTOCPP_H_
