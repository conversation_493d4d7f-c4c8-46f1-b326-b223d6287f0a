#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  var a = 1;
  function f() { a = 2; }
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   26 S> */ B(LdaSmi), I8(2),
  /*   28 E> */ B(StaGlobal), U8(0), U8(0),
                B(LdaUndefined),
  /*   33 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  var a = \"test\"; function f(b) { a = b; }
  f(\"global\");
"
frame size: 0
parameter count: 2
bytecode array length: 7
bytecodes: [
  /*   32 S> */ B(Ldar), R(arg0),
  /*   34 E> */ B(StaGlobal), U8(0), U8(0),
                B(LdaUndefined),
  /*   39 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  'use strict'; var a = 1;
  function f() { a = 2; }
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   40 S> */ B(LdaSmi), I8(2),
  /*   42 E> */ B(StaGlobal), U8(0), U8(0),
                B(LdaUndefined),
  /*   47 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  a = 1;
  function f() { a = 2; }
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   22 S> */ B(LdaSmi), I8(2),
  /*   24 E> */ B(StaGlobal), U8(0), U8(0),
                B(LdaUndefined),
  /*   29 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  a = 1;
  function f(c) {
    var b = {};
    b.name640;
    b.name641;
    b.name642;
    b.name643;
    b.name644;
    b.name645;
    b.name646;
    b.name647;
    b.name648;
    b.name649;
    b.name650;
    b.name651;
    b.name652;
    b.name653;
    b.name654;
    b.name655;
    b.name656;
    b.name657;
    b.name658;
    b.name659;
    b.name660;
    b.name661;
    b.name662;
    b.name663;
    b.name664;
    b.name665;
    b.name666;
    b.name667;
    b.name668;
    b.name669;
    b.name670;
    b.name671;
    b.name672;
    b.name673;
    b.name674;
    b.name675;
    b.name676;
    b.name677;
    b.name678;
    b.name679;
    b.name680;
    b.name681;
    b.name682;
    b.name683;
    b.name684;
    b.name685;
    b.name686;
    b.name687;
    b.name688;
    b.name689;
    b.name690;
    b.name691;
    b.name692;
    b.name693;
    b.name694;
    b.name695;
    b.name696;
    b.name697;
    b.name698;
    b.name699;
    b.name700;
    b.name701;
    b.name702;
    b.name703;
    b.name704;
    b.name705;
    b.name706;
    b.name707;
    b.name708;
    b.name709;
    b.name710;
    b.name711;
    b.name712;
    b.name713;
    b.name714;
    b.name715;
    b.name716;
    b.name717;
    b.name718;
    b.name719;
    b.name720;
    b.name721;
    b.name722;
    b.name723;
    b.name724;
    b.name725;
    b.name726;
    b.name727;
    b.name728;
    b.name729;
    b.name730;
    b.name731;
    b.name732;
    b.name733;
    b.name734;
    b.name735;
    b.name736;
    b.name737;
    b.name738;
    b.name739;
    b.name740;
    b.name741;
    b.name742;
    b.name743;
    b.name744;
    b.name745;
    b.name746;
    b.name747;
    b.name748;
    b.name749;
    b.name750;
    b.name751;
    b.name752;
    b.name753;
    b.name754;
    b.name755;
    b.name756;
    b.name757;
    b.name758;
    b.name759;
    b.name760;
    b.name761;
    b.name762;
    b.name763;
    b.name764;
    b.name765;
    b.name766;
    b.name767;
    a = 2;
  }
  f({name: 1});
"
frame size: 1
parameter count: 2
bytecode array length: 524
bytecodes: [
  /*   33 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   41 S> */ B(GetNamedProperty), R(0), U8(0), U8(0),
  /*   54 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   67 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   80 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*   93 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*  106 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  119 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  132 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  145 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  158 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  171 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  184 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  197 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  210 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  223 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  236 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  249 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  262 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  275 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  288 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  301 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  314 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  327 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  340 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  353 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  366 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  379 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  392 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  405 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  418 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  431 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  444 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  457 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  470 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  483 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  496 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  509 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  522 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  535 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  548 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  561 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  574 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  587 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  600 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  613 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  626 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  639 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  652 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  665 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  678 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  691 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  704 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  717 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  730 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  743 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  756 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  769 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  782 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  795 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  808 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  821 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  834 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  847 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  860 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  873 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  886 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  899 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  912 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  925 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  938 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  951 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  964 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  977 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /*  990 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /* 1003 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /* 1016 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /* 1029 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /* 1042 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /* 1055 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /* 1068 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /* 1081 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /* 1094 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1107 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1120 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1133 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1146 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1159 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1172 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1185 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1198 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1211 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1224 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1237 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1250 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1263 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1276 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1289 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1302 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1315 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1328 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1341 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1354 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1367 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1380 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1393 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1406 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1419 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1432 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1445 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1458 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1471 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1484 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1497 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1510 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1523 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1536 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1549 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1562 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1575 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1588 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1601 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1614 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1627 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1640 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1653 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1666 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1679 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1692 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1703 S> */ B(LdaSmi), I8(2),
  /* 1705 E> */ B(Wide), B(StaGlobal), U16(128), U16(256),
                B(LdaUndefined),
  /* 1710 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name640"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name641"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name642"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name643"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name644"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name645"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name646"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name647"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name648"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name649"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name650"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name651"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name652"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name653"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name654"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name655"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name656"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name657"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name658"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name659"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name660"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name661"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name662"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name663"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name664"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name665"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name666"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name667"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name668"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name669"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name670"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name671"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name672"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name673"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name674"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name675"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name676"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name677"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name678"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name679"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name680"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name681"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name682"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name683"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name684"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name685"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name686"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name687"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name688"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name689"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name690"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name691"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name692"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name693"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name694"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name695"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name696"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name697"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name698"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name699"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name700"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name701"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name702"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name703"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name704"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name705"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name706"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name707"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name708"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name709"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name710"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name711"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name712"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name713"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name714"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name715"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name716"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name717"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name718"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name719"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name720"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name721"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name722"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name723"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name724"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name725"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name726"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name727"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name728"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name729"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name730"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name731"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name732"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name733"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name734"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name735"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name736"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name737"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name738"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name739"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name740"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name741"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name742"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name743"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name744"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name745"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name746"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name747"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name748"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name749"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name750"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name751"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name752"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name753"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name754"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name755"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name756"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name757"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name758"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name759"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name760"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name761"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name762"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name763"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name764"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name765"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name766"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name767"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

---
snippet: "
  a = 1;
  function f(c) {
    'use strict';
    var b = {};
    b.name768;
    b.name769;
    b.name770;
    b.name771;
    b.name772;
    b.name773;
    b.name774;
    b.name775;
    b.name776;
    b.name777;
    b.name778;
    b.name779;
    b.name780;
    b.name781;
    b.name782;
    b.name783;
    b.name784;
    b.name785;
    b.name786;
    b.name787;
    b.name788;
    b.name789;
    b.name790;
    b.name791;
    b.name792;
    b.name793;
    b.name794;
    b.name795;
    b.name796;
    b.name797;
    b.name798;
    b.name799;
    b.name800;
    b.name801;
    b.name802;
    b.name803;
    b.name804;
    b.name805;
    b.name806;
    b.name807;
    b.name808;
    b.name809;
    b.name810;
    b.name811;
    b.name812;
    b.name813;
    b.name814;
    b.name815;
    b.name816;
    b.name817;
    b.name818;
    b.name819;
    b.name820;
    b.name821;
    b.name822;
    b.name823;
    b.name824;
    b.name825;
    b.name826;
    b.name827;
    b.name828;
    b.name829;
    b.name830;
    b.name831;
    b.name832;
    b.name833;
    b.name834;
    b.name835;
    b.name836;
    b.name837;
    b.name838;
    b.name839;
    b.name840;
    b.name841;
    b.name842;
    b.name843;
    b.name844;
    b.name845;
    b.name846;
    b.name847;
    b.name848;
    b.name849;
    b.name850;
    b.name851;
    b.name852;
    b.name853;
    b.name854;
    b.name855;
    b.name856;
    b.name857;
    b.name858;
    b.name859;
    b.name860;
    b.name861;
    b.name862;
    b.name863;
    b.name864;
    b.name865;
    b.name866;
    b.name867;
    b.name868;
    b.name869;
    b.name870;
    b.name871;
    b.name872;
    b.name873;
    b.name874;
    b.name875;
    b.name876;
    b.name877;
    b.name878;
    b.name879;
    b.name880;
    b.name881;
    b.name882;
    b.name883;
    b.name884;
    b.name885;
    b.name886;
    b.name887;
    b.name888;
    b.name889;
    b.name890;
    b.name891;
    b.name892;
    b.name893;
    b.name894;
    b.name895;
    a = 2;
  }
  f({name: 1});
"
frame size: 1
parameter count: 2
bytecode array length: 524
bytecodes: [
  /*   49 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   57 S> */ B(GetNamedProperty), R(0), U8(0), U8(0),
  /*   70 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   83 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   96 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*  109 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*  122 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  135 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  148 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  161 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  174 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  187 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  200 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  213 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  226 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  239 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  252 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  265 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  278 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  291 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  304 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  317 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  330 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  343 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  356 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  369 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  382 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  395 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  408 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  421 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  434 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  447 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  460 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  473 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  486 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  499 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  512 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  525 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  538 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  551 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  564 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  577 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  590 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  603 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  616 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  629 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  642 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  655 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  668 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  681 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  694 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  707 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  720 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  733 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  746 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  759 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  772 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  785 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  798 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  811 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  824 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  837 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  850 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  863 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  876 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  889 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  902 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  915 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  928 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  941 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  954 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  967 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  980 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  993 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /* 1006 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /* 1019 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /* 1032 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /* 1045 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /* 1058 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /* 1071 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /* 1084 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /* 1097 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /* 1110 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1123 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1136 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1149 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1162 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1175 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1188 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1201 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1214 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1227 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1240 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1253 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1266 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1279 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1292 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1305 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1318 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1331 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1344 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1357 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1370 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1383 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1396 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1409 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1422 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1435 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1448 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1461 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1474 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1487 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1500 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1513 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1526 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1539 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1552 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1565 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1578 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1591 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1604 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1617 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1630 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1643 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1656 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1669 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1682 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1695 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1708 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1719 S> */ B(LdaSmi), I8(2),
  /* 1721 E> */ B(Wide), B(StaGlobal), U16(128), U16(256),
                B(LdaUndefined),
  /* 1726 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name768"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name769"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name770"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name771"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name772"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name773"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name774"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name775"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name776"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name777"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name778"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name779"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name780"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name781"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name782"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name783"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name784"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name785"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name786"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name787"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name788"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name789"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name790"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name791"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name792"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name793"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name794"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name795"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name796"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name797"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name798"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name799"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name800"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name801"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name802"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name803"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name804"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name805"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name806"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name807"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name808"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name809"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name810"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name811"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name812"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name813"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name814"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name815"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name816"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name817"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name818"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name819"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name820"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name821"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name822"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name823"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name824"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name825"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name826"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name827"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name828"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name829"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name830"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name831"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name832"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name833"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name834"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name835"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name836"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name837"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name838"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name839"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name840"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name841"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name842"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name843"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name844"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name845"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name846"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name847"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name848"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name849"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name850"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name851"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name852"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name853"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name854"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name855"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name856"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name857"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name858"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name859"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name860"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name861"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name862"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name863"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name864"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name865"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name866"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name867"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name868"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name869"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name870"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name871"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name872"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name873"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name874"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name875"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name876"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name877"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name878"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name879"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name880"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name881"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name882"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name883"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name884"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name885"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name886"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name887"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name888"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name889"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name890"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name891"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name892"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name893"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name894"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name895"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

