-- Native definitions

native "ACTIVATE_TIMECYCLE_EDITOR"
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Activates built-in timecycle editing tool.
</summary>
	]]

native "ADD_AUDIO_SUBMIX_OUTPUT"
	arguments {
		int "submixId" [=[ {} ]=],
		int "outputSubmixId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Adds an output for the specified audio submix.
</summary>
<param name="submixId">The input submix.</param>
<param name="outputSubmixId">The output submix. Use `0` for the master game submix.</param>
	]]

native "ADD_AUTHORIZED_PARACHUTE_MODEL"
	arguments {
		int "modelNameHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adds the given model name hash to the list of valid models for the player ped's parachute.
</summary>
<param name="modelNameHash">Name hash of the model to allow to be used for the player ped's parachute</param>
<returns>None.</returns>
	]]

native "ADD_AUTHORIZED_PARACHUTE_PACK_MODEL"
	arguments {
		int "modelNameHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adds the given model name hash to the list of valid models for the player ped's parachute pack.
</summary>
<param name="modelNameHash">Name hash of the model to allow to be used for the player ped's parachute pack</param>
<returns>None.</returns>
	]]

native "ADD_BLIP_FOR_COORD"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Blip"
	doc [[!
<summary>
Creates a blip for the specified coordinates. You can use `SET_BLIP_` natives to change the blip.

**This is the server-side RPC native equivalent of the client native [ADD_BLIP_FOR_COORD](?\_0x5A039BB0BCA604B6).**
</summary>
<param name="x">The X coordinate to create the blip on.</param>
<param name="y">The Y coordinate.</param>
<param name="z">The Z coordinate.</param>
<returns>A blip handle.</returns>
	]]

native "ADD_BLIP_FOR_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Blip"
	doc [[!
<summary>
Create a blip that by default is red (enemy), you can use [SET_BLIP_AS_FRIENDLY](#\_0xC6F43D0E) to make it blue (friend).\
Can be used for objects, vehicles and peds.
Example of enemy:
![enemy](https://i.imgur.com/fl78svv.png)
Example of friend:
![friend](https://i.imgur.com/Q16ho5d.png)

**This is the server-side RPC native equivalent of the client native [ADD_BLIP_FOR_ENTITY](?\_0x5CDE92C702A8FCE7).**
</summary>
<param name="entity">The entity handle to create the blip.</param>
<returns>A blip handle.</returns>
	]]

native "ADD_BLIP_FOR_RADIUS"
	arguments {
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "posZ" [=[ {} ]=],
		float "radius" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Blip"
	doc [[!
<summary>
Create a blip with a radius for the specified coordinates (it doesnt create the blip sprite, so you need to use [AddBlipCoords](#\_0xC6F43D0E))
Example image:
![example](https://i.imgur.com/9hQl3DB.png)

**This is the server-side RPC native equivalent of the client native [ADD_BLIP_FOR_RADIUS](?\_0x46818D79B1F7499A).**
</summary>
<param name="posX">The x position of the blip (you can also send a vector3 instead of the bulk coordinates)</param>
<param name="posY">The y position of the blip (you can also send a vector3 instead of the bulk coordinates)</param>
<param name="posZ">The z position of the blip (you can also send a vector3 instead of the bulk coordinates)</param>
<param name="radius">The number that defines the radius of the blip circle</param>
<returns>The blip handle that can be manipulated with every `SetBlip` natives</returns>
	]]

native "ADD_CONVAR_CHANGE_LISTENER"
	arguments {
		charPtr "conVarFilter" [=[ {} ]=],
		func "handler" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Adds a listener for Console Variable changes.

The function called expects to match the following signature:

```ts
function ConVarChangeListener(conVarName: string, reserved: any);
```

*   **conVarName**: The ConVar that changed.
*   **reserved**: Currently unused.
</summary>
<param name="conVarFilter">The Console Variable to listen for, this can be a pattern like "test:\*", or null for any</param>
<param name="handler">The handler function.</param>
<returns>A cookie to remove the change handler.</returns>
	]]

native "ADD_HEALTH_CONFIG"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "defaultHealth" [=[ {} ]=],
		float "defaultArmor" [=[ {} ]=],
		float "defaultEndurance" [=[ {} ]=],
		float "fatiguedHealthThreshold" [=[ {} ]=],
		float "injuredHealthThreshold" [=[ {} ]=],
		float "dyingHealthThreshold" [=[ {} ]=],
		float "hurtHealthThreshold" [=[ {} ]=],
		float "dogTakedownThreshold" [=[ {} ]=],
		float "writheFromBulletThreshold" [=[ {} ]=],
		BOOL "meleeCardinalFatalAttack" [=[ {} ]=],
		BOOL "invincible" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adds new health config.
</summary>
<param name="configName">Name of health config. Cannot be default game health config name.</param>
<param name="defaultHealth">Default health value.</param>
<param name="defaultArmor">Default armor value.</param>
<param name="fatiguedHealthThreshold">Fatigued health threshold value.</param>
<param name="injuredHealthThreshold">Injured health threshold value.</param>
<param name="dyingHealthThreshold">Dying health threshold value.</param>
<param name="hurtHealthThreshold">Hurt health threshold value.</param>
<param name="dogTakedownThreshold">Dog takedown threshold value.</param>
<param name="writheFromBulletThreshold">Writhe from bulled threshold value.</param>
<param name="meleeCardinalFatalAttack">Melee cardinal fatal attack check value.</param>
<param name="invincible">Invincible value.</param>
	]]

native "ADD_MINIMAP_OVERLAY"
	arguments {
		charPtr "name" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Loads a minimap overlay from a GFx file in the current resource.

If you need to control the depth of overlay use [`ADD_MINIMAP_OVERLAY_WITH_DEPTH`](#\_0xED0935B5).
</summary>
<param name="name">The path to a `.gfx` file in the current resource. It has to be specified as a `file`.</param>
<returns>A minimap overlay ID.</returns>
	]]

native "ADD_MINIMAP_OVERLAY_WITH_DEPTH"
	arguments {
		charPtr "name" [=[ {} ]=],
		int "depth" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Loads a minimap overlay from a GFx file in the current resource.
</summary>
<param name="name">The path to a `.gfx` file in the current resource. It has to be specified as a `file`.</param>
<param name="depth">The depth of new overlay on the minimap. Pass `-1` for game to figure out the highest depth itself. Should not be greater than `0x7EFFFFFD`.</param>
<returns>A minimap overlay ID.</returns>
	]]

native "ADD_PED_DECORATION_FROM_HASHES"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "collection" [=[ {} ]=],
		Hash "overlay" [=[ {} ]=],
	}
	alias "_APPLY_PED_OVERLAY"
	alias "_SET_PED_DECORATION"
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Applies an Item from a PedDecorationCollection to a ped. These include tattoos and shirt decals.
collection - PedDecorationCollection filename hash
overlay - Item name hash
Example:
Entry inside "mpbeach_overlays.xml" -
<Item>
<uvPos x="0.500000" y="0.500000" />
<scale x="0.600000" y="0.500000" />
<rotation value="0.000000" />
<nameHash>FM_Hair_Fuzz</nameHash>
<txdHash>mp_hair_fuzz</txdHash>
<txtHash>mp_hair_fuzz</txtHash>
<zone>ZONE_HEAD</zone>
<type>TYPE_TATTOO</type>
<faction>FM</faction>
<garment>All</garment>
<gender>GENDER_DONTCARE</gender>
<award />
<awardLevel />
</Item>
Code:
PED::_0x5F5D1665E352A839(PLAYER::PLAYER_PED_ID(), MISC::GET_HASH_KEY("mpbeach_overlays"), MISC::GET_HASH_KEY("fm_hair_fuzz"))
```

**This is the server-side RPC native equivalent of the client native [ADD_PED_DECORATION_FROM_HASHES](?\_0x5F5D1665E352A839).**
</summary>
	]]

native "ADD_REPLACE_TEXTURE"
	arguments {
		charPtr "origTxd" [=[ {} ]=],
		charPtr "origTxn" [=[ {} ]=],
		charPtr "newTxd" [=[ {} ]=],
		charPtr "newTxn" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Experimental natives, please do not use in a live environment.
</summary>
	]]

native "ADD_STATE_BAG_CHANGE_HANDLER"
	arguments {
		charPtr "keyFilter" [=[ {} ]=],
		charPtr "bagFilter" [=[ {} ]=],
		func "handler" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Adds a handler for changes to a state bag.

The function called expects to match the following signature:

```ts
function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
```

*   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
    or `localEntity:Handle`.
*   **key**: The changed key.
*   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
*   **reserved**: Currently unused.
*   **replicated**: Whether the set is meant to be replicated.

At this time, the change handler can't opt to reject changes.

If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](#\_0x4BDF1867) to get the entity handle
If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](#\_0xA56135E0) to get the player handle
</summary>
<param name="keyFilter">The key to check for, or null for no filter.</param>
<param name="bagFilter">The bag ID to check for such as `entity:65535`, or null for no filter.</param>
<param name="handler">The handler function.</param>
<returns>A cookie to remove the change handler.</returns>
	]]

native "ADD_TEXT_ENTRY"
	arguments {
		charPtr "entryKey" [=[ {} ]=],
		charPtr "entryText" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "ADD_TEXT_ENTRY_BY_HASH"
	arguments {
		Hash "entryKey" [=[ {} ]=],
		charPtr "entryText" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "APPLY_FORCE_TO_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
		int "forceType" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "offX" [=[ {} ]=],
		float "offY" [=[ {} ]=],
		float "offZ" [=[ {} ]=],
		int "nComponent" [=[ {} ]=],
		BOOL "bLocalForce" [=[ {} ]=],
		BOOL "bLocalOffset" [=[ {} ]=],
		BOOL "bScaleByMass" [=[ {} ]=],
		BOOL "bPlayAudio" [=[ {} ]=],
		BOOL "bScaleByTimeWarp" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```cpp
enum eApplyForceTypes {
APPLY_TYPE_FORCE = 0,
APPLY_TYPE_IMPULSE = 1,
APPLY_TYPE_EXTERNAL_FORCE = 2,
APPLY_TYPE_EXTERNAL_IMPULSE = 3,
APPLY_TYPE_TORQUE = 4,
APPLY_TYPE_ANGULAR_IMPULSE = 5
}
```

**This is the server-side RPC native equivalent of the client native [APPLY_FORCE_TO_ENTITY](?\_0xC5F68BE9613E2D18).**
</summary>
<param name="entity">The entity handle</param>
<param name="forceType">The force type</param>
<param name="x">The x component of the force to apply</param>
<param name="y">The y component of the force to apply</param>
<param name="z">The z component of the force to apply</param>
<param name="offX">Offset from center of entity (X)</param>
<param name="offY">Offset from center of entity (Y)</param>
<param name="offZ">Offset from center of entity (Z)</param>
<param name="nComponent">Component of the entity to apply the force too. Only matters for breakable or articulated (ragdoll) physics. 0 means the root or parent component</param>
<param name="bLocalForce">Specifies whether the force vector passed in is in local or world coordinates. `true` means the force will get automatically transformed into world space before being applied</param>
<param name="bLocalOffset">Specifies whether the offset passed in is in local or world coordinates</param>
<param name="bScaleByMass">Specifies whether to scale the force by mass</param>
<param name="bPlayAudio">Specifies whether to play audio events related to the force being applied. The audio will depend on the entity type. Currently vehicles are the only entity types supported, and will play a suspension squeal depending on the magnitude of the force</param>
<param name="bScaleByTimeWarp">Specifies whether to scale the force by time warp. Default is `true`</param>
	]]

native "APPLY_WEATHER_CYCLES"
	arguments {
		int "numEntries" [=[ {} ]=],
		int "msPerCycle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<param name="numEntries">The number of cycle entries. Must be between 1 and 256</param>
<param name="msPerCycle">The duration in milliseconds of each cycle. Must be between 1000 and 86400000 (24 hours)</param>
<returns>Returns true if all parameters were valid, otherwise false.</returns>
	]]

native "BREAK_OFF_VEHICLE_WHEEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		BOOL "leaveDebrisTrail" [=[ {} ]=],
		BOOL "deleteWheel" [=[ {} ]=],
		BOOL "unknownFlag" [=[ {} ]=],
		BOOL "putOnFire" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Break off vehicle wheel by index. The `leaveDebrisTrail` flag requires `putOnFire` to be true.
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="wheelIndex">The wheel index.</param>
<param name="leaveDebrisTrail">Start "veh_debris_trail" ptfx.</param>
<param name="deleteWheel">True to delete wheel, otherwise detach.</param>
<param name="unknownFlag">Unknown flag.</param>
<param name="putOnFire">Set wheel on fire once detached.</param>
	]]

native "CALL_MINIMAP_SCALEFORM_FUNCTION"
	arguments {
		int "miniMap" [=[ {} ]=],
		charPtr "fnName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This is similar to the PushScaleformMovieFunction natives, except it calls in the `TIMELINE` of a minimap overlay.
</summary>
<param name="miniMap">The minimap overlay ID.</param>
<param name="fnName">A function in the overlay's TIMELINE.</param>
	]]

native "CAN_PLAYER_START_COMMERCE_SESSION"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Returns whether or not the specified player has enough information to start a commerce session for.
</summary>
<param name="playerSrc">The player handle</param>
<returns>True or false.</returns>
	]]

native "CANCEL_EVENT"
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Cancels the currently executing event.
</summary>
	]]

native "CLEAR_DRAW_ORIGIN"
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Resets the screen's draw-origin which was changed by the function [`SET_DRAW_ORIGIN`](#\_0xE10198D5) back to `x=0, y=0`. See [`SET_DRAW_ORIGIN`](#\_0xE10198D5) for further information.
</summary>
	]]

native "CLEAR_PED_PROP"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "propId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
CLEAR_PED_PROP

**This is the server-side RPC native equivalent of the client native [CLEAR_PED_PROP](?\_0x0943E5B8E078E76E).**
</summary>
<param name="ped">The ped handle.</param>
<param name="propId">The prop id you want to clear from the ped. Refer to [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F).</param>
	]]

native "CLEAR_PED_SECONDARY_TASK"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
CLEAR_PED_SECONDARY_TASK

**This is the server-side RPC native equivalent of the client native [CLEAR_PED_SECONDARY_TASK](?\_0x176CECF6F920D707).**
</summary>
	]]

native "CLEAR_PED_TASKS"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Clear a ped's tasks. Stop animations and other tasks created by scripts.

**This is the server-side RPC native equivalent of the client native [CLEAR_PED_TASKS](?\_0xE1EF3C1216AFF2CD).**
</summary>
<param name="ped">Ped id. Use PlayerPedId() for your own character.</param>
	]]

native "CLEAR_PED_TASKS_IMMEDIATELY"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Immediately stops the pedestrian from whatever it's doing. The difference between this and [CLEAR_PED_TASKS](#\_0xE1EF3C1216AFF2CD) is that this one teleports the ped but does not change the position of the ped.

**This is the server-side RPC native equivalent of the client native [CLEAR_PED_TASKS_IMMEDIATELY](?\_0xAAA34F8A7CB32098).**
</summary>
<param name="ped">Ped id.</param>
	]]

native "CLEAR_PLAYER_WANTED_LEVEL"
	arguments {
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
This executes at the same as speed as PLAYER::SET_PLAYER_WANTED_LEVEL(player, 0, false);
PLAYER::GET_PLAYER_WANTED_LEVEL(player); executes in less than half the time. Which means that it's worth first checking if the wanted level needs to be cleared before clearing. However, this is mostly about good code practice and can important in other situations. The difference in time in this example is negligible.
```

**This is the server-side RPC native equivalent of the client native [CLEAR_PLAYER_WANTED_LEVEL](?\_0xB302540597885499).**
</summary>
	]]

native "CLEAR_VEHICLE_XENON_LIGHTS_CUSTOM_COLOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Removes vehicle xenon lights custom RGB color.
</summary>
<param name="vehicle">The vehicle handle.</param>
	]]

native "CLONE_TIMECYCLE_MODIFIER"
	arguments {
		charPtr "sourceModifierName" [=[ {} ]=],
		charPtr "clonedModifierName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<param name="sourceModifierName">The source timecycle name.</param>
<param name="clonedModifierName">The clone timecycle name, must be unique.</param>
<returns>The cloned timecycle modifier index, or -1 if failed.</returns>
	]]

native "COMMIT_RUNTIME_TEXTURE"
	arguments {
		long "tex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Commits the backing pixels to the specified runtime texture.
</summary>
<param name="tex">The runtime texture handle.</param>
	]]

native "CREATE_AUDIO_SUBMIX"
	arguments {
		charPtr "name" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Creates an audio submix with the specified name, or gets the existing audio submix by that name.
</summary>
<param name="name">The audio submix name.</param>
<returns>A submix ID, or -1 if the submix could not be created.</returns>
	]]

native "CREATE_DRY_VOLUME"
	arguments {
		float "xMin" [=[ {} ]=],
		float "yMin" [=[ {} ]=],
		float "zMin" [=[ {} ]=],
		float "xMax" [=[ {} ]=],
		float "yMax" [=[ {} ]=],
		float "zMax" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Creates a volume where water effects do not apply.
Useful for preventing water collisions from flooding areas underneath them.
This has no effect on waterquads, only water created from drawables and collisions.
Don't create volumes when your local ped is swimming (e.g. use IS_PED_SWIMMING in your scripts before you call this)
</summary>
<param name="xMin">The min X component of the AABB volume where water does not affect the player.</param>
<param name="yMin">The min Y component for the AABB volume.</param>
<param name="zMin">The min Z component for the AABB volume.</param>
<param name="xMax">The max X component for the AABB volume.</param>
<param name="yMax">The max Y component for the AABB volume.</param>
<param name="zMax">The max Z component for the AABB volume.</param>
	]]

native "CREATE_DUI"
	arguments {
		charPtr "url" [=[ {} ]=],
		int "width" [=[ {} ]=],
		int "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "long"
	doc [[!
<summary>
Creates a DUI browser. This can be used to draw on a runtime texture using CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.
</summary>
<param name="url">The initial URL to load in the browser.</param>
<param name="width">The width of the backing surface.</param>
<param name="height">The height of the backing surface.</param>
<returns>A DUI object.</returns>
	]]

native "CREATE_OBJECT"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		BOOL "isNetwork" [=[ {} ]=],
		BOOL "netMissionEntity" [=[ {} ]=],
		BOOL "doorFlag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Creates an object (prop) with the specified model at the specified position, offset on the Z axis by the radius of the object's model.
This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).

**This is the server-side RPC native equivalent of the client native [CREATE_OBJECT](?\_0x509D5878EB39E842).**
</summary>
<param name="modelHash">The model to spawn.</param>
<param name="x">Spawn coordinate X component.</param>
<param name="y">Spawn coordinate Y component.</param>
<param name="z">Spawn coordinate Z component, 'ground level'.</param>
<param name="isNetwork">Whether to create a network object for the object. If false, the object exists only locally.</param>
<param name="netMissionEntity">Whether to register the object as pinned to the script host in the R\* network model.</param>
<param name="doorFlag">False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.</param>
<returns>A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.</returns>
	]]

native "CREATE_OBJECT_NO_OFFSET"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		BOOL "isNetwork" [=[ {} ]=],
		BOOL "netMissionEntity" [=[ {} ]=],
		BOOL "doorFlag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Creates an object (prop) with the specified model centered at the specified position.
This object will initially be owned by the creating script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).

**This is the server-side RPC native equivalent of the client native [CREATE_OBJECT_NO_OFFSET](?\_0x9A294B2138ABB884).**
</summary>
<param name="modelHash">The model to spawn.</param>
<param name="x">Spawn coordinate X component.</param>
<param name="y">Spawn coordinate Y component.</param>
<param name="z">Spawn coordinate Z component.</param>
<param name="isNetwork">Whether to create a network object for the object. If false, the object exists only locally.</param>
<param name="netMissionEntity">Whether to register the object as pinned to the script host in the R\* network model.</param>
<param name="doorFlag">False to create a door archetype (archetype flag bit 26 set) as a door. Required to be set to true to create door models in network mode.</param>
<returns>A script handle (fwScriptGuid index) for the object, or `0` if the object failed to be created.</returns>
	]]

native "CREATE_PED"
	arguments {
		int "pedType" [=[ {} ]=],
		Hash "modelHash" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "heading" [=[ {} ]=],
		BOOL "isNetwork" [=[ {} ]=],
		BOOL "bScriptHostPed" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Creates a ped (biped character, pedestrian, actor) with the specified model at the specified position and heading.
This ped will initially be owned by the creating script as a mission entity, and the model should be loaded already
(e.g. using REQUEST_MODEL).

**This is the server-side RPC native equivalent of the client native [CREATE_PED](?\_0xD49F9B0955C367DE).**
</summary>
<param name="pedType">Unused. Peds get set to CIVMALE/CIVFEMALE/etc. no matter the value specified.</param>
<param name="modelHash">The model of ped to spawn.</param>
<param name="x">Spawn coordinate X component.</param>
<param name="y">Spawn coordinate Y component.</param>
<param name="z">Spawn coordinate Z component.</param>
<param name="heading">Heading to face towards, in degrees.</param>
<param name="isNetwork">Whether to create a network object for the ped. If false, the ped exists only locally.</param>
<param name="bScriptHostPed">Whether to register the ped as pinned to the script host in the R\* network model.</param>
<returns>A script handle (fwScriptGuid index) for the ped, or `0` if the ped failed to be created.</returns>
	]]

native "CREATE_PED_INSIDE_VEHICLE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "pedType" [=[ {} ]=],
		Hash "modelHash" [=[ {} ]=],
		int "seat" [=[ {} ]=],
		BOOL "isNetwork" [=[ {} ]=],
		BOOL "bScriptHostPed" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
CREATE_PED_INSIDE_VEHICLE

**This is the server-side RPC native equivalent of the client native [CREATE_PED_INSIDE_VEHICLE](?\_0x7DD959874C1FD534).**
</summary>
<param name="pedType">See [`CREATE_PED`](#\_0xD49F9B0955C367DE)</param>
	]]

native "CREATE_RUNTIME_TEXTURE"
	arguments {
		long "txd" [=[ {} ]=],
		charPtr "txn" [=[ {} ]=],
		int "width" [=[ {} ]=],
		int "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "long"
	doc [[!
<summary>
Creates a blank runtime texture.
</summary>
<param name="txd">A handle to the runtime TXD to create the runtime texture in.</param>
<param name="txn">The name for the texture in the runtime texture dictionary.</param>
<param name="width">The width of the new texture.</param>
<param name="height">The height of the new texture.</param>
<returns>A runtime texture handle.</returns>
	]]

native "CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE"
	arguments {
		long "txd" [=[ {} ]=],
		charPtr "txn" [=[ {} ]=],
		charPtr "duiHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "long"
	doc [[!
<summary>
Creates a runtime texture from a DUI handle.
</summary>
<param name="txd">A handle to the runtime TXD to create the runtime texture in.</param>
<param name="txn">The name for the texture in the runtime texture dictionary.</param>
<param name="duiHandle">The DUI handle returned from GET_DUI_HANDLE.</param>
<returns>The runtime texture handle.</returns>
	]]

native "CREATE_RUNTIME_TEXTURE_FROM_IMAGE"
	arguments {
		long "txd" [=[ {} ]=],
		charPtr "txn" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "long"
	doc [[!
<summary>
Creates a runtime texture from the specified file in the current resource or a base64 data URL.
</summary>
<param name="txd">A handle to the runtime TXD to create the runtime texture in.</param>
<param name="txn">The name for the texture in the runtime texture dictionary.</param>
<param name="fileName">The file name of an image to load or a base64 data URL. This should preferably be a PNG, and has to be specified as a `file` in the resource manifest.</param>
<returns>A runtime texture handle.</returns>
	]]

native "CREATE_RUNTIME_TXD"
	arguments {
		charPtr "name" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "long"
	doc [[!
<summary>
Creates a runtime texture dictionary with the specified name.
Example:

```lua
local txd = CreateRuntimeTxd('meow')
```
</summary>
<param name="name">The name for the runtime TXD.</param>
<returns>A handle to the runtime TXD.</returns>
	]]

native "CREATE_TIMECYCLE_MODIFIER"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Create a clean timecycle modifier. See [`SET_TIMECYCLE_MODIFIER_VAR`](#\_0x6E0A422B) to add variables.
</summary>
<param name="modifierName">The new timecycle name, must be unique.</param>
<returns>The created timecycle modifier index, or -1 if failed.</returns>
	]]

native "CREATE_VEHICLE"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "heading" [=[ {} ]=],
		BOOL "isNetwork" [=[ {} ]=],
		BOOL "netMissionEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Creates a vehicle with the specified model at the specified position. This vehicle will initially be owned by the creating
script as a mission entity, and the model should be loaded already (e.g. using REQUEST_MODEL).

```
NativeDB Added Parameter 8: BOOL p7
```

**This is the server-side RPC native equivalent of the client native [CREATE_VEHICLE](?\_0xAF35D0D2583051B0).**
</summary>
<param name="modelHash">The model of vehicle to spawn.</param>
<param name="x">Spawn coordinate X component.</param>
<param name="y">Spawn coordinate Y component.</param>
<param name="z">Spawn coordinate Z component.</param>
<param name="heading">Heading to face towards, in degrees.</param>
<param name="isNetwork">Whether to create a network object for the vehicle. If false, the vehicle exists only locally.</param>
<param name="netMissionEntity">Whether to register the vehicle as pinned to the script host in the R\* network model.</param>
<returns>A script handle (fwScriptGuid index) for the vehicle, or `0` if the vehicle failed to be created.</returns>
	]]

native "CREATE_VEHICLE_SERVER_SETTER"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		charPtr "type" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "heading" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vehicle"
	doc [[!
<summary>
Equivalent to CREATE_VEHICLE, but it uses 'server setter' logic (like the former CREATE_AUTOMOBILE) as a workaround for
reliability concerns regarding entity creation RPC.

Unlike CREATE_AUTOMOBILE, this supports other vehicle types as well.
</summary>
<param name="modelHash">The model of vehicle to spawn.</param>
<param name="type">The appropriate vehicle type for the model info. Can be one of `automobile`, `bike`, `boat`, `heli`, `plane`, `submarine`, `trailer`, and (potentially), `train`. This should be the same type as the `type` field in `vehicles.meta`.</param>
<param name="x">Spawn coordinate X component.</param>
<param name="y">Spawn coordinate Y component.</param>
<param name="z">Spawn coordinate Z component.</param>
<param name="heading">Heading to face towards, in degrees.</param>
<returns>A script handle for the vehicle, or 0 if the vehicle failed to be created.</returns>
	]]

native "DELETE_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Deletes the specified entity.

**NOTE**: For trains this will only work if called on the train engine, it will not work on its carriages.
</summary>
<param name="entity">The entity to delete.</param>
	]]

native "DELETE_FUNCTION_REFERENCE"
	arguments {
		charPtr "referenceIdentity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
	]]

native "DELETE_RESOURCE_KVP"
	arguments {
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<param name="key">The key to delete</param>
	]]

native "DELETE_RESOURCE_KVP_NO_SYNC"
	arguments {
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
</summary>
<param name="key">The key to delete</param>
	]]

native "DELETE_TRAIN"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Deletes the specified `entity` and any carriage its attached to, or that is attached to it.
</summary>
<param name="entity">The carriage to delete.</param>
	]]

native "DESTROY_DUI"
	arguments {
		long "duiObject" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Destroys a DUI browser.
</summary>
<param name="duiObject">The DUI browser handle.</param>
	]]

native "DISABLE_EDITOR_RUNTIME"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables the editor runtime mode, changing game behavior to not track entity metadata.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
	]]

native "DISABLE_IDLE_CAMERA"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables the game's afk camera that starts panning around after 30 seconds of inactivity.
</summary>
<param name="state">On/Off</param>
	]]

native "DISABLE_RAW_KEY_THIS_FRAME"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Disables the specified `rawKeyIndex`, making it not trigger the regular `IS_RAW_KEY_*` natives.

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of down state.</returns>
	]]

native "DISABLE_VEHICLE_PASSENGER_IDLE_CAMERA"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables the game's afk camera that starts panning around after 30 seconds of inactivity(While riding in a car as a passenger)
</summary>
<param name="state">On/Off</param>
	]]

native "DISABLE_WORLDHORIZON_RENDERING"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Disables the game's world horizon lods rendering (see `farlods.#dd`).
Using the island hopper natives might also affect this state.
</summary>
<param name="state">On/Off</param>
	]]

native "DOES_BOAT_SINK_WHEN_WRECKED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<returns>Returns whether or not the boat sinks when wrecked.</returns>
	]]

native "DOES_ENTITY_EXIST"
	arguments {
		Object "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "DOES_PLAYER_EXIST"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Returns whether or not the player exists
</summary>
<returns>True if the player exists, false otherwise</returns>
	]]

native "DOES_PLAYER_OWN_SKU"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "skuId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Requests whether or not the player owns the specified SKU.
</summary>
<param name="playerSrc">The player handle</param>
<param name="skuId">The ID of the SKU.</param>
<returns>A boolean.</returns>
	]]

native "DOES_PLAYER_OWN_SKU_EXT"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "skuId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Requests whether or not the player owns the specified package.
</summary>
<param name="playerSrc">The player handle</param>
<param name="skuId">The package ID on Tebex.</param>
<returns>A boolean.</returns>
	]]

native "DOES_TEXTURE_EXIST"
	arguments {
		int "textureId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "bool"
	doc [[!
<summary>
In compare to `0x31DC8D3F216D8509` return true if texture its created when `0x31DC8D3F216D8509` return true if you put there any id in valid range
</summary>
<param name="textureId">texture id created by `0xC5E7204F322E49EB`.</param>
	]]

native "DOES_TIMECYCLE_MODIFIER_HAS_VAR"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
		charPtr "varName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<param name="modifierName">The name of timecycle modifier.</param>
<param name="varName">The name of timecycle variable.</param>
<returns>Whether or not variable by name was found on the specified timecycle modifier.</returns>
	]]

native "DOES_TRAIN_STOP_AT_STATIONS"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="train">The train handle</param>
<returns>True if the train stops at stations. False otherwise</returns>
	]]

native "DOES_VEHICLE_USE_FUEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Checks whether the vehicle consumes fuel. The check is done based on petrol tank volume and vehicle type. Bicycles and vehicles with petrol tank volume equal to zero (only bicycles by default) do not use fuel. All other vehicles do.

You can customize petrol tank volume using [`SET_HANDLING_FLOAT`](#\_0x90DD01C)/[`SET_VEHICLE_HANDLING_FLOAT`](#\_0x488C86D2) natives with `fieldName` equal to `fPetrolTankVolume`.
</summary>
<param name="vehicle">The vehicle handle.</param>
<returns>True if the vehicle uses fuel to move. False otherwise.</returns>
	]]

native "DOOR_SYSTEM_GET_ACTIVE"
	ns "CFX"
    apiset "client"
	returns "object"
	doc [[!
<summary>
Returns a list of door system entries: a door system hash (see [ADD_DOOR_TO_SYSTEM](#\_0x6F8838D03D1DC226)) and its object handle.

The data returned adheres to the following layout:

```
[{doorHash1, doorHandle1}, ..., {doorHashN, doorHandleN}]
```
</summary>
<returns>An object containing a list of door system entries.</returns>
	]]

native "DOOR_SYSTEM_GET_SIZE"
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<returns>The number of doors registered in the system</returns>
	]]

native "DRAW_BOX"
	arguments {
		float "x1" [=[ {} ]=],
		float "y1" [=[ {} ]=],
		float "z1" [=[ {} ]=],
		float "x2" [=[ {} ]=],
		float "y2" [=[ {} ]=],
		float "z2" [=[ {} ]=],
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
		int "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "DRAW_CORONA"
	arguments {
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "posZ" [=[ {} ]=],
		float "size" [=[ {} ]=],
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
		int "alpha" [=[ {} ]=],
		float "intensity" [=[ {} ]=],
		float "zBias" [=[ {} ]=],
		float "dirX" [=[ {} ]=],
		float "dirY" [=[ {} ]=],
		float "dirZ" [=[ {} ]=],
		float "viewThreshold" [=[ {} ]=],
		float "innerAngle" [=[ {} ]=],
		float "outerAngle" [=[ {} ]=],
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Allows drawing advanced light effects, known as coronas, which support flares, volumetric lighting, and customizable glow properties.
</summary>
<param name="posX">The X position of the corona origin.</param>
<param name="posY">The Y position of the corona origin.</param>
<param name="posZ">The Z position of the corona origin.</param>
<param name="size">The size of the corona.</param>
<param name="red">The red component of the marker color, on a scale from 0-255.</param>
<param name="green">The green component of the marker color, on a scale from 0-255.</param>
<param name="blue">The blue component of the marker color, on a scale from 0-255.</param>
<param name="alpha">The alpha component of the marker color, on a scale from 0-255.</param>
<param name="intensity">The intensity of the corona.</param>
<param name="zBias">zBias slightly shifts the depth of surfaces to make sure they don’t overlap or cause visual glitches when they are very close together. The zBias value are usually in the range of 0.0 to 1.0.</param>
<param name="dirX">The X direction of the corona.</param>
<param name="dirY">The Y direction of the corona.</param>
<param name="dirZ">The Z direction of the corona.</param>
<param name="viewThreshold">The view threshold is to determine the fading of the corona based on the distance.</param>
<param name="innerAngle">The inner angle of the corona.</param>
<param name="outerAngle">The outer angle of the corona.</param>
<param name="flags">The corona flags.</param>
	]]

native "DRAW_GIZMO"
	arguments {
		long "matrixPtr" [=[ {} ]=],
		charPtr "id" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Draws a gizmo. This function supports SDK infrastructure and is not intended to be used directly from your code.

This should be used from JavaScript or another language supporting mutable buffers like ArrayBuffer.

Matrix layout is as follows:

*   Element \[0], \[1] and \[2] should represent the right vector.
*   Element \[4], \[5] and \[6] should represent the forward vector.
*   Element \[8], \[9] and \[10] should represent the up vector.
*   Element \[12], \[13] and \[14] should represent X, Y and Z translation coordinates.
*   All other elements should be \[0, 0, 0, 1].
</summary>
<param name="matrixPtr">A mutable pointer to a 64-byte buffer of floating-point values, representing an XMFLOAT4X4 in layout.</param>
<param name="id">A unique identifier of what the gizmo is affecting.</param>
<returns>Whether or not the matrix was modified.</returns>
	]]

native "DRAW_GLOW_SPHERE"
	arguments {
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "posZ" [=[ {} ]=],
		float "radius" [=[ {} ]=],
		int "colorR" [=[ {} ]=],
		int "colorG" [=[ {} ]=],
		int "colorB" [=[ {} ]=],
		float "intensity" [=[ {} ]=],
		BOOL "invert" [=[ {} ]=],
		BOOL "marker" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Draw a glow sphere this frame. Up to 256 per single frame.
</summary>
<param name="posX">Position X.</param>
<param name="posY">Position Y.</param>
<param name="posZ">Position Z.</param>
<param name="radius">Sphere radius.</param>
<param name="colorR">Red.</param>
<param name="colorG">Green.</param>
<param name="colorB">Blue.</param>
<param name="intensity">Intensity.</param>
<param name="invert">Invert rendering.</param>
<param name="marker">Draw as a marker, otherwise as an overlay.</param>
	]]

native "DRAW_LIGHT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Draw the prepared light.
</summary>
	]]

native "DRAW_LINE"
	arguments {
		float "x1" [=[ {} ]=],
		float "y1" [=[ {} ]=],
		float "z1" [=[ {} ]=],
		float "x2" [=[ {} ]=],
		float "y2" [=[ {} ]=],
		float "z2" [=[ {} ]=],
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
		int "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "DRAW_LINE_2D"
	arguments {
		float "x1" [=[ {} ]=],
		float "y1" [=[ {} ]=],
		float "x2" [=[ {} ]=],
		float "y2" [=[ {} ]=],
		float "width" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
		int "a" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Like DRAW_RECT, but it's a line.
</summary>
<param name="x1">First x.</param>
<param name="y1">First y.</param>
<param name="x2">Second x.</param>
<param name="y2">Second y.</param>
<param name="width">Width of line.</param>
<param name="r">Red.</param>
<param name="g">Green.</param>
<param name="b">Blue.</param>
<param name="a">Alpha.</param>
	]]

native "DRAW_POLY"
	arguments {
		float "x1" [=[ {} ]=],
		float "y1" [=[ {} ]=],
		float "z1" [=[ {} ]=],
		float "x2" [=[ {} ]=],
		float "y2" [=[ {} ]=],
		float "z2" [=[ {} ]=],
		float "x3" [=[ {} ]=],
		float "y3" [=[ {} ]=],
		float "z3" [=[ {} ]=],
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
		int "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "DRAW_RECT_ROTATED"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "width" [=[ {} ]=],
		float "height" [=[ {} ]=],
		float "rotation" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
		int "a" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
DRAW_RECT, but with a rotation. Seems to be broken.
</summary>
	]]

native "DROP_PLAYER"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		charPtr "reason" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "DUPLICATE_FUNCTION_REFERENCE"
	arguments {
		charPtr "referenceIdentity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
	]]

native "ENABLE_EDITOR_RUNTIME"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Enables the editor runtime mode, changing game behavior to track entity metadata.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
	]]

native "ENABLE_ENHANCED_HOST_SUPPORT"
	arguments {
		BOOL "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "END_FIND_KVP"
	arguments {
		int "handle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<param name="handle">The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)</param>
<returns>None.</returns>
	]]

native "END_FIND_OBJECT"
	arguments {
		int "findHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "END_FIND_PED"
	arguments {
		int "findHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "END_FIND_PICKUP"
	arguments {
		int "findHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "END_FIND_VEHICLE"
	arguments {
		int "findHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "ENSURE_ENTITY_STATE_BAG"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Internal function for ensuring an entity has a state bag.
</summary>
	]]

native "ENTER_CURSOR_MODE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Enters cursor mode, suppressing mouse movement to the game and displaying a mouse cursor instead. This function supports
SDK infrastructure and is not intended to be used directly from your code.
</summary>
	]]

native "EXECUTE_COMMAND"
	arguments {
		charPtr "commandString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Depending on your use case you may need to use `add_acl resource.<your_resource_name> command.<command_name> allow` to use this native in your resource.
</summary>
	]]

native "EXPERIMENTAL_LOAD_CLONE_CREATE"
	arguments {
		charPtr "data" [=[ {} ]=],
		int "objectId" [=[ {} ]=],
		charPtr "tree" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "Entity"
	doc [[!
<summary>
This native is not implemented.
</summary>
	]]

native "EXPERIMENTAL_LOAD_CLONE_SYNC"
	arguments {
		Entity "entity" [=[ {} ]=],
		charPtr "data" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native is not implemented.
</summary>
	]]

native "EXPERIMENTAL_SAVE_CLONE_CREATE"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
This native is not implemented.
</summary>
	]]

native "EXPERIMENTAL_SAVE_CLONE_SYNC"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
This native is not implemented.
</summary>
	]]

native "FIND_FIRST_OBJECT"
	arguments {
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
	]]

native "FIND_FIRST_PED"
	arguments {
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
	]]

native "FIND_FIRST_PICKUP"
	arguments {
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
	]]

native "FIND_FIRST_VEHICLE"
	arguments {
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
	]]

native "FIND_KVP"
	arguments {
		int "handle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<param name="handle">The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)</param>
<returns>None.</returns>
	]]

native "FIND_NEXT_OBJECT"
	arguments {
		int "findHandle" [=[ {} ]=],
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "FIND_NEXT_PED"
	arguments {
		int "findHandle" [=[ {} ]=],
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "FIND_NEXT_PICKUP"
	arguments {
		int "findHandle" [=[ {} ]=],
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "FIND_NEXT_VEHICLE"
	arguments {
		int "findHandle" [=[ {} ]=],
		EntityPtr "outEntity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "FLAG_SERVER_AS_PRIVATE"
	arguments {
		BOOL "private_" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "FLUSH_RESOURCE_KVP"
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Nonsynchronous operations will not wait for a disk/filesystem flush before returning from a write or delete call. They will be much faster than their synchronous counterparts (e.g., bulk operations), however, a system crash may lose the data to some recent operations.

This native ensures all `_NO_SYNC` operations are synchronized with the disk/filesystem.
</summary>
	]]

native "FORCE_SNOW_PASS"
	arguments {
		BOOL "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Forces the game snow pass to render.
</summary>
<param name="enabled">Whether or not to force rendering to use a snow pass.</param>
	]]

native "FORMAT_STACK_TRACE"
	arguments {
		object "traceData" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
An internal function for converting a stack trace object to a string.
</summary>
	]]

native "FREEZE_ENTITY_POSITION"
	arguments {
		Entity "entity" [=[ {} ]=],
		BOOL "toggle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Freezes or unfreezes an entity preventing its coordinates to change by the player if set to `true`. You can still change the entity position using [`SET_ENTITY_COORDS`](#\_0x06843DA7060A026B).

**This is the server-side RPC native equivalent of the client native [FREEZE_ENTITY_POSITION](?\_0x428CA6DBD1094446).**
</summary>
<param name="entity">The entity to freeze/unfreeze.</param>
<param name="toggle">Freeze or unfreeze entity.</param>
	]]

native "GET_ACTIVE_PLAYERS"
	ns "CFX"
    apiset "client"
	returns "object"
	doc [[!
<summary>
Returns all player indices for 'active' physical players known to the client.
The data returned adheres to the following layout:

```
[127, 42, 13, 37]
```
</summary>
<returns>An object containing a list of player indices.</returns>
	]]

native "GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
<param name="playerSrc">The player handle</param>
	]]

native "GET_ALL_OBJECTS"
	ns "CFX"
    apiset "server"
	returns "object"
	doc [[!
<summary>
Returns all object handles known to the server.
The data returned adheres to the following layout:

```
[127, 42, 13, 37]
```
</summary>
<returns>An object containing a list of object handles.</returns>
	]]

native "GET_ALL_PEDS"
	ns "CFX"
    apiset "server"
	returns "object"
	doc [[!
<summary>
Returns all peds handles known to the server.
The data returned adheres to the following layout:

```
[127, 42, 13, 37]
```
</summary>
<returns>An object containing a list of peds handles.</returns>
	]]

native "GET_ALL_ROPES"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "object"
	doc [[!
<summary>
Returns all rope handles. The data returned adheres to the following layout:

```
[ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
```
</summary>
<returns>An object containing a list of all rope handles.</returns>
	]]

native "GET_ALL_TRACK_JUNCTIONS"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "object"
	doc [[!
<summary>
Returns all track junctions on the client
The data returned adheres to the following structure:

```
[1, 2, 4, 6, 69, 420]
```
</summary>
<returns>An object containing a list of track junctions ids.```
```</returns>
	]]

native "GET_ALL_VEHICLE_MODELS"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "object"
	doc [[!
<summary>
Returns all registered vehicle model names, including non-dlc vehicles and custom vehicles in no particular order.

**Example output**

```
	["dubsta", "dubsta2", "dubsta3", "myverycoolcar", "sultan", "sultanrs", ...]
```

This native will not return vehicles that are unregistered (i.e from a resource being stopped) during runtime.
</summary>
	]]

native "GET_ALL_VEHICLES"
	ns "CFX"
    apiset "server"
	returns "object"
	doc [[!
<summary>
Returns all vehicle handles known to the server.
The data returned adheres to the following layout:

```
[127, 42, 13, 37]
```
</summary>
<returns>An object containing a list of vehicle handles.</returns>
	]]

native "GET_AMBIENT_PED_RANGE_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_AMBIENT_PED_RANGE_MULTIPLIER_THIS_FRAME](#\_0x0B919E1FB47CC4E0).
</summary>
<returns>Returns ambient ped range multiplier value.</returns>
	]]

native "GET_AMBIENT_VEHICLE_RANGE_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_AMBIENT_VEHICLE_RANGE_MULTIPLIER_THIS_FRAME](#\_0x90B6DA738A9A25DA).
</summary>
<returns>Returns ambient vehicle range multiplier value.</returns>
	]]

native "GET_ASPECT_RATIO"
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "float"
	doc [[!
<summary>
Gets the current aspect ratio

```lua
local ratio = GetAspectRatio()
print(string.format("%.2f", ratio))
```
</summary>
	]]

native "GET_CALMING_QUAD_AT_COORDS"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
This native returns the index of a calming quad if the given point is inside its bounds.
</summary>
<param name="x">The X coordinate</param>
<param name="y">The Y coordinate</param>
<returns>The calming quad index at the given position. Returns -1 if there isn't any there.</returns>
	]]

native "GET_CALMING_QUAD_BOUNDS"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "minX" [=[ {} ]=],
		intPtr "minY" [=[ {} ]=],
		intPtr "maxX" [=[ {} ]=],
		intPtr "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The calming quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success. Bounds are undefined on failure</returns>
	]]

native "GET_CALMING_QUAD_COUNT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<returns>Returns the amount of calming quads loaded.</returns>
	]]

native "GET_CALMING_QUAD_DAMPENING"
	arguments {
		int "waterQuad" [=[ {} ]=],
		floatPtr "calmingQuadDampening" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The calming quad index</param>
<returns>Returns true on success. Dampening value is undefined on failure</returns>
	]]

native "GET_CAM_MATRIX"
	arguments {
		Cam "camera" [=[ {} ]=],
		Vector3Ptr "rightVector" [=[ {} ]=],
		Vector3Ptr "forwardVector" [=[ {} ]=],
		Vector3Ptr "upVector" [=[ {} ]=],
		Vector3Ptr "position" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Returns the world matrix of the specified camera. To turn this into a view matrix, calculate the inverse.
</summary>
	]]

native "GET_CLOSEST_TRACK_NODES"
	arguments {
		Vector3 "position" [=[ {} ]=],
		float "radius" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "object"
	doc [[!
<summary>
Get all track nodes and their track ids within the radius of the specified coordinates.
</summary>
<param name="position">Get track nodes at position</param>
<param name="radius">Get track nodes within radius</param>
<returns>Returns a list of tracks and node entries: a trackNode and a trackIdThe data returned adheres to the following layout:    [{trackNode1, trackId1}, ..., {trackNodeN, trackIdN}]</returns>
	]]

native "GET_CONSOLE_BUFFER"
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
Returns the current console output buffer.
</summary>
<returns>The most recent game console output, as a string.</returns>
	]]

native "GET_CONVAR"
	arguments {
		charPtr "varName" [=[ {} ]=],
		charPtr "default_" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Can be used to get a console variable of type `char*`, for example a string.
</summary>
<param name="varName">The console variable to look up.</param>
<param name="default_">The default value to set if none is found.</param>
<returns>Returns the convar value if it can be found, otherwise it returns the assigned `default`.</returns>
	]]

native "GET_CONVAR_BOOL"
	arguments {
		charPtr "varName" [=[ {} ]=],
		BOOL "defaultValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
<summary>
Can be used to get a console variable casted back to `bool`.
</summary>
<param name="varName">The console variable to look up.</param>
<param name="defaultValue">The default value to set if none is found.</param>
<returns>Returns the convar value if it can be found, otherwise it returns the assigned `default`.</returns>
	]]

native "GET_CONVAR_FLOAT"
	arguments {
		charPtr "varName" [=[ {} ]=],
		float "defaultValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "float"
	doc [[!
<summary>
This will have floating point inaccuracy.
</summary>
<param name="varName">The console variable to get</param>
<param name="defaultValue">The default value to set, if none are found.</param>
<returns>Returns the value set in varName, or `default` if none are specified</returns>
	]]

native "GET_CONVAR_INT"
	arguments {
		charPtr "varName" [=[ {} ]=],
		int "default_" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Can be used to get a console variable casted back to `int` (an integer value).
</summary>
<param name="varName">The console variable to look up.</param>
<param name="default_">The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).</param>
<returns>Returns the convar value if it can be found, otherwise it returns the assigned `default`.</returns>
	]]

native "GET_CURRENT_GAME_NAME"
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
This native returns the currently used game's name.
</summary>
<returns>The game name as a string, one of the following values: gta4, gta5, rdr3</returns>
	]]

native "GET_CURRENT_PED_WEAPON"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
<summary>
Returns the hash of weapon the Ped is currently using.
</summary>
<param name="ped">The target ped.</param>
<returns>The weapon hash.</returns>
	]]

native "GET_CURRENT_RESOURCE_NAME"
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Returns the name of the currently executing resource.
</summary>
<returns>The name of the resource.</returns>
	]]

native "GET_CURRENT_SCREEN_RESOLUTION"
	arguments {
		intPtr "width" [=[ {} ]=],
		intPtr "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Gets the current screen resolution.

```lua
local  width, height = GetCurrentScreenResolution()
print(string.format("Current screen resolution: %dx%d", width, height))

```
</summary>
	]]

native "GET_CURRENT_SERVER_ENDPOINT"
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
Returns the peer address of the remote game server that the user is currently connected to.
</summary>
<returns>The peer address of the game server (e.g. `127.0.0.1:30120`), or NULL if not available.</returns>
	]]

native "GET_DUI_HANDLE"
	arguments {
		long "duiObject" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
Returns the NUI window handle for a specified DUI browser object.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<returns>The NUI window handle, for use in e.g. CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.</returns>
	]]

native "GET_ENTITIES_IN_RADIUS"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "radius" [=[ {} ]=],
		int "entityType" [=[ {} ]=],
		BOOL "sortByDistance" [=[ {} ]=],
		object "models" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<summary>
### Supported types

*   \[1] : Peds (including animals) and players.
*   \[2] : Vehicles.
*   \[3] : Objects (props), doors, and projectiles.

### Coordinates need to be send unpacked (x,y,z)

```lua

-- Define the allowed model hashes
local allowedModelHashes = { GetHashKey("p_crate03x"), GetHashKey("p_crate22x") }

-- Get the player's current coordinates
local playerCoords = GetEntityCoords(PlayerPedId())

-- Retrieve all entities of type Object (type 3) within a radius of 10.0 units
-- that match the allowed model hashes
-- and sort output entities by distance
local entities = GetEntitiesInRadius(playerCoords.x, playerCoords.y, playerCoords.z, 10.0, 3, true, allowedModelHashes)

-- Iterate through the list of entities and print their ids
for i = 1, #entities do
    local entity = entities[i]
    print(entity)
end

```
</summary>
<param name="x">The X coordinate.</param>
<param name="y">The Y coordinate.</param>
<param name="z">The Z coordinate.</param>
<param name="radius">Max distance from coordinate to entity</param>
<param name="entityType">Entity types see list below</param>
<param name="sortByDistance">Sort output entites by distance from nearest to farthest</param>
<param name="models">List of allowed models its also optional</param>
<returns>An array containing entity handles for each entity.</returns>
	]]

native "GET_ENTITY_ADDRESS"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "AnyPtr"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Returns the memory address of an entity.

This native is intended for singleplayer debugging, and may not be available during multiplayer.
</summary>
<param name="entity">The handle of the entity to get the address of.</param>
<returns>A pointer containing the memory address of the entity.</returns>
	]]

native "GET_ENTITY_ARCHETYPE_NAME"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
Returns entity's archetype name, if available.
</summary>
<param name="entity">An entity handle.</param>
<returns>Entity's archetype name</returns>
	]]

native "GET_ENTITY_ATTACHED_TO"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Gets the entity that this entity is attached to.
</summary>
<param name="entity">The entity to check.</param>
<returns>The attached entity handle. 0 returned if the entity is not attached.</returns>
	]]

native "GET_ENTITY_COLLISION_DISABLED"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="entity">The target entity.</param>
<returns>Returns whether or not entity collisions are disabled.</returns>
	]]

native "GET_ENTITY_COORDS"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
<summary>
Gets the current coordinates for a specified entity. This native is used server side when using OneSync.

See [GET_ENTITY_COORDS](#\_0x3FEF770D40960D5A) for client side.
</summary>
<param name="entity">The entity to get the coordinates from.</param>
<returns>The current entity coordinates.</returns>
	]]

native "GET_ENTITY_FROM_STATE_BAG_NAME"
	arguments {
		charPtr "bagName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "Entity"
	doc [[!
<summary>
Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](#\_0x5BA35AAF).
</summary>
<param name="bagName">An internal state bag ID from the argument to a state bag change handler.</param>
<returns>The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.</returns>
	]]

native "GET_ENTITY_HEADING"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
	]]

native "GET_ENTITY_HEALTH"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Only works for vehicle and peds
</summary>
<param name="entity">The entity to check the health of</param>
<returns>If the entity is a vehicle it will return 0-1000
If the entity is a ped it will return 0-200
If the entity is an object it will return 0</returns>
	]]

native "GET_ENTITY_INDEX_FROM_MAPDATA"
	arguments {
		int "mapdata" [=[ {} ]=],
		int "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns the transient entity index for a specified mapdata/entity pair.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="mapdata">The input map data index from GET_MAPDATA_FROM_HASH_KEY.</param>
<param name="entity">The input entity handle from GET_ENTITY_MAPDATA_OWNER.</param>
<returns>A transient (non-persistable) index to the requested entity, or -1.</returns>
	]]

native "GET_ENTITY_MAPDATA_OWNER"
	arguments {
		Entity "entity" [=[ {} ]=],
		intPtr "mapdataHandle" [=[ {} ]=],
		intPtr "entityHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Retrieves the map data and entity handles from a specific entity.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="entity">An entity owned by map data.</param>
<param name="mapdataHandle">The output map data handle.</param>
<param name="entityHandle">The output entity handle.</param>
<returns>True if successful, false if not.</returns>
	]]

native "GET_ENTITY_MAX_HEALTH"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Currently it only works with peds.
</summary>
	]]

native "GET_ENTITY_MODEL"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
	]]

native "GET_ENTITY_ORPHAN_MODE"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="entity">The entity to get the orphan mode of</param>
<returns>Returns the entities current orphan mode, refer to enum in [SET_ENTITY_ORPHAN_MODE](#\_0x489E9162)</returns>
	]]

native "GET_ENTITY_POPULATION_TYPE"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
This native gets an entity's population type.
</summary>
<param name="entity">the entity to obtain the population type from</param>
<returns>Returns the population type ID, defined by the below enumeration:```cpp
enum ePopulationType
{
	POPTYPE_UNKNOWN = 0,
	POPTYPE_RANDOM_PERMANENT,
	POPTYPE_RANDOM_PARKED,
	POPTYPE_RANDOM_PATROL,
	POPTYPE_RANDOM_SCENARIO,
	POPTYPE_RANDOM_AMBIENT,
	POPTYPE_PERMANENT,
	POPTYPE_MISSION,
	POPTYPE_REPLAY,
	POPTYPE_CACHE,
	POPTYPE_TOOL
};
```</returns>
	]]

native "GET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="entity">The entity to get the flag for.</param>
<returns>Returns if the entity is allowed to participate in network-synchronized scenes initiated by clients that do not own the entity.</returns>
	]]

native "GET_ENTITY_ROTATION"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
	]]

native "GET_ENTITY_ROTATION_VELOCITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
	]]

native "GET_ENTITY_ROUTING_BUCKET"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the routing bucket for the specified entity.

Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
</summary>
<param name="entity">The entity to get the routing bucket for.</param>
<returns>The routing bucket ID.</returns>
	]]

native "GET_ENTITY_SCRIPT"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_ENTITY_SPEED"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
<summary>
Gets the current speed of the entity in meters per second.

```
To convert to MPH: speed * 2.236936
To convert to KPH: speed * 3.6
```
</summary>
<param name="entity">The entity to get the speed of</param>
<returns>The speed of the entity in meters per second</returns>
	]]

native "GET_ENTITY_TYPE"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the entity type (as an integer), which can be one of the following defined down below:

**The following entities will return type `1`:**

*   Ped
*   Player
*   Animal (Red Dead Redemption 2)
*   Horse (Red Dead Redemption 2)

**The following entities will return type `2`:**

*   Automobile
*   Bike
*   Boat
*   Heli
*   Plane
*   Submarine
*   Trailer
*   Train
*   DraftVeh (Red Dead Redemption 2)

**The following entities will return type `3`:**

*   Object
*   Door
*   Pickup

Otherwise, a value of `0` will be returned.
</summary>
<param name="entity">The entity to get the type of.</param>
<returns>The entity type returned as an integer value.</returns>
	]]

native "GET_ENTITY_VELOCITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
	]]

native "GET_EXTERNAL_KVP_FLOAT"
	arguments {
		charPtr "resource" [=[ {} ]=],
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "float"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938), but for a specified resource.
</summary>
<param name="resource">The resource to fetch from.</param>
<param name="key">The key to fetch</param>
<returns>A float that contains the value stored in the Kvp or nil/null if none.</returns>
	]]

native "GET_EXTERNAL_KVP_INT"
	arguments {
		charPtr "resource" [=[ {} ]=],
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8), but for a specified resource.
</summary>
<param name="resource">The resource to fetch from.</param>
<param name="key">The key to fetch</param>
<returns>A int that contains the value stored in the Kvp or nil/null if none.</returns>
	]]

native "GET_EXTERNAL_KVP_STRING"
	arguments {
		charPtr "resource" [=[ {} ]=],
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B), but for a specified resource.
</summary>
<param name="resource">The resource to fetch from.</param>
<param name="key">The key to fetch</param>
<returns>A string that contains the value stored in the Kvp or nil/null if none.</returns>
	]]

native "GET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER](#\_0x164A08C9).
</summary>
<returns>Returns the fall damage multiplier applied when a ped lands **on foot** from a fall below the kill fall height threshold (i.e., when the fall does not cause instant death).
The default value is `3.0`.</returns>
	]]

native "GET_FALL_DAMAGE_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_FALL_DAMAGE_MULTIPLIER](#\_0xF2E1A531).
</summary>
<returns>Returns the fall damage multiplier applied to all peds when calculating fall damage from falls **below the kill fall height threshold** (i.e., when the fall does not cause instant death).
The default value is `7.0`.</returns>
	]]

native "GET_FUEL_CONSUMPTION_RATE_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<returns>Current fuel consumption rate multiplier. 0 means that fuel is not consumed.</returns>
	]]

native "GET_FUEL_CONSUMPTION_STATE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<returns>True if fuel consumption is turned on. False otherwise.</returns>
	]]

native "GET_GAME_BUILD_NUMBER"
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Returns the internal build number of the current game being executed.

Possible values:

*   FiveM
    *   1604
    *   2060
    *   2189
    *   2372
    *   2545
    *   2612
    *   2699
    *   2802
    *   2944
    *   3095
    *   3258
    *   3323
    *   3407
    *   3570
*   RedM
    *   1311
    *   1355
    *   1436
    *   1491
*   LibertyM
    *   43
*   FXServer
    *   0
</summary>
<returns>The build number, or **0** if no build number is known.</returns>
	]]

native "GET_GAME_NAME"
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Returns the current game being executed.

Possible values:

| Return value | Meaning                        |
| ------------ | ------------------------------ |
| `fxserver`   | Server-side code ('Duplicity') |
| `fivem`      | FiveM for GTA V                |
| `libertym`   | LibertyM for GTA IV            |
| `redm`       | RedM for Red Dead Redemption 2 |
</summary>
<returns>The game the script environment is running in.</returns>
	]]

native "GET_GAME_POOL"
	arguments {
		charPtr "poolName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<summary>
Returns a list of entity handles (script GUID) for all entities in the specified pool - the data returned is an array as
follows:

```json
[ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
```

### Supported pools

*   `CPed`: Peds (including animals) and players.
*   `CObject`: Objects (props), doors, and projectiles.
*   `CNetObject`: Networked objects
*   `CVehicle`: Vehicles.
*   `CPickup`: Pickups.
</summary>
<param name="poolName">The pool name to get a list of entities from.</param>
<returns>An array containing entity handles for each entity in the named pool.</returns>
	]]

native "GET_GAME_TIMER"
	ns "CFX"
    apiset "server"
	returns "long"
	doc [[!
<summary>
Gets the current game timer in milliseconds.
</summary>
<returns>The game time.</returns>
	]]

native "GET_GLOBAL_PASSENGER_MASS_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_GLOBAL_PASSENGER_MASS_MULTIPLIER](#\_0x3422291C).
</summary>
<returns>Returns the mass of each passenger (not counting the driver) as a percentage of vehicle mass. Default value is 0.05</returns>
	]]

native "GET_HASH_KEY"
	arguments {
		charPtr "model" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
<summary>
This native converts the passed string to a hash.
</summary>
	]]

native "GET_HELI_BODY_HEALTH"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "int"
	doc [[!
<summary>
**Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
</summary>
<param name="heli">The helicopter to check</param>
<returns>Returns the current health of the helicopter's body.</returns>
	]]

native "GET_HELI_DISABLE_EXPLODE_FROM_BODY_DAMAGE"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This is a getter for [SET_DISABLE_HELI_EXPLODE_FROM_BODY_DAMAGE](#\_0xEDBC8405B3895CC9)
</summary>
<param name="heli">The helicopter to check</param>
<returns>Returns `true` if the helicopter is set to be protected from exploding due to minor body damage, `false` otherwise.</returns>
	]]

native "GET_HELI_ENGINE_HEALTH"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "int"
	doc [[!
<summary>
**Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
</summary>
<param name="heli">The helicopter to check</param>
<returns>Returns the current health of the helicopter's engine.</returns>
	]]

native "GET_HELI_GAS_TANK_HEALTH"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "int"
	doc [[!
<summary>
**Note** This native will always return `1000.0` unless [SET_VEHICLE_BODY_HEALTH](#\_0xB77D05AC8C78AADB), [SET_VEHICLE_ENGINE_HEALTH](#\_0x45F6D8EEF34ABEF1), or [SET_VEHICLE_PETROL_TANK_HEALTH](#\_0x70DB57649FA8D0D8) have been called with a value greater than `1000.0`.
</summary>
<param name="heli">The helicopter to check</param>
<returns>Returns the current health of the helicopter's gas tank.</returns>
	]]

native "GET_HELI_MAIN_ROTOR_DAMAGE_SCALE"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check</param>
<returns>Returns a value representing the damage scaling factor applied to the helicopter's main rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).</returns>
	]]

native "GET_HELI_MAIN_ROTOR_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<returns>See the client-side [GET_HELI_MAIN_ROTOR_HEALTH](#\_0xE4CB7541F413D2C5) for the return value.</returns>
	]]

native "GET_HELI_PITCH_CONTROL"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check.</param>
<returns>Returns a value representing the pitch control of the helicopter. The values range from `-1.0` (nose down) to `1.0` (nose up), with `0.0` indicating no pitch input.</returns>
	]]

native "GET_HELI_REAR_ROTOR_DAMAGE_SCALE"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check</param>
<returns>Returns a value representing the damage scaling factor applied to the helicopter's rear rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).</returns>
	]]

native "GET_HELI_REAR_ROTOR_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<summary>
This native is a getter for [SET_HELI_TAIL_ROTOR_HEALTH](#\_0xFE205F38AAA58E5B)
</summary>
<param name="vehicle">The target vehicle.</param>
<returns>Returns the health of the helicopter's rear rotor. The maximum health value is `1000`.</returns>
	]]

native "GET_HELI_ROLL_CONTROL"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check.</param>
<returns>Returns a value representing the roll control of the helicopter. The values range from `-1.0` (roll left) to `1.0` (roll right), with `0.0` indicating no roll input.</returns>
	]]

native "GET_HELI_TAIL_ROTOR_DAMAGE_SCALE"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check</param>
<returns>Returns a value representing the damage scaling factor applied to the helicopter's tail rotor. The value ranges from `0.0` (no damage scaling) to`  1.0 ` (full damage scaling).</returns>
	]]

native "GET_HELI_TAIL_ROTOR_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<summary>
**Note**: This native is deprecated, please use [`GET_HELI_REAR_ROTOR_HEALTH`](#\_0x33EE6E2B) instead.
</summary>
<param name="vehicle">The target vehicle.</param>
<returns>Return the health of the rear rotor of the helicopter, not the tail rotor.</returns>
	]]

native "GET_HELI_THROTTLE_CONTROL"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check.</param>
<returns>Returns a value representing the throttle control of the helicopter. The value ranges from `0.0` (no throttle) to `2.0` (full throttle).</returns>
	]]

native "GET_HELI_YAW_CONTROL"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="heli">The helicopter to check.</param>
<returns>Returns a value the yaw control of the helicopter. The value ranges from `-1.0` (yaw left) to `1.0` (yaw right), with `0.0` meaning no yaw input.</returns>
	]]

native "GET_HOST_ID"
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_HUD_COMPONENT_ALIGN"
	arguments {
		int "id" [=[ {} ]=],
		intPtr "horizontalAlign" [=[ {} ]=],
		intPtr "verticalAlign" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
See [SET_SCRIPT_GFX_ALIGN](#\_0xB8A850F20A067EB6) for details about how gfx align works.
</summary>
<param name="id">The hud component id.</param>
	]]

native "GET_HUD_COMPONENT_NAME"
	arguments {
		int "id" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<param name="id">The hud component id.</param>
<returns>The hud component name.</returns>
	]]

native "GET_HUD_COMPONENT_SIZE"
	arguments {
		int "id" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Vector3"
	doc [[!
<param name="id">The hud component id.</param>
<returns>A Vector3 with the hud component size X and size Y values.</returns>
	]]

native "GET_INSTANCE_ID"
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
	]]

native "GET_INTERIOR_ENTITIES_EXTENTS"
	arguments {
		int "interiorId" [=[ {} ]=],
		floatPtr "bbMinX" [=[ {} ]=],
		floatPtr "bbMinY" [=[ {} ]=],
		floatPtr "bbMinZ" [=[ {} ]=],
		floatPtr "bbMaxX" [=[ {} ]=],
		floatPtr "bbMaxY" [=[ {} ]=],
		floatPtr "bbMaxZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<returns>Interior entities extents.</returns>
	]]

native "GET_INTERIOR_PORTAL_CORNER_POSITION"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "cornerIndex" [=[ {} ]=],
		floatPtr "posX" [=[ {} ]=],
		floatPtr "posY" [=[ {} ]=],
		floatPtr "posZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="cornerIndex">Portal's corner index.</param>
<returns>Portal corner position.</returns>
	]]

native "GET_INTERIOR_PORTAL_COUNT"
	arguments {
		int "interiorId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<returns>The amount of portals in interior.</returns>
	]]

native "GET_INTERIOR_PORTAL_ENTITY_ARCHETYPE"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "entityIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="entityIndex">Portal entity index.</param>
<returns>Portal entity archetype.</returns>
	]]

native "GET_INTERIOR_PORTAL_ENTITY_COUNT"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<returns>Portal entity count.</returns>
	]]

native "GET_INTERIOR_PORTAL_ENTITY_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "entityIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="entityIndex">Portal entity index.</param>
<returns>Portal entity flag.</returns>
	]]

native "GET_INTERIOR_PORTAL_ENTITY_POSITION"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "entityIndex" [=[ {} ]=],
		floatPtr "posX" [=[ {} ]=],
		floatPtr "posY" [=[ {} ]=],
		floatPtr "posZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="entityIndex">Portal entity index.</param>
<returns>Portal entity position.</returns>
	]]

native "GET_INTERIOR_PORTAL_ENTITY_ROTATION"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "entityIndex" [=[ {} ]=],
		floatPtr "rotX" [=[ {} ]=],
		floatPtr "rotY" [=[ {} ]=],
		floatPtr "rotZ" [=[ {} ]=],
		floatPtr "rotW" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="entityIndex">Portal entity index.</param>
<returns>Portal entity rotation.</returns>
	]]

native "GET_INTERIOR_PORTAL_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<returns>Portal's flag.</returns>
	]]

native "GET_INTERIOR_PORTAL_ROOM_FROM"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<returns>Portal's room FROM index.</returns>
	]]

native "GET_INTERIOR_PORTAL_ROOM_TO"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<returns>Portal's room TO index.</returns>
	]]

native "GET_INTERIOR_POSITION"
	arguments {
		int "interiorId" [=[ {} ]=],
		floatPtr "posX" [=[ {} ]=],
		floatPtr "posY" [=[ {} ]=],
		floatPtr "posZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<returns>Interior position.</returns>
	]]

native "GET_INTERIOR_ROOM_COUNT"
	arguments {
		int "interiorId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<returns>The amount of rooms in interior.</returns>
	]]

native "GET_INTERIOR_ROOM_EXTENTS"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
		floatPtr "bbMinX" [=[ {} ]=],
		floatPtr "bbMinY" [=[ {} ]=],
		floatPtr "bbMinZ" [=[ {} ]=],
		floatPtr "bbMaxX" [=[ {} ]=],
		floatPtr "bbMaxY" [=[ {} ]=],
		floatPtr "bbMaxZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<returns>Room extents.</returns>
	]]

native "GET_INTERIOR_ROOM_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<returns>Room's flag.</returns>
	]]

native "GET_INTERIOR_ROOM_INDEX_BY_HASH"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomHash">Interior room hash.</param>
<returns>Room index, -1 if failed.</returns>
	]]

native "GET_INTERIOR_ROOM_NAME"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<returns>Room's name.</returns>
	]]

native "GET_INTERIOR_ROOM_TIMECYCLE"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<returns>Room's timecycle hash.</returns>
	]]

native "GET_INTERIOR_ROTATION"
	arguments {
		int "interiorId" [=[ {} ]=],
		floatPtr "rotx" [=[ {} ]=],
		floatPtr "rotY" [=[ {} ]=],
		floatPtr "rotZ" [=[ {} ]=],
		floatPtr "rotW" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<returns>Interior rotation in quaternion.</returns>
	]]

native "GET_INVOKING_RESOURCE"
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
	]]

native "GET_IS_HELI_ENGINE_RUNNING"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="heli">The helicopter to check</param>
<returns>Returns `true` if the helicopter's engine is running, `false` if it is not.</returns>
	]]

native "GET_IS_VEHICLE_ENGINE_RUNNING"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "GET_KILL_FALL_HEIGHT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_KILL_FALL_HEIGHT](#\_0x7E8D83E4).
</summary>
<returns>Returns the height from which non-player peds will instantly die due to fall damage.\
The default value is `10.0`.</returns>
	]]

native "GET_LANDING_GEAR_STATE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
See the client-side [GET_LANDING_GEAR_STATE](#\_0x9B0F3DCA3DB0F4CD) native for a description of landing gear states.
</summary>
<param name="vehicle">The vehicle to check.</param>
<returns>The current state of the vehicles landing gear.</returns>
	]]

native "GET_LAST_PED_IN_VEHICLE_SEAT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "seatIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<param name="seatIndex">See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).</param>
<returns>The last ped in the specified seat of the passed vehicle. Returns 0 if the specified seat was never occupied or the last ped does not exist anymore.</returns>
	]]

native "GET_MAP_ZOOM_DATA_LEVEL"
	arguments {
		int "index" [=[ {} ]=],
		floatPtr "zoomScale" [=[ {} ]=],
		floatPtr "zoomSpeed" [=[ {} ]=],
		floatPtr "scrollSpeed" [=[ {} ]=],
		floatPtr "tilesX" [=[ {} ]=],
		floatPtr "tilesY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Returns the zoom level data by index from mapzoomdata.meta file.
</summary>
<param name="index">Zoom level index.</param>
<param name="zoomScale">fZoomScale value.</param>
<param name="zoomSpeed">fZoomSpeed value.</param>
<param name="scrollSpeed">fScrollSpeed value.</param>
<param name="tilesX">vTiles X.</param>
<param name="tilesY">vTiles Y.</param>
<returns>A boolean indicating TRUE if the data was received successfully.</returns>
	]]

native "GET_MAPDATA_ENTITY_HANDLE"
	arguments {
		int "mapDataHash" [=[ {} ]=],
		int "entityInternalIdx" [=[ {} ]=],
		intPtr "entityHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Retrieves the map data entity handle.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="mapDataHash">A mapdata hash from `mapDataLoaded` event.</param>
<param name="entityInternalIdx">An internal entity's index.</param>
<param name="entityHandle">The output entity handle.</param>
<returns>True if successful, false if not.</returns>
	]]

native "GET_MAPDATA_ENTITY_MATRIX"
	arguments {
		int "mapDataHash" [=[ {} ]=],
		int "entityInternalIdx" [=[ {} ]=],
		long "matrixPtr" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Returns mapdata's entity matrix. This function supports SDK infrastructure and is not intended to be used directly from your code.

This should be used from JavaScript or another language supporting mutable buffers like ArrayBuffer.

Matrix layout is as follows:

*   Element \[0], \[1] and \[2] should represent the right vector.
*   Element \[4], \[5] and \[6] should represent the forward vector.
*   Element \[8], \[9] and \[10] should represent the up vector.
*   Element \[12], \[13] and \[14] should represent X, Y and Z translation coordinates.
*   All other elements should be \[0, 0, 0, 1].
</summary>
<param name="mapDataHash">A mapdata hash from `mapDataLoaded` event.</param>
<param name="entityInternalIdx">An internal entity's index.</param>
<param name="matrixPtr">A mutable pointer to a 64-byte buffer of floating-point values, representing an XMFLOAT4X4 in layout.</param>
<returns>Whether or not the matrix was retrieved.</returns>
	]]

native "GET_MAPDATA_FROM_HASH_KEY"
	arguments {
		Hash "mapdataHandle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns the transient map data index for a specified hash.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="mapdataHandle">The input map data handle.</param>
<returns>A transient (non-persistable) index to the requested mapdata, or -1.</returns>
	]]

native "GET_MINIMAP_TYPE"
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "int"
	doc [[!
<summary>
Get the minimap type:

```
0 = Off,
1 = Regular,
2 = Expanded,
3 = Simple,
```
</summary>
	]]

native "GET_NET_TYPE_FROM_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the specific entity type (as an integer), which can be one of the following defined down below:

#### FiveM:

```cpp
enum eNetObjEntityType
{
    Automobile = 0,
    Bike = 1,
    Boat = 2,
    Door = 3,
    Heli = 4,
    Object = 5,
    Ped = 6,
    Pickup = 7,
    PickupPlacement = 8,
    Plane = 9,
    Submarine = 10,
    Player = 11,
    Trailer = 12,
    Train = 13
};
```

#### RedM:

```cpp
enum eNetObjEntityType
{
    Animal = 0,
    Automobile = 1,
    Bike = 2,
    Boat = 3,
    Door = 4,
    Heli = 5,
    Object = 6,
    Ped = 7,
    Pickup = 8,
    PickupPlacement = 9,
    Plane = 10,
    Submarine = 11,
    Player = 12,
    Trailer = 13,
    Train = 14,
    DraftVeh = 15,
    StatsTracker = 16,
    PropSet = 17,
    AnimScene = 18,
    GroupScenario = 19,
    Herd = 20,
    Horse = 21,
    WorldState = 22,
    WorldProjectile = 23,
    Incident = 24,
    Guardzone = 25,
    PedGroup = 26,
    CombatDirector = 27,
    PedSharedTargeting = 28,
    Persistent = 29
};
```
</summary>
<param name="entity">The entity to get the specific type of.</param>
<returns>The specific entity type returned as an integer value or -1 if the entity is invalid.</returns>
	]]

native "GET_NETWORK_WALK_MODE"
	ns "CFX"
    apiset "client"
	game "ny"
	returns "bool"
	doc [[!
	]]

native "GET_NUI_CURSOR_POSITION"
	arguments {
		intPtr "x" [=[ {} ]=],
		intPtr "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "GET_NUM_PLAYER_IDENTIFIERS"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_NUM_PLAYER_INDICES"
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_NUM_PLAYER_TOKENS"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_NUM_RESOURCE_METADATA"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "metadataKey" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
</summary>
<param name="resourceName">The resource name.</param>
<param name="metadataKey">The key to look up in the resource manifest.</param>
	]]

native "GET_NUM_RESOURCES"
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
	]]

native "GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An analogue of [GET_NUMBER_OF_PED_DRAWABLE_VARIATIONS](#\_0x27561561732A7842) that returns number of drawable variations inside a single collection instead of the total number across all collections.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<returns>Number of drawables available in the given collection. Returns 0 if ped or collection does not exist.</returns>
	]]

native "GET_NUMBER_OF_PED_COLLECTION_PROP_DRAWABLE_VARIATIONS"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An analogue of [GET_NUMBER_OF_PED_PROP_DRAWABLE_VARIATIONS](#\_0x5FAF9754E789FB47) that returns number of prop variations inside a single collection instead of the total number across all collections.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<returns>Number of props available in the given collection. Returns 0 if ped or collection does not exist.</returns>
	]]

native "GET_NUMBER_OF_PED_COLLECTION_PROP_TEXTURE_VARIATIONS"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An alternative to [GET_NUMBER_OF_PED_PROP_TEXTURE_VARIATIONS](#\_0xA6E7F1CEB523E171) that uses local collection indexing instead of the global one.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="propIndex">Local prop index inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_DRAWABLE_VARIATIONS](#\_0x3B6A13E1).</param>
<returns>Number of texture variations available for the given prop. Returns 0 if ped or collection does not exist or index is out of bounds.</returns>
	]]

native "GET_NUMBER_OF_PED_COLLECTION_TEXTURE_VARIATIONS"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An alternative to [GET_NUMBER_OF_PED_TEXTURE_VARIATIONS](#\_0x8F7156A3142A6BAD) that uses local collection indexing instead of the global one.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<returns>Number of texture variations available for the given drawable component. Returns 0 if ped or collection does not exist or index is out of bounds.</returns>
	]]

native "GET_PARKED_VEHICLE_DENSITY_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PARKED_VEHICLE_DENSITY_MULTIPLIER_THIS_FRAME](#\_0xEAE6DCC7EEE3DB1D).
</summary>
<returns>Returns parked vehicle density multiplier value.</returns>
	]]

native "GET_PASSWORD_HASH"
	arguments {
		charPtr "password" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_PAUSE_MAP_POINTER_WORLD_POSITION"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Vector3"
	doc [[!
<summary>
Returns the world position the pointer is hovering on the pause map.
</summary>
<returns>A Vector3 with the pause map pointer world position X and Y values.</returns>
	]]

native "GET_PED_ARMOUR"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_PED_BONE_MATRIX"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "boneId" [=[ {} ]=],
		Vector3Ptr "forwardVector" [=[ {} ]=],
		Vector3Ptr "rightVector" [=[ {} ]=],
		Vector3Ptr "upVector" [=[ {} ]=],
		Vector3Ptr "position" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Returns the bone matrix of the specified bone id. usefull for entity attachment
</summary>
<returns>*   **forwardVector**:
*   **rightVector**:
*   **upVector**:
*   **position**:</returns>
	]]

native "GET_PED_CAUSE_OF_DEATH"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
	]]

native "GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets local index inside a collection (which can be obtained using [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B)) for the given global drawable ID. The collection name and index are used in functions like [SET_PED_COLLECTION_COMPONENT_VARIATION](#\_0x88711BBA).
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="drawableId">Global drawable ID. Same as set in [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80). Global drawable ID points to drawables as if drawables from all collections for the given component are placed into one continuous array.</param>
<returns>Local index inside a collection that the given global drawable ID corresponds to. Returns -1 if Ped is not found or the global index is out of bounds.</returns>
	]]

native "GET_PED_COLLECTION_LOCAL_INDEX_FROM_PROP"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets local index inside a collection (which can be obtained using [GET_PED_COLLECTION_NAME_FROM_PROP](#\_0x8ED0C17)) for the given global prop index. The collection name and index are used in functions like [SET_PED_COLLECTION_PROP_INDEX](#\_0x75240BCB).
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="propIndex">Global prop index. Same as set by `drawableId` in [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F). Global prop index points to prop as if props from all collections for the given component are placed into one continuous array.</param>
<returns>Local index inside a collection that the given global prop index corresponds to. Returns -1 if Ped is not found or the global index is out of bounds.</returns>
	]]

native "GET_PED_COLLECTION_NAME"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
Returns name of collection under given index for the given Ped.

Collections are groups of drawable components or props available for the given Ped. Usually collection corresponds to a certain DLC or the base game. See [SET_PED_COLLECTION_COMPONENT_VARIATION](#\_0x88711BBA), [SET_PED_COLLECTION_PROP_INDEX](#\_0x75240BCB), [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271) etc natives for more details on how to work with collections.

`GET_PED_COLLECTION_NAME` can be used together with [GET_PED_COLLECTIONS_COUNT](#\_0x45946359) to list all collections attached to Ped.
</summary>
<param name="ped">The target ped</param>
<param name="index">The target collection index</param>
<returns>Name of the collection with given index. Base game collection (always stored with index 0) is an empty string. Returns null if Ped is not found or index is out of bounds.</returns>
	]]

native "GET_PED_COLLECTION_NAME_FROM_DRAWABLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
Gets collection name for the given global drawable ID. Together with [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) is used to get collection and local index (inside the given collection) of the drawable. The collection name and index are used in functions like [SET_PED_COLLECTION_COMPONENT_VARIATION](#\_0x88711BBA).
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="drawableId">Global drawable ID. Same as set in [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80). Global drawable ID points to drawables as if drawables from all collections for the given component are placed into one continuous array.</param>
<returns>Name of the collection that the given global drawable ID corresponds to. Base game collection is an empty string. Returns null if Ped is not found or the global index is out of bounds.</returns>
	]]

native "GET_PED_COLLECTION_NAME_FROM_PROP"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
Gets collection name for the given global prop index. Together with [GET_PED_COLLECTION_LOCAL_INDEX_FROM_PROP](#\_0xFBDB885F) is used to get collection and local index (inside the given collection) of the prop. The collection name and index are used in functions like [SET_PED_COLLECTION_PROP_INDEX](#\_0x75240BCB).
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="propIndex">Global prop index. Same as set by `drawableId` in [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F). Global prop index points to prop as if props from all collections for the given component are placed into one continuous array.</param>
<returns>Name of the collection that the given global drawable ID corresponds to. Base game collection is an empty string. Returns null if Ped is not found or the global index is out of bounds.</returns>
	]]

native "GET_PED_COLLECTIONS_COUNT"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns number of variation collections available for the given Ped.

Collections are groups of drawable components or props available for the given Ped. Usually collection corresponds to a certain DLC or the base game. See [SET_PED_COLLECTION_COMPONENT_VARIATION](#\_0x88711BBA), [SET_PED_COLLECTION_PROP_INDEX](#\_0x75240BCB), [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271) etc natives for more details on how to work with collections.

`GET_PED_COLLECTIONS_COUNT` can be used together with [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) to list all collections attached to Ped.
</summary>
<param name="ped">The target ped</param>
<returns>Number of Ped variation collections. 0 if Ped is not found.</returns>
	]]

native "GET_PED_DECORATIONS"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "object"
	doc [[!
<summary>
Returns a list of decorations applied to a ped.

The data returned adheres to the following layout:

```
[ [ collectionHash1, overlayHash1 ], ..., [c ollectionHashN, overlayHashN ] ]
```

This command will return undefined data if invoked on a remote player ped.
</summary>
<param name="ped">The ped you want to retrieve data for.</param>
<returns>An object containing a list of applied decorations.</returns>
	]]

native "GET_PED_DENSITY_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PED_DENSITY_MULTIPLIER_THIS_FRAME](#\_0x95E3D6257B166CF2).
</summary>
<returns>Returns ped density multiplier value.</returns>
	]]

native "GET_PED_DESIRED_HEADING"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
<param name="ped">The target ped</param>
<returns>Returns ped's desired heading.</returns>
	]]

native "GET_PED_DRAWABLE_GLOBAL_INDEX_FROM_COLLECTION"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns global drawable index based on the local one. Is it a reverse to [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) natives.

Drawables are stored inside collections. Each collection usually corresponds to a certain DCL or the base game.

If all drawables from all collections are placed into one continuous array - the global index will correspond to the index of drawable in such array. Local index is index of drawable in this array relative to the start of the given collection.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<returns>Global drawable ID that corresponds to the given collection and local drawable index. Returns -1 if Ped or collection does not exist or local index is out of bounds.</returns>
	]]

native "GET_PED_DRAWABLE_VARIATION_COLLECTION_LOCAL_INDEX"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An analogue to [GET_PED_DRAWABLE_VARIATION](#\_0x67F3780DD425D4FC) that returns collection local drawable index (inside [GET_PED_DRAWABLE_VARIATION_COLLECTION_NAME](#\_0xBCE0AB63) collection) instead of the global drawable index.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<returns>Local drawable index of the drawable that is currently used in the given ped and component.</returns>
	]]

native "GET_PED_DRAWABLE_VARIATION_COLLECTION_NAME"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
An analogue to [GET_PED_DRAWABLE_VARIATION](#\_0x67F3780DD425D4FC) that returns collection name instead of the global drawable index.

Should be used together with [GET_PED_DRAWABLE_VARIATION_COLLECTION_LOCAL_INDEX](#\_0x9970386F).
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<returns>Collection name to which the current drawable used in the given ped and component belongs to. Returns null if Ped is not found or index is out of bounds.</returns>
	]]

native "GET_PED_EYE_COLOR"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for [\_SET_PED_EYE_COLOR](#\_0x50B56988B170AFDF). Returns -1 if fails to get.
</summary>
<param name="ped">The target ped</param>
<returns>Returns ped's eye colour, or -1 if fails to get.</returns>
	]]

native "GET_PED_FACE_FEATURE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [\_SET_PED_FACE_FEATURE](#\_0x71A5C1DBA060049E). Returns 0.0 if fails to get.
</summary>
<param name="ped">The target ped</param>
<param name="index">Face feature index</param>
<returns>Returns ped's face feature value, or 0.0 if fails to get.</returns>
	]]

native "GET_PED_HAIR_COLOR"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for [\_SET_PED_HAIR_COLOR](#\_0x4CFFC65454C93A49). Returns -1 if fails to get.
</summary>
<param name="ped">The target ped</param>
<returns>Returns ped's primary hair colour.</returns>
	]]

native "GET_PED_HAIR_HIGHLIGHT_COLOR"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for [\_SET_PED_HAIR_COLOR](#\_0x4CFFC65454C93A49). Returns -1 if fails to get.
</summary>
<param name="ped">The target ped</param>
<returns>Returns ped's secondary hair colour.</returns>
	]]

native "GET_PED_HEAD_OVERLAY_DATA"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
		intPtr "overlayValue" [=[ {} ]=],
		intPtr "colourType" [=[ {} ]=],
		intPtr "firstColour" [=[ {} ]=],
		intPtr "secondColour" [=[ {} ]=],
		floatPtr "overlayOpacity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
A getter for [SET_PED_HEAD_OVERLAY](#\_0x48F44967FA05CC1E) and [\_SET_PED_HEAD_OVERLAY_COLOR](#\_0x497BF74A7B9CB952) natives.
</summary>
<param name="ped">The target ped</param>
<param name="index">Overlay index</param>
<param name="overlayValue">Overlay value pointer</param>
<param name="colourType">Colour type pointer</param>
<param name="firstColour">First colour pointer</param>
<param name="secondColour">Second colour pointer</param>
<param name="overlayOpacity">Opacity pointer</param>
<returns>Returns ped's head overlay data.</returns>
	]]

native "GET_PED_IN_VEHICLE_SEAT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "seatIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<param name="seatIndex">See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).</param>
<returns>The ped in the specified seat of the passed vehicle. Returns 0 if the specified seat is not occupied.</returns>
	]]

native "GET_PED_MAX_HEALTH"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_PED_MODEL_HEALTH_CONFIG"
	arguments {
		Hash "modelHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Hash"
	doc [[!
<summary>
Gets a ped model's health config.
</summary>
<param name="modelHash">Ped's model.</param>
	]]

native "GET_PED_MODEL_PERSONALITY"
	arguments {
		Hash "modelHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Hash"
	doc [[!
<summary>
Gets a ped model's personality type.
</summary>
<param name="modelHash">Ped's model.</param>
	]]

native "GET_PED_MOVEMENT_CLIPSET"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="ped">The target ped.</param>
<returns>The current movement clipset hash.</returns>
	]]

native "GET_PED_PROP_COLLECTION_LOCAL_INDEX"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
An analogue to [GET_PED_PROP_INDEX](#\_0x898CC20EA75BACD8) that returns collection local prop index (inside [GET_PED_PROP_COLLECTION_NAME](#\_0x6B5653E4) collection) instead of the global prop index.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<returns>Local drawable index of the drawable that is currently used in the given ped and component, or -1 if the ped does not have a prop at the specified anchor point</returns>
	]]

native "GET_PED_PROP_COLLECTION_NAME"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "charPtr"
	doc [[!
<summary>
An analogue to [GET_PED_PROP_INDEX](#\_0x898CC20EA75BACD8) that returns collection name instead of the global drawable index.

Should be used together with [GET_PED_PROP_COLLECTION_LOCAL_INDEX](#\_0xCD420AD1).
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<returns>Collection name to which the current prop used in the given ped and anchor point belongs to. Returns null if Ped is not found, does not have a prop at the specified anchor point, or if the index is out of bounds.</returns>
	]]

native "GET_PED_PROP_GLOBAL_INDEX_FROM_COLLECTION"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns global prop index based on the local one. Is it a reverse to [GET_PED_COLLECTION_NAME_FROM_PROP](#\_0x8ED0C17) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_PROP](#\_0xFBDB885F) natives.

Props are stored inside collections. Each collection usually corresponds to a certain DCL or the base game.

If all props from all collections are placed into one continuous array - the global index will correspond to the index of the prop in such array. Local index is index of the prop in this array relative to the start of the given collection.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="propIndex">Local prop index inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_DRAWABLE_VARIATIONS](#\_0x3B6A13E1).</param>
<returns>Global prop index that corresponds to the given collection and local prop index. Returns -1 if Ped or collection does not exist or local index is out of bounds.</returns>
	]]

native "GET_PED_RELATIONSHIP_GROUP_HASH"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
<summary>
Gets the current relationship group hash of a ped.
</summary>
<param name="ped">The target ped</param>
<returns>The relationship group hash.</returns>
	]]

native "GET_PED_SCRIPT_TASK_COMMAND"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
<summary>
Gets the script task command currently assigned to the ped.
</summary>
<param name="ped">The target ped.</param>
<returns>The script task command currently assigned to the ped. A value of 0x811E343C denotes no script task is assigned.</returns>
	]]

native "GET_PED_SCRIPT_TASK_STAGE"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the stage of the peds scripted task.
</summary>
<param name="ped">The target ped.</param>
<returns>The stage of the ped's scripted task. A value of 3 denotes no script task is assigned.</returns>
	]]

native "GET_PED_SOURCE_OF_DAMAGE"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Get the last entity that damaged the ped. This native is used server side when using OneSync.
</summary>
<param name="ped">The target ped</param>
<returns>The entity id. Returns 0 if the ped has not been damaged recently.</returns>
	]]

native "GET_PED_SOURCE_OF_DEATH"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Get the entity that killed the ped. This native is used server side when using OneSync.
</summary>
<param name="ped">The target ped</param>
<returns>The entity id. Returns 0 if the ped has no killer.</returns>
	]]

native "GET_PED_SPECIFIC_TASK_TYPE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the type of a ped's specific task given an index of the CPedTaskSpecificDataNode nodes.
A ped will typically have a task at index 0, if a ped has multiple tasks at once they will be in the order 0, 1, 2, etc.
</summary>
<param name="ped">The target ped.</param>
<param name="index">A zero-based index with a maximum value of 7.</param>
<returns>The type of the specific task.
1604: A value of 530 denotes no script task is assigned or an invalid input was given.
2060+: A value of 531 denotes no script task is assigned or an invalid input was given.</returns>
	]]

native "GET_PED_STEALTH_MOVEMENT"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped is stealthy.</returns>
	]]

native "GET_PED_SWEAT"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PED_SWEAT](#\_0x27B0405F59637D1F).
</summary>
<param name="ped">The target ped</param>
<returns>Returns ped's sweat.</returns>
	]]

native "GET_PLAYER_CAMERA_ROTATION"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
<summary>
Gets the current camera rotation for a specified player. This native is used server side when using OneSync.
</summary>
<param name="playerSrc">The player handle.</param>
<returns>The player's camera rotation. Values are in radians.</returns>
	]]

native "GET_PLAYER_ENDPOINT"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_PLAYER_FAKE_WANTED_LEVEL"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the current fake wanted level for a specified player. This native is used server side when using OneSync.
</summary>
<param name="playerSrc">The target player</param>
<returns>The fake wanted level</returns>
	]]

native "GET_PLAYER_FOCUS_POS"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
<summary>
Gets the focus position (i.e. the position of the active camera in the game world) of a player.
</summary>
<param name="playerSrc">The player to get the focus position of</param>
<returns>Returns a `Vector3` containing the focus position of the player.</returns>
	]]

native "GET_PLAYER_FROM_INDEX"
	arguments {
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_PLAYER_FROM_SERVER_ID"
	arguments {
		int "serverId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "Player"
	doc [[!
<summary>
Gets a local client's Player ID from its server ID counterpart, assuming the passed `serverId` exists on the client.

If no matching client is found, or an invalid value is passed over as the `serverId` native's parameter, the native result will be `-1`.

It's worth noting that this native method can only retrieve information about clients that are culled to the connected client.
</summary>
<param name="serverId">The player's server ID.</param>
<returns>A valid Player ID if one is found, `-1` if not.</returns>
	]]

native "GET_PLAYER_FROM_STATE_BAG_NAME"
	arguments {
		charPtr "bagName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
On the server this will return the players source, on the client it will return the player handle.
</summary>
<param name="bagName">An internal state bag ID from the argument to a state bag change handler.</param>
<returns>The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.</returns>
	]]

native "GET_PLAYER_GUID"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_PLAYER_IDENTIFIER"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "identiferIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
To get the number of identifiers, use [GET_NUM_PLAYER_IDENTIFIERS](#\_0xFF7F66AB)

To get a specific type of identifier, use [GET_PLAYER_IDENTIFIER_BY_TYPE](#\_0xA61C8FC6)
</summary>
<returns>Returns the identifier at the specific index, if out of bounds returns `null`</returns>
	]]

native "GET_PLAYER_IDENTIFIER_BY_TYPE"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		charPtr "identifierType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
Get an identifier from a player by the type of the identifier.
Known [Identifiers](https://docs.fivem.net/docs/scripting-reference/runtimes/lua/functions/GetPlayerIdentifiers/#identifier-types)
</summary>
<param name="playerSrc">The player to get the identifier for</param>
<param name="identifierType">The string to match in an identifier, this can be `"license"` for example.</param>
<returns>The identifier that matches the string provided</returns>
	]]

native "GET_PLAYER_INVINCIBLE"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<param name="playerSrc">The player handle</param>
<returns>A boolean to tell if the player is invincible.</returns>
	]]

native "GET_PLAYER_INVINCIBLE_2"
	arguments {
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Unlike [GET_PLAYER_INVINCIBLE](#\_0xB721981B2B939E07) this native gets both [SET_PLAYER_INVINCIBLE_KEEP_RAGDOLL_ENABLED](#\_0x6BC97F4F4BB3C04B) and [SET_PLAYER_INVINCIBLE](#\_0x239528EACDC3E7DE) invincibility state.
</summary>
<param name="player">The player id</param>
<returns>A boolean to tell if the player is invincible.</returns>
	]]

native "GET_PLAYER_KILL_FALL_HEIGHT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_KILL_FALL_HEIGHT](#\_0xAEF2C6A4).
</summary>
<returns>Returns the height from which the player will instantly die due to fall damage.\
The default value is `15.0`.</returns>
	]]

native "GET_PLAYER_LAST_MSG"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_PLAYER_MAX_ARMOUR"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="playerSrc">The player handle</param>
	]]

native "GET_PLAYER_MAX_HEALTH"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="playerSrc">The player handle</param>
	]]

native "GET_PLAYER_MAX_STAMINA"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="playerId">The player index.</param>
<returns>The value of player max stamina.</returns>
	]]

native "GET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER](#\_0x4A3DC7ECCC321032).
</summary>
<param name="playerId">The player index.</param>
<returns>Returns player melee weapon damage modifier value.</returns>
	]]

native "GET_PLAYER_MELEE_WEAPON_DEFENSE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_MELEE_WEAPON_DEFENSE_MODIFIER](#\_0xAE540335B4ABC4E2).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player melee weapon defense modifier.</returns>
	]]

native "GET_PLAYER_NAME"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_PLAYER_PED"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
<summary>
Used to get the player's Ped Entity ID when a valid `playerSrc` is passed.
</summary>
<param name="playerSrc">The player source, passed as a string.</param>
<returns>Returns a valid Ped Entity ID if the passed `playerSrc` is valid, `0` if not.</returns>
	]]

native "GET_PLAYER_PEER_STATISTICS"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "peerStatistic" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
```cpp
const int ENET_PACKET_LOSS_SCALE = 65536;

enum PeerStatistics
{
	// PacketLoss will only update once every 10 seconds, use PacketLossEpoch if you want the time
	// since the last time the packet loss was updated.

	// the amount of packet loss the player has, needs to be scaled with PACKET_LOSS_SCALE
	PacketLoss = 0,
	// The variance in the packet loss
	PacketLossVariance = 1,
	// The time since the last packet update in ms, relative to the peers connection time
	PacketLossEpoch = 2,
	// The mean amount of time it takes for a packet to get to the client (ping)
	RoundTripTime = 3,
	// The variance in the round trip time
	RoundTripTimeVariance = 4,
	// Despite their name, these are only updated once every 5 seconds, you can get the last time this was updated with PacketThrottleEpoch
	// The last recorded round trip time of a packet
	LastRoundTripTime = 5,
	// The last round trip time variance
	LastRoundTripTimeVariance = 6,
	// The time since the last packet throttle update, relative to the peers connection time
	PacketThrottleEpoch = 7,
};
```

These statistics only update once every 10 seconds.
</summary>
<param name="playerSrc">The player to get the stats of</param>
<param name="peerStatistic">The statistic to get, this will error if its out of range</param>
<returns>See `ENetStatisticType` for what this will return.</returns>
	]]

native "GET_PLAYER_PING"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
See [GET_PLAYER_PEER_STATISTICS](#\_0x9A928294) if you want more detailed information, like packet loss, and packet/rtt variance
</summary>
<returns>Returns the mean amount of time a packet takes to get to the client</returns>
	]]

native "GET_PLAYER_ROUTING_BUCKET"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the routing bucket for the specified player.

Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
</summary>
<param name="playerSrc">The player to get the routing bucket for.</param>
<returns>The routing bucket ID.</returns>
	]]

native "GET_PLAYER_SERVER_ID"
	arguments {
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
	]]

native "GET_PLAYER_STAMINA"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="playerId">The player index.</param>
<returns>The value of player stamina.</returns>
	]]

native "GET_PLAYER_TEAM"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="playerSrc">The player handle</param>
	]]

native "GET_PLAYER_TIME_IN_PURSUIT"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		BOOL "lastPursuit" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
```
Gets the amount of time player has spent evading the cops.
Counter starts and increments only when cops are chasing the player.
If the player is evading, the timer will pause.
```
</summary>
<param name="playerSrc">The target player</param>
<param name="lastPursuit">False = CurrentPursuit, True = LastPursuit</param>
<returns>Returns -1, if the player is not wanted or wasn't in pursuit before, depending on the lastPursuit parameter
Returns 0, if lastPursuit == False and the player has a wanted level, but the pursuit has not started yet
Otherwise, will return the milliseconds of the pursuit.</returns>
	]]

native "GET_PLAYER_TIME_ONLINE"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the current time online for a specified player.
</summary>
<param name="playerSrc">A player.</param>
<returns>The current time online in seconds.</returns>
	]]

native "GET_PLAYER_TOKEN"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
Gets a player's token. Tokens can be used to enhance banning logic, however are specific to a server.
</summary>
<param name="playerSrc">A player.</param>
<param name="index">Index between 0 and GET_NUM_PLAYER_TOKENS.</param>
<returns>A token value.</returns>
	]]

native "GET_PLAYER_VEHICLE_DAMAGE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_VEHICLE_DAMAGE_MODIFIER](#\_0xA50E117CDDF82F0C).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player vehicle damage modifier.</returns>
	]]

native "GET_PLAYER_VEHICLE_DEFENSE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_VEHICLE_DEFENSE_MODIFIER](#\_0x4C60E6EFDAFF2462).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player vehicle defense modifier.</returns>
	]]

native "GET_PLAYER_WANTED_CENTRE_POSITION"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
<summary>
Gets the current known coordinates for the specified player from cops perspective. This native is used server side when using OneSync.
</summary>
<param name="playerSrc">The target player</param>
<returns>The player's position known by police. Vector zero if the player has no wanted level.</returns>
	]]

native "GET_PLAYER_WANTED_LEVEL"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
```
Returns given players wanted level server-side.
```
</summary>
<param name="playerSrc">The target player</param>
<returns>The wanted level</returns>
	]]

native "GET_PLAYER_WEAPON_DAMAGE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_WEAPON_DAMAGE_MODIFIER](#\_0xCE07B9F7817AADA3).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player weapon damage modifier.</returns>
	]]

native "GET_PLAYER_WEAPON_DEFENSE_MODIFIER"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_PLAYER_WEAPON_DEFENSE_MODIFIER](#\_0x2D83BC011CA14A3C).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player weapon defense modifier.</returns>
	]]

native "GET_PLAYER_WEAPON_DEFENSE_MODIFIER_2"
	arguments {
		Player "playerId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [\_SET_PLAYER_WEAPON_DEFENSE_MODIFIER\_2](#\_0xBCFDE9EDE4CF27DC).
</summary>
<param name="playerId">The player index.</param>
<returns>The value of player weapon defense modifier 2.</returns>
	]]

native "GET_RANDOM_VEHICLE_DENSITY_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_RANDOM_VEHICLE_DENSITY_MULTIPLIER_THIS_FRAME](#\_0xB3B3359379FE77D3).
Same as vehicle density multiplier.
</summary>
<returns>Returns random vehicle density multiplier value.</returns>
	]]

native "GET_REGISTERED_COMMANDS"
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<summary>
Returns all commands that are registered in the command system.
The data returned adheres to the following layout:

```
[
{
"name": "cmdlist",
"resource": "resource",
"arity" = -1,
},
{
"name": "command1"
"resource": "resource_2",
"arity" = -1,
}
]
```
</summary>
<returns>An object containing registered commands.</returns>
	]]

native "GET_RESOURCE_BY_FIND_INDEX"
	arguments {
		int "findIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<param name="findIndex">The index of the resource (starting at 0)</param>
<returns>The resource name as a `string`</returns>
	]]

native "GET_RESOURCE_COMMANDS"
	arguments {
		charPtr "resource" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<summary>
Returns all commands registered by the specified resource.
The data returned adheres to the following layout:

```
[
{
"name": "cmdlist",
"resource": "example_resource",
"arity" = -1,
},
{
"name": "command1"
"resource": "example_resource2",
"arity" = -1,
}
]
```
</summary>
<returns>An object containing registered commands.</returns>
	]]

native "GET_RESOURCE_KVP_FLOAT"
	arguments {
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "float"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
</summary>
<param name="key">The key to fetch</param>
<returns>The floating-point value stored under the specified key, or 0.0 if not found.</returns>
	]]

native "GET_RESOURCE_KVP_INT"
	arguments {
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
</summary>
<param name="key">The key to fetch</param>
<returns>The integer value stored under the specified key, or 0 if not found.</returns>
	]]

native "GET_RESOURCE_KVP_STRING"
	arguments {
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
</summary>
<param name="key">The key to fetch</param>
<returns>The string value stored under the specified key, or nil/null if not found.</returns>
	]]

native "GET_RESOURCE_METADATA"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "metadataKey" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Gets the metadata value at a specified key/index from a resource's manifest.
See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
</summary>
<param name="resourceName">The resource name.</param>
<param name="metadataKey">The key in the resource manifest.</param>
<param name="index">The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].</param>
	]]

native "GET_RESOURCE_PATH"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
Returns the physical on-disk path of the specified resource.
</summary>
<param name="resourceName">The name of the resource.</param>
<returns>The resource directory name, possibly without trailing slash.</returns>
	]]

native "GET_RESOURCE_STATE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Returns the current state of the specified resource.
</summary>
<param name="resourceName">The name of the resource.</param>
<returns>The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.</returns>
	]]

native "GET_ROPE_FLAGS"
	arguments {
		int "rope" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
```cpp
enum eRopeFlags
{
    DrawShadowEnabled = 2,
	Breakable = 4,
	RopeUnwindingFront = 8,
	RopeWinding = 32
}
```
</summary>
<param name="rope">The rope to get the flags for.</param>
<returns>The rope's flags.</returns>
	]]

native "GET_ROPE_LENGTH_CHANGE_RATE"
	arguments {
		int "rope" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="rope">The rope to get the length change rate for.</param>
<returns>The rope's length change rate.</returns>
	]]

native "GET_ROPE_TIME_MULTIPLIER"
	arguments {
		int "rope" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="rope">The rope to get the time multiplier for.</param>
<returns>The rope's time multiplier.</returns>
	]]

native "GET_ROPE_UPDATE_ORDER"
	arguments {
		int "rope" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="rope">The rope to get the update order for.</param>
<returns>The rope's update order.</returns>
	]]

native "GET_RUNTIME_TEXTURE_HEIGHT"
	arguments {
		long "tex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the height of the specified runtime texture.
</summary>
<param name="tex">A handle to the runtime texture.</param>
<returns>The height in pixels.</returns>
	]]

native "GET_RUNTIME_TEXTURE_PITCH"
	arguments {
		long "tex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the row pitch of the specified runtime texture, for use when creating data for `SET_RUNTIME_TEXTURE_ARGB_DATA`.
</summary>
<param name="tex">A handle to the runtime texture.</param>
<returns>The row pitch in bytes.</returns>
	]]

native "GET_RUNTIME_TEXTURE_WIDTH"
	arguments {
		long "tex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the width of the specified runtime texture.
</summary>
<param name="tex">A handle to the runtime texture.</param>
<returns>The width in pixels.</returns>
	]]

native "GET_SCENARIO_PED_DENSITY_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_SCENARIO_PED_DENSITY_MULTIPLIER_THIS_FRAME](#\_0x7A556143A1C03898).
</summary>
<returns>Returns scenario ped density multiplier value.</returns>
	]]

native "GET_SELECTED_PED_WEAPON"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Hash"
	doc [[!
<summary>
An alias of [GET_CURRENT_PED_WEAPON](#\_0xB0237302).

Note, the client-side [GET_SELECTED_PED_WEAPON](#\_0x0A6DB4965674D243) native returns the weapon selected via the HUD (weapon wheel). This data is not available to FXServer.
</summary>
<param name="ped">The target ped.</param>
<returns>The weapon hash.</returns>
	]]

native "GET_SHAPE_TEST_RESULT_INCLUDING_MATERIAL"
	arguments {
		int "shapeTestHandle" [=[ {} ]=],
		BOOLPtr "hit" [=[ {} ]=],
		Vector3Ptr "endCoords" [=[ {} ]=],
		Vector3Ptr "surfaceNormal" [=[ {} ]=],
		HashPtr "materialHash" [=[ {} ]=],
		EntityPtr "entityHit" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "int"
	doc [[!
<summary>
Returns the result of a shape test, also returning the material of any touched surface.

When used with an asynchronous shape test, this native should be looped until returning 0 or 2, after which the handle is invalidated.

Unless the return value is 2, the other return values are undefined.
</summary>
<param name="shapeTestHandle">A shape test handle.</param>
<param name="hit">Whether or not the shape test hit any collisions.</param>
<param name="endCoords">The resulting coordinates where the shape test hit a collision.</param>
<param name="surfaceNormal">The surface normal of the hit position.</param>
<param name="materialHash">hash of the hit material or surface type, see materialFX.dat</param>
<param name="entityHit">Any dynamic entity hit by the shape test.</param>
<returns>`0` if the handle is invalid, `1` if the shape test is still pending, or `2` if the shape test has completed, and the handle should be invalidated.</returns>
	]]

native "GET_STATE_BAG_KEYS"
	arguments {
		charPtr "bagName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<param name="bagName">The name of the bag.</param>
<returns>Returns an array containing all keys for which the state bag has associated values.</returns>
	]]

native "GET_STATE_BAG_VALUE"
	arguments {
		charPtr "bagName" [=[ {} ]=],
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "object"
	doc [[!
<summary>
Returns the value of a state bag key.
</summary>
<returns>Value.</returns>
	]]

native "GET_THRUSTER_SIDE_RCS_THROTTLE"
	arguments {
		Vehicle "jetpack" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="jetpack">The jetpack to check.</param>
<returns>Returns a value representing the side RCS (Reaction Control System) throttle of the jetpack. The values range from `0.0` (no throttle) to `1.0` (full throttle).</returns>
	]]

native "GET_THRUSTER_THROTTLE"
	arguments {
		Vehicle "jetpack" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "float"
	doc [[!
<param name="jetpack">The jetpack to check.</param>
<returns>Returns a value representing the main throttle of the jetpack. The values range from `0.0` (no throttle) to `1.0` (full throttle)</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_COUNT"
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<returns>Returns the amount of timecycle modifiers loaded.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_INDEX_BY_NAME"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<param name="modifierName">The timecycle modifier name.</param>
<returns>The timecycle modifier index.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_NAME_BY_INDEX"
	arguments {
		int "modifierIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<param name="modifierIndex">The timecycle modifier index.</param>
<returns>The timecycle modifier name.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_STRENGTH"
	ns "CFX"
    apiset "client"
	returns "float"
	doc [[!
<summary>
A getter for [SET_TIMECYCLE_MODIFIER_STRENGTH](#\_0x82E7FFCD5B2326B3).
</summary>
<returns>Returns current timecycle modifier strength.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_VAR"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
		charPtr "varName" [=[ {} ]=],
		floatPtr "value1" [=[ {} ]=],
		floatPtr "value2" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<param name="modifierName">The name of timecycle modifier.</param>
<param name="varName">The name of timecycle variable.</param>
<returns>Whether or not variable by name was found on the specified timecycle modifier.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_VAR_COUNT"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<param name="modifierName">The timecycle modifier name.</param>
<returns>The amount of variables used on a specified timecycle modifier.</returns>
	]]

native "GET_TIMECYCLE_MODIFIER_VAR_NAME_BY_INDEX"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
		int "modifierVarIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<param name="modifierName">The name of timecycle modifier.</param>
<param name="modifierVarIndex">The index of a variable on the specified timecycle modifier.</param>
<returns>The name of a variable by index.</returns>
	]]

native "GET_TIMECYCLE_VAR_COUNT"
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Returns the amount of variables available to be applied on timecycle modifiers.
</summary>
<returns>The amount of available variables for timecycle modifiers.</returns>
	]]

native "GET_TIMECYCLE_VAR_DEFAULT_VALUE_BY_INDEX"
	arguments {
		int "varIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "float"
	doc [[!
<summary>
See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
</summary>
<param name="varIndex">The index of variable.</param>
<returns>The default value of a timecycle variable.</returns>
	]]

native "GET_TIMECYCLE_VAR_NAME_BY_INDEX"
	arguments {
		int "varIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "charPtr"
	doc [[!
<summary>
See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
</summary>
<param name="varIndex">The index of variable.</param>
<returns>The name of a timecycle variable.</returns>
	]]

native "GET_TRACK_BRAKING_DISTANCE"
	arguments {
		int "track" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="track">The track id (between 0 - 27)</param>
<returns>The braking distance of the track. Used by trains to determine the point to slow down at when entering a station.</returns>
	]]

native "GET_TRACK_MAX_SPEED"
	arguments {
		int "track" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="track">The track id (between 0 - 27)</param>
<returns>The max speed of the track</returns>
	]]

native "GET_TRACK_NODE_COORDS"
	arguments {
		int "trackIndex" [=[ {} ]=],
		int "trackNode" [=[ {} ]=],
		Vector3Ptr "coords" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
Gets the coordinates of a specific track node.
</summary>
<param name="trackIndex">The track index</param>
<param name="trackNode">The track node</param>
<param name="coords">The resulting track node coords</param>
<returns>Returns if it succeeds in getting coords or not</returns>
	]]

native "GET_TRACK_NODE_COUNT"
	arguments {
		int "trackIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the specified tracks node count.
</summary>
<param name="trackIndex">The track index</param>
<returns>The amount of track nodes on the specified track</returns>
	]]

native "GET_TRAIN_BACKWARD_CARRIAGE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "int"
	doc [[!
<param name="train">The train handle</param>
<returns>The handle of the carriage behind this train in the chain. Otherwise returns 0 if the train is the caboose of the chain.</returns>
	]]

native "GET_TRAIN_CARRIAGE_ENGINE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="train">The entity handle.</param>
<returns>The train engine carriage.</returns>
	]]

native "GET_TRAIN_CARRIAGE_INDEX"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<param name="train">The entity handle.</param>
<returns>The carriage index. -1 returned if invalid result.</returns>
	]]

native "GET_TRAIN_CRUISE_SPEED"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the trains desired speed.
</summary>
<param name="train">The train handle</param>
<returns>The desired cruise speed of the train. Not the speed the train is currently traveling at</returns>
	]]

native "GET_TRAIN_CURRENT_TRACK_NODE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="train">The target train.</param>
<returns>Train's current track node index.</returns>
	]]

native "GET_TRAIN_DIRECTION"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Gets the direction the train is facing
</summary>
<param name="train">The train handle</param>
<returns>True if the train is moving forward on the track, False otherwise</returns>
	]]

native "GET_TRAIN_DOOR_COUNT"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the door count for the specified train.
</summary>
<param name="train">The train to obtain the door count for.</param>
<returns>The door count.</returns>
	]]

native "GET_TRAIN_DOOR_OPEN_RATIO"
	arguments {
		Vehicle "train" [=[ {} ]=],
		int "doorIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the ratio that a door is open for on a train.
</summary>
<param name="train">The train to obtain the door ratio for.</param>
<param name="doorIndex">Zero-based door index.</param>
<returns>A value between 0.0 (fully closed) and 1.0 (fully open).</returns>
	]]

native "GET_TRAIN_FORWARD_CARRIAGE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "int"
	doc [[!
<param name="train">The train handle</param>
<returns>The handle of the carriage in front of this train in the chain. Otherwise returns 0 if the train has no carriage in front of it</returns>
	]]

native "GET_TRAIN_SPEED"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the speed the train is currently going.
</summary>
<param name="train">The train handle</param>
<returns>The current speed of the train</returns>
	]]

native "GET_TRAIN_STATE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "int"
	doc [[!
<param name="train">The train handle</param>
<returns>The trains current state```cpp
enum eTrainState
{
    MOVING = 0,
    ENTERING_STATION,
    OPENING_DOORS,
    STOPPED,
    CLOSING_DOORS,
    LEAVING_STATION,
}
```</returns>
	]]

native "GET_TRAIN_TRACK_INDEX"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "int"
	doc [[!
<param name="train">The train handle</param>
<returns>The track index the train is currently on.</returns>
	]]

native "GET_VEHICLE_ALARM_TIME_LEFT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_BODY_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_CHEAT_POWER_INCREASE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_VEHICLE_CHEAT_POWER_INCREASE](#\_0xB59E4BD37AE292DB).
</summary>
<param name="vehicle">The target vehicle.</param>
<returns>Returns vehicle's cheat power increase modifier value.</returns>
	]]

native "GET_VEHICLE_CLUTCH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_COLOURS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "colorPrimary" [=[ {} ]=],
		intPtr "colorSecondary" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_CURRENT_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_CURRENT_RPM"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_CUSTOM_PRIMARY_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "r" [=[ {} ]=],
		intPtr "g" [=[ {} ]=],
		intPtr "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_CUSTOM_SECONDARY_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "r" [=[ {} ]=],
		intPtr "g" [=[ {} ]=],
		intPtr "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_BOOST"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "color" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_CURRENT_GEAR"
	alias "GET_VEHICLE_DASHBOARD_WATER_TEMP"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Retrieves the current gear displayed on the dashboard of the vehicle the player is in, returned as a float. This value represents the gear shown in the instrument cluster, such as "R" (0.0) or positive values (e.g., 1.0, 2.0, etc.) for drive gears.
</summary>
<returns>The current gear.</returns>
	]]

native "GET_VEHICLE_DASHBOARD_FUEL"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_LIGHTS"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the state of the player vehicle's dashboard lights as a bit set
indicator_left = 1
indicator_right = 2
handbrakeLight = 4
engineLight = 8
ABSLight = 16
gasLight = 32
oilLight = 64
headlights = 128
highBeam = 256
batteryLight = 512
</summary>
	]]

native "GET_VEHICLE_DASHBOARD_OIL_PRESSURE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_OIL_TEMP"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_RPM"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<returns>float 0 to ~1.1 representing the angle of the rpm gauge on the player's vehicle dashboard</returns>
	]]

native "GET_VEHICLE_DASHBOARD_SPEED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_TEMP"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DASHBOARD_VACUUM"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DENSITY_MULTIPLIER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_VEHICLE_DENSITY_MULTIPLIER_THIS_FRAME](#\_0x245A6883D966D537).
</summary>
<returns>Returns vehicle density multiplier value.</returns>
	]]

native "GET_VEHICLE_DIRT_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_DOOR_LOCK_STATUS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
```lua
enum_VehicleLockStatus = {
    None = 0,
    Locked = 2,
    LockedForPlayer = 3,
    StickPlayerInside = 4, -- Doesn't allow players to exit the vehicle with the exit vehicle key.
    CanBeBrokenInto = 7, -- Can be broken into the car. If the glass is broken, the value will be set to 1
    CanBeBrokenIntoPersist = 8, -- Can be broken into persist
    CannotBeTriedToEnter = 10, -- Cannot be tried to enter (Nothing happens when you press the vehicle enter key).
}
```

It should be [noted](https://forum.cfx.re/t/4863241) that while the [client-side command](#\_0x25BC98A59C2EA962) and its
setter distinguish between states 0 (unset) and 1 (unlocked), the game will synchronize both as state 0, so the server-side
command will return only '0' if unlocked.
</summary>
<param name="vehicle">A vehicle handle.</param>
<returns>The door lock status for the specified vehicle.</returns>
	]]

native "GET_VEHICLE_DOOR_STATUS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "doorIndex" [=[ {"cs_split":true} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Returns the open position of the specified door on the target vehicle.
</summary>
<param name="vehicle">The target vehicle.</param>
<param name="doorIndex">Index of door to check (0-6).</param>
<returns>A number from 0 to 7.</returns>
	]]

native "GET_VEHICLE_DOORS_LOCKED_FOR_PLAYER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Currently it only works when set to "all players".
</summary>
	]]

native "GET_VEHICLE_DRAWN_WHEEL_ANGLE_MULT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets a vehicle's multiplier used with a wheel's GET_VEHICLE_WHEEL_STEERING_ANGLE to determine the angle the wheel is rendered.
</summary>
	]]

native "GET_VEHICLE_ENGINE_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_ENGINE_TEMPERATURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_EXTRA_COLOURS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "pearlescentColor" [=[ {} ]=],
		intPtr "wheelColor" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_FLIGHT_NOZZLE_POSITION"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
<summary>
Gets the flight nozzel position for the specified vehicle. See the client-side [\_GET_VEHICLE_FLIGHT_NOZZLE_POSITION](#\_0xDA62027C8BDB326E) native for usage examples.
</summary>
<param name="vehicle">The vehicle to check.</param>
<returns>The flight nozzel position between 0.0 (flying normally) and 1.0 (VTOL mode)</returns>
	]]

native "GET_VEHICLE_FUEL_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_GEAR_RATIO"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "gear" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets vehicles gear ratio on choosen gear.
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="gear">The vehicles gear you want to get.</param>
	]]

native "GET_VEHICLE_GRAVITY_AMOUNT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_HANDBRAKE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "GET_VEHICLE_HANDLING_FLOAT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Returns the effective handling data of a vehicle as a floating-point value.
Example: `local fSteeringLock = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSteeringLock')`
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="class_">The handling class to get. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to get. These match the keys in `handling.meta`.</param>
<returns>A floating-point value.</returns>
	]]

native "GET_VEHICLE_HANDLING_INT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Returns the effective handling data of a vehicle as an integer value.
Example: `local modelFlags = GetVehicleHandlingInt(vehicle, 'CHandlingData', 'strModelFlags')`
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="class_">The handling class to get. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to get. These match the keys in `handling.meta`.</param>
<returns>An integer.</returns>
	]]

native "GET_VEHICLE_HANDLING_VECTOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Vector3"
	doc [[!
<summary>
Returns the effective handling data of a vehicle as a vector value.
Example: `local inertiaMultiplier = GetVehicleHandlingVector(vehicle, 'CHandlingData', 'vecInertiaMultiplier')`
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="class_">The handling class to get. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to get. These match the keys in `handling.meta`.</param>
<returns>An integer.</returns>
	]]

native "GET_VEHICLE_HAS_FLAG"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "flagIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "bool"
	doc [[!
<summary>
**Note**: Flags are not the same based on your `gamebuild`. Please see [here](https://docs.fivem.net/docs/game-references/vehicle-references/vehicle-flags) to see a complete list of all vehicle flags.

Get vehicle.meta flag by index. Useful examples include `FLAG_LAW_ENFORCEMENT` (31), `FLAG_RICH_CAR` (36), `FLAG_IS_ELECTRIC` (43), `FLAG_IS_OFFROAD_VEHICLE` (48).
</summary>
<param name="vehicle">The vehicle to obtain flags for.</param>
<param name="flagIndex">Flag index.</param>
<returns>A boolean for whether the flag is set.### Example```lua
local vehicleFlags = {
    FLAG_SMALL_WORKER = 0,
    FLAG_BIG = 1,
    FLAG_NO_BOOT = 2,
    FLAG_ONLY_DURING_OFFICE_HOURS = 3
    -- This is just a example, see fivem-docs to see all flags.
}

local function getAllVehicleFlags(vehicle)
    local flags = {}
    for i = 0, 256 do
        if GetVehicleHasFlag(vehicle, i) then
            flags[#flags+1] = i
        end
    end
    return flags
end

local flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
print(getAllVehicleFlags)
``````javascript
const VEHICLE_FLAGS = {
    FLAG_SMALL_WORKER: 0,
    FLAG_BIG: 1,
    FLAG_NO_BOOT: 2,
    FLAG_ONLY_DURING_OFFICE_HOURS: 3
    // This is just a example, see fivem-docs to see all flags.
};

function getAllVehicleFlags(mVehicle = GetVehiclePedIsIn(PlayerPedId(), false)) {
    const flags = [];
    for (let i = 0; i < 204; i++) {
        if (GetVehicleHasFlag(mVehicle, i)) {
            flags.push(i);
        }
    }
    return flags;
}

let flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false);
console.log(getAllVehicleFlags);
```</returns>
	]]

native "GET_VEHICLE_HEADLIGHTS_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_HIGH_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_HOMING_LOCKON_STATE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Gets the lock on state for the specified vehicle. See the client-side [GET_VEHICLE_HOMING_LOCKON_STATE](#\_0xE6B0E8CFC3633BF0) native for a description of lock on states.
</summary>
<param name="vehicle">The vehicle to check.</param>
<returns>The lock on state.</returns>
	]]

native "GET_VEHICLE_HORN_TYPE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "Hash"
	doc [[!
<summary>
This is a getter for the client-side native [`START_VEHICLE_HORN`](#\_0x9C8C6504B5B63D2C), which allows you to return the horn type of the vehicle.

**Note**: This native only gets the hash value set with `START_VEHICLE_HORN`. If a wrong hash is passed into `START_VEHICLE_HORN`, it will return this wrong hash.

```cpp
enum eHornTypes
{
    NORMAL = 1330140148,
    HELDDOWN = -2087385909,
    AGGRESSIVE = -92810745
}
```
</summary>
<param name="vehicle">The vehicle to check the horn type.</param>
<returns>Returns the vehicle horn type hash, or `0` if one is not set.</returns>
	]]

native "GET_VEHICLE_INDICATOR_LIGHTS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the vehicle indicator light state. 0 = off, 1 = left, 2 = right, 3 = both
</summary>
<returns>An integer.</returns>
	]]

native "GET_VEHICLE_INTERIOR_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "color" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_LIGHT_MULTIPLIER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_LIGHTS_STATE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		BOOLPtr "lightsOn" [=[ {} ]=],
		BOOLPtr "highbeamsOn" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "GET_VEHICLE_LIVERY"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_LOCK_ON_TARGET"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vehicle"
	doc [[!
<summary>
Gets the vehicle that is locked on to for the specified vehicle.
</summary>
<param name="vehicle">The vehicle to check.</param>
<returns>The vehicle that is locked on. 0 returned if no vehicle is locked on.</returns>
	]]

native "GET_VEHICLE_NEON_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "red" [=[ {} ]=],
		intPtr "green" [=[ {} ]=],
		intPtr "blue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Getter to check the neon colour of a vehicle. This native is the server side getter of [GET_VEHICLE_NEON_LIGHTS_COLOUR](#\_0x7619EEE8C886757F).
</summary>
<param name="vehicle">The vehicle to check.</param>
<param name="red">Pointer to an integer where the red component of the neon color will be stored.</param>
<param name="green">Pointer to an integer where the green component of the neon color will be stored.</param>
<param name="blue">Pointer to an integer where the blue component of the neon color will be stored.</param>
<returns>None. The neon color values are retrieved and stored in the `red`, `green`, and `blue` pointers. Make sure to store the returned values in variables for further use.</returns>
	]]

native "GET_VEHICLE_NEON_ENABLED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "neonIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Getter to check if one of the neon lights of a vehicle is enabled. This native is the server side getter of [IS_VEHICLE_NEON_LIGHT_ENABLED](#\_0x8C4B92553E4766A5).

```cpp
enum neonIndex
{
    NEON_BACK = 0,   // Back neon
    NEON_RIGHT = 1,  // Right neon
    NEON_LEFT = 2,   // Left neon
    NEON_FRONT = 3   // Front neon
};
```
</summary>
<param name="vehicle">The vehicle to check.</param>
<param name="neonIndex">A value from the neonIndex enum representing the neon light to check.</param>
<returns>Returns `true` if the specified neon light is enabled, `false` otherwise.</returns>
	]]

native "GET_VEHICLE_NEXT_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_NUMBER_OF_WHEELS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_NUMBER_PLATE_TEXT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
	]]

native "GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_OIL_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_PED_IS_IN"
	arguments {
		Ped "ped" [=[ {} ]=],
		BOOL "lastVehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vehicle"
	doc [[!
<summary>
Gets the vehicle the specified Ped is/was in depending on bool value. This native is used server side when using OneSync.
</summary>
<param name="ped">The target ped</param>
<param name="lastVehicle">False = CurrentVehicle, True = LastVehicle</param>
<returns>The vehicle id. Returns 0 if the ped is/was not in a vehicle.</returns>
	]]

native "GET_VEHICLE_PETROL_TANK_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_RADIO_STATION_INDEX"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_ROOF_LIVERY"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_STEERING_ANGLE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_STEERING_SCALE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_THROTTLE_OFFSET"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	alias "GET_VEHICLE_CURRENT_ACCELERATION"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<returns>A float among -1 and 1 according if the vehicle is moving forwards or backwards</returns>
	]]

native "GET_VEHICLE_TOP_SPEED_MODIFIER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [MODIFY_VEHICLE_TOP_SPEED](#\_0x93A3996368C94158). Returns -1.0 if a modifier is not set.
</summary>
<param name="vehicle">The target vehicle.</param>
<returns>Returns vehicle's modified top speed.</returns>
	]]

native "GET_VEHICLE_TOTAL_REPAIRS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<returns>Returns the total amount of repairs. Each repair will increase the count to make it possible to detect client repairs.
The value has a range from 0 to 15. Next value after 15 is 0.</returns>
	]]

native "GET_VEHICLE_TURBO_PRESSURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_TYPE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Returns the type of the passed vehicle.

For client scripts, reference the more detailed [GET_VEHICLE_TYPE_RAW](#\_0xDE73BC10) native.

### Vehicle types

*   automobile
*   bike
*   boat
*   heli
*   plane
*   submarine
*   trailer
*   train
</summary>
<param name="vehicle">The vehicle's entity handle.</param>
<returns>If the entity is a vehicle, the vehicle type. If it is not a vehicle, the return value will be null.</returns>
	]]

native "GET_VEHICLE_TYPE_RAW"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Returns the model type of the vehicle as defined by:

```cpp
enum VehicleType
{
	VEHICLE_TYPE_NONE = -1,
	VEHICLE_TYPE_CAR = 0,
	VEHICLE_TYPE_PLANE = 1,
	VEHICLE_TYPE_TRAILER = 2,
	VEHICLE_TYPE_QUADBIKE = 3,
	VEHICLE_TYPE_DRAFT = 4,
	VEHICLE_TYPE_SUBMARINECAR = 5,
	VEHICLE_TYPE_AMPHIBIOUS_AUTOMOBILE = 6,
	VEHICLE_TYPE_AMPHIBIOUS_QUADBIKE = 7,
	VEHICLE_TYPE_HELI = 8,
	VEHICLE_TYPE_BLIMP = 9,
	VEHICLE_TYPE_AUTOGYRO = 10,
	VEHICLE_TYPE_BIKE = 11,
	VEHICLE_TYPE_BICYCLE = 12,
	VEHICLE_TYPE_BOAT = 13,
	VEHICLE_TYPE_TRAIN = 14,
	VEHICLE_TYPE_SUBMARINE = 15,
};
```
</summary>
<param name="vehicle">The vehicle's entity handle.</param>
<returns>Returns the vehicles model type</returns>
	]]

native "GET_VEHICLE_TYRE_SMOKE_COLOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "r" [=[ {} ]=],
		intPtr "g" [=[ {} ]=],
		intPtr "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "GET_VEHICLE_WHEEL_BRAKE_PRESSURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets brake pressure of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
Normal values around 1.0f when braking.
</summary>
	]]

native "GET_VEHICLE_WHEEL_FLAGS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Gets the flags of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<returns>An unsigned int containing bit flags.</returns>
	]]

native "GET_VEHICLE_WHEEL_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_WHEEL_IS_POWERED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Gets whether the wheel is powered.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
This is a shortcut to a flag in GET_VEHICLE_WHEEL_FLAGS.
</summary>
	]]

native "GET_VEHICLE_WHEEL_POWER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets power being sent to a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
	]]

native "GET_VEHICLE_WHEEL_RIM_COLLIDER_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<returns>Float representing size of the rim collider. Not sure what it is used for, probably to detect whether bullets hit rim or tire and puncture it (and to determine size of the wheel when tire is fully blown).</returns>
	]]

native "GET_VEHICLE_WHEEL_ROTATION_SPEED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the rotation speed of a wheel.
This is used internally to calcuate GET_VEHICLE_WHEEL_SPEED.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<returns>The angular velocity of the wheel.</returns>
	]]

native "GET_VEHICLE_WHEEL_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Returns vehicle's wheels' size (size is the same for all the wheels, cannot get/set specific wheel of vehicle).
Only works on non-default wheels (returns 0 in case of default wheels).
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<returns>Float representing size of the wheel (usually between 0.5 and 1.5)</returns>
	]]

native "GET_VEHICLE_WHEEL_SPEED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets speed of a wheel at the tyre.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<returns>An integer.</returns>
	]]

native "GET_VEHICLE_WHEEL_STEERING_ANGLE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets steering angle of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<returns>The steering angle of the wheel, with 0 being straight.</returns>
	]]

native "GET_VEHICLE_WHEEL_SURFACE_MATERIAL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<returns>Integer representing the index of the current surface material of that wheel. Check materials.dat for the indexes.</returns>
	]]

native "GET_VEHICLE_WHEEL_SUSPENSION_COMPRESSION"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the current suspension compression of a wheel.
Returns a positive value. 0 means the suspension is fully extended, the wheel is off the ground.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<returns>The current compression of the wheel's suspension.</returns>
	]]

native "GET_VEHICLE_WHEEL_TIRE_COLLIDER_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<returns>Float representing size of the wheel collider.</returns>
	]]

native "GET_VEHICLE_WHEEL_TIRE_COLLIDER_WIDTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<returns>Float representing width of the wheel collider.</returns>
	]]

native "GET_VEHICLE_WHEEL_TRACTION_VECTOR_LENGTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Gets the traction vector length of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
	]]

native "GET_VEHICLE_WHEEL_TYPE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_WHEEL_WIDTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Returns vehicle's wheels' width (width is the same for all the wheels, cannot get/set specific wheel of vehicle).
Only works on non-default wheels (returns 0 in case of default wheels).
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<returns>Float representing width of the wheel (usually between 0.1 and 1.5)</returns>
	]]

native "GET_VEHICLE_WHEEL_X_OFFSET"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
Returns the offset of the specified wheel relative to the wheel's axle center.
</summary>
	]]

native "GET_VEHICLE_WHEEL_Y_ROTATION"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	alias "GET_VEHICLE_WHEEL_XROT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
	]]

native "GET_VEHICLE_WHEELIE_STATE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
List of known states:

```
1: Not wheeling.
65: Vehicle is ready to do wheelie (burnouting).
129: Vehicle is doing wheelie.
```
</summary>
<param name="vehicle">Vehicle</param>
<returns>Vehicle's current wheelie state.</returns>
	]]

native "GET_VEHICLE_WINDOW_TINT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "GET_VEHICLE_XENON_LIGHTS_CUSTOM_COLOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		intPtr "red" [=[ {} ]=],
		intPtr "green" [=[ {} ]=],
		intPtr "blue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Returns vehicle xenon lights custom RGB color values. Do note this native doesn't return non-RGB colors that was set with [\_SET_VEHICLE_XENON_LIGHTS_COLOR](#\_0xE41033B25D003A07).
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="red">Red color (0-255).</param>
<param name="green">Green color (0-255).</param>
<param name="blue">Blue color (0-255).</param>
<returns>A boolean indicating if vehicle have custom xenon lights RGB color.</returns>
	]]

native "GET_VEHICLE_XMAS_SNOW_FACTOR"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [SET_VEHICLE_XMAS_SNOW_FACTOR](#\_0x80CC4C9E).
</summary>
<returns>Returns the grip factor for the vehicles wheels during xmas weather. default value is 0.2.</returns>
	]]

native "GET_VISUAL_SETTING_FLOAT"
	arguments {
		charPtr "name" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "float"
	doc [[!
<summary>
A getter for [SET_VISUAL_SETTING_FLOAT](#\_0xD1D31681).
</summary>
<param name="name">The name of the value to get, such as `pedLight.color.red`.</param>
<returns>Returns the floating point value of the specified visual setting on success.</returns>
	]]

native "GET_WATER_QUAD_ALPHA"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "a0" [=[ {} ]=],
		intPtr "a1" [=[ {} ]=],
		intPtr "a2" [=[ {} ]=],
		intPtr "a3" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="a0">The a0 level</param>
<param name="a1">The a1 level</param>
<param name="a2">The a2 level</param>
<param name="a3">The a3 level</param>
<returns>Returns true on success. Alpha values are undefined on failure</returns>
	]]

native "GET_WATER_QUAD_AT_COORDS"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
This native returns the index of a water quad if the given point is inside its bounds.

*If you also want to check for water level, check out [`GetWaterQuadAtCoords_3d`](#\_0xF8E03DB8)*
</summary>
<param name="x">The X coordinate</param>
<param name="y">The Y coordinate</param>
<returns>The water quad index at the given position. Returns -1 if there isn't any there.</returns>
	]]

native "GET_WATER_QUAD_AT_COORDS_3D"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
This alternative implementation of [`GetWaterQuadAtCoords`](#\_0x17321452) also checks the height of the water level.
</summary>
<param name="x">The X coordinate</param>
<param name="y">The Y coordinate</param>
<param name="z">The water level inside the water quad</param>
<returns>The water quad index at the given position. Returns -1 if there isn't any there. Also returns -1 if the given point is above the water level.</returns>
	]]

native "GET_WATER_QUAD_BOUNDS"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "minX" [=[ {} ]=],
		intPtr "minY" [=[ {} ]=],
		intPtr "maxX" [=[ {} ]=],
		intPtr "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success. Bounds are undefined on failure</returns>
	]]

native "GET_WATER_QUAD_COUNT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<returns>Returns the amount of water quads loaded.</returns>
	]]

native "GET_WATER_QUAD_HAS_LIMITED_DEPTH"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "hasLimitedDepth" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<returns>Returns if the given water quad has a limited depth.</returns>
	]]

native "GET_WATER_QUAD_IS_INVISIBLE"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "isInvisible" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<returns>Returns if the given water quad is invisible</returns>
	]]

native "GET_WATER_QUAD_LEVEL"
	arguments {
		int "waterQuad" [=[ {} ]=],
		floatPtr "waterQuadLevel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
*level is defined as "z" in water.xml*
</summary>
<param name="waterQuad">The returned water quad level</param>
<returns>Returns true on success. Level is undefined on failure</returns>
	]]

native "GET_WATER_QUAD_NO_STENCIL"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "noStencil" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<returns>Returns if the given water quad has no stencil.</returns>
	]]

native "GET_WATER_QUAD_TYPE"
	arguments {
		int "waterQuad" [=[ {} ]=],
		intPtr "waterType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Valid type definitions:

*   **0** Square
*   **1** Right triangle where the 90 degree angle is at maxX, minY
*   **2** Right triangle where the 90 degree angle is at minX, minY
*   **3** Right triangle where the 90 degree angle is at minX, maxY
*   **4** Right triangle where the 90 degree angle is at maxY, maxY
</summary>
<param name="waterQuad">The water quad index</param>
<returns>Returns true on success. Type is undefined on failure</returns>
	]]

native "GET_WAVE_QUAD_AMPLITUDE"
	arguments {
		int "waveQuad" [=[ {} ]=],
		floatPtr "waveQuadAmplitude" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waveQuad">The wave quad index</param>
<returns>Returns true on success. Amplitude is undefined on failure</returns>
	]]

native "GET_WAVE_QUAD_AT_COORDS"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
This native returns the index of a wave quad if the given point is inside its bounds.
</summary>
<param name="x">The X coordinate</param>
<param name="y">The Y coordinate</param>
<returns>The wave quad index at the given position. Returns -1 if there isn't any there.</returns>
	]]

native "GET_WAVE_QUAD_BOUNDS"
	arguments {
		int "waveQuad" [=[ {} ]=],
		intPtr "minX" [=[ {} ]=],
		intPtr "minY" [=[ {} ]=],
		intPtr "maxX" [=[ {} ]=],
		intPtr "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waveQuad">The wave quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success. Bounds are undefined on failure</returns>
	]]

native "GET_WAVE_QUAD_COUNT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<returns>Returns the amount of wave quads loaded.</returns>
	]]

native "GET_WAVE_QUAD_DIRECTION"
	arguments {
		int "waveQuad" [=[ {} ]=],
		floatPtr "directionX" [=[ {} ]=],
		floatPtr "directionY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waveQuad">The wave quad index</param>
<param name="directionX">The wave quad X direction</param>
<param name="directionY">The wave quad Y direction</param>
<returns>Returns true on success. Direction values are undefined on failure</returns>
	]]

native "GET_WEAPON_ACCURACY_SPREAD"
	arguments {
		Hash "weaponHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for the accuracy spread of a weapon.
</summary>
<param name="weaponHash">Weapon name hash.</param>
<returns>The accuracy spread of a weapon.</returns>
	]]

native "GET_WEAPON_ANIMATION_OVERRIDE"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Hash"
	doc [[!
<summary>
A getter for [SET_WEAPON_ANIMATION_OVERRIDE](#\_0x1055AC3A667F09D9).
</summary>
<param name="ped">The target ped.</param>
<returns>The weapon animation override.</returns>
	]]

native "GET_WEAPON_COMPONENT_ACCURACY_MODIFIER"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for `CWeaponAccuracyModifier` in a weapon component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>A weapon component accuracy modifier.</returns>
	]]

native "GET_WEAPON_COMPONENT_CAMERA_HASH"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for `CameraHash` in a weapon scope component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>The hash of the scope camera.</returns>
	]]

native "GET_WEAPON_COMPONENT_CLIP_SIZE"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for `ClipSize` in a weapon component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>A weapon component clip size.</returns>
	]]

native "GET_WEAPON_COMPONENT_DAMAGE_MODIFIER"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for `CWeaponDamageModifier` in a weapon component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>A weapon component damage modifier.</returns>
	]]

native "GET_WEAPON_COMPONENT_RANGE_DAMAGE_MODIFIER"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for `CWeaponFallOffModifier` damage modifier value in a weapon component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>A weapon component damage modifier.</returns>
	]]

native "GET_WEAPON_COMPONENT_RANGE_MODIFIER"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for `CWeaponFallOffModifier` range modifier value in a weapon component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>A weapon component range modifier.</returns>
	]]

native "GET_WEAPON_COMPONENT_RETICULE_HASH"
	arguments {
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
A getter for `ReticuleHash` in a weapon scope component.
</summary>
<param name="componentHash">Weapon component name hash.</param>
<returns>The hash of the reticule camera.</returns>
	]]

native "GET_WEAPON_DAMAGE_MODIFIER"
	arguments {
		Hash "weaponHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for [\_SET_WEAPON_DAMAGE_MODIFIER](#\_0x4757F00BC6323CFE).
</summary>
<param name="weaponHash">Weapon name hash.</param>
<returns>A weapon damage modifier.</returns>
	]]

native "GET_WEAPON_RECOIL_SHAKE_AMPLITUDE"
	arguments {
		Hash "weaponHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "float"
	doc [[!
<summary>
A getter for the recoil shake amplitude of a weapon.
</summary>
<param name="weaponHash">Weapon name hash.</param>
<returns>The recoil shake amplitude of a weapon.</returns>
	]]

native "GET_WORLD_COORD_FROM_SCREEN_COORD"
	arguments {
		float "screenX" [=[ {} ]=],
		float "screenY" [=[ {} ]=],
		Vector3Ptr "worldVector" [=[ {} ]=],
		Vector3Ptr "normalVector" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Converts a screen coordinate into its relative world coordinate.
</summary>
<param name="screenX">A screen horizontal axis coordinate (0.0 - 1.0).</param>
<param name="screenY">A screen vertical axis coordinate (0.0 - 1.0).</param>
<param name="worldVector">The world coord vector pointer.</param>
<param name="normalVector">The screen normal vector pointer.</param>
<returns>A Vector3 representing the world coordinates relative to the specified screen coordinates and a screen plane normal Vector3 (normalised).</returns>
	]]

native "GIVE_WEAPON_COMPONENT_TO_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
GIVE_WEAPON_COMPONENT_TO_PED

**This is the server-side RPC native equivalent of the client native [GIVE_WEAPON_COMPONENT_TO_PED](?\_0xD966D51AA5B28BB9).**
</summary>
	]]

native "GIVE_WEAPON_TO_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
		int "ammoCount" [=[ {} ]=],
		BOOL "isHidden" [=[ {} ]=],
		BOOL "bForceInHand" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
GIVE_WEAPON_TO_PED

**This is the server-side RPC native equivalent of the client native [GIVE_WEAPON_TO_PED](?\_0xBF0FD6E56C964FCB).**
</summary>
	]]

native "HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "HAS_MINIMAP_OVERLAY_LOADED"
	arguments {
		int "id" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Returns whether or not the specific minimap overlay has loaded.
</summary>
<param name="id">A minimap overlay ID.</param>
<returns>A boolean indicating load status.</returns>
	]]

native "HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<returns>Returns whether or not the target vehicle has been damaged by bullets.</returns>
	]]

native "HAS_VEHICLE_BEEN_OWNED_BY_PLAYER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "IS_ACE_ALLOWED"
	arguments {
		charPtr "object" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
	]]

native "IS_BIGMAP_ACTIVE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Returns true if the minimap is currently expanded. False if it's the normal minimap state.
Use [`IsBigmapFull`](#\_0x66EE14B2) to check if the full map is currently revealed on the minimap.
</summary>
<returns>A bool indicating if the minimap is currently expanded or normal state.</returns>
	]]

native "IS_BIGMAP_FULL"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<returns>Returns true if the full map is currently revealed on the minimap.
Use [`IsBigmapActive`](#\_0xFFF65C63) to check if the minimap is currently expanded or in it's normal state.</returns>
	]]

native "IS_BOAT_ANCHORED_AND_FROZEN"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<returns>Returns whether or not the boat is anchored and frozen.</returns>
	]]

native "IS_BOAT_WRECKED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="vehicle">The target vehicle.</param>
<returns>Returns whether or not the boat is wrecked.</returns>
	]]

native "IS_DISABLED_RAW_KEY_DOWN"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is pressed down, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of down state.</returns>
	]]

native "IS_DISABLED_RAW_KEY_PRESSED"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is pressed, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of pressed state.</returns>
	]]

native "IS_DISABLED_RAW_KEY_RELEASED"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` was released, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of released state.</returns>
	]]

native "IS_DISABLED_RAW_KEY_UP"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is up, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of up state.</returns>
	]]

native "IS_DUI_AVAILABLE"
	arguments {
		long "duiObject" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Returns whether or not a browser is created for a specified DUI browser object.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<returns>A boolean indicating TRUE if the browser is created.</returns>
	]]

native "IS_DUPLICITY_VERSION"
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
<summary>
Gets whether or not this is the CitizenFX server.
</summary>
<returns>A boolean value.</returns>
	]]

native "IS_ENTITY_POSITION_FROZEN"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
A getter for [FREEZE_ENTITY_POSITION](#\_0x428CA6DBD1094446).
</summary>
<param name="entity">The entity to check for</param>
<returns>Boolean stating if it is frozen or not.</returns>
	]]

native "IS_ENTITY_VISIBLE"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
This native checks if the given entity is visible.
</summary>
<returns>Returns `true` if the entity is visible, `false` otherwise.</returns>
	]]

native "IS_FLASH_LIGHT_ON"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped's flash light is on.</returns>
	]]

native "IS_HELI_TAIL_BOOM_BREAKABLE"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This is a getter for [SET_HELI_TAIL_EXPLODE_THROW_DASHBOARD](#\_0x3EC8BF18AA453FE9)
</summary>
<param name="heli">The helicopter to check</param>
<returns>Returns `true` if the helicopter's tail boom can break, `false` if it cannot.</returns>
	]]

native "IS_HELI_TAIL_BOOM_BROKEN"
	arguments {
		Vehicle "heli" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="heli">The helicopter to check</param>
<returns>Returns `true` if the helicopter's tail boom is broken, `false` if it is intact.</returns>
	]]

native "IS_NUI_FOCUS_KEEPING_INPUT"
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Checks if keyboard input is enabled during NUI focus using `SET_NUI_FOCUS_KEEP_INPUT`.
</summary>
<returns>True or false.</returns>
	]]

native "IS_NUI_FOCUSED"
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Returns the current NUI focus state previously set with `SET_NUI_FOCUS`.
</summary>
<returns>True or false.</returns>
	]]

native "IS_PED_A_PLAYER"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
This native checks if the given ped is a player.
</summary>
<returns>Returns `true` if the ped is a player, `false` otherwise.</returns>
	]]

native "IS_PED_COLLECTION_COMPONENT_VARIATION_GEN9_EXCLUSIVE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
An alternative to [IS_PED_COMPONENT_VARIATION_GEN9\_EXCLUSIVE](#\_0xC767B581) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) natives.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<returns>Whether or not the ped component variation is a gen9 exclusive (stub assets).</returns>
	]]

native "IS_PED_COLLECTION_COMPONENT_VARIATION_VALID"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
An alternative to [IS_PED_COMPONENT_VARIATION_VALID](#\_0xE825F6B6CEA7671D) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) natives.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_COLLECTION_TEXTURE_VARIATIONS](#\_0xD2C15D7).</param>
<returns>Returns true if the component variation is valid. This may be useful for randomizing components using loops.</returns>
	]]

native "IS_PED_COMPONENT_VARIATION_GEN9_EXCLUSIVE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<param name="componentId">The component id.</param>
<param name="drawableId">The drawable id.</param>
<returns>Whether or not the ped component variation is a gen9 exclusive (stub assets).</returns>
	]]

native "IS_PED_HANDCUFFED"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped is handcuffed.</returns>
	]]

native "IS_PED_RAGDOLL"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped is ragdolling.</returns>
	]]

native "IS_PED_STRAFING"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped is strafing.</returns>
	]]

native "IS_PED_USING_ACTION_MODE"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="ped">The target ped.</param>
<returns>Whether or not the ped is using action mode.</returns>
	]]

native "IS_PLAYER_ACE_ALLOWED"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		charPtr "object" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "IS_PLAYER_COMMERCE_INFO_LOADED"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Requests whether or not the commerce data for the specified player has loaded.
</summary>
<param name="playerSrc">The player handle</param>
<returns>A boolean.</returns>
	]]

native "IS_PLAYER_COMMERCE_INFO_LOADED_EXT"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Requests whether or not the commerce data for the specified player has loaded from Tebex.
</summary>
<param name="playerSrc">The player handle</param>
<returns>A boolean.</returns>
	]]

native "IS_PLAYER_EVADING_WANTED_LEVEL"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
```
This will return true if the player is evading wanted level, meaning that the wanted level stars are blink.
Otherwise will return false.

If the player is not wanted, it simply returns false.
```
</summary>
<param name="playerSrc">The target player</param>
<returns>boolean value, depending if the player is evading his wanted level or not.</returns>
	]]

native "IS_PLAYER_IN_FREE_CAM_MODE"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "bool"
	doc [[!
<param name="playerSrc">The player to get the free camera mode status of</param>
<returns>Returns if the player is in free camera mode.</returns>
	]]

native "IS_PLAYER_USING_SUPER_JUMP"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<param name="playerSrc">The player handle</param>
<returns>A boolean.</returns>
	]]

native "IS_PRINCIPAL_ACE_ALLOWED"
	arguments {
		charPtr "principal" [=[ {} ]=],
		charPtr "object" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
	]]

native "IS_RAW_KEY_DOWN"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is pressed down on the keyboard.

This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of down state.</returns>
	]]

native "IS_RAW_KEY_PRESSED"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is pressed on the keyboard.

This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of pressed state.</returns>
	]]

native "IS_RAW_KEY_RELEASED"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` was just released on the keyboard.

This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of released state.</returns>
	]]

native "IS_RAW_KEY_UP"
	arguments {
		int "rawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Gets if the specified `rawKeyIndex` is up  on the keyboard.

This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="rawKeyIndex">Index of raw key from keyboard.</param>
<returns>Returns bool value of up state.</returns>
	]]

native "IS_STREAMING_FILE_READY"
	arguments {
		charPtr "registerAs" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Returns whether an asynchronous streaming file registration completed.
</summary>
<param name="registerAs">The file name to check, for example `asset.ydr`.</param>
<returns>Whether or not the streaming file has been registered.</returns>
	]]

native "IS_TRACK_ENABLED"
	arguments {
		int "track" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
Getter for [SET_TRACK_ENABLED](#\_0x4B41E84C)
</summary>
<param name="track">The track id (between 0 - 27)</param>
<returns>If this track is enabled.</returns>
	]]

native "IS_TRACK_SWITCHED_OFF"
	arguments {
		int "track" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
Getter for [SWITCH_TRAIN_TRACK](#\_0xFD813BB7DB977F20). Determines if ambient trains are able to spawn on this track.
</summary>
<param name="track">The track id (between 0 - 27)</param>
<returns>If this track allows ambient trains to spawn</returns>
	]]

native "IS_TRAIN_CABOOSE"
	arguments {
		Vehicle "train" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "bool"
	doc [[!
<param name="train">The train handle</param>
<returns>Returns true if the train is the caboose of the chain.</returns>
	]]

native "IS_VEHICLE_ALARM_SET"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_ENGINE_STARTING"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_EXTRA_TURNED_ON"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "extraId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_INTERIOR_LIGHT_ON"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_NEEDS_TO_BE_HOTWIRED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_PREVIOUSLY_OWNED_BY_PLAYER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_SIREN_ON"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_TYRE_BURST"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelID" [=[ {} ]=],
		BOOL "completely" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_WANTED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "IS_VEHICLE_WHEEL_BROKEN_OFF"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Getter for [BREAK_OFF_VEHICLE_WHEEL](?\_0xA274CADB).
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="wheelIndex">The wheel index.</param>
	]]

native "IS_VEHICLE_WINDOW_INTACT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "windowIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
See the client-side [IS_VEHICLE_WINDOW_INTACT](#\_0x46E571A0E20D01F1) for a window indexes list.
</summary>
<param name="vehicle">The target vehicle.</param>
<param name="windowIndex">The window index.</param>
	]]

native "LEAVE_CURSOR_MODE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Leaves cursor mode. This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
	]]

native "LOAD_PLAYER_COMMERCE_DATA"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Requests the commerce data for the specified player, including the owned SKUs. Use `IS_PLAYER_COMMERCE_INFO_LOADED` to check if it has loaded.
</summary>
<param name="playerSrc">The player handle</param>
	]]

native "LOAD_PLAYER_COMMERCE_DATA_EXT"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Requests the commerce data from Tebex for the specified player, including the owned SKUs.

Use [`IS_PLAYER_COMMERCE_INFO_LOADED_EXT`](#\_0x1D14F4FE) to check if it has loaded.

This will not automatically update whenever a client purchases a package, if you want to fetch new purchases you will need to call this native again.

This native will temporarily cache the players commerce data for 10 seconds, a call to this native after 10 seconds will re-fetch the players commerce data.
</summary>
<param name="playerSrc">The player handle</param>
	]]

native "LOAD_RESOURCE_FILE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "charPtr"
	doc [[!
<summary>
Reads the contents of a text file in a specified resource.
If executed on the client, this file has to be included in `files` in the resource manifest.
Example: `local data = LoadResourceFile("devtools", "data.json")`
</summary>
<param name="resourceName">The resource name.</param>
<param name="fileName">The file in the resource.</param>
<returns>The file contents</returns>
	]]

native "LOAD_WATER_FROM_PATH"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Define the xml in a resources fxmanifest, under the file(s) section.
</summary>
<param name="resourceName">The name of the resource containing your modified water definition</param>
<param name="fileName">The name of the file</param>
<returns>Returns true on success.</returns>
	]]

native "MUMBLE_ADD_VOICE_CHANNEL_LISTEN"
	arguments {
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Starts listening to the specified channel, when available.
</summary>
<param name="channel">A game voice channel ID.</param>
	]]

native "MUMBLE_ADD_VOICE_TARGET_CHANNEL"
	arguments {
		int "targetId" [=[ {} ]=],
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Adds the specified channel to the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="channel">A game voice channel ID.</param>
	]]

native "MUMBLE_ADD_VOICE_TARGET_PLAYER"
	arguments {
		int "targetId" [=[ {} ]=],
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Adds the specified player to the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="player">A game player index.</param>
	]]

native "MUMBLE_ADD_VOICE_TARGET_PLAYER_BY_SERVER_ID"
	arguments {
		int "targetId" [=[ {} ]=],
		int "serverId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Adds the specified player to the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="serverId">The player's server id.</param>
	]]

native "MUMBLE_CLEAR_VOICE_CHANNEL"
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "MUMBLE_CLEAR_VOICE_TARGET"
	arguments {
		int "targetId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Clears the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
	]]

native "MUMBLE_CLEAR_VOICE_TARGET_CHANNELS"
	arguments {
		int "targetId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Clears channels from the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
	]]

native "MUMBLE_CLEAR_VOICE_TARGET_PLAYERS"
	arguments {
		int "targetId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Clears players from the target list for the specified Mumble voice target ID.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
	]]

native "MUMBLE_CREATE_CHANNEL"
	arguments {
		int "id" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Create a permanent voice channel.
</summary>
<param name="id">ID of the channel.</param>
	]]

native "MUMBLE_DOES_CHANNEL_EXIST"
	arguments {
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Check whether specified channel exists on the Mumble server.
</summary>
<param name="channel">A game voice channel ID.</param>
<returns>True if the specific channel exists. False otherwise.</returns>
	]]

native "MUMBLE_GET_TALKER_PROXIMITY"
	ns "CFX"
    apiset "client"
	returns "float"
	doc [[!
<returns>Talker proximity value.</returns>
	]]

native "MUMBLE_GET_VOICE_CHANNEL_FROM_SERVER_ID"
	arguments {
		int "serverId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Returns the mumble voice channel from a player's server id.
</summary>
<param name="serverId">The player's server id.</param>
<returns>Int representing the identifier of the voice channel.</returns>
	]]

native "MUMBLE_IS_ACTIVE"
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<returns>True if the player has enabled voice chat.</returns>
	]]

native "MUMBLE_IS_CONNECTED"
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
This native will return true if the user succesfully connected to the voice server.
If the user disabled the voice-chat setting it will return false.
</summary>
<returns>True if the player is connected to a mumble server.</returns>
	]]

native "MUMBLE_IS_PLAYER_MUTED"
	arguments {
		int "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Checks if the player is currently muted
</summary>
<param name="playerSrc">The player to get the mute state for</param>
<returns>Whether or not the player is muted</returns>
	]]

native "MUMBLE_IS_PLAYER_TALKING"
	arguments {
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<param name="player">The target player.</param>
<returns>Whether or not the player is talking.</returns>
	]]

native "MUMBLE_REMOVE_VOICE_CHANNEL_LISTEN"
	arguments {
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Stops listening to the specified channel.
</summary>
<param name="channel">A game voice channel ID.</param>
	]]

native "MUMBLE_REMOVE_VOICE_TARGET_CHANNEL"
	arguments {
		int "targetId" [=[ {} ]=],
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Removes the specified voice channel from the user's voice targets.

Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_CHANNEL](#\_0x4D386C9E)
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="channel">The game voice channel ID to remove from the target.</param>
	]]

native "MUMBLE_REMOVE_VOICE_TARGET_PLAYER"
	arguments {
		int "targetId" [=[ {} ]=],
		Player "player" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Removes the specified player from the user's voice targets.

Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER](#\_0x32C5355A)
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="player">The player index to remove from the target.</param>
	]]

native "MUMBLE_REMOVE_VOICE_TARGET_PLAYER_BY_SERVER_ID"
	arguments {
		int "targetId" [=[ {} ]=],
		int "serverId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Removes the specified player from the user's voice targets.

Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER_BY_SERVER_ID](#\_0x25F2B65F)
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive).</param>
<param name="serverId">The player's server id to remove from the target.</param>
	]]

native "MUMBLE_SET_ACTIVE"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="state">Voice chat state.</param>
	]]

native "MUMBLE_SET_AUDIO_INPUT_DISTANCE"
	arguments {
		float "distance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the current input distance. The player will be able to talk to other players within this distance.
</summary>
<param name="distance">The input distance.</param>
	]]

native "MUMBLE_SET_AUDIO_INPUT_INTENT"
	arguments {
		Hash "intentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Use this native to disable noise suppression and high pass filters.

The possible intents for this are as follows (backticks are used to represent hashes):

| Index | Description |
|-|-|
| \`speech\` | Default intent |
| \`music\` | Disable noise suppression and high pass filter |
</summary>
<param name="intentHash">The intent hash.</param>
	]]

native "MUMBLE_SET_AUDIO_OUTPUT_DISTANCE"
	arguments {
		float "distance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the current output distance. The player will be able to hear other players talking within this distance.
</summary>
<param name="distance">The output distance.</param>
	]]

native "MUMBLE_SET_PLAYER_MUTED"
	arguments {
		int "playerSrc" [=[ {} ]=],
		bool "toggle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Mutes or unmutes the specified player
</summary>
<param name="playerSrc">The player to set the mute state of</param>
<param name="toggle">Whether to mute or unmute the player</param>
	]]

native "MUMBLE_SET_SERVER_ADDRESS"
	arguments {
		charPtr "address" [=[ {} ]=],
		int "port" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Changes the Mumble server address to connect to, and reconnects to the new address.

Setting the address to an empty string and the port to -1 will reset to the built in FXServer Mumble Implementation.
</summary>
<param name="address">The address of the mumble server.</param>
<param name="port">The port of the mumble server.</param>
	]]

native "MUMBLE_SET_SUBMIX_FOR_SERVER_ID"
	arguments {
		int "serverId" [=[ {} ]=],
		int "submixId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the audio submix ID for a specified player using Mumble 'Native Audio' functionality.
</summary>
<param name="serverId">The player's server ID.</param>
<param name="submixId">The submix ID.</param>
	]]

native "MUMBLE_SET_TALKER_PROXIMITY"
	arguments {
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="value">Proximity value.</param>
	]]

native "MUMBLE_SET_VOICE_CHANNEL"
	arguments {
		int "channel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="channel">A game voice channel ID.</param>
	]]

native "MUMBLE_SET_VOICE_TARGET"
	arguments {
		int "targetId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the current Mumble voice target ID to broadcast voice to.
</summary>
<param name="targetId">A Mumble voice target ID, ranging from 1..30 (inclusive). 0 disables voice targets, and 31 is server loopback.</param>
	]]

native "MUMBLE_SET_VOLUME_OVERRIDE"
	arguments {
		Player "player" [=[ {} ]=],
		float "volume" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Overrides the output volume for a particular player on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.

Set to -1.0 to reset the Volume override.
</summary>
<param name="player">A game player index.</param>
<param name="volume">The volume, ranging from 0.0 to 1.0 (or above).</param>
	]]

native "MUMBLE_SET_VOLUME_OVERRIDE_BY_SERVER_ID"
	arguments {
		int "serverId" [=[ {} ]=],
		float "volume" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Overrides the output volume for a particular player with the specified server id and player name on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.
</summary>
<param name="serverId">The player's server id.</param>
<param name="volume">The volume, ranging from 0.0 to 1.0 (or above).</param>
	]]

native "NETWORK_DOES_ENTITY_EXIST_WITH_NETWORK_ID"
	arguments {
		int "netId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "BOOL"
	doc [[!
<param name="netId">The network id to check</param>
<returns>Returns `true` if both the network id exist, and the network id has a game object.</returns>
	]]

native "NETWORK_GET_ENTITY_FROM_NETWORK_ID"
	arguments {
		int "netId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Entity"
	doc [[!
	]]

native "NETWORK_GET_ENTITY_OWNER"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<summary>
Returns the owner ID of the specified entity.
</summary>
<param name="entity">The entity to get the owner for.</param>
<returns>On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.</returns>
	]]

native "NETWORK_GET_FIRST_ENTITY_OWNER"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
<summary>
Returns the first owner ID of the specified entity.
</summary>
<param name="entity">The entity to get the first owner for.</param>
<returns>The server ID of the first entity owner.</returns>
	]]

native "NETWORK_GET_NETWORK_ID_FROM_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "Vector3"
	doc [[!
<param name="playerSrc">The player handle</param>
	]]

native "ONESYNC_ENABLE_REMOTE_ATTACHMENT_SANITIZATION"
	arguments {
		BOOL "enable" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Toggles a check that prevents attaching (networked) entities to remotely owned peds. This is disabled by default.
</summary>
<param name="enable">Whether to enable sanitization.</param>
	]]

native "OVERRIDE_PEDS_CAN_STAND_ON_TOP_FLAG"
	arguments {
		BOOL "flag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets whether peds can stand on top of *all* vehicles without falling off.

Note this flag is not replicated automatically, you will have to manually do so.
</summary>
<param name="flag">true to override, false to use default game behavior.</param>
	]]

native "OVERRIDE_PEDS_USE_DEFAULT_DRIVE_BY_CLIPSET"
	arguments {
		BOOL "flag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Allows the bypassing of default game behavior that prevents the use of [SET_PED_DRIVE_BY_CLIPSET_OVERRIDE](#\_0xED34AB6C5CB36520) in certain scenarios to avoid clipping issues (e.g., when there is more than one Ped in a vehicle).

Note: This flag and the overridden clipset are not replicated values and require synchronization through user scripts. Additionally, current game behavior also restricts applying this clipset locally when in first-person mode and will require a temporary workaround.
</summary>
<param name="flag">true to override, false to use default game behavior.</param>
	]]

native "OVERRIDE_POP_GROUPS"
	arguments {
		charPtr "path" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Replaces the `popgroups` (CPopGroupList) meta file with the file in the specified path.
</summary>
<param name="path">The file path to load (`popgroups`, `@resource/popgroups`), or `null` to reload the default population groups file.</param>
	]]

native "OVERRIDE_REACTION_TO_VEHICLE_SIREN"
	arguments {
		BOOL "state" [=[ {} ]=],
		int "reaction" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Setting the state to true and a value between 0 and 2 will cause pedestrian vehicles to react accordingly to sirens.

```cpp
enum Reactions {
    Left = 0,
    Right = 1,
    Stop = 2
}
```
</summary>
<param name="state">Toggle on or off</param>
<param name="reaction">Decide how they should react</param>
	]]

native "OVERRIDE_VEHICLE_PEDS_CAN_STAND_ON_TOP_FLAG"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		BOOL "can" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Overrides whether or not peds can stand on top of the specified vehicle.

Note this flag is not replicated automatically, you will have to manually do so.
</summary>
<param name="vehicle">The vehicle.</param>
<param name="can">Can they?</param>
	]]

native "PERFORM_HTTP_REQUEST_INTERNAL"
	arguments {
		charPtr "requestData" [=[ {} ]=],
		int "requestDataLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "PERFORM_HTTP_REQUEST_INTERNAL_EX"
	arguments {
		object "requestData" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "int"
	doc [[!
	]]

native "PREPARE_LIGHT"
	arguments {
		int "lightType" [=[ {} ]=],
		int "flags" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
		float "intensity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Create a new light with specified type, flags, position, color, and intensity.
</summary>
<param name="lightType">The type of the light</param>
<param name="flags">Light flags</param>
<param name="x">X coordinate of the light position</param>
<param name="y">Y coordinate of the light position</param>
<param name="z">Z coordinate of the light position</param>
<param name="r">Red component of the light color (0-255)</param>
<param name="g">Green component of the light color (0-255)</param>
<param name="b">Blue component of the light color (0-255)</param>
<param name="intensity">Intensity of the light</param>
	]]

native "PRINT_STRUCTURED_TRACE"
	arguments {
		charPtr "jsonString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Prints 'structured trace' data to the server `file descriptor 3` channel. This is not generally useful outside of
server monitoring utilities.
</summary>
<param name="jsonString">JSON data to submit as `payload` in the `script_structured_trace` event.</param>
	]]

native "PROFILER_ENTER_SCOPE"
	arguments {
		charPtr "scopeName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Scope entry for profiler.
</summary>
<param name="scopeName">Scope name.</param>
	]]

native "PROFILER_EXIT_SCOPE"
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Scope exit for profiler.
</summary>
	]]

native "PROFILER_IS_RECORDING"
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
<summary>
Returns true if the profiler is active.
</summary>
<returns>True or false.</returns>
	]]

native "REGISTER_ARCHETYPES"
	arguments {
		func "factory" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a set of archetypes with the game engine. These should match `CBaseArchetypeDef` class information from the game.
</summary>
<param name="factory">A function returning a list of archetypes.</param>
	]]

native "REGISTER_COMMAND"
	arguments {
		charPtr "commandName" [=[ {} ]=],
		func "handler" [=[ {} ]=],
		BOOL "restricted" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.

Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).

The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).

**Example result**:

![](https://i.imgur.com/TaCnG09.png)
</summary>
<param name="commandName">The command you want to register.</param>
<param name="handler">A handler function that gets called whenever the command is executed.</param>
<param name="restricted">If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.</param>
	]]

native "REGISTER_CONSOLE_LISTENER"
	arguments {
		func "listener" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Registers a listener for console output messages.
</summary>
<param name="listener">A function of `(channel: string, message: string) => void`. The message might contain `\n`.</param>
	]]

native "REGISTER_FONT_FILE"
	arguments {
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Registers a specified .gfx file as GFx font library.
The .gfx file has to be registered with the streamer already.
</summary>
<param name="fileName">The name of the .gfx file, without extension.</param>
	]]

native "REGISTER_FONT_ID"
	arguments {
		charPtr "fontName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Registers a specified font name for use with text draw commands.
</summary>
<param name="fontName">The name of the font in the GFx font library.</param>
<returns>An index to use with [SET_TEXT_FONT](#\_0x66E0276CC5F6B9DA) and similar natives.</returns>
	]]

native "REGISTER_KEY_MAPPING"
	arguments {
		charPtr "commandString" [=[ {} ]=],
		charPtr "description" [=[ {} ]=],
		charPtr "defaultMapper" [=[ {} ]=],
		charPtr "defaultParameter" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Registers a key mapping for the current resource.

See the related [cookbook post](https://cookbook.fivem.net/2020/01/06/using-the-new-console-key-bindings/) for more information.

Below you can find some examples on how to create these keybindings as well as the alternate keybinding syntax, which is preceded by `~!` to indicate that it's an alternate key.
</summary>
<param name="commandString">The command to execute, and the identifier of the binding.</param>
<param name="description">A description for in the settings menu.</param>
<param name="defaultMapper">The [mapper ID](https://docs.fivem.net/docs/game-references/input-mapper-parameter-ids/) to use for the default binding, e.g. `keyboard`.</param>
<param name="defaultParameter">The [IO parameter ID](https://docs.fivem.net/docs/game-references/input-mapper-parameter-ids/) to use for the default binding, e.g. `f3`.</param>
	]]

native "REGISTER_NUI_CALLBACK"
	arguments {
		charPtr "callbackType" [=[ {} ]=],
		func "callback" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "REGISTER_NUI_CALLBACK_TYPE"
	arguments {
		charPtr "callbackType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "REGISTER_RAW_KEYMAP"
	arguments {
		charPtr "keymapName" [=[ {} ]=],
		func "onKeyDown" [=[ {} ]=],
		func "onKeyUp" [=[ {} ]=],
		int "rawKeyIndex" [=[ {} ]=],
		BOOL "canBeDisabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Registers a keymap that will be triggered whenever `rawKeyIndex` is pressed or released.

`onKeyUp` and `onKeyDown` will not provide any arguments.

```ts
function onStateChange();
```
</summary>
<param name="keymapName">A **unique** name that the keymap will be bound to, duplicates will result in the keymap not being registered.</param>
<param name="onKeyDown">The function to run when the key is being pressed, or `nil`.</param>
<param name="onKeyUp">The function to run when the key is no longer being pressed, or `nil`.</param>
<param name="rawKeyIndex">The virtual key to bind this keymap to, see a list [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)</param>
<param name="canBeDisabled">If calls to [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014) will disable this keymap, if a keymap was disabled when the key was pressed down it will still call `onKeyUp` on release.</param>
	]]

native "REGISTER_RAW_NUI_CALLBACK"
	arguments {
		charPtr "callbackType" [=[ {} ]=],
		func "callback" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "REGISTER_RESOURCE_AS_EVENT_HANDLER"
	arguments {
		charPtr "eventName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
</summary>
<param name="eventName">An event name, or "\*" to disable HLL event filtering for this resource.</param>
	]]

native "REGISTER_RESOURCE_ASSET"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "charPtr"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a cached resource asset with the resource system, similar to the automatic scanning of the `stream/` folder.
</summary>
<param name="resourceName">The resource to add the asset to.</param>
<param name="fileName">A file name in the resource.</param>
<returns>A cache string to pass to `REGISTER_STREAMING_FILE_FROM_CACHE` on the client.</returns>
	]]

native "REGISTER_RESOURCE_BUILD_TASK_FACTORY"
	arguments {
		charPtr "factoryId" [=[ {} ]=],
		func "factoryFn" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Registers a build task factory for resources.
The function should return an object (msgpack map) with the following fields:

```
{
// returns whether the specific resource should be built
shouldBuild = func(resourceName: string): bool,

// asynchronously start building the specific resource.
// call cb when completed
build = func(resourceName: string, cb: func(success: bool, status: string): void): void
}
```
</summary>
<param name="factoryId">The identifier for the build task.</param>
<param name="factoryFn">The factory function.</param>
	]]

native "REGISTER_ROPE_DATA"
	arguments {
		int "numSections" [=[ {} ]=],
		float "radius" [=[ {} ]=],
		charPtr "diffuseTextureName" [=[ {} ]=],
		charPtr "normalMapName" [=[ {} ]=],
		float "distanceMappingScale" [=[ {} ]=],
		float "uvScaleX" [=[ {} ]=],
		float "uvScaleY" [=[ {} ]=],
		float "specularFresnel" [=[ {} ]=],
		float "specularFalloff" [=[ {} ]=],
		float "specularIntensity" [=[ {} ]=],
		float "bumpiness" [=[ {} ]=],
		int "color" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Registers a custom rope data with the game. For guidance on what these values should be use common:/data/ropedata.xml as a reference.
Returns a rope type which can be passed into [ADD_ROPE](#\_0xE832D760399EB220) to use a custom rope design.
Once a rope data is registered it can be used indefinitely and you should take caution not too register too many as to exceed the games limit.
</summary>
<returns>Returns a non-negative value on success, or -1 if the rope data could not be registered or an invalid argument is passed.</returns>
	]]

native "REGISTER_STREAMING_FILE_FROM_CACHE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
		charPtr "cacheString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a dynamic streaming asset from the server with the GTA streaming module system.
</summary>
<param name="resourceName">The resource to add the asset to.</param>
<param name="fileName">A file name in the resource.</param>
<param name="cacheString">The string returned from `REGISTER_RESOURCE_ASSET` on the server.</param>
	]]

native "REGISTER_STREAMING_FILE_FROM_KVS"
	arguments {
		charPtr "kvsKey" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a KVP value as an asset with the GTA streaming module system. This function currently won't work.
</summary>
<param name="kvsKey">The KVP key in the current resource to register as an asset.</param>
	]]

native "REGISTER_STREAMING_FILE_FROM_URL"
	arguments {
		charPtr "registerAs" [=[ {} ]=],
		charPtr "url" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Registers a file from an URL as a streaming asset in the GTA streaming subsystem. This will asynchronously register the asset, and caching is done based on the URL itself - cache headers are ignored.

Use `IS_STREAMING_FILE_READY` to check if the asset has been registered successfully.
</summary>
<param name="registerAs">The file name to register as, for example `asset.ydr`.</param>
<param name="url">The URL to fetch the asset from.</param>
	]]

native "REGISTER_TRACK_JUNCTION"
	arguments {
		int "trackIndex" [=[ {} ]=],
		int "trackNode" [=[ {} ]=],
		int "newIndex" [=[ {} ]=],
		int "newNode" [=[ {} ]=],
		bool "direction" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "int"
	doc [[!
<summary>
Registers a track junction that when enabled will cause a train on the defined trackIndex, node and direction to change its current track index and begin traveling on the new node
</summary>
<param name="trackIndex">The track index a train should be on</param>
<param name="trackNode">The node a train should be on</param>
<param name="newIndex">The new track index for a train to be placed on</param>
<param name="newNode">The new track node for a train to be placed on</param>
<param name="direction">The direction a train should be traveling for this junction</param>
<returns>The track junction's handle or -1 if invalid.</returns>
	]]

native "REMAP_RAW_KEYMAP"
	arguments {
		charPtr "keymapName" [=[ {} ]=],
		int "newRawKeyIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Remaps the keymap bound to `keymapName` to `newRawKeyIndex`

Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
</summary>
<param name="keymapName">the name given to the keymap in [REGISTER_RAW_KEYMAP](#\_0x49C1F6DC)</param>
<param name="newRawKeyIndex">Index of raw key from keyboard.</param>
	]]

native "REMOVE_ALL_PED_WEAPONS"
	arguments {
		Ped "ped" [=[ {} ]=],
		BOOL "p1" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Parameter `p1` does not seem to be used or referenced in game binaries.\
**Note:** When called for networked entities, a `CRemoveAllWeaponsEvent` will be created per request.

**This is the server-side RPC native equivalent of the client native [REMOVE_ALL_PED_WEAPONS](?\_0xF25DF915FA38C5F3).**
</summary>
<param name="ped">The ped entity</param>
	]]

native "REMOVE_BLIP"
	arguments {
		BlipPtr "blip" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Removes the blip from your map.
**Note:** This function only works on the script that created the blip, if you wish to remove blips created by other scripts, see [`SET_THIS_SCRIPT_CAN_REMOVE_BLIPS_CREATED_BY_ANY_SCRIPT`](#\_0x86A652570E5F25DD).

**This is the server-side RPC native equivalent of the client native [REMOVE_BLIP](?\_0x86A652570E5F25DD).**
</summary>
<param name="blip">Blip handle to remove.</param>
	]]

native "REMOVE_CONVAR_CHANGE_LISTENER"
	arguments {
		int "cookie" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<param name="cookie">The cookie returned from [ADD_CONVAR_CHANGE_LISTENER](#\_0xAB7F7241)</param>
	]]

native "REMOVE_DRY_VOLUME"
	arguments {
		int "handle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Removes a dry volume from the game session.
See CREATE_DRY_VOLUME for more info
</summary>
<param name="handle">The handle of the dry volume that needs to be removed.</param>
	]]

native "REMOVE_HEALTH_CONFIG"
	arguments {
		charPtr "configName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Removes health config.
</summary>
<param name="configName">Removes config name. Cannot be default game health config name.</param>
	]]

native "REMOVE_REPLACE_TEXTURE"
	arguments {
		charPtr "origTxd" [=[ {} ]=],
		charPtr "origTxn" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Experimental natives, please do not use in a live environment.
</summary>
	]]

native "REMOVE_STATE_BAG_CHANGE_HANDLER"
	arguments {
		int "cookie" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
**Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.

Removes a handler for changes to a state bag.
</summary>
<param name="cookie">The cookie.</param>
	]]

native "REMOVE_TEXTURE"
	arguments {
		int "textureId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Removes the specified texture and remove it from the ped.
Unlike `0x6BEFAA907B076859` which only marks the texture as "can be reused" (and keeps it until will be reused), this function deletes it right away. Can fix some sync issues. `DOES_TEXTURE_EXIST` can be use to wait until fully unloaded by game

```lua
RemoveTexture(textureId)
while DoesTextureExist(textureId) do 
    Wait(0)
end
```
</summary>
<param name="textureId">texture id created by `0xC5E7204F322E49EB`.</param>
	]]

native "REMOVE_TIMECYCLE_MODIFIER"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="modifierName">The timecycle modifier name.</param>
	]]

native "REMOVE_TIMECYCLE_MODIFIER_VAR"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
		charPtr "varName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="modifierName">The name of timecycle modifier.</param>
<param name="varName">The name of timecycle variable.</param>
	]]

native "REMOVE_TRACK_JUNCTION"
	arguments {
		int "junctionIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
Removes the specified track junction.
</summary>
<param name="junctionIndex">The junctions index</param>
<returns>Returns if it succeeds in removing a junction or not</returns>
	]]

native "REMOVE_WEAPON_COMPONENT_FROM_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
		Hash "componentHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
REMOVE_WEAPON_COMPONENT_FROM_PED

**This is the server-side RPC native equivalent of the client native [REMOVE_WEAPON_COMPONENT_FROM_PED](?\_0x1E8BE90C74FB4C09).**
</summary>
	]]

native "REMOVE_WEAPON_FROM_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
This native removes a specified weapon from your selected ped.
Weapon Hashes: pastebin.com/0wwDZgkF
Example:
C#:
Function.Call(Hash.REMOVE_WEAPON_FROM_PED, Game.Player.Character, 0x99B507EA);
C++:
WEAPON::REMOVE_WEAPON_FROM_PED(PLAYER::PLAYER_PED_ID(), 0x99B507EA);
The code above removes the knife from the player.
```

**This is the server-side RPC native equivalent of the client native [REMOVE_WEAPON_FROM_PED](?\_0x4899CB088EDF59B8).**
</summary>
	]]

native "REQUEST_PLAYER_COMMERCE_SESSION"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "skuId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Requests the specified player to buy the passed SKU. This'll pop up a prompt on the client, which upon acceptance
will open the browser prompting further purchase details.
</summary>
<param name="playerSrc">The player handle</param>
<param name="skuId">The ID of the SKU.</param>
	]]

native "REQUEST_RESOURCE_FILE_SET"
	arguments {
		charPtr "setName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Requests a resource file set with the specified name to be downloaded and mounted on top of the current resource.

Resource file sets are specified in `fxmanifest.lua` with the following syntax:

```lua
file_set 'addon_ui' {
    'ui/addon/index.html',
    'ui/addon/**.js',
}
```

This command will trigger a script error if the request failed.
</summary>
<param name="setName">The name of the file set as specified in `fxmanifest.lua`.</param>
<returns>`TRUE` if the set is mounted, `FALSE` if the request is still pending.</returns>
	]]

native "RESET_ENTITY_DRAW_OUTLINE_RENDER_TECHNIQUE"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
This function undoes changes made by [`SET_ENTITY_DRAW_OUTLINE_RENDER_TECHNIQUE`](#\_0x68DFF2DD), restoring the original outline rendering behavior. The default render technique group is `unlit`.
</summary>
	]]

native "RESET_FLY_THROUGH_WINDSCREEN_PARAMS"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Resets parameters which is used by the game for checking is ped needs to fly through windscreen after a crash to default values.
</summary>
	]]

native "RESET_MAP_ZOOM_DATA_LEVEL"
	arguments {
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Resets values from the zoom level data by index to defaults from mapzoomdata.meta.
</summary>
<param name="index">Zoom level index.</param>
	]]

native "RESET_MAPDATA_ENTITY_MATRIX"
	arguments {
		int "mapDataHash" [=[ {} ]=],
		int "entityInternalIdx" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Resets mapdata entity transform matrix to its original state.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="mapDataHash">A mapdata hash from `mapDataLoaded` event.</param>
<param name="entityInternalIdx">An internal entity's index.</param>
<returns>True if successful, false if not.</returns>
	]]

native "RESET_PED_MODEL_PERSONALITY"
	arguments {
		Hash "modelHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Restores an overridden ped model personality type to the default value.
</summary>
<param name="modelHash">Ped's model.</param>
	]]

native "RESET_VEHICLE_PEDS_CAN_STAND_ON_TOP_FLAG"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Resets whether or not peds can stand on top of the specified vehicle.

Note this flag is not replicated automatically, you will have to manually do so.
</summary>
<param name="vehicle">The vehicle.</param>
	]]

native "RESET_WATER"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Resets the water to the games default water.xml.
</summary>
	]]

native "SAVE_RESOURCE_FILE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
		charPtr "data" [=[ {} ]=],
		int "dataLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
<summary>
Writes the specified data to a file in the specified resource.
Using a length of `-1` will automatically detect the length assuming the data is a C string.
</summary>
<param name="resourceName">The name of the resource.</param>
<param name="fileName">The name of the file.</param>
<param name="data">The data to write to the file.</param>
<param name="dataLength">The length of the written data.</param>
<returns>A value indicating if the write succeeded.</returns>
	]]

native "SCAN_RESOURCE_ROOT"
	arguments {
		charPtr "rootPath" [=[ {} ]=],
		func "callback" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Scans the resources in the specified resource root. This function is only available in the 'monitor mode' process and is
not available for user resources.
</summary>
<param name="rootPath">The resource directory to scan.</param>
<param name="callback">A callback that will receive an object with results.</param>
	]]

native "SCHEDULE_RESOURCE_TICK"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Schedules the specified resource to run a tick as soon as possible, bypassing the server's fixed tick rate.
</summary>
<param name="resourceName">The resource to tick.</param>
	]]

native "SELECT_ENTITY_AT_CURSOR"
	arguments {
		int "hitFlags" [=[ {} ]=],
		BOOL "precise" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Entity"
	doc [[!
<summary>
Gets the selected entity at the current mouse cursor position, and changes the current selection depth. This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="hitFlags">A bit mask of entity types to match.</param>
<param name="precise">Whether to do a *precise* test, i.e. of visual coordinates, too.</param>
<returns>An entity handle, or zero.</returns>
	]]

native "SELECT_ENTITY_AT_POS"
	arguments {
		float "fracX" [=[ {} ]=],
		float "fracY" [=[ {} ]=],
		int "hitFlags" [=[ {} ]=],
		BOOL "precise" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "Entity"
	doc [[!
<summary>
Gets the selected entity at the specified mouse cursor position, and changes the current selection depth. This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="fracX">Mouse cursor X fraction.</param>
<param name="fracY">Mouse cursor Y fraction.</param>
<param name="hitFlags">A bit mask of entity types to match.</param>
<param name="precise">Whether to do a *precise* test, i.e. of visual coordinates, too.</param>
<returns>An entity handle, or zero.</returns>
	]]

native "SEND_DUI_MESSAGE"
	arguments {
		long "duiObject" [=[ {} ]=],
		charPtr "jsonString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sends a message to the specific DUI root page. This is similar to SEND_NUI_MESSAGE.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="jsonString">The message, encoded as JSON.</param>
	]]

native "SEND_DUI_MOUSE_DOWN"
	arguments {
		long "duiObject" [=[ {} ]=],
		charPtr "button" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Injects a 'mouse down' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="button">Either `'left'`, `'middle'` or `'right'`.</param>
	]]

native "SEND_DUI_MOUSE_MOVE"
	arguments {
		long "duiObject" [=[ {} ]=],
		int "x" [=[ {} ]=],
		int "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Injects a 'mouse move' event for a DUI object. Coordinates are in browser space.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="x">The mouse X position.</param>
<param name="y">The mouse Y position.</param>
	]]

native "SEND_DUI_MOUSE_UP"
	arguments {
		long "duiObject" [=[ {} ]=],
		charPtr "button" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Injects a 'mouse up' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="button">Either `'left'`, `'middle'` or `'right'`.</param>
	]]

native "SEND_DUI_MOUSE_WHEEL"
	arguments {
		long "duiObject" [=[ {} ]=],
		int "deltaY" [=[ {} ]=],
		int "deltaX" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Injects a 'mouse wheel' event for a DUI object.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="deltaY">The wheel Y delta.</param>
<param name="deltaX">The wheel X delta.</param>
	]]

native "SEND_LOADING_SCREEN_MESSAGE"
	arguments {
		charPtr "jsonString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<summary>
Sends a message to the `loadingScreen` NUI frame, which contains the HTML page referenced in `loadscreen` resources.
</summary>
<param name="jsonString">The JSON-encoded message.</param>
<returns>A success value.</returns>
	]]

native "SEND_NUI_MESSAGE"
	arguments {
		charPtr "jsonString" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "SET_AIM_COOLDOWN"
	arguments {
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adds a cooldown between instances of moving and then aiming.
Can be optionally used to hinder 'speedboosting'
To turn off, set value to 0
</summary>
<param name="value">Number of milliseconds between allowed aiming</param>
	]]

native "SET_AUDIO_SUBMIX_EFFECT_PARAM_FLOAT"
	arguments {
		int "submixId" [=[ {} ]=],
		int "effectSlot" [=[ {} ]=],
		int "paramIndex" [=[ {} ]=],
		float "paramValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets a floating-point parameter for a submix effect.
</summary>
<param name="submixId">The submix.</param>
<param name="effectSlot">The effect slot for the submix. It is expected that the effect is set in this slot beforehand.</param>
<param name="paramIndex">The parameter index for the effect.</param>
<param name="paramValue">The parameter value to set.</param>
	]]

native "SET_AUDIO_SUBMIX_EFFECT_PARAM_INT"
	arguments {
		int "submixId" [=[ {} ]=],
		int "effectSlot" [=[ {} ]=],
		int "paramIndex" [=[ {} ]=],
		int "paramValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets an integer parameter for a submix effect.
</summary>
<param name="submixId">The submix.</param>
<param name="effectSlot">The effect slot for the submix. It is expected that the effect is set in this slot beforehand.</param>
<param name="paramIndex">The parameter index for the effect.</param>
<param name="paramValue">The parameter value to set.</param>
	]]

native "SET_AUDIO_SUBMIX_EFFECT_RADIO_FX"
	arguments {
		int "submixId" [=[ {} ]=],
		int "effectSlot" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Assigns a RadioFX effect to a submix effect slot.

The parameter values for this effect are as follows (backticks are used to represent hashes):

| Index | Type | Description |
|-|-|-|
| \`enabled\` | int | Enables or disables RadioFX on this DSP. |
| \`default\` | int | Sets default parameters for the RadioFX DSP and enables it. |
| \`freq_low\` | float |  |
| \`freq_hi\` | float |  |
| \`fudge\` | float |  |
| \`rm_mod_freq\` | float |  |
| \`rm_mix\` | float |  |
| \`o_freq_lo\` | float |  |
| \`o_freq_hi\` | float |  |
</summary>
<param name="submixId">The submix.</param>
<param name="effectSlot">The effect slot for the submix.</param>
	]]

native "SET_AUDIO_SUBMIX_OUTPUT_VOLUMES"
	arguments {
		int "submixId" [=[ {} ]=],
		int "outputSlot" [=[ {} ]=],
		float "frontLeftVolume" [=[ {} ]=],
		float "frontRightVolume" [=[ {} ]=],
		float "rearLeftVolume" [=[ {} ]=],
		float "rearRightVolume" [=[ {} ]=],
		float "channel5Volume" [=[ {} ]=],
		float "channel6Volume" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the volumes for the sound channels in a submix effect.
Values can be between 0.0 and 1.0.
Channel 5 and channel 6 are not used in voice chat but are believed to be center and LFE channels.
Output slot starts at 0 for the first ADD_AUDIO_SUBMIX_OUTPUT call then incremented by 1 on each subsequent call.
</summary>
<param name="submixId">The submix.</param>
<param name="outputSlot">The output slot index.</param>
<param name="frontLeftVolume">The volume for the front left channel.</param>
<param name="frontRightVolume">The volume for the front right channel.</param>
<param name="rearLeftVolume">The volume for the rear left channel.</param>
<param name="rearRightVolume">The volume for the rear right channel.</param>
<param name="channel5Volume">The volume for channel 5.</param>
<param name="channel6Volume">The volume for channel 6.</param>
	]]

native "SET_BACKFACECULLING"
	arguments {
		BOOL "toggle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "SET_BLIP_SPRITE"
	arguments {
		Blip "blip" [=[ {} ]=],
		int "spriteId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the displayed sprite for a specific blip.
There's a [list of sprites](https://docs.fivem.net/game-references/blips/) on the FiveM documentation site.

**This is the server-side RPC native equivalent of the client native [SET_BLIP_SPRITE](?\_0xDF735600A4696DAF).**
</summary>
<param name="blip">The blip to change.</param>
<param name="spriteId">The sprite ID to set.</param>
	]]

native "SET_CALMING_QUAD_BOUNDS"
	arguments {
		int "waterQuad" [=[ {} ]=],
		int "minX" [=[ {} ]=],
		int "minY" [=[ {} ]=],
		int "maxX" [=[ {} ]=],
		int "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The calming quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success.</returns>
	]]

native "SET_CALMING_QUAD_DAMPENING"
	arguments {
		int "calmingQuad" [=[ {} ]=],
		float "dampening" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="calmingQuad">The calming quad</param>
<param name="dampening">The dampening value</param>
<returns>Returns true on success.</returns>
	]]

native "SET_CONVAR"
	arguments {
		charPtr "varName" [=[ {} ]=],
		charPtr "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "SET_CONVAR_REPLICATED"
	arguments {
		charPtr "varName" [=[ {} ]=],
		charPtr "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Used to replicate a server variable onto clients.
</summary>
<param name="varName">The console variable name.</param>
<param name="value">The value to set for the given console variable.</param>
	]]

native "SET_CONVAR_SERVER_INFO"
	arguments {
		charPtr "varName" [=[ {} ]=],
		charPtr "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "SET_CURRENT_PED_WEAPON"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
		BOOL "bForceInHand" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_CURRENT_PED_WEAPON

**This is the server-side RPC native equivalent of the client native [SET_CURRENT_PED_WEAPON](?\_0xADF692B254977C0C).**
</summary>
	]]

native "SET_CURSOR_LOCATION"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "BOOL"
	doc [[!
<param name="x">X position.</param>
<param name="y">Y position.</param>
<returns>A boolean.</returns>
	]]

native "SET_DEFAULT_VEHICLE_NUMBER_PLATE_TEXT_PATTERN"
	arguments {
		int "plateIndex" [=[ {} ]=],
		charPtr "pattern" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the default number plate text pattern for vehicles seen on the local client with the specified plate index as their *default* index (`plateProbabilities` from carvariations).

For consistency, this should be used with the same value on all clients, since vehicles *without* custom text will use a seeded random number generator with this pattern to determine the default plate text.

The default value is `11AAA111`, and using this or a NULL string will revert to the default game RNG.

### Pattern string format

*   `1` will lead to a random number from 0-9.
*   `A` will lead to a random letter from A-Z.
*   `.` will lead to a random letter *or* number, with 50% probability of being either.
*   `^1` will lead to a literal `1` being emitted.
*   `^A` will lead to a literal `A` being emitted.
*   Any other character will lead to said character being emitted.
*   A string shorter than 8 characters will be padded on the right.
</summary>
<param name="plateIndex">A plate index, or `-1` to set a default for any indices that do not have a specific value.</param>
<param name="pattern">A number plate pattern string, or a null value to reset to default.</param>
	]]

native "SET_DISCORD_APP_ID"
	arguments {
		charPtr "appId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native sets the app id for the discord rich presence implementation.
</summary>
<param name="appId">A valid Discord API App Id, can be generated at https://discordapp.com/developers/applications/</param>
	]]

native "SET_DISCORD_RICH_PRESENCE_ACTION"
	arguments {
		int "index" [=[ {} ]=],
		charPtr "label" [=[ {} ]=],
		charPtr "url" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets a clickable button to be displayed in a player's Discord rich presence.
</summary>
<param name="index">The button index, either 0 or 1.</param>
<param name="label">The text to display on the button.</param>
<param name="url">The URL to open when clicking the button. This has to start with `fivem://connect/` or `https://`.</param>
	]]

native "SET_DISCORD_RICH_PRESENCE_ASSET"
	arguments {
		charPtr "assetName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native sets the image asset for the discord rich presence implementation.
</summary>
<param name="assetName">The name of a valid asset registered on Discordapp's developer dashboard. note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.</param>
	]]

native "SET_DISCORD_RICH_PRESENCE_ASSET_SMALL"
	arguments {
		charPtr "assetName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native sets the small image asset for the discord rich presence implementation.
</summary>
<param name="assetName">The name of a valid asset registered on Discordapp's developer dashboard. Note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.</param>
	]]

native "SET_DISCORD_RICH_PRESENCE_ASSET_SMALL_TEXT"
	arguments {
		charPtr "text" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native sets the hover text of the small image asset for the discord rich presence implementation.
</summary>
<param name="text">Text to be displayed when hovering over small image asset. Note that you must also set a valid small image asset using the SET_DISCORD_RICH_PRESENCE_ASSET_SMALL native.</param>
	]]

native "SET_DISCORD_RICH_PRESENCE_ASSET_TEXT"
	arguments {
		charPtr "text" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
This native sets the hover text of the image asset for the discord rich presence implementation.
</summary>
<param name="text">Text to be displayed when hovering over image asset. Note that you must also set a valid image asset using the SET_DISCORD_RICH_PRESENCE_ASSET native.</param>
	]]

native "SET_DRAW_ORIGIN"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		BOOL "is2d" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Sets the on-screen drawing origin for draw-functions in world coordinates.

The effect can be reset by calling [`CLEAR_DRAW_ORIGIN`](#\_0xDD76B263) and is limited to 32 different origins each frame.
</summary>
	]]

native "SET_DUI_URL"
	arguments {
		long "duiObject" [=[ {} ]=],
		charPtr "url" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Navigates the specified DUI browser to a different URL.
</summary>
<param name="duiObject">The DUI browser handle.</param>
<param name="url">The new URL.</param>
	]]

native "SET_EMITTER_PROBE_LENGTH"
	arguments {
		float "probeLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Allows StaticEmitter's without a linked entity to make use of environment features like occlusion and reverb even if they are located higher than 20.0 units above any static collision inside interiors.

This native allows you to extend the probe range up to 150.0 units.
</summary>
<param name="probeLength">The desired probe length (20.0 - 150.0)</param>
	]]

native "SET_ENTITY_COORDS"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "xPos" [=[ {} ]=],
		float "yPos" [=[ {} ]=],
		float "zPos" [=[ {} ]=],
		BOOL "alive" [=[ {} ]=],
		BOOL "deadFlag" [=[ {} ]=],
		BOOL "ragdollFlag" [=[ {} ]=],
		BOOL "clearArea" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the coordinates (world position) for a specified entity, offset by the radius of the entity on the Z axis.

**This is the server-side RPC native equivalent of the client native [SET_ENTITY_COORDS](?\_0x06843DA7060A026B).**
</summary>
<param name="entity">The entity to change coordinates for.</param>
<param name="xPos">The X coordinate.</param>
<param name="yPos">The Y coordinate.</param>
<param name="zPos">The Z coordinate, ground level.</param>
<param name="alive">Unused by the game, potentially used by debug builds of GTA in order to assert whether or not an entity was alive.</param>
<param name="deadFlag">Whether to disable physics for dead peds, too, and not just living peds.</param>
<param name="ragdollFlag">A special flag used for ragdolling peds.</param>
<param name="clearArea">Whether to clear any entities in the target area.</param>
	]]

native "SET_ENTITY_DISTANCE_CULLING_RADIUS"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "radius" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
It overrides the default distance culling radius of an entity. Set to `0.0` to reset.
If you want to interact with an entity outside of your players' scopes set the radius to a huge number.

**WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
</summary>
<param name="entity">The entity handle to override the distance culling radius.</param>
<param name="radius">The new distance culling radius.</param>
	]]

native "SET_ENTITY_DRAW_OUTLINE"
	arguments {
		Entity "entity" [=[ {} ]=],
		BOOL "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Draws an outline around a given entity. This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="entity">A valid entity handle.</param>
<param name="enabled">Whether or not to draw an outline.</param>
	]]

native "SET_ENTITY_DRAW_OUTLINE_COLOR"
	arguments {
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
		int "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets color for entity outline. `255, 0, 255, 255` by default.
</summary>
<param name="red">Red component of color.</param>
<param name="green">Green component of color.</param>
<param name="blue">Blue component of color.</param>
<param name="alpha">Alpha component of color, ignored for shader `0`.</param>
	]]

native "SET_ENTITY_DRAW_OUTLINE_RENDER_TECHNIQUE"
	arguments {
		charPtr "techniqueGroup" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the render technique for drawing an entity's outline. This function allows you to specify a technique group name to control how the entity's outline is rendered in the game.

List of known technique group's:

```
alt0
alt1
alt2
alt3
alt4
alt5
alt6
alt7
alt8
blit
cube
default
geometry
imposter
imposterdeferred
lightweight0
lightweight0CutOut
lightweight0CutOutTint
lightweight0WaterRefractionAlpha
lightweight4
lightweight4CutOut
lightweight4CutOutTint
lightweight4WaterRefractionAlpha
lightweight8
lightweight8CutOut
lightweight8CutOutTint
lightweight8WaterRefractionAlpha
lightweightHighQuality0
lightweightHighQuality0CutOut
lightweightHighQuality0WaterRefractionAlpha
lightweightHighQuality4
lightweightHighQuality4CutOut
lightweightHighQuality4WaterRefractionAlpha
lightweightHighQuality8
lightweightHighQuality8CutOut
lightweightHighQuality8WaterRefractionAlpha
lightweightNoCapsule4
lightweightNoCapsule8
multilight
tessellate
ui
unlit
waterreflection
waterreflectionalphaclip
waterreflectionalphacliptint
wdcascade
```
</summary>
<param name="techniqueGroup">Technique group name.</param>
	]]

native "SET_ENTITY_DRAW_OUTLINE_SHADER"
	arguments {
		int "shader" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets variant of shader that will be used to draw entity outline.

Variants are:

*   **0**: Default value, gauss shader.
*   **1**: 2px wide solid color outline.
*   **2**: Fullscreen solid color except for entity.
</summary>
<param name="shader">An outline shader variant.</param>
	]]

native "SET_ENTITY_HEADING"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "heading" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Set the heading of an entity in degrees also known as "Yaw".

**This is the server-side RPC native equivalent of the client native [SET_ENTITY_HEADING](?\_0x8E2530AA8ADA980E).**
</summary>
<param name="entity">The entity to set the heading for.</param>
<param name="heading">The heading in degrees.</param>
	]]

native "SET_ENTITY_IGNORE_REQUEST_CONTROL_FILTER"
	arguments {
		Entity "entity" [=[ {} ]=],
		bool "ignore" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
It allows to flag an entity to ignore the request control filter policy.
</summary>
<param name="entity">The entity handle to ignore the request control filter.</param>
<param name="ignore">Define if the entity ignores the request control filter policy.</param>
	]]

native "SET_ENTITY_MATRIX"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "forwardX" [=[ {} ]=],
		float "forwardY" [=[ {} ]=],
		float "forwardZ" [=[ {} ]=],
		float "rightX" [=[ {} ]=],
		float "rightY" [=[ {} ]=],
		float "rightZ" [=[ {} ]=],
		float "upX" [=[ {} ]=],
		float "upY" [=[ {} ]=],
		float "upZ" [=[ {} ]=],
		float "atX" [=[ {} ]=],
		float "atY" [=[ {} ]=],
		float "atZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets an entity's matrix. Arguments are in the same order as with GET_ENTITY_MATRIX.
</summary>
<param name="entity">A valid entity handle.</param>
	]]

native "SET_ENTITY_ORPHAN_MODE"
	arguments {
		Entity "entity" [=[ {} ]=],
		int "orphanMode" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```cpp
enum EntityOrphanMode {
    // Default, this will delete the entity when it isn't relevant to any players
    // NOTE: this *doesn't* mean when they're no longer in scope
    DeleteWhenNotRelevant = 0,
    // The entity will be deleted whenever its original owner disconnects
    // NOTE: if this is set when the entities original owner has already left it will be
    // marked for deletion (similar to just calling DELETE_ENTITY)
    DeleteOnOwnerDisconnect = 1,
    // The entity will never be deleted by the server when it does relevancy checks
    // you should only use this on entities that need to be relatively persistent
    KeepEntity = 2
}
```

Sets what the server will do when the entity no longer has its original owner. By default the server will cleanup entities that it considers "no longer relevant".

When used on trains, this native will recursively call onto all attached carriages.

**NOTE**: When used with `KeepEntity` (2) this native only guarantees that the ***server*** will not delete the entity, client requests to delete the entity will still work perfectly fine.
</summary>
<param name="entity">The entity to set the orphan mode of</param>
<param name="orphanMode">The mode that the server should use for determining if an entity should be removed.</param>
	]]

native "SET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED"
	arguments {
		Entity "entity" [=[ {} ]=],
		bool "allow" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Enables or disables the owner check for the specified entity in network-synchronized scenes. When set to `false`, the entity cannot participate in synced scenes initiated by clients that do not own the entity.

By default, this is `false` for all entities, meaning only the entity's owner can include it in networked synchronized scenes.
</summary>
<param name="entity">The entity to set the flag for.</param>
<param name="allow">Whether to allow remote synced scenes for the entity.</param>
	]]

native "SET_ENTITY_ROTATION"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "pitch" [=[ {} ]=],
		float "roll" [=[ {} ]=],
		float "yaw" [=[ {} ]=],
		int "rotationOrder" [=[ {} ]=],
		BOOL "bDeadCheck" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the rotation of a specified entity in the game world.

```
NativeDB Introduced: v323
```

**This is the server-side RPC native equivalent of the client native [SET_ENTITY_ROTATION](?\_0x8524A8B0171D5E07).**
</summary>
<param name="entity">The entity to rotate.</param>
<param name="pitch">The pitch (X-axis) rotation in degrees.</param>
<param name="roll">The roll (Y-axis) rotation in degrees.</param>
<param name="yaw">The yaw (Z-axis) rotation in degrees.</param>
<param name="rotationOrder">Specifies the order in which yaw, pitch, and roll are applied, see [`GET_ENTITY_ROTATION`](#\_0xAFBD61CC738D9EB9) for the available rotation orders.</param>
<param name="bDeadCheck">Usually set to `true`. Determines whether to check if the entity is dead before applying the rotation.</param>
	]]

native "SET_ENTITY_ROUTING_BUCKET"
	arguments {
		Entity "entity" [=[ {} ]=],
		int "bucket" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the routing bucket for the specified entity.

Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
</summary>
<param name="entity">The entity to set the routing bucket for.</param>
<param name="bucket">The bucket ID.</param>
	]]

native "SET_ENTITY_VELOCITY"
	arguments {
		Entity "entity" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
```

**This is the server-side RPC native equivalent of the client native [SET_ENTITY_VELOCITY](?\_0x1C99BB7B6E96D16F).**
</summary>
	]]

native "SET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER"
	arguments {
		float "multiplier" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for [GET_FALL_DAMAGE_LAND_ON_FOOT_MULTIPLIER](#\_0x3C8A1C92).
</summary>
<param name="multiplier">fall damage multiplier to apply when a ped lands on foot from a fall below the kill fall height threshold (i.e., when the fall does not cause instant death). Default value is `3.0`.</param>
	]]

native "SET_FALL_DAMAGE_MULTIPLIER"
	arguments {
		float "multiplier" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for [GET_FALL_DAMAGE_MULTIPLIER](#\_0x2D6A0A83).
</summary>
<param name="multiplier">fall damage multiplier applied to all peds when calculating fall damage from falls below the kill fall height threshold (i.e., when the fall does not cause instant death). Default value is `7.0`.</param>
	]]

native "SET_FLASH_LIGHT_KEEP_ON_WHILE_MOVING"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Allows Weapon-Flashlight beams to stay visible while moving. Normally it only stays on while aiming.
</summary>
<param name="state">On/Off</param>
	]]

native "SET_FLY_THROUGH_WINDSCREEN_PARAMS"
	arguments {
		float "vehMinSpeed" [=[ {} ]=],
		float "unkMinSpeed" [=[ {} ]=],
		float "unkModifier" [=[ {} ]=],
		float "minDamage" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Sets some in-game parameters which is used for checks is ped needs to fly through windscreen after a crash.
</summary>
<param name="vehMinSpeed">Vehicle minimum speed (default 35.0).</param>
<param name="unkMinSpeed">Unknown minimum speed (default 40.0).</param>
<param name="unkModifier">Unknown modifier (default 17.0).</param>
<param name="minDamage">Minimum damage (default 2000.0).</param>
<returns>A bool indicating if parameters was set successfully.</returns>
	]]

native "SET_FOG_VOLUME_RENDER_DISABLED"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
This completely disables rendering of fog volumes (vfxfogvolumeinfo.ymt).
</summary>
<param name="state">Toggle on or off.</param>
	]]

native "SET_FUEL_CONSUMPTION_RATE_MULTIPLIER"
	arguments {
		float "multiplier" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets fuel consumption rate multiplier for all vehicles operated by a player. This is a way to slow down or speed up fuel consumption for all vehicles at a time. If 0 - it practically means that fuel will not be consumed. By default is set to 1.

When the multiplier is set to 1 a default 65 litre gas tank car with average fuel consumption can stay idle for ~16.67 hours or run with max RPM for ~2.5 hours.

To customize fuel consumption per vehicle / vehicle class use [`SET_HANDLING_FLOAT`](#\_0x90DD01C)/[`SET_VEHICLE_HANDLING_FLOAT`](#\_0x488C86D2) natives with `fieldName` equal to `fPetrolConsumptionRate`. By default it is set to 0.5 for all vehicles.
</summary>
<param name="multiplier">Global fuel consumption multiplier. If negative - 0 will be used instead.</param>
	]]

native "SET_FUEL_CONSUMPTION_STATE"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Turns on and off fuel consumption in all vehicles operated by a player. NPC operated vehicles will not consume fuel to avoid traffic disruptions.

The default Gta5 behaviour is fuel consumption turned off.
</summary>
<param name="state">True to turn on. False to turn off.</param>
	]]

native "SET_GAME_TYPE"
	arguments {
		charPtr "gametypeName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "SET_GLOBAL_PASSENGER_MASS_MULTIPLIER"
	arguments {
		float "massMul" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="massMul">Weight of each passenger (not counting the driver) as a percentage of vehicle mass. Is applied to all vehicles. Default value is 0.05</param>
	]]

native "SET_HANDLING_FIELD"
	arguments {
		charPtr "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		Any "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a global handling override for a specific vehicle class. The name is supposed to match the `handlingName` field from handling.meta.
Example: `SetHandlingField('AIRTUG', 'CHandlingData', 'fSteeringLock', 360.0)`
</summary>
<param name="vehicle">The vehicle class to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The value to set.</param>
	]]

native "SET_HANDLING_FLOAT"
	arguments {
		charPtr "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a global handling override for a specific vehicle class. The name is supposed to match the `handlingName` field from handling.meta.
Example: `SetHandlingFloat('AIRTUG', 'CHandlingData', 'fSteeringLock', 360.0)`
</summary>
<param name="vehicle">The vehicle class to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The floating-point value to set.</param>
	]]

native "SET_HANDLING_INT"
	arguments {
		charPtr "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a global handling override for a specific vehicle class. The name is supposed to match the `handlingName` field from handling.meta.
</summary>
<param name="vehicle">The vehicle class to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The integer value to set.</param>
	]]

native "SET_HANDLING_VECTOR"
	arguments {
		charPtr "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		Vector3 "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a global handling override for a specific vehicle class. The name is supposed to match the `handlingName` field from handling.meta.
Example: `SetHandlingVector('AIRTUG', 'CHandlingData', 'vecCentreOfMassOffset', vector3(0.0, 0.0, -5.0))`
</summary>
<param name="vehicle">The vehicle class to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The Vector3 value to set.</param>
	]]

native "SET_HEALTH_CONFIG_DEFAULT_ARMOR"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default armor value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_DEFAULT_ENDURANCE"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default endurance value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_DEFAULT_HEALTH"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default health value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_DOG_TAKEDOWN_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default dog takedown threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_DYING_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default dying health threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_FATIGUED_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default fatigued health threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_HURT_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default hurt health threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_INJURED_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default injured health threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_INVINCIBLE"
	arguments {
		charPtr "configName" [=[ {} ]=],
		BOOL "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default invincible value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_MELEE_FATAL_ATTACK"
	arguments {
		charPtr "configName" [=[ {} ]=],
		BOOL "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default melee cardinal fatal attack value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HEALTH_CONFIG_WRITHE_FROM_BULLET_THRESHOLD"
	arguments {
		charPtr "configName" [=[ {} ]=],
		float "newValue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets default writhe from bullet threshold value for specific health config.
</summary>
<param name="configName">Name of health config.</param>
<param name="newValue">Value</param>
	]]

native "SET_HTTP_HANDLER"
	arguments {
		func "handler" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the handler for HTTP requests made to the executing resource.

Example request URL: `http://localhost:30120/http-test/ping` - this request will be sent to the `http-test` resource with the `/ping` path.

The handler function assumes the following signature:

```ts
function HttpHandler(
  request: {
    address: string;
    headers: Record<string, string>;
    method: string;
    path: string;
    setDataHandler(handler: (data: string) => void): void;
    setDataHandler(handler: (data: ArrayBuffer) => void, binary: 'binary'): void;
    setCancelHandler(handler: () => void): void;
  },
  response: {
    writeHead(code: number, headers?: Record<string, string | string[]>): void;
    write(data: string): void;
    send(data?: string): void;
  }
): void;
```

*   **request**: The request object.
    *   **address**: The IP address of the request sender.
    *   **path**: The path to where the request was sent.
    *   **headers**: The headers sent with the request.
    *   **method**: The request method.
    *   **setDataHandler**: Sets the handler for when a data body is passed with the request. Additionally you can pass the `'binary'` argument to receive a `BufferArray` in JavaScript or `System.Byte[]` in C# (has no effect in Lua).
    *   **setCancelHandler**: Sets the handler for when the request is cancelled.
*   **response**: An object to control the response.
    *   **writeHead**: Sets the status code & headers of the response. Can be only called once and won't work if called after running other response functions.
    *   **write**: Writes to the response body without sending it. Can be called multiple times.
    *   **send**: Writes to the response body and then sends it along with the status code & headers, finishing the request.
</summary>
<param name="handler">The handler function.</param>
	]]

native "SET_HUD_COMPONENT_ALIGN"
	arguments {
		int "id" [=[ {} ]=],
		int "horizontalAlign" [=[ {} ]=],
		int "verticalAlign" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
See [SET_SCRIPT_GFX_ALIGN](#\_0xB8A850F20A067EB6) for details about how gfx align works.
</summary>
<param name="id">The hud component id.</param>
<param name="horizontalAlign">The horizontal alignment.</param>
<param name="verticalAlign">The vertical alignment.</param>
	]]

native "SET_HUD_COMPONENT_SIZE"
	arguments {
		int "id" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="id">The hud component id.</param>
<param name="x">New size X.</param>
<param name="y">New size Y.</param>
	]]

native "SET_IGNORE_VEHICLE_OWNERSHIP_FOR_STOWING"
	arguments {
		BOOL "ignore" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Sets whether or not ownership checks should be performed while trying to stow a carriable on a hunting wagon.
</summary>
<param name="ignore">true to let the local player stow carriables on any hunting wagon, false to use the default behaviour.</param>
	]]

native "SET_INTERIOR_PORTAL_CORNER_POSITION"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "cornerIndex" [=[ {} ]=],
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "posZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="cornerIndex">Interior corner index.</param>
	]]

native "SET_INTERIOR_PORTAL_ENTITY_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "entityIndex" [=[ {} ]=],
		int "flag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="entityIndex">Portal entity index.</param>
<param name="flag">New flag value.</param>
	]]

native "SET_INTERIOR_PORTAL_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "flag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="flag">New flag value.</param>
	]]

native "SET_INTERIOR_PORTAL_ROOM_FROM"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "roomFrom" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="roomFrom">New value.</param>
	]]

native "SET_INTERIOR_PORTAL_ROOM_TO"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "portalIndex" [=[ {} ]=],
		int "roomTo" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="portalIndex">Interior portal index.</param>
<param name="roomTo">New value.</param>
	]]

native "SET_INTERIOR_PROBE_LENGTH"
	arguments {
		float "probeLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Overwrite the games default CPortalTracker interior detection range.
This fixes potentially unwanted behaviour in the base game and allows you to build custom interiors with larger ceiling heights without running into graphical glitches.

By default CPortalTracker will probe 4 units downward trying to reach collisions that are part of the interior the entity is in.
If no collision can be found 16 units are used in some circumstances.

There are 30+ hard coded special cases, only some of them exposed via script (for example `ENABLE_STADIUM_PROBES_THIS_FRAME`).

This native allows you to extend the probe range up to 150 units which is the same value the game uses for the `xs_arena_interior`
</summary>
<param name="probeLength">The desired probe length (0.0 - 150.0)</param>
	]]

native "SET_INTERIOR_ROOM_EXTENTS"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
		float "bbMinX" [=[ {} ]=],
		float "bbMinY" [=[ {} ]=],
		float "bbMinZ" [=[ {} ]=],
		float "bbMaxX" [=[ {} ]=],
		float "bbMaxY" [=[ {} ]=],
		float "bbMaxZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
	]]

native "SET_INTERIOR_ROOM_FLAG"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
		int "flag" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<param name="flag">New flag value.</param>
	]]

native "SET_INTERIOR_ROOM_TIMECYCLE"
	arguments {
		int "interiorId" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
		int "timecycleHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="interiorId">The target interior.</param>
<param name="roomIndex">Interior room index.</param>
<param name="timecycleHash">Timecycle hash.</param>
	]]

native "SET_KEY_MAPPING_HIDE_RESOURCES"
	arguments {
		bool "hide" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Toggles the visibility of resource names in the FiveM key mapping page.
</summary>
<param name="hide">`true` will disable the display of resource names, and `false` will enable it.</param>
	]]

native "SET_KILL_FALL_HEIGHT"
	arguments {
		float "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for [GET_KILL_FALL_HEIGHT](#\_0x884C8B5A).
</summary>
<param name="height">height from which the non-player peds will instantly die from fall damage. Default value is `10.0`.</param>
	]]

native "SET_LIGHT_ALPHA"
	arguments {
		float "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the alpha transparency of the light.
</summary>
<param name="alpha">The alpha transparency value (0.0 to 1.0)</param>
	]]

native "SET_LIGHT_AO"
	arguments {
		float "intensity" [=[ {} ]=],
		float "radius" [=[ {} ]=],
		float "bias" [=[ {} ]=],
		float "intensity2" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set ambient occlusion (AO) parameters for a specified light.
</summary>
<param name="intensity">The AO intensity</param>
<param name="radius">The AO radius</param>
<param name="bias">The AO bias</param>
<param name="intensity2">Secondary AO intensity</param>
	]]

native "SET_LIGHT_CAPSULE_SIZE"
	arguments {
		float "size" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the capsule size of a specified light.
</summary>
<param name="size">The capsule size value</param>
	]]

native "SET_LIGHT_CLIP_RECT"
	arguments {
		int "x" [=[ {} ]=],
		int "y" [=[ {} ]=],
		int "width" [=[ {} ]=],
		int "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the clip rectangle for a created light.
</summary>
<param name="x">The x-coordinate of the clip rectangle</param>
<param name="y">The y-coordinate of the clip rectangle</param>
<param name="width">The width of the clip rectangle</param>
<param name="height">The height of the clip rectangle</param>
	]]

native "SET_LIGHT_COLOR"
	arguments {
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the color of a specified light.
</summary>
<param name="r">Red color component (0-255)</param>
<param name="g">Green color component (0-255)</param>
<param name="b">Blue color component (0-255)</param>
	]]

native "SET_LIGHT_CONE"
	arguments {
		float "innerConeAngle" [=[ {} ]=],
		float "outerConeAngle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the inner and outer cone angles of a specified light.
</summary>
<param name="innerConeAngle">The inner cone angle in degrees</param>
<param name="outerConeAngle">The outer cone angle in degrees</param>
	]]

native "SET_LIGHT_COORDS"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the world coordinates of a specified light.
</summary>
<param name="x">The X coordinate</param>
<param name="y">The Y coordinate</param>
<param name="z">The Z coordinate</param>
	]]

native "SET_LIGHT_DIRECTION"
	arguments {
		float "xDir" [=[ {} ]=],
		float "yDir" [=[ {} ]=],
		float "zDir" [=[ {} ]=],
		float "xTanDir" [=[ {} ]=],
		float "yTanDir" [=[ {} ]=],
		float "zTanDir" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the forward and tangent direction vectors for an existing light, allowing control over its orientation (useful for spotlights and directional lights).
</summary>
<param name="xDir">, **yDir**, **zDir**: Components of the normalized forward (direction) vector</param>
<param name="xTanDir">, **yTanDir**, **zTanDir**: Components of the normalized tangent vector (defines rotation around the forward axis)</param>
	]]

native "SET_LIGHT_EXTRAFLAGS"
	arguments {
		int "extraFlags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set additional configuration flags for an existing light
</summary>
<param name="extraFlags">Bitmask of extra flags</param>
	]]

native "SET_LIGHT_FADE_DISTANCE"
	arguments {
		int "fadeDistance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the fade distance.
</summary>
<param name="fadeDistance">Maximum distance</param>
	]]

native "SET_LIGHT_FALLOFF"
	arguments {
		float "falloff" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adjust the falloff parameter for an existing light, affecting how light intensity decreases over distance.
</summary>
<param name="falloff">A floating‑point value determining the rate at which light intensity diminishes with distance (must be > 0; values ≤ 0 will be clamped internally)</param>
	]]

native "SET_LIGHT_FLAGS"
	arguments {
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set or update specific flags for a created light to control its behavior or properties.
</summary>
<param name="flags">Bitmask of flags to apply to the light</param>
	]]

native "SET_LIGHT_HEADLIGHT"
	arguments {
		float "intensity" [=[ {} ]=],
		float "range" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the headlight properties of a created light, adjusting its intensity and range.
</summary>
<param name="intensity">The intensity level of the headlight</param>
<param name="range">The effective range of the headlight</param>
	]]

native "SET_LIGHT_INTENSITY"
	arguments {
		float "intensity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the intensity of an existing light.
</summary>
<param name="intensity">The intensity value to set</param>
	]]

native "SET_LIGHT_INTERIOR"
	arguments {
		int "interiorId" [=[ {} ]=],
		bool "isPortal" [=[ {} ]=],
		int "roomIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the interior and room where the light should be active.
</summary>
<param name="interiorId">The ID of the interior where the light should be active</param>
<param name="isPortal">Attach to a portal or room</param>
<param name="roomIndex">The specific room</param>
	]]

native "SET_LIGHT_PLANE"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "w" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the plane parameters for a light.
</summary>
<param name="x">X component of the plane</param>
<param name="y">Y component of the plane</param>
<param name="z">Z component of the plane</param>
<param name="w">W component of the plane</param>
	]]

native "SET_LIGHT_RADIUS"
	arguments {
		float "radius" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the radius of a created light.
</summary>
<param name="radius">The radius value to set</param>
	]]

native "SET_LIGHT_SHADOW_DETAILS"
	arguments {
		int "shadowFlags" [=[ {} ]=],
		float "shadowDistance" [=[ {} ]=],
		float "shadowFade" [=[ {} ]=],
		float "shadowDepthBiasScale" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the shadow details for a created light.
</summary>
<param name="shadowFlags">Flags controlling shadow behavior</param>
<param name="shadowDistance">The distance at which shadows are rendered</param>
<param name="shadowFade">The fade distance for shadows</param>
<param name="shadowDepthBiasScale">The depth bias scale</param>
	]]

native "SET_LIGHT_SHADOW_FADE_DISTANCE"
	arguments {
		int "fadeDistance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the fade distance for the shadows of a created light.
</summary>
<param name="fadeDistance">The distance at which the shadow fades</param>
	]]

native "SET_LIGHT_SPECULAR_FADE_DISTANCE"
	arguments {
		int "fadeDistance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the specular fade distance for a created light.
</summary>
<param name="fadeDistance">The distance at which specular highlights fade</param>
	]]

native "SET_LIGHT_TEXTURE"
	arguments {
		charPtr "textureDict" [=[ {} ]=],
		int "textureHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Assign a texture to an existing light source, allowing custom light shapes or patterns using textures from streaming assets.
</summary>
<param name="textureDict">The name of the texture dictionary (TXD) containing the texture</param>
<param name="textureHash">Hash of the texture</param>
	]]

native "SET_LIGHT_TYPE"
	arguments {
		int "lightType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Change the light type of a already created light.
Certain light type needs more configurations to work properly (Like direction, flags or size)
</summary>
<param name="lightType">The type of light</param>
	]]

native "SET_LIGHT_VOLUME_DETAILS"
	arguments {
		float "volIntensity" [=[ {} ]=],
		float "volSizeScale" [=[ {} ]=],
		float "r" [=[ {} ]=],
		float "g" [=[ {} ]=],
		float "b" [=[ {} ]=],
		float "i" [=[ {} ]=],
		float "outerExponent" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set volumetric light properties for an existing light, enabling custom volumetric effects such as fog-like glow.
</summary>
<param name="volIntensity">Intensity of the volumetric effect</param>
<param name="volSizeScale">Scale of the volumetric volume</param>
<param name="r">Red channel for volumetric outer color (0–255)</param>
<param name="g">Green channel for volumetric outer color (0–255)</param>
<param name="b">Blue channel for volumetric outer color (0–255)</param>
<param name="i">Intensity (alpha) of the volumetric outer color</param>
<param name="outerExponent">Exponent controlling falloff of the volumetric outer glow</param>
	]]

native "SET_LIGHT_VOLUMETRIC_FADE_DISTANCE"
	arguments {
		int "volumetricFadeDistance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the fade distance for volumetric lightingn.
</summary>
<param name="volumetricFadeDistance">The maximum distance</param>
	]]

native "SET_MANUAL_SHUTDOWN_LOADING_SCREEN_NUI"
	arguments {
		BOOL "manualShutdown" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets whether or not `SHUTDOWN_LOADING_SCREEN` automatically shuts down the NUI frame for the loading screen. If this is enabled,
you will have to manually invoke `SHUTDOWN_LOADING_SCREEN_NUI` whenever you want to hide the NUI loading screen.
</summary>
<param name="manualShutdown">TRUE to manually shut down the loading screen NUI.</param>
	]]

native "SET_MAP_NAME"
	arguments {
		charPtr "mapName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "SET_MAP_ZOOM_DATA_LEVEL"
	arguments {
		int "index" [=[ {} ]=],
		float "zoomScale" [=[ {} ]=],
		float "zoomSpeed" [=[ {} ]=],
		float "scrollSpeed" [=[ {} ]=],
		float "tilesX" [=[ {} ]=],
		float "tilesY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets values to the zoom level data by index.
</summary>
<param name="index">Zoom level index.</param>
<param name="zoomScale">fZoomScale value.</param>
<param name="zoomSpeed">fZoomSpeed value.</param>
<param name="scrollSpeed">fScrollSpeed value.</param>
<param name="tilesX">vTiles X.</param>
<param name="tilesY">vTiles Y.</param>
	]]

native "SET_MILLISECONDS_PER_GAME_MINUTE"
	arguments {
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Overrides how many real ms are equal to one game minute.
A setter for [`GetMillisecondsPerGameMinute`](#\_0x2F8B4D1C595B11DB).
</summary>
<param name="value">Milliseconds.</param>
	]]

native "SET_MINIMAP_CLIP_TYPE"
	arguments {
		int "type" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the type for the minimap blip clipping object to be either rectangular or rounded.
</summary>
<param name="type">0 for rectangular, 1 for rounded.</param>
	]]

native "SET_MINIMAP_COMPONENT_POSITION"
	arguments {
		charPtr "name" [=[ {} ]=],
		charPtr "alignX" [=[ {} ]=],
		charPtr "alignY" [=[ {} ]=],
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "sizeX" [=[ {} ]=],
		float "sizeY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Overrides the minimap component data (from `common:/data/ui/frontend.xml`) for a specified component.
</summary>
<param name="name">The name of the minimap component to override.</param>
<param name="alignX">Equivalent to the `alignX` field in `frontend.xml`.</param>
<param name="alignY">Equivalent to the `alignY` field in `frontend.xml`.</param>
<param name="posX">Equivalent to the `posX` field in `frontend.xml`.</param>
<param name="posY">Equivalent to the `posY` field in `frontend.xml`.</param>
<param name="sizeX">Equivalent to the `sizeX` field in `frontend.xml`.</param>
<param name="sizeY">Equivalent to the `sizeY` field in `frontend.xml`.</param>
	]]

native "SET_MINIMAP_OVERLAY_DISPLAY"
	arguments {
		int "miniMap" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "xScale" [=[ {} ]=],
		float "yScale" [=[ {} ]=],
		float "alpha" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the display info for a minimap overlay.
</summary>
<param name="miniMap">The minimap overlay ID.</param>
<param name="x">The X position for the overlay. This is equivalent to a game coordinate X.</param>
<param name="y">The Y position for the overlay. This is equivalent to a game coordinate Y, except that it's inverted (gfxY = -gameY).</param>
<param name="xScale">The X scale for the overlay. This is equivalent to the Flash \_xscale property, therefore 100 = 100%.</param>
<param name="yScale">The Y scale for the overlay. This is equivalent to the Flash \_yscale property.</param>
<param name="alpha">The alpha value for the overlay. This is equivalent to the Flash \_alpha property, therefore 100 = 100%.</param>
	]]

native "SET_MINIMAP_TYPE"
	arguments {
		int "type" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Possible Types:

```
0 = Off,
1 = Regular,
2 = Expanded,
3 = Simple,
```
</summary>
<param name="type">Type to set the minimap to.</param>
	]]

native "SET_MODEL_HEADLIGHT_CONFIGURATION"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		float "ratePerSecond" [=[ {} ]=],
		float "headlightRotation" [=[ {} ]=],
		BOOL "invertRotation" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
**This native is deprecated and does nothing!**
</summary>
	]]

native "SET_MP_GAMER_TAGS_USE_VEHICLE_BEHAVIOR"
	arguments {
		bool "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets whether all tags should group (normal game behavior) or should remain independent and above each ped's respective head when in a vehicle.
</summary>
<param name="enabled">Whether tags should use normal game behavior. Default is true.</param>
	]]

native "SET_MP_GAMER_TAGS_VISIBLE_DISTANCE"
	arguments {
		float "distance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the maximum distance at which all tags will be visible and which beyond will not be displayed. Distance is measured from the camera position.
</summary>
<param name="distance">The visible distance. Default is 100.0f.</param>
	]]

native "SET_NETWORK_WALK_MODE"
	arguments {
		bool "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "ny"
	returns "void"
	doc [[!
	]]

native "SET_NUI_FOCUS"
	arguments {
		BOOL "hasFocus" [=[ {} ]=],
		BOOL "hasCursor" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "SET_NUI_FOCUS_KEEP_INPUT"
	arguments {
		BOOL "keepInput" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "SET_NUI_ZINDEX"
	arguments {
		int "zIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Set the z-index of the NUI resource.
</summary>
<param name="zIndex">New z-index value.</param>
	]]

native "SET_PED_AMMO"
	arguments {
		Ped "ped" [=[ {} ]=],
		Hash "weaponHash" [=[ {} ]=],
		int "ammo" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
NativeDB Added Parameter 4: BOOL p3
```

**This is the server-side RPC native equivalent of the client native [SET_PED_AMMO](?\_0x14E56BC5B5DB6A19).**
</summary>
	]]

native "SET_PED_ARMOUR"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "amount" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Sets the armor of the specified ped.
ped: The Ped to set the armor of.
amount: A value between 0 and 100 indicating the value to set the Ped's armor to.
```

**This is the server-side RPC native equivalent of the client native [SET_PED_ARMOUR](?\_0xCEA04D83135264CC).**
</summary>
	]]

native "SET_PED_CAN_RAGDOLL"
	arguments {
		Ped "ped" [=[ {} ]=],
		BOOL "toggle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_PED_CAN_RAGDOLL

**This is the server-side RPC native equivalent of the client native [SET_PED_CAN_RAGDOLL](?\_0xB128377056A54E2A).**
</summary>
	]]

native "SET_PED_COLLECTION_COMPONENT_VARIATION"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
		int "paletteId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
An alternative to [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) natives.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_COLLECTION_TEXTURE_VARIATIONS](#\_0xD2C15D7).</param>
<param name="paletteId">0 to 3.</param>
	]]

native "SET_PED_COLLECTION_PRELOAD_PROP_DATA"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
An alternative to [SET_PED_PRELOAD_PROP_DATA](#\_0x2B16A3BFF1FBCE49) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_PROP](#\_0x8ED0C17) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_PROP](#\_0xFBDB885F) natives.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="propIndex">Local prop index inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_DRAWABLE_VARIATIONS](#\_0x3B6A13E1).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_TEXTURE_VARIATIONS](#\_0x75CAF9CC).</param>
	]]

native "SET_PED_COLLECTION_PRELOAD_VARIATION_DATA"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
An alternative to [SET_PED_PRELOAD_VARIATION_DATA](#\_0x39D55A620FCB6A3A) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_DRAWABLE](#\_0xD6BBA48B) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_DRAWABLE](#\_0x94EB1FE4) natives.
</summary>
<param name="ped">The target ped</param>
<param name="componentId">One of the components from [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="drawableId">Local drawable Id inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_DRAWABLE_VARIATIONS](#\_0x310D0271).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_COLLECTION_TEXTURE_VARIATIONS](#\_0xD2C15D7).</param>
	]]

native "SET_PED_COLLECTION_PROP_INDEX"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "anchorPoint" [=[ {} ]=],
		charPtr "collection" [=[ {} ]=],
		int "propIndex" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
		BOOL "attach" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
An alternative to [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F) that uses local collection indexing instead of the global one.

The local / collection relative indexing is useful because the global index may get shifted after Title Update. While local index will remain the same which simplifies migration to the newer game version.

Collection name and local index inside the collection can be obtained from the global index using [GET_PED_COLLECTION_NAME_FROM_PROP](#\_0x8ED0C17) and [GET_PED_COLLECTION_LOCAL_INDEX_FROM_PROP](#\_0xFBDB885F) natives.
</summary>
<param name="ped">The target ped</param>
<param name="anchorPoint">One of the anchor points from [SET_PED_PROP_INDEX](#\_0x93376B65A266EB5F)</param>
<param name="collection">Name of the collection. Empty string for the base game collection. See [GET_PED_COLLECTION_NAME](#\_0xFED5D83A) in order to list all available collections.</param>
<param name="propIndex">Local prop index inside the given collection. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_DRAWABLE_VARIATIONS](#\_0x3B6A13E1).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_COLLECTION_PROP_TEXTURE_VARIATIONS](#\_0x75CAF9CC).</param>
<param name="attach">Attached or not.</param>
	]]

native "SET_PED_COMPONENT_VARIATION"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
		int "paletteId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
This native is used to set component variation on a ped. Components, drawables and textures IDs are related to the ped model.

### MP Freemode list of components

**0**: Face
**1**: Mask
**2**: Hair
**3**: Torso
**4**: Leg
**5**: Parachute / bag
**6**: Shoes
**7**: Accessory
**8**: Undershirt
**9**: Kevlar
**10**: Badge
**11**: Torso 2
List of Component IDs

```cpp
// Components
enum ePedVarComp
{
PV_COMP_INVALID = 0xFFFFFFFF,
PV_COMP_HEAD = 0, // "HEAD"
PV_COMP_BERD = 1, // "BEARD"
PV_COMP_HAIR = 2, // "HAIR"
PV_COMP_UPPR = 3, // "UPPER"
PV_COMP_LOWR = 4, // "LOWER"
PV_COMP_HAND = 5, // "HAND"
PV_COMP_FEET = 6, // "FEET"
PV_COMP_TEEF = 7, // "TEETH"
PV_COMP_ACCS = 8, // "ACCESSORIES"
PV_COMP_TASK = 9, // "TASK"
PV_COMP_DECL = 10, // "DECL"
PV_COMP_JBIB = 11, // "JBIB"
PV_COMP_MAX = 12,
};
```

**This is the server-side RPC native equivalent of the client native [SET_PED_COMPONENT_VARIATION](?\_0x262B14F48D29DE80).**
</summary>
<param name="ped">The ped handle.</param>
<param name="componentId">The component that you want to set.</param>
<param name="drawableId">The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_DRAWABLE_VARIATIONS](#\_0x27561561732A7842).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_TEXTURE_VARIATIONS](#\_0x8F7156A3142A6BAD).</param>
<param name="paletteId">0 to 3.</param>
	]]

native "SET_PED_CONFIG_FLAG"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "flagId" [=[ {} ]=],
		BOOL "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```cpp
// Potential names and hash collisions included as comments
enum ePedConfigFlags {
CPED_CONFIG_FLAG_CreatedByFactory = 0,
CPED_CONFIG_FLAG_CanBeShotInVehicle = 1,
CPED_CONFIG_FLAG_NoCriticalHits = 2,
CPED_CONFIG_FLAG_DrownsInWater = 3,
CPED_CONFIG_FLAG_DrownsInSinkingVehicle = 4,
CPED_CONFIG_FLAG_DiesInstantlyWhenSwimming = 5,
CPED_CONFIG_FLAG_HasBulletProofVest = 6,
CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
CPED_CONFIG_FLAG_NeverFallOffSkis = 8,
CPED_CONFIG_FLAG_NeverEverTargetThisPed = 9,
CPED_CONFIG_FLAG_ThisPedIsATargetPriority = 10,
CPED_CONFIG_FLAG_TargettableWithNoLos = 11,
CPED_CONFIG_FLAG_DoesntListenToPlayerGroupCommands = 12,
CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
CPED_CONFIG_FLAG_DoesntDropWeaponsWhenDead = 14,
CPED_CONFIG_FLAG_SetDelayedWeaponAsCurrent = 15,
CPED_CONFIG_FLAG_KeepTasksAfterCleanUp = 16,
CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
CPED_CONFIG_FLAG_HasAScriptBrain = 18,
CPED_CONFIG_FLAG_WaitingForScriptBrainToLoad = 19,
CPED_CONFIG_FLAG_AllowMedicsToReviveMe = 20,
CPED_CONFIG_FLAG_MoneyHasBeenGivenByScript = 21,
CPED_CONFIG_FLAG_NotAllowedToCrouch = 22,
CPED_CONFIG_FLAG_DeathPickupsPersist = 23,
CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
CPED_CONFIG_FLAG_ForceDieIfInjured = 25,
CPED_CONFIG_FLAG_DontDragMeOutCar = 26,
CPED_CONFIG_FLAG_StayInCarOnJack = 27,
CPED_CONFIG_FLAG_ForceDieInCar = 28,
CPED_CONFIG_FLAG_GetOutUndriveableVehicle = 29,
CPED_CONFIG_FLAG_WillRemainOnBoatAfterMissionEnds = 30,
CPED_CONFIG_FLAG_DontStoreAsPersistent = 31,
CPED_CONFIG_FLAG_WillFlyThroughWindscreen = 32,
CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
CPED_CONFIG_FLAG_HasHelmet = 34,
CPED_CONFIG_FLAG_UseHelmet = 35,
CPED_CONFIG_FLAG_DontTakeOffHelmet = 36,
CPED_CONFIG_FLAG_HideInCutscene = 37,
CPED_CONFIG_FLAG_PedIsEnemyToPlayer = 38,
CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
CPED_CONFIG_FLAG_PedGeneratesDeadBodyEvents = 40,
CPED_CONFIG_FLAG_DontAttackPlayerWithoutWantedLevel = 41,
CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
CPED_CONFIG_FLAG_AllowLockonToFriendlyPlayers = 45,
_0xDB115BFA = 46,
CPED_CONFIG_FLAG_PedBeingDeleted = 47,
CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
CPED_CONFIG_FLAG_BlockGroupPedAimedAtResponse = 49,
CPED_CONFIG_FLAG_WillFollowLeaderAnyMeans = 50,
CPED_CONFIG_FLAG_BlippedByScript = 51,
CPED_CONFIG_FLAG_DrawRadarVisualField = 52,
CPED_CONFIG_FLAG_StopWeaponFiringOnImpact = 53,
CPED_CONFIG_FLAG_DissableAutoFallOffTests = 54,
CPED_CONFIG_FLAG_SteerAroundDeadBodies = 55,
CPED_CONFIG_FLAG_ConstrainToNavMesh = 56,
CPED_CONFIG_FLAG_SyncingAnimatedProps = 57,
CPED_CONFIG_FLAG_IsFiring = 58,
CPED_CONFIG_FLAG_WasFiring = 59,
CPED_CONFIG_FLAG_IsStanding = 60,
CPED_CONFIG_FLAG_WasStanding = 61,
CPED_CONFIG_FLAG_InVehicle = 62,
CPED_CONFIG_FLAG_OnMount = 63,
CPED_CONFIG_FLAG_AttachedToVehicle = 64,
CPED_CONFIG_FLAG_IsSwimming = 65,
CPED_CONFIG_FLAG_WasSwimming = 66,
CPED_CONFIG_FLAG_IsSkiing = 67,
CPED_CONFIG_FLAG_IsSitting = 68,
CPED_CONFIG_FLAG_KilledByStealth = 69,
CPED_CONFIG_FLAG_KilledByTakedown = 70,
CPED_CONFIG_FLAG_Knockedout = 71,
CPED_CONFIG_FLAG_ClearRadarBlipOnDeath = 72,
CPED_CONFIG_FLAG_JustGotOffTrain = 73,
CPED_CONFIG_FLAG_JustGotOnTrain = 74,
CPED_CONFIG_FLAG_UsingCoverPoint = 75,
CPED_CONFIG_FLAG_IsInTheAir = 76,
CPED_CONFIG_FLAG_KnockedUpIntoAir = 77,
CPED_CONFIG_FLAG_IsAimingGun = 78,
CPED_CONFIG_FLAG_HasJustLeftCar = 79,
CPED_CONFIG_FLAG_TargetWhenInjuredAllowed = 80,
CPED_CONFIG_FLAG_CurrLeftFootCollNM = 81,
CPED_CONFIG_FLAG_PrevLeftFootCollNM = 82,
CPED_CONFIG_FLAG_CurrRightFootCollNM = 83,
CPED_CONFIG_FLAG_PrevRightFootCollNM = 84,
CPED_CONFIG_FLAG_HasBeenBumpedInCar = 85,
CPED_CONFIG_FLAG_InWaterTaskQuitToClimbLadder = 86,
CPED_CONFIG_FLAG_NMTwoHandedWeaponBothHandsConstrained = 87,
CPED_CONFIG_FLAG_CreatedBloodPoolTimer = 88,
CPED_CONFIG_FLAG_DontActivateRagdollFromAnyPedImpact = 89,
CPED_CONFIG_FLAG_GroupPedFailedToEnterCover = 90,
CPED_CONFIG_FLAG_AlreadyChattedOnPhone = 91,
CPED_CONFIG_FLAG_AlreadyReactedToPedOnRoof = 92,
CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
CPED_CONFIG_FLAG_BlockCoweringInCover = 94,
CPED_CONFIG_FLAG_BlockPeekingInCover = 95,
CPED_CONFIG_FLAG_JustLeftCarNotCheckedForDoors = 96,
CPED_CONFIG_FLAG_VaultFromCover = 97,
CPED_CONFIG_FLAG_AutoConversationLookAts = 98,
CPED_CONFIG_FLAG_UsingCrouchedPedCapsule = 99,
CPED_CONFIG_FLAG_HasDeadPedBeenReported = 100,
CPED_CONFIG_FLAG_ForcedAim = 101,
CPED_CONFIG_FLAG_SteersAroundPeds = 102,
CPED_CONFIG_FLAG_SteersAroundObjects = 103,
CPED_CONFIG_FLAG_OpenDoorArmIK = 104,
CPED_CONFIG_FLAG_ForceReload = 105,
CPED_CONFIG_FLAG_DontActivateRagdollFromVehicleImpact = 106,
CPED_CONFIG_FLAG_DontActivateRagdollFromBulletImpact = 107,
CPED_CONFIG_FLAG_DontActivateRagdollFromExplosions = 108,
CPED_CONFIG_FLAG_DontActivateRagdollFromFire = 109,
CPED_CONFIG_FLAG_DontActivateRagdollFromElectrocution = 110,
CPED_CONFIG_FLAG_IsBeingDraggedToSafety = 111,
CPED_CONFIG_FLAG_HasBeenDraggedToSafety = 112,
CPED_CONFIG_FLAG_KeepWeaponHolsteredUnlessFired = 113,
CPED_CONFIG_FLAG_ForceScriptControlledKnockout = 114,
CPED_CONFIG_FLAG_FallOutOfVehicleWhenKilled = 115,
CPED_CONFIG_FLAG_GetOutBurningVehicle = 116,
CPED_CONFIG_FLAG_BumpedByPlayer = 117,
CPED_CONFIG_FLAG_RunFromFiresAndExplosions = 118,
CPED_CONFIG_FLAG_TreatAsPlayerDuringTargeting = 119,
CPED_CONFIG_FLAG_IsHandCuffed = 120,
CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
CPED_CONFIG_FLAG_DisableMelee = 122,
CPED_CONFIG_FLAG_DisableUnarmedDrivebys = 123,
CPED_CONFIG_FLAG_JustGetsPulledOutWhenElectrocuted = 124,
CPED_CONFIG_FLAG_UNUSED_REPLACE_ME = 125,
CPED_CONFIG_FLAG_WillNotHotwireLawEnforcementVehicle = 126,
CPED_CONFIG_FLAG_WillCommandeerRatherThanJack = 127,
CPED_CONFIG_FLAG_CanBeAgitated = 128,
CPED_CONFIG_FLAG_ForcePedToFaceLeftInCover = 129,
CPED_CONFIG_FLAG_ForcePedToFaceRightInCover = 130,
CPED_CONFIG_FLAG_BlockPedFromTurningInCover = 131,
CPED_CONFIG_FLAG_KeepRelationshipGroupAfterCleanUp = 132,
CPED_CONFIG_FLAG_ForcePedToBeDragged = 133,
CPED_CONFIG_FLAG_PreventPedFromReactingToBeingJacked = 134,
CPED_CONFIG_FLAG_IsScuba = 135,
CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
CPED_CONFIG_FLAG_RemoveDeadExtraFarAway = 137,
CPED_CONFIG_FLAG_RidingTrain = 138,
CPED_CONFIG_FLAG_ArrestResult = 139,
CPED_CONFIG_FLAG_CanAttackFriendly = 140,
CPED_CONFIG_FLAG_WillJackAnyPlayer = 141,
CPED_CONFIG_FLAG_BumpedByPlayerVehicle = 142,
CPED_CONFIG_FLAG_DodgedPlayerVehicle = 143,
CPED_CONFIG_FLAG_WillJackWantedPlayersRatherThanStealCar = 144,
CPED_CONFIG_FLAG_NoCopWantedAggro = 145,
CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
CPED_CONFIG_FLAG_StairsDetected = 147,
CPED_CONFIG_FLAG_SlopeDetected = 148,
CPED_CONFIG_FLAG_HelmetHasBeenShot = 149,
CPED_CONFIG_FLAG_CowerInsteadOfFlee = 150,
CPED_CONFIG_FLAG_CanActivateRagdollWhenVehicleUpsideDown = 151,
CPED_CONFIG_FLAG_AlwaysRespondToCriesForHelp = 152,
CPED_CONFIG_FLAG_DisableBloodPoolCreation = 153,
CPED_CONFIG_FLAG_ShouldFixIfNoCollision = 154,
CPED_CONFIG_FLAG_CanPerformArrest = 155,
CPED_CONFIG_FLAG_CanPerformUncuff = 156,
CPED_CONFIG_FLAG_CanBeArrested = 157,
CPED_CONFIG_FLAG_MoverConstrictedByOpposingCollisions = 158,
CPED_CONFIG_FLAG_PlayerPreferFrontSeatMP = 159,
CPED_CONFIG_FLAG_DontActivateRagdollFromImpactObject = 160,
CPED_CONFIG_FLAG_DontActivateRagdollFromMelee = 161,
CPED_CONFIG_FLAG_DontActivateRagdollFromWaterJet = 162,
CPED_CONFIG_FLAG_DontActivateRagdollFromDrowning = 163,
CPED_CONFIG_FLAG_DontActivateRagdollFromFalling = 164,
CPED_CONFIG_FLAG_DontActivateRagdollFromRubberBullet = 165,
CPED_CONFIG_FLAG_IsInjured = 166,
CPED_CONFIG_FLAG_DontEnterVehiclesInPlayersGroup = 167,
CPED_CONFIG_FLAG_SwimmingTasksRunning = 168,
CPED_CONFIG_FLAG_PreventAllMeleeTaunts = 169,
CPED_CONFIG_FLAG_ForceDirectEntry = 170,
CPED_CONFIG_FLAG_AlwaysSeeApproachingVehicles = 171,
CPED_CONFIG_FLAG_CanDiveAwayFromApproachingVehicles = 172,
CPED_CONFIG_FLAG_AllowPlayerToInterruptVehicleEntryExit = 173,
CPED_CONFIG_FLAG_OnlyAttackLawIfPlayerIsWanted = 174,
CPED_CONFIG_FLAG_PlayerInContactWithKinematicPed = 175,
CPED_CONFIG_FLAG_PlayerInContactWithSomethingOtherThanKinematicPed = 176,
CPED_CONFIG_FLAG_PedsJackingMeDontGetIn = 177,
CPED_CONFIG_FLAG_AdditionalRappellingPed = 178,
CPED_CONFIG_FLAG_PedIgnoresAnimInterruptEvents = 179,
CPED_CONFIG_FLAG_IsInCustody = 180,
CPED_CONFIG_FLAG_ForceStandardBumpReactionThresholds = 181,
CPED_CONFIG_FLAG_LawWillOnlyAttackIfPlayerIsWanted = 182,
CPED_CONFIG_FLAG_IsAgitated = 183,
CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
CPED_CONFIG_FLAG_UseKinematicModeWhenStationary = 185,
CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
CPED_CONFIG_FLAG_HasHurtStarted = 187,
CPED_CONFIG_FLAG_DisableHurt = 188,
CPED_CONFIG_FLAG_PlayerIsWeird = 189,
CPED_CONFIG_FLAG_PedHadPhoneConversation = 190,
CPED_CONFIG_FLAG_BeganCrossingRoad = 191,
CPED_CONFIG_FLAG_WarpIntoLeadersVehicle = 192,
CPED_CONFIG_FLAG_DoNothingWhenOnFootByDefault = 193,
CPED_CONFIG_FLAG_UsingScenario = 194,
CPED_CONFIG_FLAG_VisibleOnScreen = 195,
CPED_CONFIG_FLAG_DontCollideWithKinematic = 196,
CPED_CONFIG_FLAG_ActivateOnSwitchFromLowPhysicsLod = 197,
CPED_CONFIG_FLAG_DontActivateRagdollOnPedCollisionWhenDead = 198,
CPED_CONFIG_FLAG_DontActivateRagdollOnVehicleCollisionWhenDead = 199,
CPED_CONFIG_FLAG_HasBeenInArmedCombat = 200,
CPED_CONFIG_FLAG_UseDiminishingAmmoRate = 201,
CPED_CONFIG_FLAG_Avoidance_Ignore_All = 202,
CPED_CONFIG_FLAG_Avoidance_Ignored_by_All = 203,
CPED_CONFIG_FLAG_Avoidance_Ignore_Group1 = 204,
CPED_CONFIG_FLAG_Avoidance_Member_of_Group1 = 205,
CPED_CONFIG_FLAG_ForcedToUseSpecificGroupSeatIndex = 206,
CPED_CONFIG_FLAG_LowPhysicsLodMayPlaceOnNavMesh = 207,
CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
CPED_CONFIG_FLAG_DodgedPlayer = 209,
CPED_CONFIG_FLAG_WaitingForPlayerControlInterrupt = 210,
CPED_CONFIG_FLAG_ForcedToStayInCover = 211,
CPED_CONFIG_FLAG_GeneratesSoundEvents = 212,
CPED_CONFIG_FLAG_ListensToSoundEvents = 213,
CPED_CONFIG_FLAG_AllowToBeTargetedInAVehicle = 214,
CPED_CONFIG_FLAG_WaitForDirectEntryPointToBeFreeWhenExiting = 215,
CPED_CONFIG_FLAG_OnlyRequireOnePressToExitVehicle = 216,
CPED_CONFIG_FLAG_ForceExitToSkyDive = 217,
CPED_CONFIG_FLAG_SteersAroundVehicles = 218,
CPED_CONFIG_FLAG_AllowPedInVehiclesOverrideTaskFlags = 219,
CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
CPED_CONFIG_FLAG_DisableExitToSkyDive = 221,
CPED_CONFIG_FLAG_ScriptHasDisabledCollision = 222,
CPED_CONFIG_FLAG_UseAmbientModelScaling = 223,
CPED_CONFIG_FLAG_DontWatchFirstOnNextHurryAway = 224,
CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
CPED_CONFIG_FLAG_ForceRagdollUponDeath = 227,
CPED_CONFIG_FLAG_CanLosePropsOnDamage = 228,
CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
CPED_CONFIG_FLAG_AllowedToDetachTrailer = 230,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromFront = 231,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromBack = 232,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromLeft = 233,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromRight = 234,
CPED_CONFIG_FLAG_AllowBlockDeadPedRagdollActivation = 235,
CPED_CONFIG_FLAG_IsHoldingProp = 236,
CPED_CONFIG_FLAG_BlocksPathingWhenDead = 237,
CPED_CONFIG_FLAG_ForcePlayNormalScenarioExitOnNextScriptCommand = 238,
CPED_CONFIG_FLAG_ForcePlayImmediateScenarioExitOnNextScriptCommand = 239,
CPED_CONFIG_FLAG_ForceSkinCharacterCloth = 240,
CPED_CONFIG_FLAG_LeaveEngineOnWhenExitingVehicles = 241,
CPED_CONFIG_FLAG_PhoneDisableTextingAnimations = 242,
CPED_CONFIG_FLAG_PhoneDisableTalkingAnimations = 243,
CPED_CONFIG_FLAG_PhoneDisableCameraAnimations = 244,
CPED_CONFIG_FLAG_DisableBlindFiringInShotReactions = 245,
CPED_CONFIG_FLAG_AllowNearbyCoverUsage = 246,
CPED_CONFIG_FLAG_InStrafeTransition = 247,
CPED_CONFIG_FLAG_CanPlayInCarIdles = 248,
CPED_CONFIG_FLAG_CanAttackNonWantedPlayerAsLaw = 249,
CPED_CONFIG_FLAG_WillTakeDamageWhenVehicleCrashes = 250,
CPED_CONFIG_FLAG_AICanDrivePlayerAsRearPassenger = 251,
CPED_CONFIG_FLAG_PlayerCanJackFriendlyPlayers = 252,
CPED_CONFIG_FLAG_OnStairs = 253,
CPED_CONFIG_FLAG_SimulatingAiming = 254,
CPED_CONFIG_FLAG_AIDriverAllowFriendlyPassengerSeatEntry = 255,
CPED_CONFIG_FLAG_ParentCarIsBeingRemoved = 256,
CPED_CONFIG_FLAG_AllowMissionPedToUseInjuredMovement = 257,
CPED_CONFIG_FLAG_CanLoseHelmetOnDamage = 258,
CPED_CONFIG_FLAG_NeverDoScenarioExitProbeChecks = 259,
CPED_CONFIG_FLAG_SuppressLowLODRagdollSwitchWhenCorpseSettles = 260,
CPED_CONFIG_FLAG_PreventUsingLowerPrioritySeats = 261,
CPED_CONFIG_FLAG_JustLeftVehicleNeedsReset = 262,
CPED_CONFIG_FLAG_TeleportIfCantReachPlayer = 263,
CPED_CONFIG_FLAG_PedsInVehiclePositionNeedsReset = 264,
CPED_CONFIG_FLAG_PedsFullyInSeat = 265,
CPED_CONFIG_FLAG_AllowPlayerLockOnIfFriendly = 266,
CPED_CONFIG_FLAG_UseCameraHeadingForDesiredDirectionLockOnTest = 267,
CPED_CONFIG_FLAG_TeleportToLeaderVehicle = 268,
CPED_CONFIG_FLAG_Avoidance_Ignore_WeirdPedBuffer = 269,
CPED_CONFIG_FLAG_OnStairSlope = 270,
CPED_CONFIG_FLAG_HasPlayedNMGetup = 271,
CPED_CONFIG_FLAG_DontBlipCop = 272,
CPED_CONFIG_FLAG_SpawnedAtExtendedRangeScenario = 273,
CPED_CONFIG_FLAG_WalkAlongsideLeaderWhenClose = 274,
CPED_CONFIG_FLAG_KillWhenTrapped = 275,
CPED_CONFIG_FLAG_EdgeDetected = 276,
CPED_CONFIG_FLAG_AlwaysWakeUpPhysicsOfIntersectedPeds = 277,
CPED_CONFIG_FLAG_EquippedAmbientLoadOutWeapon = 278,
CPED_CONFIG_FLAG_AvoidTearGas = 279,
CPED_CONFIG_FLAG_StoppedSpeechUponFreezing = 280,
CPED_CONFIG_FLAG_DisableGoToWritheWhenInjured = 281,
CPED_CONFIG_FLAG_OnlyUseForcedSeatWhenEnteringHeliInGroup = 282,
CPED_CONFIG_FLAG_ThrownFromVehicleDueToExhaustion = 283,
CPED_CONFIG_FLAG_UpdateEnclosedSearchRegion = 284,
CPED_CONFIG_FLAG_DisableWeirdPedEvents = 285,
CPED_CONFIG_FLAG_ShouldChargeNow = 286,
CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
CPED_CONFIG_FLAG_AllowMinorReactionsAsMissionPed = 289,
CPED_CONFIG_FLAG_BlockDeadBodyShockingEventsWhenDead = 290,
CPED_CONFIG_FLAG_PedHasBeenSeen = 291,
CPED_CONFIG_FLAG_PedIsInReusePool = 292,
CPED_CONFIG_FLAG_PedWasReused = 293,
CPED_CONFIG_FLAG_DisableShockingEvents = 294,
CPED_CONFIG_FLAG_MovedUsingLowLodPhysicsSinceLastActive = 295,
CPED_CONFIG_FLAG_NeverReactToPedOnRoof = 296,
CPED_CONFIG_FLAG_ForcePlayFleeScenarioExitOnNextScriptCommand = 297,
CPED_CONFIG_FLAG_JustBumpedIntoVehicle = 298,
CPED_CONFIG_FLAG_DisableShockingDrivingOnPavementEvents = 299,
CPED_CONFIG_FLAG_ShouldThrowSmokeNow = 300,
CPED_CONFIG_FLAG_DisablePedConstraints = 301,
CPED_CONFIG_FLAG_ForceInitialPeekInCover = 302,
CPED_CONFIG_FLAG_CreatedByDispatch = 303,
CPED_CONFIG_FLAG_PointGunLeftHandSupporting = 304,
CPED_CONFIG_FLAG_DisableJumpingFromVehiclesAfterLeader = 305,
CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerPedImpact = 306,
CPED_CONFIG_FLAG_DontActivateRagdollFromAiRagdollImpact = 307,
CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerRagdollImpact = 308,
CPED_CONFIG_FLAG_DisableQuadrupedSpring = 309,
CPED_CONFIG_FLAG_IsInCluster = 310,
CPED_CONFIG_FLAG_ShoutToGroupOnPlayerMelee = 311,
CPED_CONFIG_FLAG_IgnoredByAutoOpenDoors = 312,
CPED_CONFIG_FLAG_PreferInjuredGetup = 313,
CPED_CONFIG_FLAG_ForceIgnoreMeleeActiveCombatant = 314,
CPED_CONFIG_FLAG_CheckLoSForSoundEvents = 315,
CPED_CONFIG_FLAG_JackedAbandonedCar = 316,
CPED_CONFIG_FLAG_CanSayFollowedByPlayerAudio = 317,
CPED_CONFIG_FLAG_ActivateRagdollFromMinorPlayerContact = 318,
CPED_CONFIG_FLAG_HasPortablePickupAttached = 319,
CPED_CONFIG_FLAG_ForcePoseCharacterCloth = 320,
CPED_CONFIG_FLAG_HasClothCollisionBounds = 321,
CPED_CONFIG_FLAG_HasHighHeels = 322,
CPED_CONFIG_FLAG_TreatAsAmbientPedForDriverLockOn = 323,
CPED_CONFIG_FLAG_DontBehaveLikeLaw = 324,
CPED_CONFIG_FLAG_SpawnedAtScenario = 325,
CPED_CONFIG_FLAG_DisablePoliceInvestigatingBody = 326,
CPED_CONFIG_FLAG_DisableWritheShootFromGround = 327,
CPED_CONFIG_FLAG_LowerPriorityOfWarpSeats = 328,
CPED_CONFIG_FLAG_DisableTalkTo = 329,
CPED_CONFIG_FLAG_DontBlip = 330,
CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
CPED_CONFIG_FLAG_IgnoreLegIkRestrictions = 332,
CPED_CONFIG_FLAG_ScriptForceNoTimesliceIntelligenceUpdate = 333,
CPED_CONFIG_FLAG_JackedOutOfMyVehicle = 334,
CPED_CONFIG_FLAG_WentIntoCombatAfterBeingJacked = 335,
CPED_CONFIG_FLAG_DontActivateRagdollForVehicleGrab = 336,
CPED_CONFIG_FLAG_ForcePackageCharacterCloth = 337,
CPED_CONFIG_FLAG_DontRemoveWithValidOrder = 338,
CPED_CONFIG_FLAG_AllowTaskDoNothingTimeslicing = 339,
CPED_CONFIG_FLAG_ForcedToStayInCoverDueToPlayerSwitch = 340,
CPED_CONFIG_FLAG_ForceProneCharacterCloth = 341,
CPED_CONFIG_FLAG_NotAllowedToJackAnyPlayers = 342,
CPED_CONFIG_FLAG_InToStrafeTransition = 343,
CPED_CONFIG_FLAG_KilledByStandardMelee = 344,
CPED_CONFIG_FLAG_AlwaysLeaveTrainUponArrival = 345,
CPED_CONFIG_FLAG_ForcePlayDirectedNormalScenarioExitOnNextScriptCommand = 346,
CPED_CONFIG_FLAG_OnlyWritheFromWeaponDamage = 347,
CPED_CONFIG_FLAG_UseSloMoBloodVfx = 348,
CPED_CONFIG_FLAG_EquipJetpack = 349,
CPED_CONFIG_FLAG_PreventDraggedOutOfCarThreatResponse = 350,
CPED_CONFIG_FLAG_ScriptHasCompletelyDisabledCollision = 351,
CPED_CONFIG_FLAG_NeverDoScenarioNavChecks = 352,
CPED_CONFIG_FLAG_ForceSynchronousScenarioExitChecking = 353,
CPED_CONFIG_FLAG_ThrowingGrenadeWhileAiming = 354,
CPED_CONFIG_FLAG_HeadbobToRadioEnabled = 355,
CPED_CONFIG_FLAG_ForceDeepSurfaceCheck = 356,
CPED_CONFIG_FLAG_DisableDeepSurfaceAnims = 357,
CPED_CONFIG_FLAG_DontBlipNotSynced = 358,
CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
CPED_CONFIG_FLAG_PreventAutoShuffleToTurretSeat = 360,
CPED_CONFIG_FLAG_DisableEventInteriorStatusCheck = 361,
CPED_CONFIG_FLAG_HasReserveParachute = 362,
CPED_CONFIG_FLAG_UseReserveParachute = 363,
CPED_CONFIG_FLAG_TreatDislikeAsHateWhenInCombat = 364,
CPED_CONFIG_FLAG_OnlyUpdateTargetWantedIfSeen = 365,
CPED_CONFIG_FLAG_AllowAutoShuffleToDriversSeat = 366,
CPED_CONFIG_FLAG_DontActivateRagdollFromSmokeGrenade = 367,
CPED_CONFIG_FLAG_LinkMBRToOwnerOnChain = 368,
CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayer = 369,
CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayerVehicle = 370,
CPED_CONFIG_FLAG_InFPSUnholsterTransition = 371,
CPED_CONFIG_FLAG_PreventReactingToSilencedCloneBullets = 372,
CPED_CONFIG_FLAG_DisableInjuredCryForHelpEvents = 373,
CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
CPED_CONFIG_FLAG_DontDropJetpackOnDeath = 375,
CPED_CONFIG_FLAG_UseFPSUnholsterTransitionDuringCombatRoll = 376,
CPED_CONFIG_FLAG_ExitingFPSCombatRoll = 377,
CPED_CONFIG_FLAG_ScriptHasControlOfPlayer = 378,
CPED_CONFIG_FLAG_PlayFPSIdleFidgetsForProjectile = 379,
CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInBikes = 380,
CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInAircraft = 381,
CPED_CONFIG_FLAG_WasPlayingFPSGetup = 382,
CPED_CONFIG_FLAG_WasPlayingFPSMeleeActionResult = 383,
CPED_CONFIG_FLAG_PreferNoPriorityRemoval = 384,
CPED_CONFIG_FLAG_FPSFidgetsAbortedOnFire = 385,
CPED_CONFIG_FLAG_ForceFPSIKWithUpperBodyAnim = 386,
CPED_CONFIG_FLAG_SwitchingCharactersInFirstPerson = 387,
CPED_CONFIG_FLAG_IsClimbingLadder = 388,
CPED_CONFIG_FLAG_HasBareFeet = 389,
CPED_CONFIG_FLAG_UNUSED_REPLACE_ME_2 = 390,
CPED_CONFIG_FLAG_GoOnWithoutVehicleIfItIsUnableToGetBackToRoad = 391,
CPED_CONFIG_FLAG_BlockDroppingHealthSnacksOnDeath = 392,
CPED_CONFIG_FLAG_ResetLastVehicleOnVehicleExit = 393,
CPED_CONFIG_FLAG_ForceThreatResponseToNonFriendToFriendMeleeActions = 394,
CPED_CONFIG_FLAG_DontRespondToRandomPedsDamage = 395,
CPED_CONFIG_FLAG_AllowContinuousThreatResponseWantedLevelUpdates = 396,
CPED_CONFIG_FLAG_KeepTargetLossResponseOnCleanup = 397,
CPED_CONFIG_FLAG_PlayersDontDragMeOutOfCar = 398,
CPED_CONFIG_FLAG_BroadcastRepondedToThreatWhenGoingToPointShooting = 399,
CPED_CONFIG_FLAG_IgnorePedTypeForIsFriendlyWith = 400,
CPED_CONFIG_FLAG_TreatNonFriendlyAsHateWhenInCombat = 401,
CPED_CONFIG_FLAG_DontLeaveVehicleIfLeaderNotInVehicle = 402,
CPED_CONFIG_FLAG_ChangeFromPermanentToAmbientPopTypeOnMigration = 403,
CPED_CONFIG_FLAG_AllowMeleeReactionIfMeleeProofIsOn = 404,
CPED_CONFIG_FLAG_UsingLowriderLeans = 405,
CPED_CONFIG_FLAG_UsingAlternateLowriderLeans = 406,
CPED_CONFIG_FLAG_UseNormalExplosionDamageWhenBlownUpInVehicle = 407,
CPED_CONFIG_FLAG_DisableHomingMissileLockForVehiclePedInside = 408,
CPED_CONFIG_FLAG_DisableTakeOffScubaGear = 409,
CPED_CONFIG_FLAG_IgnoreMeleeFistWeaponDamageMult = 410,
CPED_CONFIG_FLAG_LawPedsCanFleeFromNonWantedPlayer = 411,
CPED_CONFIG_FLAG_ForceBlipSecurityPedsIfPlayerIsWanted = 412,
CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
CPED_CONFIG_FLAG_UseGoToPointForScenarioNavigation = 414,
CPED_CONFIG_FLAG_DontClearLocalPassengersWantedLevel = 415,
CPED_CONFIG_FLAG_BlockAutoSwapOnWeaponPickups = 416,
CPED_CONFIG_FLAG_ThisPedIsATargetPriorityForAI = 417,
CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
CPED_CONFIG_FLAG_ForceHelmetVisorSwitch = 419,
CPED_CONFIG_FLAG_IsPerformingVehicleMelee = 420,
CPED_CONFIG_FLAG_UseOverrideFootstepPtFx = 421,
CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamage = 423,
CPED_CONFIG_FLAG_AllowBikeAlternateAnimations = 424,
CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamageNonSynced = 425,
CPED_CONFIG_FLAG_UseLockpickVehicleEntryAnimations = 426,
CPED_CONFIG_FLAG_IgnoreInteriorCheckForSprinting = 427,
CPED_CONFIG_FLAG_SwatHeliSpawnWithinLastSpottedLocation = 428,
CPED_CONFIG_FLAG_DisableStartEngine = 429,
CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
CPED_CONFIG_FLAG_DisableTurretOrRearSeatPreference = 431,
CPED_CONFIG_FLAG_DisableWantedHelicopterSpawning = 432,
CPED_CONFIG_FLAG_UseTargetPerceptionForCreatingAimedAtEvents = 433,
CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
CPED_CONFIG_FLAG_ForceIgnoreMaxMeleeActiveSupportCombatants = 435,
CPED_CONFIG_FLAG_StayInDefensiveAreaWhenInVehicle = 436,
CPED_CONFIG_FLAG_DontShoutTargetPosition = 437,
CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
CPED_CONFIG_FLAG_CreatedByConcealedPlayer = 439,
CPED_CONFIG_FLAG_PermanentlyDisablePotentialToBeWalkedIntoResponse = 440,
CPED_CONFIG_FLAG_PreventVehExitDueToInvalidWeapon = 441,
CPED_CONFIG_FLAG_IgnoreNetSessionFriendlyFireCheckForAllowDamage = 442,
CPED_CONFIG_FLAG_DontLeaveCombatIfTargetPlayerIsAttackedByPolice = 443,
CPED_CONFIG_FLAG_CheckLockedBeforeWarp = 444,
CPED_CONFIG_FLAG_DontShuffleInVehicleToMakeRoom = 445,
CPED_CONFIG_FLAG_GiveWeaponOnGetup = 446,
CPED_CONFIG_FLAG_DontHitVehicleWithProjectiles = 447,
CPED_CONFIG_FLAG_DisableForcedEntryForOpenVehiclesFromTryLockedDoor = 448,
CPED_CONFIG_FLAG_FiresDummyRockets = 449,
CPED_CONFIG_FLAG_PedIsArresting = 450,
CPED_CONFIG_FLAG_IsDecoyPed = 451,
CPED_CONFIG_FLAG_HasEstablishedDecoy = 452,
CPED_CONFIG_FLAG_BlockDispatchedHelicoptersFromLanding = 453,
CPED_CONFIG_FLAG_DontCryForHelpOnStun = 454,
CPED_CONFIG_FLAG_HitByTranqWeapon = 455,
CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
CPED_CONFIG_FLAG_ForcedAimFromArrest = 457,
CPED_CONFIG_FLAG_DontChangeTargetFromMelee = 458,
_0x4376ABF2 = 459,
CPED_CONFIG_FLAG_RagdollFloatsIndefinitely = 460,
CPED_CONFIG_FLAG_BlockElectricWeaponDamage = 461,
_0x262A3B8E = 462,
_0x1AA79A25 = 463,
}
```

**This is the server-side RPC native equivalent of the client native [SET_PED_CONFIG_FLAG](?\_0x1913FE4CBF41C463).**
</summary>
	]]

native "SET_PED_DEFAULT_COMPONENT_VARIATION"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Sets Ped Default Clothes
```

**This is the server-side RPC native equivalent of the client native [SET_PED_DEFAULT_COMPONENT_VARIATION](?\_0x45EEE61580806D63).**
</summary>
	]]

native "SET_PED_HAIR_TINT"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "colorID" [=[ {} ]=],
		int "highlightColorID" [=[ {} ]=],
	}
	alias "_SET_PED_HAIR_COLOR"
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the tint index for the hair on the specified ped.

```
NativeDB Introduced: v323
```

**This is the server-side RPC native equivalent of the client native [SET_PED_HAIR_TINT](?\_0x4CFFC65454C93A49).**
</summary>
<param name="ped">The Ped whose hair tint is to be set.</param>
<param name="colorID">The tint index for the primary hair color.</param>
<param name="highlightColorID">The tint index for the hair highlight color.</param>
	]]

native "SET_PED_HEAD_BLEND_DATA"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "shapeFirstID" [=[ {} ]=],
		int "shapeSecondID" [=[ {} ]=],
		int "shapeThirdID" [=[ {} ]=],
		int "skinFirstID" [=[ {} ]=],
		int "skinSecondID" [=[ {} ]=],
		int "skinThirdID" [=[ {} ]=],
		float "shapeMix" [=[ {} ]=],
		float "skinMix" [=[ {} ]=],
		float "thirdMix" [=[ {} ]=],
		BOOL "isParent" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
For more info please refer to [this](https://gtaforums.com/topic/858970-all-gtao-face-ids-pedset-ped-head-blend-data-explained) topic.
**Other information:**
IDs start at zero and go Male Non-DLC, Female Non-DLC, Male DLC, and Female DLC.</br>
This native function is often called prior to calling natives such as:

*   [`SetPedHairColor`](#\_0xA23FE32C)
*   [`SetPedHeadOverlayColor`](#\_0x78935A27)
*   [`SetPedHeadOverlay`](#\_0xD28DBA90)
*   [`SetPedFaceFeature`](#\_0x6C8D4458)

**This is the server-side RPC native equivalent of the client native [SET_PED_HEAD_BLEND_DATA](?\_0x9414E18B9434C2FE).**
</summary>
<param name="ped">The ped entity</param>
<param name="shapeFirstID">Controls the shape of the first ped's face</param>
<param name="shapeSecondID">Controls the shape of the second ped's face</param>
<param name="shapeThirdID">Controls the shape of the third ped's face</param>
<param name="skinFirstID">Controls the first id's skin tone</param>
<param name="skinSecondID">Controls the second id's skin tone</param>
<param name="skinThirdID">Controls the third id's skin tone</param>
<param name="shapeMix">0.0 - 1.0 Of whose characteristics to take Mother -> Father (shapeFirstID and shapeSecondID)</param>
<param name="skinMix">0.0 - 1.0 Of whose characteristics to take Mother -> Father (skinFirstID and skinSecondID)</param>
<param name="thirdMix">Overrides the others in favor of the third IDs.</param>
<param name="isParent">IsParent is set for "children" of the player character's grandparents during old-gen character creation. It has unknown effect otherwise.</param>
	]]

native "SET_PED_HEAD_OVERLAY"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "overlayID" [=[ {} ]=],
		int "index" [=[ {} ]=],
		float "opacity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
OverlayID ranges from 0 to 12, index from 0 to _GET_NUM_OVERLAY_VALUES(overlayID)-1, and opacity from 0.0 to 1.0.
overlayID       Part                  Index, to disable
0               Blemishes             0 - 23, 255
1               Facial Hair           0 - 28, 255
2               Eyebrows              0 - 33, 255
3               Ageing                0 - 14, 255
4               Makeup                0 - 74, 255
5               Blush                 0 - 6, 255
6               Complexion            0 - 11, 255
7               Sun Damage            0 - 10, 255
8               Lipstick              0 - 9, 255
9               Moles/Freckles        0 - 17, 255
10              Chest Hair            0 - 16, 255
11              Body Blemishes        0 - 11, 255
12              Add Body Blemishes    0 - 1, 255
```

**Note:**
You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.

**This is the server-side RPC native equivalent of the client native [SET_PED_HEAD_OVERLAY](?\_0x48F44967FA05CC1E).**
</summary>
<param name="ped">The ped entity</param>
<param name="overlayID">The overlay id displayed up above.</param>
<param name="index">An integer representing the index (from 0 to `_GET_NUM_OVERLAY_VALUES(overlayID)-1`)</param>
<param name="opacity">A float ranging from 0.0 to 1.0</param>
	]]

native "SET_PED_INTO_VEHICLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		Vehicle "vehicle" [=[ {} ]=],
		int "seatIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_PED_INTO_VEHICLE

**This is the server-side RPC native equivalent of the client native [SET_PED_INTO_VEHICLE](?\_0xF75B0D629E1C063D).**
</summary>
<param name="seatIndex">See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669). -2 for the first available seat.</param>
	]]

native "SET_PED_MELEE_COMBAT_LIMITS"
	arguments {
		int "primaryCount" [=[ {} ]=],
		int "secondaryCount" [=[ {} ]=],
		int "populationPedCount" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Override the limits on the number and types of melee combatants. The game is limited to at most ten combatants among the three types: primary, secondary, and observers.

This native infers the number of observers based on the primary and secondary counts.
</summary>
<param name="primaryCount">The number of peds that engage in combat (default: 1)</param>
<param name="secondaryCount">The number of peds that engage in taunting (default: 3)</param>
<param name="populationPedCount">The maximum number of population peds (ambient and scenario) that can engage in combat (default: 3)</param>
	]]

native "SET_PED_MODEL_HEALTH_CONFIG"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		charPtr "configName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a ped model's health config.
Takes effect only after setting player model with `SET_PLAYER_MODEL`.
</summary>
<param name="modelHash">Ped's model.</param>
<param name="configName">Name of health config.</param>
	]]

native "SET_PED_MODEL_PERSONALITY"
	arguments {
		Hash "modelHash" [=[ {} ]=],
		Hash "personalityHash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Overrides a ped model personality type.
</summary>
<param name="modelHash">Ped's model.</param>
<param name="personalityHash">Personality hash.</param>
	]]

native "SET_PED_PROP_INDEX"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "componentId" [=[ {} ]=],
		int "drawableId" [=[ {} ]=],
		int "textureId" [=[ {} ]=],
		BOOL "attach" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
This native is used to set prop variation on a ped. Components, drawables and textures IDs are related to the ped model.

### MP Freemode list of props

**0**: Hats
**1**: Glasses
**2**: Ears
**6**: Watches
**7**: Bracelets
List of Prop IDs

```cpp
enum eAnchorPoints
{
ANCHOR_HEAD = 0, // "p_head"
ANCHOR_EYES = 1, // "p_eyes"
ANCHOR_EARS = 2, // "p_ears"
ANCHOR_MOUTH = 3, // "p_mouth"
ANCHOR_LEFT_HAND = 4, // "p_lhand"
ANCHOR_RIGHT_HAND = 5, // "p_rhand"
ANCHOR_LEFT_WRIST = 6, // "p_lwrist"
ANCHOR_RIGHT_WRIST = 7, // "p_rwrist"
ANCHOR_HIP = 8, // "p_lhip"
ANCHOR_LEFT_FOOT = 9, // "p_lfoot"
ANCHOR_RIGHT_FOOT = 10, // "p_rfoot"
ANCHOR_PH_L_HAND = 11, // "ph_lhand"
ANCHOR_PH_R_HAND = 12, // "ph_rhand"
NUM_ANCHORS = 13,
};
```

**This is the server-side RPC native equivalent of the client native [SET_PED_PROP_INDEX](?\_0x93376B65A266EB5F).**
</summary>
<param name="ped">The ped handle.</param>
<param name="componentId">The component that you want to set. Refer to [SET_PED_COMPONENT_VARIATION](#\_0x262B14F48D29DE80).</param>
<param name="drawableId">The drawable id that is going to be set. Refer to [GET_NUMBER_OF_PED_PROP_DRAWABLE_VARIATIONS](#\_0x5FAF9754E789FB47).</param>
<param name="textureId">The texture id of the drawable. Refer to [GET_NUMBER_OF_PED_PROP_TEXTURE_VARIATIONS](#\_0xA6E7F1CEB523E171).</param>
<param name="attach">Attached or not.</param>
	]]

native "SET_PED_RANDOM_COMPONENT_VARIATION"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "p1" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
p1 is always 0 in R* scripts; and a quick disassembly seems to indicate that p1 is unused.
```

**This is the server-side RPC native equivalent of the client native [SET_PED_RANDOM_COMPONENT_VARIATION](?\_0xC8A9481A01E63C28).**
</summary>
	]]

native "SET_PED_RANDOM_PROPS"
	arguments {
		Ped "ped" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_PED_RANDOM_PROPS

**This is the server-side RPC native equivalent of the client native [SET_PED_RANDOM_PROPS](?\_0xC44AA05345C992C6).**
</summary>
<param name="ped">The ped handle.</param>
	]]

native "SET_PED_RESET_FLAG"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "flagId" [=[ {} ]=],
		BOOL "doReset" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
PED::SET_PED_RESET_FLAG(PLAYER::PLAYER_PED_ID(), 240, 1);
Known values:

**This is the server-side RPC native equivalent of the client native [SET_PED_RESET_FLAG](?\_0xC1E8A365BF3B29F2).**
</summary>
	]]

native "SET_PED_TO_RAGDOLL"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "minTime" [=[ {} ]=],
		int "maxTime" [=[ {} ]=],
		int "ragdollType" [=[ {} ]=],
		BOOL "bAbortIfInjured" [=[ {} ]=],
		BOOL "bAbortIfDead" [=[ {} ]=],
		BOOL "bForceScriptControl" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
p4/p5: Unusued in TU27

### Ragdoll Types

**0**: CTaskNMRelax
**1**: CTaskNMScriptControl: Hardcoded not to work in networked environments.
**Else**: CTaskNMBalance

**This is the server-side RPC native equivalent of the client native [SET_PED_TO_RAGDOLL](?\_0xAE99FB955581844A).**
</summary>
<param name="ped">The ped to ragdoll.</param>
<param name="minTime">Time(ms) Ped is in ragdoll mode; only applies to ragdoll types 0 and not 1.</param>
<param name="bAbortIfInjured">unused</param>
<param name="bAbortIfDead">unused</param>
	]]

native "SET_PED_TO_RAGDOLL_WITH_FALL"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "minTime" [=[ {} ]=],
		int "maxTime" [=[ {} ]=],
		int "nFallType" [=[ {} ]=],
		float "dirX" [=[ {} ]=],
		float "dirY" [=[ {} ]=],
		float "dirZ" [=[ {} ]=],
		float "fGroundHeight" [=[ {} ]=],
		float "grab1X" [=[ {} ]=],
		float "grab1Y" [=[ {} ]=],
		float "grab1Z" [=[ {} ]=],
		float "grab2X" [=[ {} ]=],
		float "grab2Y" [=[ {} ]=],
		float "grab2Z" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```cpp
enum eNMFallType {
TYPE_FROM_HIGH = 0,
TYPE_OVER_WALL = 1,
TYPE_DOWN_STAIRS = 2,
TYPE_DIE_TYPES = 3,
TYPE_DIE_FROM_HIGH = 4,
TYPE_DIE_OVER_WALL = 5,
TYPE_DIE_DOWN_STAIRS = 6
}
```

```
Return variable is never used in R*'s scripts.
Not sure what p2 does. It seems like it would be a time judging by it's usage in R*'s scripts, but didn't seem to affect anything in my testings.
x, y, and z are coordinates, most likely to where the ped will fall.
p7 is probably the force of the fall, but untested, so I left the variable name the same.
p8 to p13 are always 0f in R*'s scripts.
(Simplified) Example of the usage of the function from R*'s scripts:
ped::set_ped_to_ragdoll_with_fall(ped, 1500, 2000, 1, -entity::get_entity_forward_vector(ped), 1f, 0f, 0f, 0f, 0f, 0f, 0f);
```

**This is the server-side RPC native equivalent of the client native [SET_PED_TO_RAGDOLL_WITH_FALL](?\_0xD76632D99E4966C8).**
</summary>
<param name="ped">The ped to ragdoll.</param>
<param name="nFallType">The type of fall.</param>
<param name="dirX">The x direction of the fall.</param>
<param name="dirY">The y direction of the fall.</param>
<param name="dirZ">The z direction of the fall.</param>
<param name="fGroundHeight">The ground height (z).</param>
<param name="grab1X">unused</param>
<param name="grab1Y">unused</param>
<param name="grab1Z">unused</param>
<param name="grab2X">unused</param>
<param name="grab2Y">unused</param>
<param name="grab2Z">unused</param>
	]]

native "SET_PED_TURNING_THRESHOLDS"
	arguments {
		float "min" [=[ {} ]=],
		float "max" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Purpose: The game's default values for these make shooting while traveling Left quite a bit slower than shooting while traveling right (This could be a game-balance thing?)

Default Min: -45 Degrees
Default Max: 135 Degrees

```
   \ ,- ~ ||~ - ,
, ' \    x   x    ' ,
```

,      \    x    x   x  ,
,         \  x     x      ,
,            \     x    x  ,
,              \      x    ,
,                \   x     ,
,                 \   x x ,
,                  \  x ,
,                 , '
' - , \_ \_ \_ ,  '  \\

If the transition angle is within the shaded portion (x), there will be no transition(Quicker)
The angle corresponds to where you are looking(North on the circle) vs. the heading of your Ped.
Note: For some reason,

You can set these values to whatever you'd like with this native, but keep in mind that the transitional spin is only clockwise for some reason.

I'd personally recommend something like -135/135
</summary>
<param name="min">Leftside angle on the above diagram</param>
<param name="max">Rightside angle on the above diagram</param>
	]]

native "SET_PLAYER_CONTROL"
	arguments {
		Player "player" [=[ {} ]=],
		BOOL "bHasControl" [=[ {} ]=],
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Flags:
SPC_AMBIENT_SCRIPT = (1 << 1),
SPC_CLEAR_TASKS = (1 << 2),
SPC_REMOVE_FIRES = (1 << 3),
SPC_REMOVE_EXPLOSIONS = (1 << 4),
SPC_REMOVE_PROJECTILES = (1 << 5),
SPC_DEACTIVATE_GADGETS = (1 << 6),
SPC_REENABLE_CONTROL_ON_DEATH = (1 << 7),
SPC_LEAVE_CAMERA_CONTROL_ON = (1 << 8),
SPC_ALLOW_PLAYER_DAMAGE = (1 << 9),
SPC_DONT_STOP_OTHER_CARS_AROUND_PLAYER = (1 << 10),
SPC_PREVENT_EVERYBODY_BACKOFF = (1 << 11),
SPC_ALLOW_PAD_SHAKE = (1 << 12)
See: https://alloc8or.re/gta5/doc/enums/eSetPlayerControlFlag.txt
```

**This is the server-side RPC native equivalent of the client native [SET_PLAYER_CONTROL](?\_0x8D32347D6D4C40A2).**
</summary>
	]]

native "SET_PLAYER_CULLING_RADIUS"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		float "radius" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the culling radius for the specified player.
Set to `0.0` to reset.

**WARNING**: Culling natives are deprecated and have known, [unfixable issues](https://forum.cfx.re/t/issue-with-culling-radius-and-server-side-entities/4900677/4)
</summary>
<param name="playerSrc">The player to set the culling radius for.</param>
<param name="radius">The radius.</param>
	]]

native "SET_PLAYER_INVINCIBLE"
	arguments {
		Player "player" [=[ {} ]=],
		BOOL "bInvincible" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Make the player impervious to all forms of damage.

**This is the server-side RPC native equivalent of the client native [SET_PLAYER_INVINCIBLE](?\_0x239528EACDC3E7DE).**
</summary>
<param name="player">The player index.</param>
	]]

native "SET_PLAYER_KILL_FALL_HEIGHT"
	arguments {
		float "height" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for [GET_PLAYER_KILL_FALL_HEIGHT](#\_0x13BC2C63).
</summary>
<param name="height">height from which the player peds will instantly die from fall damage. Default value is `15.0`.</param>
	]]

native "SET_PLAYER_MAX_STAMINA"
	arguments {
		Player "playerId" [=[ {} ]=],
		float "maxStamina" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<param name="playerId">The player index</param>
<param name="maxStamina">The value you want to set</param>
<returns>Did you manage to set the value.</returns>
	]]

native "SET_PLAYER_MODEL"
	arguments {
		Player "player" [=[ {} ]=],
		Hash "model" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Set the model for a specific Player. Note that this will destroy the current Ped for the Player and create a new one, any reference to the old ped will be invalid after calling this.
As per usual, make sure to request the model first and wait until it has loaded.

**This is the server-side RPC native equivalent of the client native [SET_PLAYER_MODEL](?\_0x00A1CADD00108836).**
</summary>
<param name="player">The player to set the model for</param>
<param name="model">The model to use</param>
	]]

native "SET_PLAYER_ROUTING_BUCKET"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		int "bucket" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the routing bucket for the specified player.

Routing buckets are also known as 'dimensions' or 'virtual worlds' in past echoes, however they are population-aware.
</summary>
<param name="playerSrc">The player to set the routing bucket for.</param>
<param name="bucket">The bucket ID.</param>
	]]

native "SET_PLAYER_STAMINA"
	arguments {
		Player "playerId" [=[ {} ]=],
		float "stamina" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<param name="playerId">The player index</param>
<param name="stamina">The value you want to set</param>
<returns>Did you manage to set the value.</returns>
	]]

native "SET_PLAYER_TALKING_OVERRIDE"
	arguments {
		Player "player" [=[ {} ]=],
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
the status of default voip system. It affects on `NETWORK_IS_PLAYER_TALKING` and `mp_facial` animation.
This function doesn't need to be called every frame, it works like a switcher.
</summary>
<param name="player">The target player.</param>
<param name="state">Overriding state.</param>
	]]

native "SET_PLAYER_WANTED_LEVEL"
	arguments {
		Player "player" [=[ {} ]=],
		int "wantedLevel" [=[ {} ]=],
		BOOL "delayedResponse" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_PLAYER_WANTED_LEVEL

**This is the server-side RPC native equivalent of the client native [SET_PLAYER_WANTED_LEVEL](?\_0x39FF19C64EF7DA5B).**
</summary>
<param name="player">the target player</param>
<param name="wantedLevel">the wanted level 1-5</param>
<param name="delayedResponse">false = 0-10sec police spawn response time, true = 10-20sec police spawn response time</param>
	]]

native "SET_REACTION_TO_VEHICLE_WITH_SIREN_DISABLED"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
This completely disables pedestrian vehicles from reacting to sirens. They will not try to do any maneuver to evade.
</summary>
<param name="state">Toggle on or off.</param>
	]]

native "SET_RESOURCE_KVP"
	arguments {
		charPtr "key" [=[ {} ]=],
		charPtr "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RESOURCE_KVP_FLOAT"
	arguments {
		charPtr "key" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RESOURCE_KVP_FLOAT_NO_SYNC"
	arguments {
		charPtr "key" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RESOURCE_KVP_INT"
	arguments {
		charPtr "key" [=[ {} ]=],
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RESOURCE_KVP_INT_NO_SYNC"
	arguments {
		charPtr "key" [=[ {} ]=],
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RESOURCE_KVP_NO_SYNC"
	arguments {
		charPtr "key" [=[ {} ]=],
		charPtr "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
</summary>
<param name="key">The key to set</param>
<param name="value">The value to write</param>
	]]

native "SET_RICH_PRESENCE"
	arguments {
		charPtr "presenceState" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets the player's rich presence detail state for social platform providers to a specified string.
</summary>
<param name="presenceState">The rich presence string to set.</param>
	]]

native "SET_ROPE_LENGTH_CHANGE_RATE"
	arguments {
		int "rope" [=[ {} ]=],
		float "lengthChangeRate" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set's the ropes length change rate, which is the speed that rope should wind if started.
</summary>
<param name="rope">The rope to set the length change rate for.</param>
<param name="lengthChangeRate">The rope's new length change rate.</param>
	]]

native "SET_ROPES_CREATE_NETWORK_WORLD_STATE"
	arguments {
		BOOL "shouldCreate" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Toggles whether the usage of [ADD_ROPE](#\_0xE832D760399EB220) should create an underlying CNetworkRopeWorldStateData. By default this is set to false.
</summary>
<param name="shouldCreate">Whether to create an underlying network world state</param>
	]]

native "SET_ROUTING_BUCKET_ENTITY_LOCKDOWN_MODE"
	arguments {
		int "bucketId" [=[ {} ]=],
		charPtr "mode" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the entity lockdown mode for a specific routing bucket.

Lockdown modes are:

| Mode       | Meaning                                                    |
| ---------- | ---------------------------------------------------------- |
| `strict`   | No entities can be created by clients at all.              |
| `relaxed`  | Only script-owned entities created by clients are blocked. |
| `inactive` | Clients can create any entity they want.                   |
</summary>
<param name="bucketId">The routing bucket ID to adjust.</param>
<param name="mode">One of aforementioned modes.</param>
	]]

native "SET_ROUTING_BUCKET_POPULATION_ENABLED"
	arguments {
		int "bucketId" [=[ {} ]=],
		BOOL "mode" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets whether or not the specified routing bucket has automatically-created population enabled.
</summary>
<param name="bucketId">The routing bucket ID to adjust.</param>
<param name="mode">`true` to enable population, `false` to disable population.</param>
	]]

native "SET_RUNTIME_TEXTURE_ARGB_DATA"
	arguments {
		long "tex" [=[ {} ]=],
		charPtr "buffer" [=[ {} ]=],
		int "length" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
	]]

native "SET_RUNTIME_TEXTURE_IMAGE"
	arguments {
		long "tex" [=[ {} ]=],
		charPtr "fileName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Replaces the pixel data in a runtime texture with the image data from a file in the current resource, or a data URL.

If the bitmap is a different size compared to the existing texture, it will be resampled.

This command may end up executed asynchronously, and only update the texture data at a later time.
</summary>
<param name="tex">A runtime texture handle.</param>
<param name="fileName">The file name of an image to load, or a base64 "data:" URL. This should preferably be a PNG, and has to be specified as a `file` in the resource manifest.</param>
<returns>TRUE for success, FALSE for failure.</returns>
	]]

native "SET_RUNTIME_TEXTURE_PIXEL"
	arguments {
		long "tex" [=[ {} ]=],
		int "x" [=[ {} ]=],
		int "y" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
		int "a" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a pixel in the specified runtime texture. This will have to be committed using `COMMIT_RUNTIME_TEXTURE` to have any effect.
</summary>
<param name="tex">A handle to the runtime texture.</param>
<param name="x">The X position of the pixel to change.</param>
<param name="y">The Y position of the pixel to change.</param>
<param name="r">The new R value (0-255).</param>
<param name="g">The new G value (0-255).</param>
<param name="b">The new B value (0-255).</param>
<param name="a">The new A value (0-255).</param>
	]]

native "SET_SNAKEOIL_FOR_ENTRY"
	arguments {
		charPtr "name" [=[ {} ]=],
		charPtr "path" [=[ {} ]=],
		charPtr "data" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
	]]

native "SET_STATE_BAG_VALUE"
	arguments {
		charPtr "bagName" [=[ {} ]=],
		charPtr "keyName" [=[ {} ]=],
		charPtr "valueData" [=[ {} ]=],
		int "valueLength" [=[ {} ]=],
		BOOL "replicated" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
Internal function for setting a state bag value.
</summary>
	]]

native "SET_TEXT_CHAT_ENABLED"
	arguments {
		BOOL "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
	]]

native "SET_TEXT_FONT_FOR_CURRENT_COMMAND"
	arguments {
		int "fontId" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
<summary>
Sets the text font for the current text drawing command.
</summary>
<param name="fontId">The index of the font.</param>
	]]

native "SET_TEXT_JUSTIFICATION"
	arguments {
		int "justifyType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "SET_TEXT_WRAP"
	arguments {
		float "start" [=[ {} ]=],
		float "end" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "rdr3"
	returns "void"
	doc [[!
	]]

native "SET_TIMECYCLE_MODIFIER_VAR"
	arguments {
		charPtr "modifierName" [=[ {} ]=],
		charPtr "varName" [=[ {} ]=],
		float "value1" [=[ {} ]=],
		float "value2" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="modifierName">The name of timecycle modifier.</param>
<param name="varName">The name of timecycle variable.</param>
<param name="value1">The first value of variable.</param>
<param name="value2">The second value of variable.</param>
	]]

native "SET_TRACK_BRAKING_DISTANCE"
	arguments {
		int "track" [=[ {} ]=],
		float "brakingDistance" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the braking distance of the track. Used by trains to determine the point to slow down when entering a station.
</summary>
<param name="track">The track id (between 0 - 27)</param>
<param name="brakingDistance">The new braking distance</param>
	]]

native "SET_TRACK_ENABLED"
	arguments {
		int "track" [=[ {} ]=],
		bool "enabled" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Toggles the track being active. If disabled mission trains will not be able to spawn on this track and will look for the next closest track to spawn
</summary>
<param name="track">The track id (between 0 - 27)</param>
<param name="enabled">Should this track be enabled</param>
	]]

native "SET_TRACK_JUNCTION_ACTIVE"
	arguments {
		int "junctionIndex" [=[ {} ]=],
		bool "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "bool"
	doc [[!
<summary>
Sets the state of a track junction.
</summary>
<param name="junctionIndex">The junctions index</param>
<param name="state">Toggles the state of the junction</param>
<returns>Returns if it succeeds setting the junctions state or not</returns>
	]]

native "SET_TRACK_MAX_SPEED"
	arguments {
		int "track" [=[ {} ]=],
		int "newSpeed" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the max speed for the train tracks. Used by ambient trains and for station calculations
</summary>
<param name="track">The track id (between 0 - 27)</param>
<param name="newSpeed">The tracks new speed</param>
	]]

native "SET_TRAIN_DOOR_OPEN_RATIO"
	arguments {
		Vehicle "train" [=[ {} ]=],
		int "doorIndex" [=[ {} ]=],
		float "ratio" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the ratio that a door is open for on a train.
</summary>
<param name="train">The train to set the door ratio for.</param>
<param name="doorIndex">Zero-based door index.</param>
<param name="ratio">A value between 0.0 (fully closed) and 1.0 (fully open).</param>
	]]

native "SET_TRAIN_STATE"
	arguments {
		Vehicle "train" [=[ {} ]=],
		int "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="train">The train handle</param>
<param name="state">The trains new state</param>
<returns>The trains current state, refer to [GET_TRAIN_STATE](#\_0x81B50033)</returns>
	]]

native "SET_TRAIN_STOP_AT_STATIONS"
	arguments {
		Vehicle "train" [=[ {} ]=],
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Toggles a train's ability to stop at stations
</summary>
<param name="train">The train handle</param>
<param name="state">True to make the train stop at stations. False to make the train not stop at stations</param>
	]]

native "SET_TRAINS_FORCE_DOORS_OPEN"
	arguments {
		bool "forceOpen" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Enables or disables whether train doors should be forced open whilst a player is inside the train. This is enabled by default in multiplayer.
</summary>
<param name="forceOpen">Should force open.</param>
	]]

native "SET_VEHICLE_ALARM"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_VEHICLE_ALARM

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_ALARM](?\_0xCDE5E70C1DDB954C).**
</summary>
	]]

native "SET_VEHICLE_ALARM_TIME_LEFT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "time" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_AUTO_REPAIR_DISABLED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		BOOL "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables the vehicle from being repaired when a vehicle extra is enabled.
</summary>
<param name="vehicle">The vehicle to set disable auto vehicle repair.</param>
<param name="value">Setting the value to  true prevents the vehicle from being repaired when a extra is enabled. Setting the value to false allows the vehicle from being repaired when a extra is enabled.</param>
	]]

native "SET_VEHICLE_BODY_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
p2 often set to 1000.0 in the decompiled scripts.
```

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_BODY_HEALTH](?\_0xB77D05AC8C78AADB).**
</summary>
	]]

native "SET_VEHICLE_CLUTCH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "clutch" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_COLOUR_COMBINATION"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "colorCombination" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the selected vehicle's colors to their default value (specific variant specified using the colorCombination parameter).
Range of possible values for colorCombination is currently unknown, I couldn't find where these values are stored either (Disquse's guess was vehicles.meta but I haven't seen it in there.)

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_COLOUR_COMBINATION](?\_0x33E8CD3322E2FE31).**
</summary>
<param name="vehicle">The vehicle to modify.</param>
<param name="colorCombination">One of the default color values of the vehicle.</param>
	]]

native "SET_VEHICLE_COLOURS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "colorPrimary" [=[ {} ]=],
		int "colorSecondary" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
colorPrimary & colorSecondary are the paint indexes for the vehicle.
For a list of valid paint indexes, view: pastebin.com/pwHci0xK

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_COLOURS](?\_0x4F1D4BE3A7F24601).**
</summary>
	]]

native "SET_VEHICLE_CURRENT_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "gear" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="vehicle">The vehicle handle.</param>
<param name="gear">The gear you want the vehicle to use.</param>
	]]

native "SET_VEHICLE_CURRENT_RPM"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "rpm" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_CUSTOM_PRIMARY_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
```

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_CUSTOM_PRIMARY_COLOUR](?\_0x7141766F91D15BEA).**
</summary>
	]]

native "SET_VEHICLE_CUSTOM_SECONDARY_COLOUR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "r" [=[ {} ]=],
		int "g" [=[ {} ]=],
		int "b" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
```

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_CUSTOM_SECONDARY_COLOUR](?\_0x36CED73BFED89754).**
</summary>
	]]

native "SET_VEHICLE_DIRT_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "dirtLevel" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the dirt level of the passed vehicle.

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_DIRT_LEVEL](?\_0x79D3B596FE44EE8B).**
</summary>
<param name="vehicle">The vehicle to set.</param>
<param name="dirtLevel">A number between 0.0 and 15.0 representing the vehicles dirt level.</param>
	]]

native "SET_VEHICLE_DOOR_BROKEN"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "doorIndex" [=[ {} ]=],
		BOOL "deleteDoor" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_DOOR_BROKEN](?\_0xD4D4F6A4AB575A33).**
</summary>
	]]

native "SET_VEHICLE_DOORS_LOCKED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "doorLockStatus" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Locks the doors of a specified vehicle to a defined lock state, affecting how players and NPCs can interact with the vehicle.

```
NativeDB Introduced: v323
```

```cpp
enum eVehicleLockState {
// No specific lock state, vehicle behaves according to the game's default settings.
VEHICLELOCK_NONE = 0,
// Vehicle is fully unlocked, allowing free entry by players and NPCs.
VEHICLELOCK_UNLOCKED = 1,
// Vehicle is locked, preventing entry by players and NPCs.
VEHICLELOCK_LOCKED = 2,
// Vehicle locks out only players, allowing NPCs to enter.
VEHICLELOCK_LOCKOUT_PLAYER_ONLY = 3,
// Vehicle is locked once a player enters, preventing others from entering.
VEHICLELOCK_LOCKED_PLAYER_INSIDE = 4,
// Vehicle starts in a locked state, but may be unlocked through game events.
VEHICLELOCK_LOCKED_INITIALLY = 5,
// Forces the vehicle's doors to shut and lock.
VEHICLELOCK_FORCE_SHUT_DOORS = 6,
// Vehicle is locked but can still be damaged.
VEHICLELOCK_LOCKED_BUT_CAN_BE_DAMAGED = 7,
// Vehicle is locked, but its trunk/boot remains unlocked.
VEHICLELOCK_LOCKED_BUT_BOOT_UNLOCKED = 8,
// Vehicle is locked and does not allow passengers, except for the driver.
VEHICLELOCK_LOCKED_NO_PASSENGERS = 9,
// Vehicle is completely locked, preventing entry entirely, even if previously inside.
VEHICLELOCK_CANNOT_ENTER = 10
};
```

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_DOORS_LOCKED](?\_0xB664292EAECF7FA6).**
</summary>
<param name="vehicle">The vehicle whose doors are to be locked.</param>
<param name="doorLockStatus">The lock state to apply to the vehicle's doors, see `eVehicleLockState`.</param>
	]]

native "SET_VEHICLE_ENGINE_TEMPERATURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "temperature" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_FLAG"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "flagIndex" [=[ {} ]=],
		bool "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "bool"
	doc [[!
<summary>
This native is a setter for [`GET_VEHICLE_HAS_FLAG`](#\_0xD85C9F57).
</summary>
<param name="vehicle">The vehicle to set flag for.</param>
<param name="flagIndex">Flag index.</param>
<param name="value">`true` to enable the flag, `false` to disable it.</param>
	]]

native "SET_VEHICLE_FUEL_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "level" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_GEAR_RATIO"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "gear" [=[ {} ]=],
		float "ratio" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the vehicles gear ratio on choosen gear, reverse gear needs to be a negative float and forward moving gear needs to be a positive float. Refer to the examples if confused.
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="gear">The vehicles gear you want to change.</param>
<param name="ratio">The gear ratio you want to use.</param>
	]]

native "SET_VEHICLE_GRAVITY_AMOUNT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "gravity" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_HANDLING_FIELD"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		Any "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a handling override for a specific vehicle. Certain handling flags can only be set globally using `SET_HANDLING_FIELD`, this might require some experimentation.
Example: `SetVehicleHandlingField(vehicle, 'CHandlingData', 'fSteeringLock', 360.0)`
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The value to set.</param>
	]]

native "SET_VEHICLE_HANDLING_FLOAT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a handling override for a specific vehicle. Certain handling flags can only be set globally using `SET_HANDLING_FLOAT`, this might require some experimentation.
Example: `SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fSteeringLock', 360.0)`
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The floating-point value to set.</param>
	]]

native "SET_VEHICLE_HANDLING_INT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		int "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a handling override for a specific vehicle. Certain handling flags can only be set globally using `SET_HANDLING_INT`, this might require some experimentation.
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The integer value to set.</param>
	]]

native "SET_VEHICLE_HANDLING_VECTOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "class_" [=[ {} ]=],
		charPtr "fieldName" [=[ {} ]=],
		Vector3 "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets a handling override for a specific vehicle. Certain handling flags can only be set globally using `SET_HANDLING_VECTOR`, this might require some experimentation.
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="class_">The handling class to set. Only "CHandlingData" is supported at this time.</param>
<param name="fieldName">The field name to set. These match the keys in `handling.meta`.</param>
<param name="value">The Vector3 value to set.</param>
	]]

native "SET_VEHICLE_HIGH_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "gear" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_NEXT_GEAR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "nextGear" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<param name="vehicle">The vehicle handle.</param>
<param name="nextGear">The vehicles next gear.</param>
	]]

native "SET_VEHICLE_NITRO_PTFX_RANGE"
	arguments {
		float "range" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the maximum distance in which [\_SET_VEHICLE_NITRO_ENABLED](#\_0xC8E9B6B71B8E660D) PTFX are rendered. Distance is measured from the camera position.
</summary>
<param name="range">The visible distance. Default is 50.0f.</param>
	]]

native "SET_VEHICLE_NUMBER_PLATE_TEXT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		charPtr "plateText" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
SET_VEHICLE_NUMBER_PLATE_TEXT

**This is the server-side RPC native equivalent of the client native [SET_VEHICLE_NUMBER_PLATE_TEXT](?\_0x95A88F0B409CDA47).**
</summary>
<param name="vehicle">The vehicle to set the plate for</param>
<param name="plateText">The text to set the plate to, 8 chars maximum</param>
	]]

native "SET_VEHICLE_OIL_LEVEL"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "level" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_PITCH_BIAS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the vehicle's pitch bias. Only works on planes.
</summary>
<param name="vehicle">Target vehicle.</param>
<param name="value">Pitch bias value.</param>
	]]

native "SET_VEHICLE_ROLL_BIAS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Set the vehicle's roll bias. Only works on planes.
</summary>
<param name="vehicle">Target vehicle.</param>
<param name="value">Roll bias value.</param>
	]]

native "SET_VEHICLE_STEERING_ANGLE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "angle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_STEERING_SCALE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "scale" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_SUSPENSION_HEIGHT"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "newHeight" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the height of the vehicle's suspension.
This changes the same value set by Suspension in the mod shop.
Negatives values raise the car. Positive values lower the car.

This is change is visual only. The collision of the vehicle will not move.
</summary>
	]]

native "SET_VEHICLE_TURBO_PRESSURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "pressure" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_WHEEL_BRAKE_PRESSURE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "pressure" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets brake pressure of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
Normal values around 1.0f
</summary>
	]]

native "SET_VEHICLE_WHEEL_FLAGS"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the flags of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
<param name="flags">bit flags</param>
	]]

native "SET_VEHICLE_WHEEL_HEALTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "health" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_WHEEL_IS_POWERED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		BOOL "powered" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets whether the wheel is powered.
On all wheel drive cars this works to change which wheels receive power, but if a car's fDriveBiasFront doesn't send power to that wheel, it won't get power anyway. This can be fixed by changing the fDriveBiasFront with SET_VEHICLE_HANDLING_FLOAT.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
This is a shortcut to a flag in SET_VEHICLE_WHEEL_FLAGS.
</summary>
	]]

native "SET_VEHICLE_WHEEL_POWER"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "power" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets power being sent to a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
	]]

native "SET_VEHICLE_WHEEL_RIM_COLLIDER_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Not sure what this changes, probably determines physical rim size in case the tire is blown.
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<param name="value">Size of rim collider.</param>
	]]

native "SET_VEHICLE_WHEEL_ROTATION_SPEED"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "speed" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the rotation speed of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
	]]

native "SET_VEHICLE_WHEEL_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "size" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Sets vehicle's wheels' size (size is the same for all the wheels, cannot get/set specific wheel of vehicle).
Only works on non-default wheels.
Returns whether change was successful (can be false if trying to set size for non-default wheels).
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="size">Size of the wheels (usually between 0.5 and 1.5 is reasonable).</param>
<returns>Bool - whether change was successful or not</returns>
	]]

native "SET_VEHICLE_WHEEL_TIRE_COLLIDER_SIZE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Use along with SetVehicleWheelSize to resize the wheels (this native sets the collider size affecting physics while SetVehicleWheelSize will change visual size).
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<param name="value">Radius of tire collider.</param>
	]]

native "SET_VEHICLE_WHEEL_TIRE_COLLIDER_WIDTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Use along with SetVehicleWheelWidth to resize the wheels (this native sets the collider width affecting physics while SetVehicleWheelWidth will change visual width).
</summary>
<param name="vehicle">The vehicle to obtain data for.</param>
<param name="wheelIndex">Index of wheel, 0-3.</param>
<param name="value">Width of tire collider.</param>
	]]

native "SET_VEHICLE_WHEEL_TRACTION_VECTOR_LENGTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "length" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets the traction vector length of a wheel.
Max number of wheels can be retrieved with the native GET_VEHICLE_NUMBER_OF_WHEELS.
</summary>
	]]

native "SET_VEHICLE_WHEEL_WIDTH"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		float "width" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
Sets vehicle's wheels' width (width is the same for all the wheels, cannot get/set specific wheel of vehicle).
Only works on non-default wheels.
Returns whether change was successful (can be false if trying to set width for non-default wheels).
</summary>
<param name="vehicle">The vehicle to set data for.</param>
<param name="width">Width of the wheels (usually between 0.1 and 1.5 is reasonable).</param>
<returns>Bool - whether change was successful or not</returns>
	]]

native "SET_VEHICLE_WHEEL_X_OFFSET"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "offset" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Adjusts the offset of the specified wheel relative to the wheel's axle center.
Needs to be called every frame in order to function properly, as GTA will reset the offset otherwise.
This function can be especially useful to set the track width of a vehicle, for example:

```
function SetVehicleFrontTrackWidth(vehicle, width)
SetVehicleWheelXOffset(vehicle, 0, -width/2)
SetVehicleWheelXOffset(vehicle, 1, width/2)
end
```
</summary>
	]]

native "SET_VEHICLE_WHEEL_Y_ROTATION"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "wheelIndex" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	alias "SET_VEHICLE_WHEEL_XROT"
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
	]]

native "SET_VEHICLE_WHEELIE_STATE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Example script: https://pastebin.com/J6XGbkCW

List of known states:

```
1: Not wheeling.
65: Vehicle is ready to do wheelie (burnouting).
129: Vehicle is doing wheelie.
```
</summary>
<param name="vehicle">Vehicle</param>
<param name="state">Wheelie state</param>
	]]

native "SET_VEHICLE_XENON_LIGHTS_CUSTOM_COLOR"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
		int "red" [=[ {} ]=],
		int "green" [=[ {} ]=],
		int "blue" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets custom vehicle xenon lights color, allowing to use RGB palette. The game will ignore lights color set by [\_SET_VEHICLE_XENON_LIGHTS_COLOR](#\_0xE41033B25D003A07) when custom color is active. This native is not synced between players. Requires xenon lights mod to be set on vehicle.
</summary>
<param name="vehicle">The vehicle handle.</param>
<param name="red">Red color (0-255).</param>
<param name="green">Green color (0-255).</param>
<param name="blue">Blue color (0-255).</param>
	]]

native "SET_VEHICLE_XMAS_SNOW_FACTOR"
	arguments {
		float "gripFactor" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<param name="gripFactor">amount of grip strength vehicle wheels have when xmas weather is active, 0.0 being normal weather grip. 0.2 is the default.</param>
	]]

native "SET_VISUAL_SETTING_FLOAT"
	arguments {
		charPtr "name" [=[ {} ]=],
		float "value" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Overrides a floating point value from `visualsettings.dat` temporarily.
</summary>
<param name="name">The name of the value to set, such as `pedLight.color.red`.</param>
<param name="value">The value to write.</param>
	]]

native "SET_WATER_AREA_CLIP_RECT"
	arguments {
		int "minX" [=[ {} ]=],
		int "minY" [=[ {} ]=],
		int "maxX" [=[ {} ]=],
		int "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Sets world clip boundaries for water quads file (water.xml, water_heistisland.xml)
Used internally by LOAD_GLOBAL_WATER_FILE
</summary>
	]]

native "SET_WATER_QUAD_ALPHA"
	arguments {
		int "waterQuad" [=[ {} ]=],
		int "a0" [=[ {} ]=],
		int "a1" [=[ {} ]=],
		int "a2" [=[ {} ]=],
		int "a3" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="a0">The a0 level</param>
<param name="a1">The a1 level</param>
<param name="a2">The a2 level</param>
<param name="a3">The a3 level</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_BOUNDS"
	arguments {
		int "waterQuad" [=[ {} ]=],
		int "minX" [=[ {} ]=],
		int "minY" [=[ {} ]=],
		int "maxX" [=[ {} ]=],
		int "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This native allows you to update the bounds of a specified water quad index.
</summary>
<param name="waterQuad">The water quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_HAS_LIMITED_DEPTH"
	arguments {
		int "waterQuad" [=[ {} ]=],
		BOOL "hasLimitedDepth" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="hasLimitedDepth">Unknown effect</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_IS_INVISIBLE"
	arguments {
		int "waterQuad" [=[ {} ]=],
		BOOL "isInvisible" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="isInvisible">Unknown effect</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_LEVEL"
	arguments {
		int "waterQuad" [=[ {} ]=],
		float "level" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="level">The water level inside the water quad</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_NO_STENCIL"
	arguments {
		int "waterQuad" [=[ {} ]=],
		bool "noStencil" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waterQuad">The water quad index</param>
<param name="noStencil">Unknown effect</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WATER_QUAD_TYPE"
	arguments {
		int "waterQuad" [=[ {} ]=],
		int "type" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This native allows you to update the water quad type.

Valid type definitions:

*   **0** Square
*   **1** Right triangle where the 90 degree angle is at maxX, minY
*   **2** Right triangle where the 90 degree angle is at minX, minY
*   **3** Right triangle where the 90 degree angle is at minX, maxY
*   **4** Right triangle where the 90 degree angle is at maxY, maxY
</summary>
<param name="waterQuad">The water quad index</param>
<param name="type">The water quad type</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WAVE_QUAD_AMPLITUDE"
	arguments {
		int "waveQuad" [=[ {} ]=],
		float "amplitude" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<param name="waveQuad">The wave quad index</param>
<param name="amplitude">The amplitude value</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WAVE_QUAD_BOUNDS"
	arguments {
		int "waveQuad" [=[ {} ]=],
		int "minX" [=[ {} ]=],
		int "minY" [=[ {} ]=],
		int "maxX" [=[ {} ]=],
		int "maxY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
This native allows you to update the bounds of a specified water quad index.
</summary>
<param name="waveQuad">The wave quad index</param>
<param name="minX">The minX coordinate</param>
<param name="minY">The minY coordinate</param>
<param name="maxX">The maxX coordinate</param>
<param name="maxY">The maxY coordinate</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WAVE_QUAD_DIRECTION"
	arguments {
		int "waveQuad" [=[ {} ]=],
		float "directionX" [=[ {} ]=],
		float "directionY" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "BOOL"
	doc [[!
<summary>
directionX/Y should be constrained between -1.0 and 1.0
A positive value will create the wave starting at min and rolling towards max
A negative value will create the wave starting at max and rolling towards min
Applying both values allows you to make diagonal waves
</summary>
<param name="waveQuad">The wave quad index</param>
<param name="directionX">The minX coordinate</param>
<param name="directionY">The minY coordinate</param>
<returns>Returns true on success.</returns>
	]]

native "SET_WEAPON_ACCURACY_SPREAD"
	arguments {
		Hash "weaponHash" [=[ {} ]=],
		float "spread" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for the accuracy spread of a weapon.
</summary>
<param name="weaponHash">Weapon name hash.</param>
<param name="spread">Accuracy spread</param>
	]]

native "SET_WEAPON_RECOIL_SHAKE_AMPLITUDE"
	arguments {
		Hash "weaponHash" [=[ {} ]=],
		float "amplitude" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
A setter for the recoil shake amplitude of a weapon.
</summary>
<param name="weaponHash">Weapon name hash.</param>
<param name="amplitude">Recoil shake amplitude</param>
	]]

native "SET_WEAPONS_NO_AIM_BLOCKING"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables weapons aim blocking due to environment for local player.
For non-player peds [SET_PED_ENABLE_WEAPON_BLOCKING](#\_0x97A790315D3831FD) can be used.
</summary>
<param name="state">On/Off</param>
	]]

native "SET_WEAPONS_NO_AUTORELOAD"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables the game's built-in auto-reloading.
</summary>
<param name="state">On/Off</param>
	]]

native "SET_WEAPONS_NO_AUTOSWAP"
	arguments {
		BOOL "state" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Disables autoswapping to another weapon when the current weapon runs out of ammo.
</summary>
<param name="state">On/Off</param>
	]]

native "SET_WEATHER_CYCLE_ENTRY"
	arguments {
		int "index" [=[ {} ]=],
		charPtr "typeName" [=[ {} ]=],
		int "timeMult" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "BOOL"
	doc [[!
<param name="index">The index of the entry to set. Must be between 0 and 255</param>
<param name="typeName">The name of the weather type for this cycle</param>
<param name="timeMult">The relative duration of this cycle, which is multiplied by `msPerCycle` during ['APPLY_WEATHER_CYCLES'](#\_0x3422291C). Must be between 1 and 255</param>
<returns>Returns true if all parameters were valid, otherwise false.</returns>
	]]

native "SET_WEATHER_OWNED_BY_NETWORK"
	arguments {
		BOOL "network" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Sets whether or not the weather should be owned by the network subsystem.

To be able to use [\_SET_WEATHER_TYPE_TRANSITION](#\_0x578C752848ECFA0C), this has to be set to false.
</summary>
<param name="network">true to let the network control weather, false to not use network weather behavior.</param>
	]]

native "SET_WET_CLOTH_PIN_RADIUS_SCALE"
	arguments {
		float "scale" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Modifies the radius scale used in the simulation of wet cloth physics.
This affects how cloth behaves when wet, changing how it sticks or reacts to movement.
</summary>
<param name="scale">A value that controls the wet cloth radius scale, default: 0.3, maximum: 1.0(used for dry cloth by default), lower values increase the adhesion effect of wet cloth, making it cling more tightly to the surface.</param>
	]]

native "SHUTDOWN_LOADING_SCREEN_NUI"
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Shuts down the `loadingScreen` NUI frame, similarly to `SHUTDOWN_LOADING_SCREEN`.
</summary>
	]]

native "START_FIND_EXTERNAL_KVP"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
		charPtr "prefix" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "int"
	doc [[!
<summary>
Equivalent of [START_FIND_KVP](#\_0xDD379006), but for another resource than the current one.
</summary>
<param name="resourceName">The resource to try finding the key/values for</param>
<param name="prefix">A prefix match</param>
<returns>A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)</returns>
	]]

native "START_FIND_KVP"
	arguments {
		charPtr "prefix" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "int"
	doc [[!
<param name="prefix">A prefix match</param>
<returns>A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)</returns>
	]]

native "START_RESOURCE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "STATE_BAG_HAS_KEY"
	arguments {
		charPtr "bagName" [=[ {} ]=],
		charPtr "key" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "bool"
	doc [[!
<param name="bagName">The name of the bag.</param>
<param name="key">The key used to check data existence.</param>
<returns>Returns true if the data associated with the specified key exists; otherwise, returns false.</returns>
	]]

native "STOP_RESOURCE"
	arguments {
		charPtr "resourceName" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "TASK_COMBAT_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Ped "targetPed" [=[ {} ]=],
		int "p2" [=[ {} ]=],
		int "p3" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Makes the specified ped attack the target ped.
p2 should be 0
p3 should be 16
```

**This is the server-side RPC native equivalent of the client native [TASK_COMBAT_PED](?\_0xF166E48407BAC484).**
</summary>
	]]

native "TASK_DRIVE_BY"
	arguments {
		Ped "driverPed" [=[ {} ]=],
		Ped "targetPed" [=[ {} ]=],
		Vehicle "targetVehicle" [=[ {} ]=],
		float "targetX" [=[ {} ]=],
		float "targetY" [=[ {} ]=],
		float "targetZ" [=[ {} ]=],
		float "distanceToShoot" [=[ {} ]=],
		int "pedAccuracy" [=[ {} ]=],
		BOOL "p8" [=[ {} ]=],
		Hash "firingPattern" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Example:
TASK::TASK_DRIVE_BY(l_467[1/*22*/], PLAYER::PLAYER_PED_ID(), 0, 0.0, 0.0, 2.0, 300.0, 100, 0, ${firing_pattern_burst_fire_driveby});
Needs working example. Doesn't seem to do anything.
I marked p2 as targetVehicle as all these shooting related tasks seem to have that in common.
I marked p6 as distanceToShoot as if you think of GTA's Logic with the native SET_VEHICLE_SHOOT natives, it won't shoot till it gets within a certain distance of the target.
I marked p7 as pedAccuracy as it seems it's mostly 100 (Completely Accurate), 75, 90, etc. Although this could be the ammo count within the gun, but I highly doubt it. I will change this comment once I find out if it's ammo count or not.
```

**This is the server-side RPC native equivalent of the client native [TASK_DRIVE_BY](?\_0x2F8AF0E82773A171).**
</summary>
	]]

native "TASK_ENTER_VEHICLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		Vehicle "vehicle" [=[ {} ]=],
		int "timeout" [=[ {} ]=],
		int "seatIndex" [=[ {} ]=],
		float "speed" [=[ {} ]=],
		int "flag" [=[ {} ]=],
		Any "p6" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
speed 1.0 = walk, 2.0 = run
p5 1 = normal, 3 = teleport to vehicle, 8 = normal/carjack ped from seat, 16 = teleport directly into vehicle
p6 is always 0
```

**This is the server-side RPC native equivalent of the client native [TASK_ENTER_VEHICLE](?\_0xC20E50AA46D09CA8).**
</summary>
<param name="seatIndex">See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).</param>
	]]

native "TASK_EVERYONE_LEAVE_VEHICLE"
	arguments {
		Vehicle "vehicle" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
TASK_EVERYONE_LEAVE_VEHICLE

**This is the server-side RPC native equivalent of the client native [TASK_EVERYONE_LEAVE_VEHICLE](?\_0x7F93691AB4B92272).**
</summary>
	]]

native "TASK_GO_STRAIGHT_TO_COORD"
	arguments {
		Ped "ped" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "speed" [=[ {} ]=],
		int "timeout" [=[ {} ]=],
		float "targetHeading" [=[ {} ]=],
		float "distanceToSlide" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
TASK_GO_STRAIGHT_TO_COORD

**This is the server-side RPC native equivalent of the client native [TASK_GO_STRAIGHT_TO_COORD](?\_0xD76B57B44F1E6F8B).**
</summary>
<param name="ped">The ped handle.</param>
<param name="x">The x coordinate.</param>
<param name="y">The y coordinate.</param>
<param name="z">The z coordinate.</param>
<param name="speed">The ped movement speed.</param>
<param name="timeout">\-1 , other values appear to break the ped movement.</param>
<param name="targetHeading">The heading you want the ped to be on x,y,z coord.</param>
<param name="distanceToSlide">The distance from x,y,z where the ped will start sliding.</param>
	]]

native "TASK_GO_TO_COORD_ANY_MEANS"
	arguments {
		Ped "ped" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "fMoveBlendRatio" [=[ {} ]=],
		Vehicle "vehicle" [=[ {} ]=],
		BOOL "bUseLongRangeVehiclePathing" [=[ {} ]=],
		int "drivingFlags" [=[ {} ]=],
		float "fMaxRangeToShootTargets" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Tells a ped to go to a coord by any means.

```cpp
enum eDrivingMode {
DF_StopForCars = 1,
DF_StopForPeds = 2,
DF_SwerveAroundAllCars = 4,
DF_SteerAroundStationaryCars = 8,
DF_SteerAroundPeds = 16,
DF_SteerAroundObjects = 32,
DF_DontSteerAroundPlayerPed = 64,
DF_StopAtLights = 128,
DF_GoOffRoadWhenAvoiding = 256,
DF_DriveIntoOncomingTraffic = 512,
DF_DriveInReverse = 1024,
// If pathfinding fails, cruise randomly instead of going on a straight line
DF_UseWanderFallbackInsteadOfStraightLine = 2048,
DF_AvoidRestrictedAreas = 4096,
// These only work on MISSION_CRUISE
DF_PreventBackgroundPathfinding = 8192,
DF_AdjustCruiseSpeedBasedOnRoadSpeed = 16384,
DF_UseShortCutLinks =  262144,
DF_ChangeLanesAroundObstructions = 524288,
// cruise tasks ignore this anyway--only used for goto's
DF_UseSwitchedOffNodes =  2097152,
// if you're going to be primarily driving off road
DF_PreferNavmeshRoute =  4194304,
// Only works for planes using MISSION_GOTO, will cause them to drive along the ground instead of fly
DF_PlaneTaxiMode =  8388608,
DF_ForceStraightLine = 16777216,
DF_UseStringPullingAtJunctions = 33554432,
DF_AvoidHighways = *********,
DF_ForceJoinInRoadDirection = 1073741824,
// Standard driving mode. stops for cars, peds, and lights, goes around stationary obstructions
DRIVINGMODE_STOPFORCARS = 786603, // DF_StopForCars|DF_StopForPeds|DF_SteerAroundObjects|DF_SteerAroundStationaryCars|DF_StopAtLights|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,		// Obey lights too
// Like the above, but doesn't steer around anything in its way - will only wait instead.
DRIVINGMODE_STOPFORCARS_STRICT = 262275, // DF_StopForCars|DF_StopForPeds|DF_StopAtLights|DF_UseShortCutLinks, // Doesn't deviate an inch.
// Default "alerted" driving mode. drives around everything, doesn't obey lights
DRIVINGMODE_AVOIDCARS = 786469, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars,
// Very erratic driving. difference between this and AvoidCars is that it doesn't use the brakes at ALL to help with steering
DRIVINGMODE_AVOIDCARS_RECKLESS = 786468, // DF_SwerveAroundAllCars|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions,
// Smashes through everything
DRIVINGMODE_PLOUGHTHROUGH = 262144, // DF_UseShortCutLinks
// Drives normally except for the fact that it ignores lights
DRIVINGMODE_STOPFORCARS_IGNORELIGHTS = 786475, // DF_StopForCars|DF_SteerAroundStationaryCars|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions
// Try to swerve around everything, but stop for lights if necessary
DRIVINGMODE_AVOIDCARS_OBEYLIGHTS = 786597, // DF_SwerveAroundAllCars|DF_StopAtLights|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
// Swerve around cars, be careful around peds, and stop for lights
DRIVINGMODE_AVOIDCARS_STOPFORPEDS_OBEYLIGHTS = 786599 // DF_SwerveAroundAllCars|DF_StopAtLights|DF_StopForPeds|DF_SteerAroundObjects|DF_UseShortCutLinks|DF_ChangeLanesAroundObstructions|DF_StopForCars
};
```

**This is the server-side RPC native equivalent of the client native [TASK_GO_TO_COORD_ANY_MEANS](?\_0x5BC448CB78FA3E88).**
</summary>
<param name="ped">The `Ped` Handle.</param>
<param name="x">The goto target coordinate.</param>
<param name="y">The goto target coordinate.</param>
<param name="z">The goto target coordinate.</param>
<param name="fMoveBlendRatio">0.0 = still, 1.0 = walk, 2.0 = run, 3.0 = sprint.</param>
<param name="vehicle">If defined, the pedestrian will only move if said vehicle exists. If you don't want any sort of association, just set it to `0`.</param>
<param name="bUseLongRangeVehiclePathing">Setting to `true` tells the vehicle to use longrange vehicle pathing.</param>
<param name="drivingFlags">See `eDrivingMode` enum.</param>
<param name="fMaxRangeToShootTargets">Determines the maximum distance at which the `Ped` will engage in combat with threatening targets.</param>
	]]

native "TASK_GO_TO_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
		Entity "target" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		float "distance" [=[ {} ]=],
		float "speed" [=[ {} ]=],
		float "p5" [=[ {} ]=],
		int "p6" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
The entity will move towards the target until time is over (duration) or get in target's range (distance). p5 and p6 are unknown, but you could leave p5 = 1073741824 or 100 or even 0 (didn't see any difference but on the decompiled scripts, they use 1073741824 mostly) and p6 = 0
Note: I've only tested it on entity -> ped and target -> vehicle. It could work differently on other entities, didn't try it yet.
Example: TASK::TASK_GO_TO_ENTITY(pedHandle, vehicleHandle, 5000, 4.0, 100, 1073741824, 0)
Ped will run towards the vehicle for 5 seconds and stop when time is over or when he gets 4 meters(?) around the vehicle (with duration = -1, the task duration will be ignored).
```

**This is the server-side RPC native equivalent of the client native [TASK_GO_TO_ENTITY](?\_0x6A071245EB0D1882).**
</summary>
	]]

native "TASK_HANDS_UP"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		Ped "facingPed" [=[ {} ]=],
		int "p3" [=[ {} ]=],
		BOOL "p4" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
In the scripts, p3 was always -1.
p3 seems to be duration or timeout of turn animation.
Also facingPed can be 0 or -1 so ped will just raise hands up.
```

**This is the server-side RPC native equivalent of the client native [TASK_HANDS_UP](?\_0xF2EAB31979A7F910).**
</summary>
	]]

native "TASK_LEAVE_ANY_VEHICLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "p1" [=[ {} ]=],
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Flags are the same flags used in [`TASK_LEAVE_VEHICLE`](#\_0xD3DBCE61A490BE02)

**This is the server-side RPC native equivalent of the client native [TASK_LEAVE_ANY_VEHICLE](?\_0x504D54DF3F6F2247).**
</summary>
	]]

native "TASK_LEAVE_VEHICLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		Vehicle "vehicle" [=[ {} ]=],
		int "flags" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Flags from decompiled scripts:
0 = normal exit and closes door.
1 = normal exit and closes door.
16 = teleports outside, door kept closed.  (This flag does not seem to work for the front seats in buses, NPCs continue to exit normally)
64 = normal exit and closes door, maybe a bit slower animation than 0.
256 = normal exit but does not close the door.
4160 = ped is throwing himself out, even when the vehicle is still.
262144 = ped moves to passenger seat first, then exits normally
Others to be tried out: 320, 512, 131072.
```

**This is the server-side RPC native equivalent of the client native [TASK_LEAVE_VEHICLE](?\_0xD3DBCE61A490BE02).**
</summary>
	]]

native "TASK_PLAY_ANIM"
	arguments {
		Ped "ped" [=[ {} ]=],
		charPtr "animDictionary" [=[ {} ]=],
		charPtr "animationName" [=[ {} ]=],
		float "blendInSpeed" [=[ {} ]=],
		float "blendOutSpeed" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		int "flag" [=[ {} ]=],
		float "playbackRate" [=[ {} ]=],
		BOOL "lockX" [=[ {} ]=],
		BOOL "lockY" [=[ {} ]=],
		BOOL "lockZ" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
[Animations list](https://alexguirre.github.io/animations-list/)

```cpp
enum eScriptedAnimFlags
{
AF_LOOPING = 1,
AF_HOLD_LAST_FRAME = 2,
AF_REPOSITION_WHEN_FINISHED = 4,
AF_NOT_INTERRUPTABLE = 8,
AF_UPPERBODY = 16,
AF_SECONDARY = 32,
AF_REORIENT_WHEN_FINISHED = 64,
AF_ABORT_ON_PED_MOVEMENT = 128,
AF_ADDITIVE = 256,
AF_TURN_OFF_COLLISION = 512,
AF_OVERRIDE_PHYSICS = 1024,
AF_IGNORE_GRAVITY = 2048,
AF_EXTRACT_INITIAL_OFFSET = 4096,
AF_EXIT_AFTER_INTERRUPTED = 8192,
AF_TAG_SYNC_IN = 16384,
AF_TAG_SYNC_OUT = 32768,
AF_TAG_SYNC_CONTINUOUS = 65536,
AF_FORCE_START = 131072,
AF_USE_KINEMATIC_PHYSICS = 262144,
AF_USE_MOVER_EXTRACTION = 524288,
AF_HIDE_WEAPON = 1048576,
AF_ENDS_IN_DEAD_POSE = 2097152,
AF_ACTIVATE_RAGDOLL_ON_COLLISION = 4194304,
AF_DONT_EXIT_ON_DEATH = 8388608,
AF_ABORT_ON_WEAPON_DAMAGE = 16777216,
AF_DISABLE_FORCED_PHYSICS_UPDATE = 33554432,
AF_PROCESS_ATTACHMENTS_ON_START = 67108864,
AF_EXPAND_PED_CAPSULE_FROM_SKELETON = 134217728,
AF_USE_ALTERNATIVE_FP_ANIM = 268435456,
AF_BLENDOUT_WRT_LAST_FRAME = *********,
AF_USE_FULL_BLENDING = 1073741824
}
```

**This is the server-side RPC native equivalent of the client native [TASK_PLAY_ANIM](?\_0xEA47FE3719165B94).**
</summary>
<param name="ped">The ped you want to play the animation</param>
<param name="animDictionary">The animation dictionary</param>
<param name="animationName">The animation name</param>
<param name="blendInSpeed">The speed at which the animation blends in. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant</param>
<param name="blendOutSpeed">The speed at which the animation blends out. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant</param>
<param name="duration">The duration of the animation in milliseconds. -1 will play the animation until canceled</param>
<param name="flag">The animation flags (see enum)</param>
<param name="playbackRate">The playback rate (between 0.0 and 1.0)</param>
	]]

native "TASK_PLAY_ANIM_ADVANCED"
	arguments {
		Ped "ped" [=[ {} ]=],
		charPtr "animDictionary" [=[ {} ]=],
		charPtr "animationName" [=[ {} ]=],
		float "posX" [=[ {} ]=],
		float "posY" [=[ {} ]=],
		float "posZ" [=[ {} ]=],
		float "rotX" [=[ {} ]=],
		float "rotY" [=[ {} ]=],
		float "rotZ" [=[ {} ]=],
		float "blendInSpeed" [=[ {} ]=],
		float "blendOutSpeed" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		Any "flag" [=[ {} ]=],
		float "animTime" [=[ {} ]=],
		Any "p14" [=[ {} ]=],
		Any "p15" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Similar in functionality to [`TASK_PLAY_ANIM`](#\_0xEA47FE3719165B94), except the position and rotation parameters let you specify the initial position and rotation of the task. The ped is teleported to the position specified.
[Animations list](https://alexguirre.github.io/animations-list/)

**This is the server-side RPC native equivalent of the client native [TASK_PLAY_ANIM_ADVANCED](?\_0x83CDB10EA29B370B).**
</summary>
<param name="ped">The ped you want to play the animation</param>
<param name="animDictionary">The animation dictionary</param>
<param name="animationName">The animation name</param>
<param name="posX">Initial X position of the task</param>
<param name="posY">Initial Y position of the task</param>
<param name="posZ">Initial Z position of the task</param>
<param name="rotX">Initial X rotation of the task</param>
<param name="rotY">Initial Y rotation of the task</param>
<param name="rotZ">Initial Z rotation of the task</param>
<param name="blendInSpeed">The speed at which the animation blends in. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant</param>
<param name="blendOutSpeed">The speed at which the animation blends out. Lower is slower and higher is faster, 1.0 is normal, 8.0 is basically instant</param>
<param name="duration">The duration of the animation in milliseconds. -1 will play the animation until canceled</param>
<param name="flag">See [`TASK_PLAY_ANIM`](#\_0xEA47FE3719165B94)</param>
<param name="animTime">Value between 0.0 and 1.0, lets you start an animation from the given point</param>
	]]

native "TASK_REACT_AND_FLEE_PED"
	arguments {
		Ped "ped" [=[ {} ]=],
		Ped "fleeTarget" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
TASK_REACT_AND_FLEE_PED

**This is the server-side RPC native equivalent of the client native [TASK_REACT_AND_FLEE_PED](?\_0x72C896464915D1B1).**
</summary>
	]]

native "TASK_SHOOT_AT_COORD"
	arguments {
		Ped "ped" [=[ {} ]=],
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		Hash "firingPattern" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Firing Pattern Hash Information: https://pastebin.com/Px036isB
```

**This is the server-side RPC native equivalent of the client native [TASK_SHOOT_AT_COORD](?\_0x46A6CC01E0826106).**
</summary>
	]]

native "TASK_SHOOT_AT_ENTITY"
	arguments {
		Entity "entity" [=[ {} ]=],
		Entity "target" [=[ {} ]=],
		int "duration" [=[ {} ]=],
		Hash "firingPattern" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
//this part of the code is to determine at which entity the player is aiming, for example if you want to create a mod where you give orders to peds
Entity aimedentity;
Player player = PLAYER::PLAYER_ID();
PLAYER::_GET_AIMED_ENTITY(player, &aimedentity);
//bg is an array of peds
TASK::TASK_SHOOT_AT_ENTITY(bg[i], aimedentity, 5000, MISC::GET_HASH_KEY("FIRING_PATTERN_FULL_AUTO"));
in practical usage, getting the entity the player is aiming at and then task the peds to shoot at the entity, at a button press event would be better.
Firing Pattern Hash Information: https://pastebin.com/Px036isB
```

**This is the server-side RPC native equivalent of the client native [TASK_SHOOT_AT_ENTITY](?\_0x08DA95E8298AE772).**
</summary>
	]]

native "TASK_WARP_PED_INTO_VEHICLE"
	arguments {
		Ped "ped" [=[ {} ]=],
		Vehicle "vehicle" [=[ {} ]=],
		int "seatIndex" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
NativeDB Introduced: v323
```

Warp a ped into a vehicle.
**Note**: It's better to use [`TASK_ENTER_VEHICLE`](#\_0xC20E50AA46D09CA8) with the flag "warp" flag instead of this native.

**This is the server-side RPC native equivalent of the client native [TASK_WARP_PED_INTO_VEHICLE](?\_0x9A7D091411C5F684).**
</summary>
<param name="ped">The Ped to be warped into the vehicle.</param>
<param name="vehicle">The target vehicle into which the ped will be warped.</param>
<param name="seatIndex">See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669).</param>
	]]

native "TEMP_BAN_PLAYER"
	arguments {
		charPtr "playerSrc" [=[ {} ]=],
		charPtr "reason" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
	]]

native "TRIGGER_CLIENT_EVENT_INTERNAL"
	arguments {
		charPtr "eventName" [=[ {} ]=],
		charPtr "eventTarget" [=[ {} ]=],
		charPtr "eventPayload" [=[ {} ]=],
		int "payloadLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
The backing function for TriggerClientEvent.
</summary>
	]]

native "TRIGGER_EVENT_INTERNAL"
	arguments {
		charPtr "eventName" [=[ {} ]=],
		charPtr "eventPayload" [=[ {} ]=],
		int "payloadLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "shared"
	returns "void"
	doc [[!
<summary>
The backing function for TriggerEvent.
</summary>
	]]

native "TRIGGER_LATENT_CLIENT_EVENT_INTERNAL"
	arguments {
		charPtr "eventName" [=[ {} ]=],
		charPtr "eventTarget" [=[ {} ]=],
		charPtr "eventPayload" [=[ {} ]=],
		int "payloadLength" [=[ {} ]=],
		int "bps" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
The backing function for TriggerLatentClientEvent.
</summary>
	]]

native "TRIGGER_LATENT_SERVER_EVENT_INTERNAL"
	arguments {
		charPtr "eventName" [=[ {} ]=],
		charPtr "eventPayload" [=[ {} ]=],
		int "payloadLength" [=[ {} ]=],
		int "bps" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
The backing function for TriggerLatentServerEvent.
</summary>
	]]

native "TRIGGER_SERVER_EVENT_INTERNAL"
	arguments {
		charPtr "eventName" [=[ {} ]=],
		charPtr "eventPayload" [=[ {} ]=],
		int "payloadLength" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
The backing function for TriggerServerEvent.
</summary>
	]]

native "UNREGISTER_RAW_NUI_CALLBACK"
	arguments {
		charPtr "callbackType" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	returns "void"
	doc [[!
<summary>
Will unregister and cleanup a registered NUI callback handler.

Use along side the REGISTER_RAW_NUI_CALLBACK native.
</summary>
<param name="callbackType">The callback type to target</param>
	]]

native "UPDATE_MAPDATA_ENTITY"
	arguments {
		int "mapdata" [=[ {} ]=],
		int "entity" [=[ {} ]=],
		object "entityDef" [=[ {} ]=],
	}
	ns "CFX"
    apiset "client"
	game "gta5"
	returns "void"
	doc [[!
<summary>
Transiently updates the entity with the specified mapdata index and entity index.
This function supports SDK infrastructure and is not intended to be used directly from your code.
</summary>
<param name="mapdata">A fwMapData index from GET_MAPDATA_FROM_HASH_KEY.</param>
<param name="entity">An entity index from GET_ENTITY_INDEX_FROM_MAPDATA.</param>
<param name="entityDef">The new entity definition in fwEntityDef format.</param>
	]]

native "VERIFY_PASSWORD_HASH"
	arguments {
		charPtr "password" [=[ {} ]=],
		charPtr "hash" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "BOOL"
	doc [[!
	]]

native "WAS_EVENT_CANCELED"
	ns "CFX"
    apiset "shared"
	returns "BOOL"
	doc [[!
<summary>
Returns whether or not the currently executing event was canceled.
</summary>
<returns>A boolean.</returns>
	]]

native "_ADD_BLIP_FOR_AREA"
	arguments {
		float "x" [=[ {} ]=],
		float "y" [=[ {} ]=],
		float "z" [=[ {} ]=],
		float "width" [=[ {} ]=],
		float "height" [=[ {} ]=],
	}
	alias "0xCE5D0E5E315DB238"
	ns "CFX"
    apiset "server"
	returns "Blip"
	doc [[!
<summary>
Adds a rectangular blip for the specified coordinates/area.
It is recommended to use [SET_BLIP_ROTATION](#\_0xF87683CDF73C3F6E) and [SET_BLIP_COLOUR](#\_0x03D7FB09E75D6B7E) to make the blip not rotate along with the camera.
By default, the blip will show as a *regular* blip with the specified color/sprite if it is outside of the minimap view.
Example image:
![minimap](https://i.imgur.com/qLbXWcQ.png)
![big map](https://i.imgur.com/0j7O7Rh.png)
(Native name is *likely* to actually be ADD_BLIP_FOR_AREA, but due to the usual reasons this can't be confirmed)

**This is the server-side RPC native equivalent of the client native [\_ADD_BLIP_FOR_AREA](?\_0xCE5D0E5E315DB238).**
</summary>
<param name="x">The X coordinate of the center of the blip.</param>
<param name="y">The Y coordinate of the center of the blip.</param>
<param name="z">The Z coordinate of the center of the blip.</param>
<param name="width">The width of the blip.</param>
<param name="height">The height of the blip.</param>
<returns>A handle to the blip.</returns>
	]]

native "_SET_PED_EYE_COLOR"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Used for freemode (online) characters.
Indices:

1.  black
2.  very light blue/green
3.  dark blue
4.  brown
5.  darker brown
6.  light brown
7.  blue
8.  light blue
9.  pink
10. yellow
11. purple
12. black
13. dark green
14. light brown
15. yellow/black pattern
16. light colored spiral pattern
17. shiny red
18. shiny half blue/half red
19. half black/half light blue
20. white/red perimter
21. green snake
22. red snake
23. dark blue snake
24. dark yellow
25. bright yellow
26. all black
27. red small pupil
28. devil blue/black
29. white small pupil
30. glossed over

**This is the server-side RPC native equivalent of the client native [\_SET_PED_EYE_COLOR](?\_0x50B56988B170AFDF).**
</summary>
	]]

native "_SET_PED_FACE_FEATURE"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "index" [=[ {} ]=],
		float "scale" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
Sets the various freemode face features, e.g. nose length, chin shape.
**Indexes (From 0 to 19):**
Parentheses indicate morph scale/direction as in (-1.0 to 1.0)

*   **0**: Nose Width (Thin/Wide)
*   **1**: Nose Peak (Up/Down)
*   **2**: Nose Length (Long/Short)
*   **3**: Nose Bone Curveness (Crooked/Curved)
*   **4**: Nose Tip (Up/Down)
*   **5**: Nose Bone Twist (Left/Right)
*   **6**: Eyebrow (Up/Down)
*   **7**: Eyebrow (In/Out)
*   **8**: Cheek Bones (Up/Down)
*   **9**: Cheek Sideways Bone Size (In/Out)
*   **10**: Cheek Bones Width (Puffed/Gaunt)
*   **11**: Eye Opening (Both) (Wide/Squinted)
*   **12**: Lip Thickness (Both) (Fat/Thin)
*   **13**: Jaw Bone Width (Narrow/Wide)
*   **14**: Jaw Bone Shape (Round/Square)
*   **15**: Chin Bone (Up/Down)
*   **16**: Chin Bone Length (In/Out or Backward/Forward)
*   **17**: Chin Bone Shape (Pointed/Square)
*   **18**: Chin Hole (Chin Bum)
*   **19**: Neck Thickness (Thin/Thick)
    **Note:**
    You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.

**This is the server-side RPC native equivalent of the client native [\_SET_PED_FACE_FEATURE](?\_0x71A5C1DBA060049E).**
</summary>
<param name="ped">The ped entity</param>
<param name="index">An integer ranging from 0 to 19</param>
<param name="scale">A float ranging from -1.0 to 1.0</param>
	]]

native "_SET_PED_HEAD_OVERLAY_COLOR"
	arguments {
		Ped "ped" [=[ {} ]=],
		int "overlayID" [=[ {} ]=],
		int "colorType" [=[ {} ]=],
		int "colorID" [=[ {} ]=],
		int "secondColorID" [=[ {} ]=],
	}
	ns "CFX"
    apiset "server"
	returns "void"
	doc [[!
<summary>
```
Used for freemode (online) characters.
Called after SET_PED_HEAD_OVERLAY().
```

**Note:**
You may need to call [`SetPedHeadBlendData`](#\_0x9414E18B9434C2FE) prior to calling this native in order for it to work.

**This is the server-side RPC native equivalent of the client native [\_SET_PED_HEAD_OVERLAY_COLOR](?\_0x497BF74A7B9CB952).**
</summary>
<param name="ped">The ped entity</param>
<param name="overlayID">An integer representing the overlay id</param>
<param name="colorType">1 for eyebrows, beards, makeup, and chest hair; 2 for blush and lipstick; and 0 otherwise, though not called in those cases.</param>
<param name="colorID">An integer representing the primary color id</param>
<param name="secondColorID">An integer representing the secondary color id</param>
	]]
