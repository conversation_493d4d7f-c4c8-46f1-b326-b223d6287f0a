#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
top level: yes

---
snippet: "
  var a = { func: function() { } };
"
frame size: 3
parameter count: 1
bytecode array length: 31
bytecodes: [
                B(LdaConstant), U8(0),
                B(Star1),
                B(Mov), R(closure), R(2),
  /*    0 E> */ B(CallRuntime), U16(Runtime::kDeclareGlobals), R(1), U8(2),
  /*    8 S> */ B(CreateObjectLiteral), U8(1), U8(0), U8(41),
                B(Star1),
  /*   16 E> */ B(CreateClosure), U8(2), U8(0), U8(0),
                B(DefineNamedOwnProperty), R(1), U8(3), U8(1),
                B(Ldar), R(1),
  /*    8 E> */ B(StaGlobal), U8(4), U8(3),
                B(LdaUndefined),
  /*   34 S> */ B(Return),
]
constant pool: [
  FIXED_ARRAY_TYPE,
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
]
handlers: [
]

