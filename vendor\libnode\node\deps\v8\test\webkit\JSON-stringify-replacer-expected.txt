# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Test to ensure correct behaviour of replacer functions in JSON.stringify

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS JSON.stringify(object, returnUndefined) is undefined.
PASS JSON.stringify(array, returnUndefined) is undefined.
PASS JSON.stringify(object, returnObjectFor1) is '{"0":0,"1":{},"2":2}'
PASS JSON.stringify(array, returnObjectFor1) is '[0,{},2,null]'
PASS JSON.stringify(object, returnArrayFor1) is '{"0":0,"1":[],"2":2}'
PASS JSON.stringify(array, returnArrayFor1) is '[0,[],2,null]'
PASS JSON.stringify(object, returnUndefinedFor1) is '{"0":0,"2":2}'
PASS JSON.stringify(array, returnUndefinedFor1) is '[0,null,2,null]'
PASS JSON.stringify(object, returnFunctionFor1) is '{"0":0,"2":2}'
PASS JSON.stringify(array, returnFunctionFor1) is '[0,null,2,null]'
PASS JSON.stringify(object, returnNullFor1) is '{"0":0,"1":null,"2":2}'
PASS JSON.stringify(array, returnNullFor1) is '[0,null,2,null]'
PASS JSON.stringify(object, returnStringForUndefined) is '{"0":0,"1":1,"2":2,"3":"undefined value"}'
PASS JSON.stringify(array, returnStringForUndefined) is '[0,1,2,"undefined value"]'
PASS JSON.stringify(object, returnCycleObjectFor1) threw exception TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Object'
    --- property '1' closes the circle.
PASS JSON.stringify(array, returnCycleObjectFor1) threw exception TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Object'
    --- property '1' closes the circle.
PASS JSON.stringify(object, returnCycleArrayFor1) threw exception TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Array'
    --- index 1 closes the circle.
PASS JSON.stringify(array, returnCycleArrayFor1) threw exception TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'Array'
    --- index 1 closes the circle.
PASS successfullyParsed is true

TEST COMPLETE

