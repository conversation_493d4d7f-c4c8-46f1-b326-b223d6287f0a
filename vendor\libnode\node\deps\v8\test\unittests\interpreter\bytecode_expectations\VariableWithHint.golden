#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  var test;
  (function () {
      function foo() {
          let a = typeof('str'); if (a === 'string') {}
          let b = typeof('str'); if (b === 1) {}
          let c = typeof('str'); c = 1; if (c === 'string') {}
          let d = typeof('str');
          if (d === 'string' || d === 'number') {}
          let e = 'hello world';
          if (e == 'string' || e == 'number') {}
          let f = 'hi';
          for (let i = 0; i < 2; ++i) {
              if (f === 'hi') {}
          }
          let g = true;
          if (g === 's') {}
          let j = true;
          let k = j || 's';
          if (k === 's') {}
      }
      foo();
      test = foo;
  })();
"
frame size: 10
parameter count: 1
bytecode array length: 121
bytecodes: [
  /*   62 S> */ B(LdaConstant), U8(0),
                B(TypeOf),
                B(Star0),
  /*   77 S> */ B(LdaConstant), U8(1),
                B(TestReferenceEqual), R(0),
                B(JumpIfFalse), U8(2),
  /*  116 S> */ B(LdaConstant), U8(0),
                B(TypeOf),
                B(Star1),
  /*  131 S> */ B(LdaSmi), I8(1),
  /*  137 E> */ B(TestEqualStrict), R(1), U8(0),
                B(JumpIfFalse), U8(2),
  /*  163 S> */ B(LdaConstant), U8(0),
                B(TypeOf),
                B(Star2),
  /*  178 S> */ B(LdaSmi), I8(1),
                B(Star2),
  /*  185 S> */ B(LdaConstant), U8(1),
  /*  191 E> */ B(TestEqualStrict), R(2), U8(1),
                B(JumpIfFalse), U8(2),
  /*  224 S> */ B(LdaConstant), U8(0),
                B(TypeOf),
                B(Star3),
  /*  247 S> */ B(LdaConstant), U8(1),
                B(TestReferenceEqual), R(3),
                B(JumpIfTrue), U8(9),
                B(LdaConstant), U8(2),
  /*  271 E> */ B(TestEqualStrict), R(3), U8(2),
                B(JumpIfFalse), U8(2),
  /*  304 S> */ B(LdaConstant), U8(3),
                B(Star4),
  /*  327 S> */ B(LdaConstant), U8(1),
                B(TestReferenceEqual), R(4),
                B(JumpIfTrue), U8(9),
                B(LdaConstant), U8(2),
  /*  350 E> */ B(TestEqual), R(4), U8(3),
                B(JumpIfFalse), U8(2),
  /*  382 S> */ B(LdaConstant), U8(4),
                B(Star5),
  /*  409 S> */ B(LdaZero),
                B(Star9),
  /*  414 S> */ B(LdaSmi), I8(2),
  /*  414 E> */ B(TestLessThan), R(9), U8(4),
                B(JumpIfFalse), U8(18),
  /*  438 S> */ B(LdaConstant), U8(4),
  /*  444 E> */ B(TestEqualStrict), R(5), U8(5),
                B(JumpIfFalse), U8(2),
  /*  421 S> */ B(Ldar), R(9),
                B(Inc), U8(6),
                B(Star9),
  /*  396 E> */ B(JumpLoop), U8(19), I8(0), U8(7),
  /*  483 S> */ B(LdaTrue),
                B(Star6),
  /*  497 S> */ B(LdaConstant), U8(5),
  /*  503 E> */ B(TestEqualStrict), R(6), U8(8),
                B(JumpIfFalse), U8(2),
  /*  531 S> */ B(LdaTrue),
                B(Star7),
  /*  553 S> */ B(JumpIfToBooleanTrue), U8(4),
                B(LdaConstant), U8(5),
                B(Star8),
  /*  571 S> */ B(LdaConstant), U8(5),
  /*  577 E> */ B(TestEqualStrict), R(8), U8(9),
                B(JumpIfFalse), U8(2),
                B(LdaUndefined),
  /*  593 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["str"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["number"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["hello world"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["hi"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["s"],
]
handlers: [
]

