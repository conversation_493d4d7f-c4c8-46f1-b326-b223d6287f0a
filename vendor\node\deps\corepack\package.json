{"name": "corepack", "version": "0.9.0", "homepage": "https://github.com/nodejs/corepack#readme", "bugs": {"url": "https://github.com/nodejs/corepack/issues"}, "repository": {"type": "git", "url": "https://github.com/nodejs/corepack.git"}, "license": "MIT", "bin": {"corepack": "./dist/corepack.js", "pnpm": "./dist/pnpm.js", "pnpx": "./dist/pnpx.js", "yarn": "./dist/yarn.js", "yarnpkg": "./dist/yarnpkg.js"}, "packageManager": "yarn@3.0.0", "devDependencies": {"@babel/core": "^7.14.3", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-decorators": "^7.14.2", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.14.0", "@babel/preset-typescript": "^7.13.0", "@types/debug": "^4.1.5", "@types/jest": "^26.0.23", "@types/node": "^13.9.2", "@types/semver": "^7.1.0", "@types/tar": "^4.0.3", "@types/which": "^1.3.2", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^4.2.0", "@yarnpkg/eslint-config": "^0.1.0", "@yarnpkg/fslib": "^2.1.0", "@zkochan/cmd-shim": "^5.0.0", "babel-plugin-dynamic-import-node": "^2.3.3", "clipanion": "^3.0.1", "debug": "^4.1.1", "eslint": "^7.10.0", "eslint-plugin-arca": "^0.9.5", "jest": "^26.0.0", "nock": "^13.0.4", "semver": "^7.1.3", "supports-color": "^7.1.0", "tar": "^6.0.1", "terser-webpack-plugin": "^5.1.2", "ts-loader": "^8.0.2", "ts-node": "^8.10.2", "typescript": "^4.3.2", "v8-compile-cache": "^2.3.0", "webpack": "^5.38.1", "webpack-cli": "^3.3.11", "which": "^2.0.2"}, "scripts": {"build": "rm -rf dist && webpack && ts-node ./mkshims.ts", "corepack": "ts-node ./sources/main.ts", "prepack": "node ./.yarn/releases/*.*js build", "postpack": "rm -rf dist shims", "test": "yarn jest"}, "files": ["dist", "shims", "LICENSE.md"], "publishConfig": {"executableFiles": ["./dist/npm.js", "./dist/npx.js", "./dist/pnpm.js", "./dist/pnpx.js", "./dist/yarn.js", "./dist/yarnpkg.js", "./dist/corepack.js", "./shims/npm", "./shims/npm.ps1", "./shims/npx", "./shims/npx.ps1", "./shims/pnpm", "./shims/pnpm.ps1", "./shims/pnpx", "./shims/pnpx.ps1", "./shims/yarn", "./shims/yarn.ps1", "./shims/yarnpkg", "./shims/yarnpkg.ps1"]}}