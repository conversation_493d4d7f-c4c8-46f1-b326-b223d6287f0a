#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  with ({x:42}) { return x; }
"
frame size: 1
parameter count: 1
bytecode array length: 14
bytecodes: [
  /*   34 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(ToObject), R(0),
                B(CreateWithContext), R(0), U8(1),
                B(PushContext), R(0),
  /*   50 S> */ B(LdaLookupSlot), U8(2),
  /*   59 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

