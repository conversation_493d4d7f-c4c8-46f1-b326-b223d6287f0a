// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=1f70badd10a66ed36402c4227f30d45e6696981d$
//

#include "libcef_dll/cpptoc/media_route_create_callback_cpptoc.h"
#include "libcef_dll/ctocpp/media_route_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

void CEF_CALLBACK media_route_create_callback_on_media_route_create_finished(
    struct _cef_media_route_create_callback_t* self,
    cef_media_route_create_result_t result,
    const cef_string_t* error,
    cef_media_route_t* route) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self)
    return;
  // Unverified params: error, route

  // Execute
  CefMediaRouteCreateCallbackCppToC::Get(self)->OnMediaRouteCreateFinished(
      result, CefString(error), CefMediaRouteCToCpp::Wrap(route));
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefMediaRouteCreateCallbackCppToC::CefMediaRouteCreateCallbackCppToC() {
  GetStruct()->on_media_route_create_finished =
      media_route_create_callback_on_media_route_create_finished;
}

// DESTRUCTOR - Do not edit by hand.

CefMediaRouteCreateCallbackCppToC::~CefMediaRouteCreateCallbackCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefMediaRouteCreateCallback>
CefCppToCRefCounted<CefMediaRouteCreateCallbackCppToC,
                    CefMediaRouteCreateCallback,
                    cef_media_route_create_callback_t>::
    UnwrapDerived(CefWrapperType type, cef_media_route_create_callback_t* s) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType
    CefCppToCRefCounted<CefMediaRouteCreateCallbackCppToC,
                        CefMediaRouteCreateCallback,
                        cef_media_route_create_callback_t>::kWrapperType =
        WT_MEDIA_ROUTE_CREATE_CALLBACK;
