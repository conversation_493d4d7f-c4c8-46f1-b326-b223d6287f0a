// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=2c6664443a865936b74fcea903f131011736d689$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_LIST_VALUE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_LIST_VALUE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_values_capi.h"
#include "include/cef_values.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefListValueCToCpp : public CefCToCppRefCounted<CefListValueCToCpp,
                                                      CefListValue,
                                                      cef_list_value_t> {
 public:
  CefListValueCToCpp();
  virtual ~CefListValueCToCpp();

  // CefListValue methods.
  bool IsValid() override;
  bool IsOwned() override;
  bool IsReadOnly() override;
  bool IsSame(CefRefPtr<CefListValue> that) override;
  bool IsEqual(CefRefPtr<CefListValue> that) override;
  CefRefPtr<CefListValue> Copy() override;
  bool SetSize(size_t size) override;
  size_t GetSize() override;
  bool Clear() override;
  bool Remove(size_t index) override;
  CefValueType GetType(size_t index) override;
  CefRefPtr<CefValue> GetValue(size_t index) override;
  bool GetBool(size_t index) override;
  int GetInt(size_t index) override;
  double GetDouble(size_t index) override;
  CefString GetString(size_t index) override;
  CefRefPtr<CefBinaryValue> GetBinary(size_t index) override;
  CefRefPtr<CefDictionaryValue> GetDictionary(size_t index) override;
  CefRefPtr<CefListValue> GetList(size_t index) override;
  bool SetValue(size_t index, CefRefPtr<CefValue> value) override;
  bool SetNull(size_t index) override;
  bool SetBool(size_t index, bool value) override;
  bool SetInt(size_t index, int value) override;
  bool SetDouble(size_t index, double value) override;
  bool SetString(size_t index, const CefString& value) override;
  bool SetBinary(size_t index, CefRefPtr<CefBinaryValue> value) override;
  bool SetDictionary(size_t index,
                     CefRefPtr<CefDictionaryValue> value) override;
  bool SetList(size_t index, CefRefPtr<CefListValue> value) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_LIST_VALUE_CTOCPP_H_
