// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=05c6527a7cdeb9495bca9da965956fb3006a7bdd$
//

#include "libcef_dll/ctocpp/download_item_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") bool CefDownloadItemCToCpp::IsValid() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_valid))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_valid(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefDownloadItemCToCpp::IsInProgress() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_in_progress))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_in_progress(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefDownloadItemCToCpp::IsComplete() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_complete))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_complete(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefDownloadItemCToCpp::IsCanceled() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_canceled))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_canceled(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") int64 CefDownloadItemCToCpp::GetCurrentSpeed() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_current_speed))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int64 _retval = _struct->get_current_speed(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") int CefDownloadItemCToCpp::GetPercentComplete() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_percent_complete))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_percent_complete(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") int64 CefDownloadItemCToCpp::GetTotalBytes() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_total_bytes))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int64 _retval = _struct->get_total_bytes(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") int64 CefDownloadItemCToCpp::GetReceivedBytes() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_received_bytes))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int64 _retval = _struct->get_received_bytes(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") CefTime CefDownloadItemCToCpp::GetStartTime() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_start_time))
    return CefTime();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_time_t _retval = _struct->get_start_time(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") CefTime CefDownloadItemCToCpp::GetEndTime() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_end_time))
    return CefTime();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_time_t _retval = _struct->get_end_time(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") CefString CefDownloadItemCToCpp::GetFullPath() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_full_path))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_full_path(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall") uint32 CefDownloadItemCToCpp::GetId() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_id))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  uint32 _retval = _struct->get_id(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") CefString CefDownloadItemCToCpp::GetURL() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_url))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_url(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall") CefString CefDownloadItemCToCpp::GetOriginalUrl() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_original_url))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_original_url(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall")
CefString CefDownloadItemCToCpp::GetSuggestedFileName() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_suggested_file_name))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_suggested_file_name(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall")
CefString CefDownloadItemCToCpp::GetContentDisposition() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_content_disposition))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_content_disposition(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall") CefString CefDownloadItemCToCpp::GetMimeType() {
  shutdown_checker::AssertNotShutdown();

  cef_download_item_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_mime_type))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_mime_type(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

// CONSTRUCTOR - Do not edit by hand.

CefDownloadItemCToCpp::CefDownloadItemCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefDownloadItemCToCpp::~CefDownloadItemCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_download_item_t*
CefCToCppRefCounted<CefDownloadItemCToCpp,
                    CefDownloadItem,
                    cef_download_item_t>::UnwrapDerived(CefWrapperType type,
                                                        CefDownloadItem* c) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefDownloadItemCToCpp,
                                   CefDownloadItem,
                                   cef_download_item_t>::kWrapperType =
    WT_DOWNLOAD_ITEM;
