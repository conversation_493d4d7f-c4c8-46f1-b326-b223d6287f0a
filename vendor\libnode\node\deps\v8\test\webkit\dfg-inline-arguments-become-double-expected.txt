# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves function.arguments functionality if the arguments are reassigned to refer to an int32.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a0, 42.5, c0"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a1, 42.5, c1"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a2, 42.5, c2"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a3, 42.5, c3"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a4, 42.5, c4"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a5, 42.5, c5"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a6, 42.5, c6"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a7, 42.5, c7"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a8, 42.5, c8"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a9, 42.5, c9"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a10, 42.5, c10"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a11, 42.5, c11"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a12, 42.5, c12"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a13, 42.5, c13"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a14, 42.5, c14"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a15, 42.5, c15"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a16, 42.5, c16"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a17, 42.5, c17"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a18, 42.5, c18"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a19, 42.5, c19"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a20, 42.5, c20"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a21, 42.5, c21"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a22, 42.5, c22"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a23, 42.5, c23"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a24, 42.5, c24"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a25, 42.5, c25"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a26, 42.5, c26"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a27, 42.5, c27"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a28, 42.5, c28"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a29, 42.5, c29"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a30, 42.5, c30"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a31, 42.5, c31"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a32, 42.5, c32"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a33, 42.5, c33"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a34, 42.5, c34"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a35, 42.5, c35"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a36, 42.5, c36"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a37, 42.5, c37"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a38, 42.5, c38"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a39, 42.5, c39"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a40, 42.5, c40"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a41, 42.5, c41"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a42, 42.5, c42"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a43, 42.5, c43"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a44, 42.5, c44"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a45, 42.5, c45"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a46, 42.5, c46"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a47, 42.5, c47"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a48, 42.5, c48"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a49, 42.5, c49"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a50, 42.5, c50"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a51, 42.5, c51"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a52, 42.5, c52"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a53, 42.5, c53"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a54, 42.5, c54"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a55, 42.5, c55"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a56, 42.5, c56"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a57, 42.5, c57"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a58, 42.5, c58"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a59, 42.5, c59"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a60, 42.5, c60"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a61, 42.5, c61"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a62, 42.5, c62"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a63, 42.5, c63"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a64, 42.5, c64"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a65, 42.5, c65"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a66, 42.5, c66"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a67, 42.5, c67"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a68, 42.5, c68"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a69, 42.5, c69"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a70, 42.5, c70"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a71, 42.5, c71"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a72, 42.5, c72"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a73, 42.5, c73"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a74, 42.5, c74"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a75, 42.5, c75"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a76, 42.5, c76"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a77, 42.5, c77"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a78, 42.5, c78"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a79, 42.5, c79"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a80, 42.5, c80"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a81, 42.5, c81"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a82, 42.5, c82"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a83, 42.5, c83"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a84, 42.5, c84"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a85, 42.5, c85"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a86, 42.5, c86"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a87, 42.5, c87"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a88, 42.5, c88"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a89, 42.5, c89"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a90, 42.5, c90"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a91, 42.5, c91"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a92, 42.5, c92"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a93, 42.5, c93"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a94, 42.5, c94"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a95, 42.5, c95"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a96, 42.5, c96"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a97, 42.5, c97"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a98, 42.5, c98"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a99, 42.5, c99"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a100, 42.5, c100"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a101, 42.5, c101"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a102, 42.5, c102"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a103, 42.5, c103"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a104, 42.5, c104"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a105, 42.5, c105"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a106, 42.5, c106"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a107, 42.5, c107"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a108, 42.5, c108"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a109, 42.5, c109"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a110, 42.5, c110"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a111, 42.5, c111"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a112, 42.5, c112"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a113, 42.5, c113"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a114, 42.5, c114"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a115, 42.5, c115"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a116, 42.5, c116"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a117, 42.5, c117"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a118, 42.5, c118"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a119, 42.5, c119"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a120, 42.5, c120"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a121, 42.5, c121"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a122, 42.5, c122"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a123, 42.5, c123"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a124, 42.5, c124"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a125, 42.5, c125"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a126, 42.5, c126"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a127, 42.5, c127"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a128, 42.5, c128"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a129, 42.5, c129"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a130, 42.5, c130"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a131, 42.5, c131"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a132, 42.5, c132"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a133, 42.5, c133"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a134, 42.5, c134"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a135, 42.5, c135"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a136, 42.5, c136"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a137, 42.5, c137"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a138, 42.5, c138"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a139, 42.5, c139"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a140, 42.5, c140"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a141, 42.5, c141"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a142, 42.5, c142"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a143, 42.5, c143"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a144, 42.5, c144"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a145, 42.5, c145"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a146, 42.5, c146"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a147, 42.5, c147"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a148, 42.5, c148"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a149, 42.5, c149"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a150, 42.5, c150"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a151, 42.5, c151"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a152, 42.5, c152"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a153, 42.5, c153"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a154, 42.5, c154"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a155, 42.5, c155"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a156, 42.5, c156"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a157, 42.5, c157"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a158, 42.5, c158"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a159, 42.5, c159"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a160, 42.5, c160"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a161, 42.5, c161"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a162, 42.5, c162"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a163, 42.5, c163"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a164, 42.5, c164"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a165, 42.5, c165"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a166, 42.5, c166"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a167, 42.5, c167"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a168, 42.5, c168"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a169, 42.5, c169"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a170, 42.5, c170"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a171, 42.5, c171"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a172, 42.5, c172"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a173, 42.5, c173"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a174, 42.5, c174"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a175, 42.5, c175"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a176, 42.5, c176"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a177, 42.5, c177"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a178, 42.5, c178"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a179, 42.5, c179"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a180, 42.5, c180"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a181, 42.5, c181"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a182, 42.5, c182"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a183, 42.5, c183"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a184, 42.5, c184"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a185, 42.5, c185"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a186, 42.5, c186"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a187, 42.5, c187"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a188, 42.5, c188"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a189, 42.5, c189"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a190, 42.5, c190"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a191, 42.5, c191"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a192, 42.5, c192"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a193, 42.5, c193"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a194, 42.5, c194"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a195, 42.5, c195"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a196, 42.5, c196"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a197, 42.5, c197"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a198, 42.5, c198"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a199, 42.5, c199"
PASS successfullyParsed is true

TEST COMPLETE

