# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests that DFG getter caching does not break the world if the getter throws an exception.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS bar(o) is "Returned result: 0"
PASS bar(o) is "Returned result: 1"
PASS bar(o) is "Returned result: 2"
PASS bar(o) is "Returned result: 3"
PASS bar(o) is "Returned result: 4"
PASS bar(o) is "Returned result: 5"
PASS bar(o) is "Returned result: 6"
PASS bar(o) is "Returned result: 7"
PASS bar(o) is "Returned result: 8"
PASS bar(o) is "Returned result: 9"
PASS bar(o) is "Returned result: 10"
PASS bar(o) is "Returned result: 11"
PASS bar(o) is "Returned result: 12"
PASS bar(o) is "Returned result: 13"
PASS bar(o) is "Returned result: 14"
PASS bar(o) is "Returned result: 15"
PASS bar(o) is "Returned result: 16"
PASS bar(o) is "Returned result: 17"
PASS bar(o) is "Returned result: 18"
PASS bar(o) is "Returned result: 19"
PASS bar(o) is "Returned result: 20"
PASS bar(o) is "Returned result: 21"
PASS bar(o) is "Returned result: 22"
PASS bar(o) is "Returned result: 23"
PASS bar(o) is "Returned result: 24"
PASS bar(o) is "Returned result: 25"
PASS bar(o) is "Returned result: 26"
PASS bar(o) is "Returned result: 27"
PASS bar(o) is "Returned result: 28"
PASS bar(o) is "Returned result: 29"
PASS bar(o) is "Returned result: 30"
PASS bar(o) is "Returned result: 31"
PASS bar(o) is "Returned result: 32"
PASS bar(o) is "Returned result: 33"
PASS bar(o) is "Returned result: 34"
PASS bar(o) is "Returned result: 35"
PASS bar(o) is "Returned result: 36"
PASS bar(o) is "Returned result: 37"
PASS bar(o) is "Returned result: 38"
PASS bar(o) is "Returned result: 39"
PASS bar(o) is "Returned result: 40"
PASS bar(o) is "Returned result: 41"
PASS bar(o) is "Returned result: 42"
PASS bar(o) is "Returned result: 43"
PASS bar(o) is "Returned result: 44"
PASS bar(o) is "Returned result: 45"
PASS bar(o) is "Returned result: 46"
PASS bar(o) is "Returned result: 47"
PASS bar(o) is "Returned result: 48"
PASS bar(o) is "Returned result: 49"
PASS bar(o) is "Returned result: 50"
PASS bar(o) is "Returned result: 51"
PASS bar(o) is "Returned result: 52"
PASS bar(o) is "Returned result: 53"
PASS bar(o) is "Returned result: 54"
PASS bar(o) is "Returned result: 55"
PASS bar(o) is "Returned result: 56"
PASS bar(o) is "Returned result: 57"
PASS bar(o) is "Returned result: 58"
PASS bar(o) is "Returned result: 59"
PASS bar(o) is "Returned result: 60"
PASS bar(o) is "Returned result: 61"
PASS bar(o) is "Returned result: 62"
PASS bar(o) is "Returned result: 63"
PASS bar(o) is "Returned result: 64"
PASS bar(o) is "Returned result: 65"
PASS bar(o) is "Returned result: 66"
PASS bar(o) is "Returned result: 67"
PASS bar(o) is "Returned result: 68"
PASS bar(o) is "Returned result: 69"
PASS bar(o) is "Returned result: 70"
PASS bar(o) is "Returned result: 71"
PASS bar(o) is "Returned result: 72"
PASS bar(o) is "Returned result: 73"
PASS bar(o) is "Returned result: 74"
PASS bar(o) is "Returned result: 75"
PASS bar(o) is "Returned result: 76"
PASS bar(o) is "Returned result: 77"
PASS bar(o) is "Returned result: 78"
PASS bar(o) is "Returned result: 79"
PASS bar(o) is "Returned result: 80"
PASS bar(o) is "Returned result: 81"
PASS bar(o) is "Returned result: 82"
PASS bar(o) is "Returned result: 83"
PASS bar(o) is "Returned result: 84"
PASS bar(o) is "Returned result: 85"
PASS bar(o) is "Returned result: 86"
PASS bar(o) is "Returned result: 87"
PASS bar(o) is "Returned result: 88"
PASS bar(o) is "Returned result: 89"
PASS bar(o) is "Returned result: 90"
PASS bar(o) is "Returned result: 91"
PASS bar(o) is "Returned result: 92"
PASS bar(o) is "Returned result: 93"
PASS bar(o) is "Returned result: 94"
PASS bar(o) is "Returned result: 95"
PASS bar(o) is "Returned result: 96"
PASS bar(o) is "Returned result: 97"
PASS bar(o) is "Returned result: 98"
PASS bar(o) is "Returned result: 99"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS bar(o) is "Threw exception: Oh hi, I'm an exception!"
PASS successfullyParsed is true

TEST COMPLETE

