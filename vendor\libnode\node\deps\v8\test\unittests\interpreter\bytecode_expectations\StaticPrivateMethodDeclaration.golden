#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      static #a() { return 1; }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 35
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
                B(LdaUndefined),
  /*   84 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A {
      static get #a() { return 1; }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 43
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star3),
                B(LdaNull),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*   88 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A {
      static set #a(val) { }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 43
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(LdaNull),
                B(Star3),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*   81 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A {
      static get #a() { return 1; }
      static set #a(val) { }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 46
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star3),
                B(CreateClosure), U8(4), U8(2), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*  115 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A {
      static #a() { }
      #b() { }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 51
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaConstant), U8(2),
                B(Star3),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(3), U8(1),
                B(StaCurrentContextSlot), U8(4),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(5), U8(2), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
                B(LdaUndefined),
  /*   87 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["A"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

