Tests for the descriptors of the properties implicitly defined by ES6 class syntax

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS class A {}; descriptor(A, "prototype").writable is false
PASS class A {}; var x = A.prototype; A.prototype = 3; A.prototype is x
PASS class A {}; descriptor(A, "prototype").enumerable is false
PASS class A {}; A.foo = "foo"; enumeratedProperties(A).includes("foo") is true
PASS class A {}; enumeratedProperties(A).includes("prototype") is false
PASS class A {}; descriptor(A, "prototype").configurable is false
PASS class A {}; Object.defineProperty(A, "prototype", {value: "foo"}) threw exception TypeError: Cannot redefine property: prototype.
PASS class A { static foo() {} }; descriptor(A, "foo").writable is true
PASS class A { static foo() {} }; A.foo = 3; A.foo is 3
PASS class A { static foo() {} }; descriptor(A, "foo").enumerable is false
PASS class A { static foo() {} }; enumeratedProperties(A).includes("foo") is false
PASS class A { static foo() {} }; descriptor(A, "foo").configurable is true
PASS class A { static foo() {} }; Object.defineProperty(A, "foo", {value: "bar"}); A.foo is "bar"
PASS class A { static get foo() {} }; descriptor(A, "foo").writable is undefined
PASS class A { static get foo() { return 5; } }; A.foo = 3; A.foo is 5
PASS class A { static get foo() {} }; descriptor(A, "foo").enumerable is false
PASS class A { static get foo() {} }; enumeratedProperties(A).includes("foo") is false
PASS class A { static get foo() {} }; enumeratedProperties(new A).includes("foo") is false
PASS class A { static get foo() {} }; descriptor(A, "foo").configurable is true
PASS class A { static get foo() {} }; Object.defineProperty(A, "foo", {value: "bar"}); A.foo is "bar"
PASS class A { foo() {} }; descriptor(A.prototype, "foo").writable is true
PASS class A { foo() {} }; A.prototype.foo = 3; A.prototype.foo is 3
PASS class A { foo() {} }; descriptor(A.prototype, "foo").enumerable is false
PASS class A { foo() {} }; enumeratedProperties(A.prototype).includes("foo") is false
PASS class A { foo() {} }; enumeratedProperties(new A).includes("foo") is false
PASS class A { foo() {} }; descriptor(A.prototype, "foo").configurable is true
PASS class A { foo() {} }; Object.defineProperty(A.prototype, "foo", {value: "bar"}); A.prototype.foo is "bar"
PASS class A { get foo() {} }; descriptor(A.prototype, "foo").writable is undefined
PASS class A { get foo() { return 5; } }; A.prototype.foo = 3; A.prototype.foo is 5
PASS class A { get foo() {} }; descriptor(A.prototype, "foo").enumerable is false
PASS class A { get foo() {} }; enumeratedProperties(A.prototype).includes("foo") is false
PASS class A { get foo() {} }; enumeratedProperties(new A).includes("foo") is false
PASS class A { get foo() {} }; descriptor(A.prototype, "foo").configurable is true
PASS class A { get foo() {} }; Object.defineProperty(A.prototype, "foo", {value: "bar"}); A.prototype.foo is "bar"
PASS class A { }; descriptor(A.prototype, "constructor").writable is true
PASS class A { }; A.prototype.constructor = 3; A.prototype.constructor is 3
PASS class A { }; x = {}; A.prototype.constructor = function () { return x; }; (new A) instanceof A is true
PASS class A { }; descriptor(A.prototype, "constructor").enumerable is false
PASS class A { }; enumeratedProperties(A.prototype).includes("constructor") is false
PASS class A { }; enumeratedProperties(new A).includes("constructor") is false
PASS class A { }; descriptor(A.prototype, "constructor").configurable is true
PASS class A { }; Object.defineProperty(A.prototype, "constructor", {value: "bar"}); A.prototype.constructor is "bar"
PASS successfullyParsed is true

TEST COMPLETE
