// Copyright 2015 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

assertThrows("()=>{}()", SyntaxError);
assertThrows("x=>{}()", SyntaxError);
assertThrows("(...x)=>{}()", SyntaxError);
assertThrows("(x)=>{}()", SyntaxError);
assertThrows("(x,y)=>{}()", SyntaxError);
assertThrows("(x,y,...z)=>{}()", SyntaxError);
