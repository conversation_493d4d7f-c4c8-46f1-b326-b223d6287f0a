// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=de83ca0067722af09407abc0b7723a8d91d083ad$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_CONTEXT_MENU_PARAMS_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_CONTEXT_MENU_PARAMS_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include <vector>
#include "include/capi/cef_context_menu_handler_capi.h"
#include "include/cef_context_menu_handler.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefContextMenuParamsCToCpp
    : public CefCToCppRefCounted<CefContextMenuParamsCToCpp,
                                 CefContextMenuParams,
                                 cef_context_menu_params_t> {
 public:
  CefContextMenuParamsCToCpp();
  virtual ~CefContextMenuParamsCToCpp();

  // CefContextMenuParams methods.
  int GetXCoord() override;
  int GetYCoord() override;
  TypeFlags GetTypeFlags() override;
  CefString GetLinkUrl() override;
  CefString GetUnfilteredLinkUrl() override;
  CefString GetSourceUrl() override;
  bool HasImageContents() override;
  CefString GetTitleText() override;
  CefString GetPageUrl() override;
  CefString GetFrameUrl() override;
  CefString GetFrameCharset() override;
  MediaType GetMediaType() override;
  MediaStateFlags GetMediaStateFlags() override;
  CefString GetSelectionText() override;
  CefString GetMisspelledWord() override;
  bool GetDictionarySuggestions(std::vector<CefString>& suggestions) override;
  bool IsEditable() override;
  bool IsSpellCheckEnabled() override;
  EditStateFlags GetEditStateFlags() override;
  bool IsCustomMenu() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_CONTEXT_MENU_PARAMS_CTOCPP_H_
