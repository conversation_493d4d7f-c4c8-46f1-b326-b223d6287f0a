#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
top level: yes
print callee: yes

---
snippet: "
  
        (function() {
          l = {};
          l.a = 2;
          l.b = l.a;
          return arguments.callee;
        })();
  
"
frame size: 3
parameter count: 1
bytecode array length: 37
bytecodes: [
  /*   16 E> */ B(CreateMappedArguments),
                B(Star0),
  /*   29 S> */ B(CreateEmptyObjectLiteral),
  /*   31 E> */ B(StaGlobal), U8(0), U8(0),
  /*   45 S> */ B(LdaGlobal), U8(0), U8(2),
                B(Star1),
                B(LdaSmi), I8(2),
  /*   49 E> */ B(SetNamedProperty), R(1), U8(1), U8(4),
  /*   62 S> */ B(LdaGlobal), U8(0), U8(2),
                B(Star1),
  /*   68 E> */ B(LdaGlobal), U8(0), U8(2),
                B(Star2),
  /*   70 E> */ B(GetNamedProperty), R(2), U8(1), U8(6),
  /*   66 E> */ B(SetNamedProperty), R(1), U8(2), U8(8),
  /*   98 S> */ B(GetNamedProperty), R(0), U8(3), U8(10),
  /*  105 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["l"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["callee"],
]
handlers: [
]

---
snippet: "
  
        (function() {
          l = {
            'a': 4.3,
            'b': 3.4
          };
          if (l.a < 3) {
            l.a = 3;
          } else {
            l.a = l.b;
          }
          return arguments.callee;
        })();
  
"
frame size: 3
parameter count: 1
bytecode array length: 58
bytecodes: [
  /*   16 E> */ B(CreateMappedArguments),
                B(Star0),
  /*   29 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
  /*   31 E> */ B(StaGlobal), U8(1), U8(1),
  /*   93 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
  /*   99 E> */ B(GetNamedProperty), R(1), U8(2), U8(5),
                B(Star1),
                B(LdaSmi), I8(3),
  /*  101 E> */ B(TestLessThan), R(1), U8(7),
                B(JumpIfFalse), U8(14),
  /*  118 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
                B(LdaSmi), I8(3),
  /*  122 E> */ B(SetNamedProperty), R(1), U8(2), U8(8),
                B(Jump), U8(18),
  /*  154 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
  /*  160 E> */ B(LdaGlobal), U8(1), U8(3),
                B(Star2),
  /*  162 E> */ B(GetNamedProperty), R(2), U8(3), U8(10),
  /*  158 E> */ B(SetNamedProperty), R(1), U8(2), U8(8),
  /*  200 S> */ B(GetNamedProperty), R(0), U8(4), U8(12),
  /*  207 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["l"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["a"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["callee"],
]
handlers: [
]

---
snippet: "
  
        this.f0 = function() {};
        this.f1 = function(a) {};
        this.f2 = function(a, b) {};
        this.f3 = function(a, b, c) {};
        this.f4 = function(a, b, c, d) {};
        this.f5 = function(a, b, c, d, e) {};
        (function() {
          this.f0();
          this.f1(1);
          this.f2(1, 2);
          this.f3(1, 2, 3);
          this.f4(1, 2, 3, 4);
          this.f5(1, 2, 3, 4, 5);
          return arguments.callee;
        })();
  
"
frame size: 8
parameter count: 1
bytecode array length: 121
bytecodes: [
  /*  237 E> */ B(CreateMappedArguments),
                B(Star0),
  /*  255 S> */ B(GetNamedProperty), R(this), U8(0), U8(0),
                B(Star1),
  /*  255 E> */ B(CallProperty0), R(1), R(this), U8(2),
  /*  274 S> */ B(GetNamedProperty), R(this), U8(1), U8(4),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star3),
  /*  274 E> */ B(CallProperty1), R(1), R(this), R(3), U8(6),
  /*  294 S> */ B(GetNamedProperty), R(this), U8(2), U8(8),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star3),
                B(LdaSmi), I8(2),
                B(Star4),
  /*  294 E> */ B(CallProperty2), R(1), R(this), R(3), R(4), U8(10),
  /*  317 S> */ B(GetNamedProperty), R(this), U8(3), U8(12),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star3),
                B(LdaSmi), I8(2),
                B(Star4),
                B(LdaSmi), I8(3),
                B(Star5),
                B(Mov), R(this), R(2),
  /*  317 E> */ B(CallProperty), R(1), R(2), U8(4), U8(14),
  /*  343 S> */ B(GetNamedProperty), R(this), U8(4), U8(16),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star3),
                B(LdaSmi), I8(2),
                B(Star4),
                B(LdaSmi), I8(3),
                B(Star5),
                B(LdaSmi), I8(4),
                B(Star6),
                B(Mov), R(this), R(2),
  /*  343 E> */ B(CallProperty), R(1), R(2), U8(5), U8(18),
  /*  372 S> */ B(GetNamedProperty), R(this), U8(5), U8(20),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star3),
                B(LdaSmi), I8(2),
                B(Star4),
                B(LdaSmi), I8(3),
                B(Star5),
                B(LdaSmi), I8(4),
                B(Star6),
                B(LdaSmi), I8(5),
                B(Star7),
                B(Mov), R(this), R(2),
  /*  372 E> */ B(CallProperty), R(1), R(2), U8(6), U8(22),
  /*  416 S> */ B(GetNamedProperty), R(0), U8(6), U8(24),
  /*  423 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f0"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f1"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f2"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f3"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f4"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f5"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["callee"],
]
handlers: [
]

---
snippet: "
  
        function f0() {}
        function f1(a) {}
        function f2(a, b) {}
        function f3(a, b, c) {}
        function f4(a, b, c, d) {}
        function f5(a, b, c, d, e) {}
        (function() {
          f0();
          f1(1);
          f2(1, 2);
          f3(1, 2, 3);
          f4(1, 2, 3, 4);
          f5(1, 2, 3, 4, 5);
          return arguments.callee;
        })();
  
"
frame size: 7
parameter count: 1
bytecode array length: 103
bytecodes: [
  /*  189 E> */ B(CreateMappedArguments),
                B(Star0),
  /*  202 S> */ B(LdaGlobal), U8(0), U8(0),
                B(Star1),
  /*  202 E> */ B(CallUndefinedReceiver0), R(1), U8(2),
  /*  216 S> */ B(LdaGlobal), U8(1), U8(4),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
  /*  216 E> */ B(CallUndefinedReceiver1), R(1), R(2), U8(6),
  /*  231 S> */ B(LdaGlobal), U8(2), U8(8),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(LdaSmi), I8(2),
                B(Star3),
  /*  231 E> */ B(CallUndefinedReceiver2), R(1), R(2), R(3), U8(10),
  /*  249 S> */ B(LdaGlobal), U8(3), U8(12),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(LdaSmi), I8(2),
                B(Star3),
                B(LdaSmi), I8(3),
                B(Star4),
  /*  249 E> */ B(CallUndefinedReceiver), R(1), R(2), U8(3), U8(14),
  /*  270 S> */ B(LdaGlobal), U8(4), U8(16),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(LdaSmi), I8(2),
                B(Star3),
                B(LdaSmi), I8(3),
                B(Star4),
                B(LdaSmi), I8(4),
                B(Star5),
  /*  270 E> */ B(CallUndefinedReceiver), R(1), R(2), U8(4), U8(18),
  /*  294 S> */ B(LdaGlobal), U8(5), U8(20),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(LdaSmi), I8(2),
                B(Star3),
                B(LdaSmi), I8(3),
                B(Star4),
                B(LdaSmi), I8(4),
                B(Star5),
                B(LdaSmi), I8(5),
                B(Star6),
  /*  294 E> */ B(CallUndefinedReceiver), R(1), R(2), U8(5), U8(22),
  /*  338 S> */ B(GetNamedProperty), R(0), U8(6), U8(24),
  /*  345 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f0"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f1"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f2"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f3"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f4"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["f5"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["callee"],
]
handlers: [
]

