#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return \"This is a string\";
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaConstant), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["This is a string"],
]
handlers: [
]

---
snippet: "
  var a = \"First string\"; return \"Second string\";
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   58 S> */ B(LdaConstant), U8(1),
  /*   81 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["First string"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["Second string"],
]
handlers: [
]

---
snippet: "
  var a = \"Same string\"; return \"Same string\";
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   57 S> */ B(LdaConstant), U8(0),
  /*   78 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["Same string"],
]
handlers: [
]

