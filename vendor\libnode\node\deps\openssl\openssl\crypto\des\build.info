$DESASM=des_enc.c fcrypt_b.c
IF[{- !$disabled{asm} -}]
  $DESASM_x86=des-586.S crypt586.S
  $DESASM_sparcv9=des_enc-sparc.S fcrypt_b.c dest4-sparcv9.S
  $DESASM_sparcv8=des_enc-sparc.S fcrypt_b.c

  # Now that we have defined all the arch specific variables, use the
  # appropriate one
  IF[$DESASM_{- $target{asm_arch} -}]
    $DESASM=$DESASM_{- $target{asm_arch} -}
    $DESDEF=DES_ASM
  ENDIF
ENDIF

LIBS=../../libcrypto
$COMMON=set_key.c ecb3_enc.c
$ALL=$COMMON\
     ecb_enc.c  cbc_enc.c \
     cfb64enc.c cfb64ede.c cfb_enc.c \
     ofb64ede.c ofb64enc.c ofb_enc.c \
     str2key.c  pcbc_enc.c qud_cksm.c rand_key.c \
     fcrypt.c xcbc_enc.c cbc_cksm.c

SOURCE[../../libcrypto]=$ALL $DESASM
SOURCE[../../providers/libfips.a]=$COMMON $DESASM
IF[{- !$disabled{module} && !$disabled{shared} -}]
  SOURCE[../../providers/liblegacy.a]=$DESASM
ENDIF

DEFINE[../../libcrypto]=$DESDEF
DEFINE[../../providers/libfips.a]=$DESDEF

# When all deprecated symbols are removed, libcrypto doesn't export the
# DES functions, so we must include them directly in liblegacy.a
IF[{- $disabled{'deprecated-3.0'} && !$disabled{des} -}]
  SOURCE[../../providers/liblegacy.a]=$ALL
  DEFINE[../../providers/liblegacy.a]=$DESDEF
ENDIF

GENERATE[des_enc-sparc.S]=asm/des_enc.m4
GENERATE[dest4-sparcv9.S]=asm/dest4-sparcv9.pl
INCLUDE[dest4-sparcv9.o]=..

GENERATE[des-586.S]=asm/des-586.pl
DEPEND[des-586.S]=../perlasm/x86asm.pl ../perlasm/cbc.pl
GENERATE[crypt586.S]=asm/crypt586.pl
DEPEND[crypt586.S]=../perlasm/x86asm.pl ../perlasm/cbc.pl
