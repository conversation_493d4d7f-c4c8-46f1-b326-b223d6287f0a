// Copyright 2013 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

(function() {
  function testOneSize(current_size) {
    var eval_string = 'obj = {';
    for (var current = 0; current <= current_size; ++current) {
      eval_string += 'k' + current + ':' + current + ','
    }
    eval_string += '};';
    eval(eval_string);
    for (var i = 0; i <= current_size; i++) {
      assertEquals(i, obj['k'+i]);
    }
    var current_number = 0;
    for (var x in obj) {
      assertEquals(current_number, obj[x]);
      current_number++;
    }
  }

  testOneSize(127);
  testOneSize(128);
  testOneSize(129);

  testOneSize(255);
  testOneSize(256);
  testOneSize(257);

  testOneSize(511);
  testOneSize(512);
  testOneSize(513);

  testOneSize(1023);
  testOneSize(1024);
  testOneSize(1025);

  testOneSize(2047);
  testOneSize(2048);
  testOneSize(2049);
}())
