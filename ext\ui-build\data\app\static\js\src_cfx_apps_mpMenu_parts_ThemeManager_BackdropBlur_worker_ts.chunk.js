(()=>{"use strict";const t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","#","$","%","*","+",",","-",".",":",";","=","?","@","[","]","^","_","{","|","}","~"],e=e=>{let a=0;for(let o=0;o<e.length;o++){const r=e[o];a=83*a+t.indexOf(r)}return a},a=(e,a)=>{var o="";for(let r=1;r<=a;r++){let n=Math.floor(e)/Math.pow(83,a-r)%83;o+=t[Math.floor(n)]}return o},o=t=>{let e=t/255;return e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)},r=t=>{let e=Math.max(0,Math.min(1,t));return e<=.0031308?Math.round(12.92*e*255+.5):Math.round(255*(1.055*Math.pow(e,1/2.4)-.055)+.5)},n=(t,e)=>(t<0?-1:1)*Math.pow(Math.abs(t),e);class h extends Error{constructor(t){super(t),this.name="ValidationError",this.message=t}}const s=t=>{const e=t>>8&255,a=255&t;return[o(t>>16),o(e),o(a)]},l=(t,e)=>{const a=Math.floor(t/361),o=Math.floor(t/19)%19,r=t%19;return[n((a-9)/9,2)*e,n((o-9)/9,2)*e,n((r-9)/9,2)*e]},c=(t,a,o,n)=>{(t=>{if(!t||t.length<6)throw new h("The blurhash string must be at least 6 characters");const a=e(t[0]),o=Math.floor(a/9)+1,r=a%9+1;if(t.length!==4+2*r*o)throw new h(`blurhash length mismatch: length is ${t.length} but it should be ${4+2*r*o}`)})(t),n|=1;const c=e(t[0]),M=Math.floor(c/9)+1,f=c%9+1,i=(e(t[1])+1)/166,u=new Array(f*M);for(let a=0;a<u.length;a++)if(0===a){const o=e(t.substring(2,6));u[a]=s(o)}else{const o=e(t.substring(4+2*a,6+2*a));u[a]=l(o,i*n)}const m=4*a,g=new Uint8ClampedArray(m*o);for(let t=0;t<o;t++)for(let e=0;e<a;e++){let n=0,h=0,s=0;for(let r=0;r<M;r++)for(let l=0;l<f;l++){const c=Math.cos(Math.PI*e*l/a)*Math.cos(Math.PI*t*r/o);let M=u[l+r*f];n+=M[0]*c,h+=M[1]*c,s+=M[2]*c}let l=r(n),c=r(h),i=r(s);g[4*e+0+t*m]=l,g[4*e+1+t*m]=c,g[4*e+2+t*m]=i,g[4*e+3+t*m]=255}return g},M=(t,e,a,r)=>{let n=0,h=0,s=0;const l=4*e;for(let c=0;c<e;c++)for(let e=0;e<a;e++){const a=r(c,e);n+=a*o(t[4*c+0+e*l]),h+=a*o(t[4*c+1+e*l]),s+=a*o(t[4*c+2+e*l])}let c=1/(e*a);return[n*c,h*c,s*c]};onmessage=t=>async function(t){console.time("BackdropBlur process");const[e,o,s]=function(t){const e=128,a=e*(t.width/t.height)|0,o=new OffscreenCanvas(e,a).getContext("2d");return o.drawImage(t,0,0,e,a),[o.getImageData(0,0,e,a).data,e,a]}(await async function(t){const e=await(await fetch(t,{mode:"no-cors"})).blob();return createImageBitmap(e)}(t)),l=c(((t,e,o,s,l)=>{if(s<1||s>9||l<1||l>9)throw new h("BlurHash must have between 1 and 9 components");if(e*o*4!==t.length)throw new h("Width and height must match the pixels array");let c=[];for(let a=0;a<l;a++)for(let r=0;r<s;r++){const n=0==r&&0==a?1:2,h=M(t,e,o,((t,h)=>n*Math.cos(Math.PI*r*t/e)*Math.cos(Math.PI*a*h/o)));c.push(h)}const f=c[0],i=c.slice(1);let u,m="";if(m+=a(s-1+9*(l-1),1),i.length>0){let t=Math.max(...i.map((t=>Math.max(...t)))),e=Math.floor(Math.max(0,Math.min(82,Math.floor(166*t-.5))));u=(e+1)/166,m+=a(e,1)}else u=1,m+=a(0,1);var g;return m+=a((r((g=f)[0])<<16)+(r(g[1])<<8)+r(g[2]),4),i.forEach((t=>{m+=a(((t,e)=>19*Math.floor(Math.max(0,Math.min(18,Math.floor(9*n(t[0]/e,.5)+9.5))))*19+19*Math.floor(Math.max(0,Math.min(18,Math.floor(9*n(t[1]/e,.5)+9.5))))+Math.floor(Math.max(0,Math.min(18,Math.floor(9*n(t[2]/e,.5)+9.5)))))(t,u),2)})),m})(e,o,s,9,9),64,64),f=new OffscreenCanvas(64,64),i=f.getContext("2d"),u=i.createImageData(64,64);u.data.set(l),i.putImageData(u,0,0);let m=null;try{const t=await f.convertToBlob();m=URL.createObjectURL(t)}catch(t){console.error(t)}console.timeEnd("BackdropBlur process"),postMessage(m)}(t.data)})();
//# sourceMappingURL=src_cfx_apps_mpMenu_parts_ThemeManager_BackdropBlur_worker_ts.chunk.js.map