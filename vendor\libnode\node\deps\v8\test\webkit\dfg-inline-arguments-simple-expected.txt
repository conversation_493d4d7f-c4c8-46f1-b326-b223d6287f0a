# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves basic function.arguments functionality.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a0, b0, c0"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a1, b1, c1"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a2, b2, c2"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a3, b3, c3"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a4, b4, c4"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a5, b5, c5"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a6, b6, c6"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a7, b7, c7"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a8, b8, c8"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a9, b9, c9"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a10, b10, c10"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a11, b11, c11"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a12, b12, c12"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a13, b13, c13"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a14, b14, c14"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a15, b15, c15"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a16, b16, c16"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a17, b17, c17"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a18, b18, c18"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a19, b19, c19"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a20, b20, c20"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a21, b21, c21"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a22, b22, c22"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a23, b23, c23"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a24, b24, c24"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a25, b25, c25"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a26, b26, c26"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a27, b27, c27"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a28, b28, c28"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a29, b29, c29"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a30, b30, c30"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a31, b31, c31"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a32, b32, c32"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a33, b33, c33"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a34, b34, c34"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a35, b35, c35"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a36, b36, c36"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a37, b37, c37"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a38, b38, c38"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a39, b39, c39"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a40, b40, c40"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a41, b41, c41"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a42, b42, c42"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a43, b43, c43"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a44, b44, c44"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a45, b45, c45"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a46, b46, c46"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a47, b47, c47"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a48, b48, c48"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a49, b49, c49"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a50, b50, c50"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a51, b51, c51"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a52, b52, c52"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a53, b53, c53"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a54, b54, c54"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a55, b55, c55"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a56, b56, c56"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a57, b57, c57"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a58, b58, c58"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a59, b59, c59"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a60, b60, c60"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a61, b61, c61"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a62, b62, c62"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a63, b63, c63"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a64, b64, c64"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a65, b65, c65"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a66, b66, c66"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a67, b67, c67"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a68, b68, c68"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a69, b69, c69"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a70, b70, c70"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a71, b71, c71"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a72, b72, c72"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a73, b73, c73"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a74, b74, c74"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a75, b75, c75"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a76, b76, c76"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a77, b77, c77"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a78, b78, c78"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a79, b79, c79"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a80, b80, c80"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a81, b81, c81"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a82, b82, c82"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a83, b83, c83"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a84, b84, c84"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a85, b85, c85"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a86, b86, c86"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a87, b87, c87"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a88, b88, c88"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a89, b89, c89"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a90, b90, c90"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a91, b91, c91"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a92, b92, c92"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a93, b93, c93"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a94, b94, c94"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a95, b95, c95"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a96, b96, c96"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a97, b97, c97"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a98, b98, c98"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a99, b99, c99"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a100, b100, c100"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a101, b101, c101"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a102, b102, c102"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a103, b103, c103"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a104, b104, c104"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a105, b105, c105"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a106, b106, c106"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a107, b107, c107"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a108, b108, c108"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a109, b109, c109"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a110, b110, c110"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a111, b111, c111"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a112, b112, c112"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a113, b113, c113"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a114, b114, c114"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a115, b115, c115"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a116, b116, c116"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a117, b117, c117"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a118, b118, c118"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a119, b119, c119"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a120, b120, c120"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a121, b121, c121"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a122, b122, c122"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a123, b123, c123"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a124, b124, c124"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a125, b125, c125"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a126, b126, c126"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a127, b127, c127"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a128, b128, c128"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a129, b129, c129"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a130, b130, c130"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a131, b131, c131"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a132, b132, c132"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a133, b133, c133"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a134, b134, c134"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a135, b135, c135"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a136, b136, c136"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a137, b137, c137"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a138, b138, c138"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a139, b139, c139"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a140, b140, c140"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a141, b141, c141"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a142, b142, c142"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a143, b143, c143"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a144, b144, c144"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a145, b145, c145"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a146, b146, c146"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a147, b147, c147"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a148, b148, c148"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a149, b149, c149"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a150, b150, c150"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a151, b151, c151"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a152, b152, c152"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a153, b153, c153"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a154, b154, c154"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a155, b155, c155"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a156, b156, c156"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a157, b157, c157"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a158, b158, c158"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a159, b159, c159"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a160, b160, c160"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a161, b161, c161"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a162, b162, c162"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a163, b163, c163"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a164, b164, c164"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a165, b165, c165"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a166, b166, c166"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a167, b167, c167"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a168, b168, c168"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a169, b169, c169"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a170, b170, c170"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a171, b171, c171"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a172, b172, c172"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a173, b173, c173"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a174, b174, c174"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a175, b175, c175"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a176, b176, c176"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a177, b177, c177"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a178, b178, c178"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a179, b179, c179"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a180, b180, c180"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a181, b181, c181"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a182, b182, c182"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a183, b183, c183"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a184, b184, c184"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a185, b185, c185"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a186, b186, c186"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a187, b187, c187"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a188, b188, c188"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a189, b189, c189"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a190, b190, c190"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a191, b191, c191"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a192, b192, c192"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a193, b193, c193"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a194, b194, c194"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a195, b195, c195"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a196, b196, c196"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a197, b197, c197"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a198, b198, c198"
PASS argsToStr(baz("a" + __i, "b" + __i, "c" + __i)) is "[object Arguments]: a199, b199, c199"
PASS successfullyParsed is true

TEST COMPLETE

