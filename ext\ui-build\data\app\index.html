<!doctype html><html lang="en"><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/><title>CFX UI</title><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5TCZ2ZG4');</script><script>window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'appStart',
      game: nuiTargetGame === 'gta5'
        ? 'FiveM - prod'
        : 'RedM - prod',
    });</script><style>@keyframes shineLoader {
      from {
        background-color: white;
      }

      to {
        background-color: rgb(109, 109, 109);
      }
    }
    .loader {
      position: fixed;

      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      background-color: rgba(150, 150, 150, 1);
      animation: shineLoader 1s linear infinite;

      z-index: 1;
    }
    .loader .mask {
      position: absolute;
      inset: 0;

      display: flex;
      flex-direction: column;
      align-items: center;

      padding-top: 70vh;

      box-sizing: border-box;

      font-family: Arial, Helvetica, sans-serif;
      font-weight: bold;
      font-size: 16px;

      color: rgba(150, 150, 150, 1);
      background-color: black;

      --mask-gta5: url(static/media/a1363a75e0fae8dac9ecgta5.svg);
      --mask-rdr3: url(static/media/670ab3c849cbf44e2eadrdr3.svg);

      -webkit-mask-image: linear-gradient(red, red), var(--mask);
      -webkit-mask-composite: xor;
      -webkit-mask-position: center center;
      -webkit-mask-repeat: no-repeat;
      -webkit-mask-size: 100%, 150px;

      transform: var(--transform);

      z-index: 1;
    }
    .loader .mask span {
      transform: var(--transform);
    }

    @keyframes shineLoaderFadeOut {
      from {
        background-color: rgba(150, 150, 150, 1);
      }

      to {
        background-color: transparent;
      }
    }
    .loader.hide {
      animation: shineLoaderFadeOut .3s linear;
      background-color: transparent;
      pointer-events: none;
    }

    @keyframes maskFadeOut {
      from {
        background-color: black;
      }

      50% {
        color: transparent;
        background-color: black;
      }

      to {
        color: transparent;
        background-color: transparent;
      }
    }
    .loader.hide .mask {
      animation: maskFadeOut 1.5s ease-in forwards;
      animation-delay: .3s;
    }</style><script defer="defer" src="static/js/main.js"></script><link href="main.css" rel="stylesheet"></head><body><div id="backdrop-outlet"></div><div id="cfxui-root"></div><div id="overlay-outlet"></div><div id="loader" class="loader"><div id="loader-mask" class="mask"><span>loading...</span></div><script>const $loaderMask = document.getElementById('loader-mask');

      $loaderMask.style.setProperty('--mask', `var(--mask-${nuiTargetGame})`);

      if (nuiTargetGame === 'gta5') {
        $loaderMask.style.setProperty('--transform', `rotateY(180deg)`);
      }</script></div></body><svg xmlns="http://www.w3.org/2000/svg" version="1.1" style="height: 0px; width: 0px;"><defs><linearGradient id="pin-gradient" x1="0%" y1="100%" x2="100%" y2="0%"><stop offset="0%" style="stop-color: #aa3191; stop-opacity: 1"/><stop offset="100%" style="stop-color: #ff8fb2; stop-opacity: 1"/></linearGradient><linearGradient id="boost-gradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color: #aa3191; stop-opacity: 1"/><stop offset="100%" style="stop-color: #ff8fb2; stop-opacity: 1"/></linearGradient><linearGradient id="burst-gradient" x1="0%" y1="00%" x2="0%" y2="100%"><stop offset="0%" style="stop-color: #ff0037; stop-opacity: 1"/><stop offset="100%" style="stop-color: #ff8800; stop-opacity: 1"/></linearGradient></defs><symbol id="logo-gta5" viewBox="0 0 542 622"><g><polygon points="261.7,169.9 258.5,180.9 175.9,98.3 181,82.8 262.8,164.6 "/><polygon points="426.1,281.5 192.6,48 206.2,7.1 398.8,199.7 "/><polygon points="130.4,620.9 2,620.9 164.3,133 248,216.8 "/><polygon points="539.2,620.9 410.9,620.9 311.3,280 482.8,451.5 "/><polygon points="214.6,2 332.9,2 392.1,179.5 "/><polygon points="459.6,382 292.2,214.6 283.6,185.4 449.3,351 "/></g></symbol><symbol id="logo-rdr3" viewBox="0 0 323 591" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;"><g><g><path d="M84.235,451.272l4.813,11.234l0,16.751l-4.813,23.973l148.814,0l-1.604,-28.787l1.604,-23.171l-148.814,0Z" style="fill:none;fill-rule:nonzero;"/><path d="M7.421,23.973l0.802,-0.803l-0.802,-0.802l0,1.605Z" class="rdr-fill"/><path d="M323,6.119l-92.758,29.991l-4.112,12.036l94.664,-30.693l2.206,-11.334Z" class="rdr-fill"/><path d="M316.382,40.222l1.504,-7.823l-97.171,31.495l-2.808,8.225l98.475,-31.897Z" class="rdr-fill"/><path d="M214.297,82.551l-8.423,24.675l-11.232,39.119l-6.016,28.386l96.067,14.344l10.73,-41.928l18.953,-96.995l-100.079,32.399Z" class="rdr-fill"/><path d="M319.089,0l-76.413,0l-9.627,27.784l86.04,-27.784Z" class="rdr-fill"/><path d="M102.686,77.636l-95.767,30.994l-0.301,3.31l0,7.423l95.567,-30.894l0.501,-10.833Z" class="rdr-fill"/><path d="M101.182,110.035l0.401,-7.323l-94.965,30.694l0,7.322l94.564,-30.693Z" class="rdr-fill"/><path d="M248.192,376.043l-5.616,-0.601l17.248,-87.466l-92.257,-13.742l-6.518,30.593l-12.034,67.204l-61.571,0l12.835,-174.43l0,-65.6l0.502,-12.437l-94.163,30.392l0,66.001l-2.507,5.617l0.201,5.216l-2.507,39.52l-1.805,8.024l0.201,21.566l0,296.1l304.749,0l0,-19.158l13.638,-200.008l-49.639,3.209l-20.757,0Zm-15.143,127.187l-148.814,0l4.813,-23.973l0,-16.851l-4.813,-11.234l148.814,0l-1.604,23.17l1.604,28.888Z" class="rdr-fill"/><path d="M102.887,70.213l1.805,-65.499l-95.968,-3.109l0.301,7.122l-0.802,13.641l0,70.414l-0.602,8.225l95.266,-30.794Z" class="rdr-fill"/></g></g></symbol><symbol id="logo-ny" viewBox="0 0 150 150" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;"><g><path d="M26,145L54.571,145C54.952,144.905 55.143,144.714 55.143,144.429L55.143,69C43.714,57.286 33.905,47.476 25.714,39.571L25.429,39.571L25.429,144.429C25.524,144.81 25.714,145 26,145ZM54.857,57.857L55.143,57.857L55.143,54.143C43.714,42.429 33.905,32.619 25.714,24.714L25.429,24.714L25.429,28.429C36.857,40.048 46.667,49.857 54.857,57.857ZM54.857,43L55.143,43L55.143,31.571C46.857,23 38,14.143 28.571,5L26,5C25.619,5 25.429,5.19 25.429,5.571L25.429,13.571C36.857,25.19 46.667,35 54.857,43ZM57.714,30.429L124,30.429C124.381,30.333 124.571,30.143 124.571,29.857L124.571,5.571C124.571,5.19 124.381,5 124,5L32.571,5L32.571,5.286C41.714,14.619 50.095,23 57.714,30.429Z"/></g></symbol><symbol id="logo-cfxre" viewBox="0 0 1152 256" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;"><g><path d="M555.37060995 202.28144183c-12.72545011 2.0715849-36.40070613 4.4391105-51.49368185 4.4391105-39.95199453 0-47.35051204-19.5320862-47.35051204-75.16893786 0-57.41249584 9.17416171-75.46487856 46.46268994-75.46487856 14.50109432 0 39.06417244 2.3675256 52.38150395 4.7350512l1.1837628-26.63466302c-12.72545011-2.959407-36.99258753-7.3985175-58.00437725-7.3985175-60.66784355 0-76.05675996 31.66565492-76.05675996 104.76300788 0 69.25012386 14.2051536 104.4670672 76.05675996 104.4670672 18.94020482 0 43.79922364-3.55128841 57.70843655-6.51069541l-.8878221-27.22654443zM624.91661253 112.31546896h39.06417243V84.79298384h-39.06417243v-9.17416171c0-20.41990832 4.1431698-26.93060373 17.75644201-26.93060373 9.17416171 0 23.67525602.5918814 23.67525602.5918814l.2959407-26.04278162c0-.2959407-21.89961181-3.2553477-31.96159562-3.2553477-30.77783283 0-41.72763874 12.72545011-41.72763874 55.34091095v9.4701024H575.7904563v27.52248513h17.16456061v120.447865h31.96159563v-120.447865zM674.33894784 84.79298384l42.91140154 73.68923436-42.91140154 74.28111576h34.32912123l29.00218863-50.60585974 29.29812932 50.60585974h34.32912123l-44.09516434-75.16893786 44.09516434-72.80141226h-34.32912123l-29.29812932 51.19774114-29.00218863-51.19774114h-34.32912123zM824.38088287 190.14787312h34.62506193v42.61546084h-34.62506193zM896.5907713 232.76333396h32.25753632V129.48002957s23.08337462-10.3579245 50.01397834-15.68485711V81.53763613c-25.15495952 4.7350512-50.30991904 21.01178972-50.30991904 21.01178972V84.79298383h-31.96159563v147.97035013zM1061.42986052 207.90431514c-24.56307812 0-33.44129913-11.83762801-33.73723983-35.51288403h91.74161708l2.0715849-23.08337462c0-46.16674924-20.71584902-67.77042036-62.14754705-67.77042036-40.83981664 0-64.21913196 24.56307812-64.21913196 78.72022627 0 52.08556324 17.16456062 75.76081926 61.55566565 75.76081926 26.04278163 0 59.78002145-6.8066361 59.78002145-6.8066361l-.5918814-23.97119672s-31.07377352 2.6634663-54.45308884 2.6634663zm-34.03318053-60.37190285c.2959407-28.41030723 10.0619838-39.36011314 31.96159563-39.36011314 21.60367111 0 30.48189212 9.76604311 30.48189212 39.36011314H1027.39668z" fill-rule="nonzero"></path></g><g><path d="M242.956504 146.0804v-.723216l-4.339296-57.85728c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-33.629544c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-4.339296 57.85728v.723216c-.12059627 1.446432.361608 2.65239468 1.446432 3.61608 1.084824.96368532 2.350452 1.446432 3.796884 1.446432h44.116176c1.446432 0 2.71206-.48274668 3.796884-1.446432 1.084824-.96368532 1.56757068-2.169648 1.446432-3.61608zm137.230236 84.435468c0 8.79973068-2.77172532 13.198692-8.316984 13.198692H244.58374c1.56757068 0 2.892864-.57314868 3.977688-1.717638 1.084824-1.14448932 1.56757068-2.50051932 1.446432-4.06809l-3.61608-46.285824c-.12113868-1.56757068-.78288132-2.92360068-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-49.178688c-1.56702827 0-2.95307173.57314868-4.158492 1.717638-1.20542027 1.14448932-1.86824773 2.50051932-1.988844 4.06809l-3.61608 46.285824c-.12059627 1.56757068.361608 2.92360068 1.446432 4.06809s2.41065973 1.717638 3.977688 1.717638H59.440444c-5.54471627 0-8.316984-4.39896132-8.316984-13.198692 0-6.508944 1.56702827-13.50063468 4.700904-20.973264l75.395268-188.759376c.96422773-2.29024427 2.531256-4.27908827 4.700904-5.966532 2.169648-1.68744373 4.45989227-2.531256 6.870552-2.531256h61.292556c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-2.71206 34.714368c-.12059627 1.68744373.361608 3.073668 1.446432 4.158492 1.084824 1.084824 2.41065973 1.627236 3.977688 1.627236h30.013464c1.56757068 0 2.892864-.542412 3.977688-1.627236 1.084824-1.084824 1.56757068-2.47104827 1.446432-4.158492l-2.71206-34.714368c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h61.292556c2.41011732 0 4.700904.84381227 6.870552 2.531256 2.169648 1.68744373 3.73721868 3.67628773 4.700904 5.966532l75.395268 188.759376c3.13333332 7.47262932 4.700904 14.46432 4.700904 20.973264z" fill-rule="nonzero"></path></g><g><path d="M242.956504 146.0804v-.723216l-4.339296-57.85728c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-33.629544c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-4.339296 57.85728v.723216c-.12059627 1.446432.361608 2.65239468 1.446432 3.61608 1.084824.96368532 2.350452 1.446432 3.796884 1.446432h44.116176c1.446432 0 2.71206-.48274668 3.796884-1.446432 1.084824-.96368532 1.56757068-2.169648 1.446432-3.61608zm137.230236 84.435468c0 8.79973068-2.77172532 13.198692-8.316984 13.198692H244.58374c1.56757068 0 2.892864-.57314868 3.977688-1.717638 1.084824-1.14448932 1.56757068-2.50051932 1.446432-4.06809l-3.61608-46.285824c-.12113868-1.56757068-.78288132-2.92360068-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h-49.178688c-1.56702827 0-2.95307173.57314868-4.158492 1.717638-1.20542027 1.14448932-1.86824773 2.50051932-1.988844 4.06809l-3.61608 46.285824c-.12059627 1.56757068.361608 2.92360068 1.446432 4.06809s2.41065973 1.717638 3.977688 1.717638H59.440444c-5.54471627 0-8.316984-4.39896132-8.316984-13.198692 0-6.508944 1.56702827-13.50063468 4.700904-20.973264l75.395268-188.759376c.96422773-2.29024427 2.531256-4.27908827 4.700904-5.966532 2.169648-1.68744373 4.45989227-2.531256 6.870552-2.531256h61.292556c-1.56702827 0-2.95307173.57260627-4.158492 1.717638-1.20542027 1.14503173-1.86824773 2.50106173-1.988844 4.06809l-2.71206 34.714368c-.12059627 1.68744373.361608 3.073668 1.446432 4.158492 1.084824 1.084824 2.41065973 1.627236 3.977688 1.627236h30.013464c1.56757068 0 2.892864-.542412 3.977688-1.627236 1.084824-1.084824 1.56757068-2.47104827 1.446432-4.158492l-2.71206-34.714368c-.12113868-1.56702827-.78288132-2.92305827-1.988844-4.06809s-2.59092132-1.717638-4.158492-1.717638h61.292556c2.41011732 0 4.700904.84381227 6.870552 2.531256 2.169648 1.68744373 3.73721868 3.67628773 4.700904 5.966532l75.395268 188.759376c3.13333332 7.47262932 4.700904 14.46432 4.700904 20.973264z" fill-rule="nonzero"></path></g></symbol></svg></html>