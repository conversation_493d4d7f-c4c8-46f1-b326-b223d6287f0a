#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f(a) { return a.func(); }
  f(new (function Obj() { this.func = function() { return; }})())
"
frame size: 1
parameter count: 2
bytecode array length: 10
bytecodes: [
  /*   25 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
                B(Star0),
  /*   25 E> */ B(CallProperty0), R(0), R(arg0), U8(2),
  /*   32 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  function f(a, b, c) { return a.func(b, c); }
  f(new (function Obj() { this.func = function() { return; }})(), 1, 2)
"
frame size: 1
parameter count: 4
bytecode array length: 12
bytecodes: [
  /*   31 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
                <PERSON>(Star0),
  /*   31 E> */ B(CallProperty2), R(0), R(arg0), R(arg1), R(arg2), U8(2),
  /*   42 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  function f(a, b) { return a.func(b + b, b); }
  f(new (function Obj() { this.func = function() { return; }})(), 1)
"
frame size: 3
parameter count: 3
bytecode array length: 18
bytecodes: [
  /*   28 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
                B(Star0),
                B(Ldar), R(arg1),
  /*   35 E> */ B(Add), R(arg1), U8(2),
                B(Star2),
  /*   28 E> */ B(CallProperty2), R(0), R(arg0), R(2), R(arg1), U8(3),
  /*   43 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  function f(a) {
    var b = {};
    b.name384;
    b.name385;
    b.name386;
    b.name387;
    b.name388;
    b.name389;
    b.name390;
    b.name391;
    b.name392;
    b.name393;
    b.name394;
    b.name395;
    b.name396;
    b.name397;
    b.name398;
    b.name399;
    b.name400;
    b.name401;
    b.name402;
    b.name403;
    b.name404;
    b.name405;
    b.name406;
    b.name407;
    b.name408;
    b.name409;
    b.name410;
    b.name411;
    b.name412;
    b.name413;
    b.name414;
    b.name415;
    b.name416;
    b.name417;
    b.name418;
    b.name419;
    b.name420;
    b.name421;
    b.name422;
    b.name423;
    b.name424;
    b.name425;
    b.name426;
    b.name427;
    b.name428;
    b.name429;
    b.name430;
    b.name431;
    b.name432;
    b.name433;
    b.name434;
    b.name435;
    b.name436;
    b.name437;
    b.name438;
    b.name439;
    b.name440;
    b.name441;
    b.name442;
    b.name443;
    b.name444;
    b.name445;
    b.name446;
    b.name447;
    b.name448;
    b.name449;
    b.name450;
    b.name451;
    b.name452;
    b.name453;
    b.name454;
    b.name455;
    b.name456;
    b.name457;
    b.name458;
    b.name459;
    b.name460;
    b.name461;
    b.name462;
    b.name463;
    b.name464;
    b.name465;
    b.name466;
    b.name467;
    b.name468;
    b.name469;
    b.name470;
    b.name471;
    b.name472;
    b.name473;
    b.name474;
    b.name475;
    b.name476;
    b.name477;
    b.name478;
    b.name479;
    b.name480;
    b.name481;
    b.name482;
    b.name483;
    b.name484;
    b.name485;
    b.name486;
    b.name487;
    b.name488;
    b.name489;
    b.name490;
    b.name491;
    b.name492;
    b.name493;
    b.name494;
    b.name495;
    b.name496;
    b.name497;
    b.name498;
    b.name499;
    b.name500;
    b.name501;
    b.name502;
    b.name503;
    b.name504;
    b.name505;
    b.name506;
    b.name507;
    b.name508;
    b.name509;
    b.name510;
    b.name511;
    a.func;
    return a.func(); }
  f(new (function Obj() { this.func = function() { return; }})())
"
frame size: 2
parameter count: 2
bytecode array length: 540
bytecodes: [
  /*   26 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   34 S> */ B(GetNamedProperty), R(0), U8(0), U8(0),
  /*   47 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   60 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   73 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*   86 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*   99 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  112 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  125 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  138 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  151 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  164 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  177 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  190 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  203 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  216 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  229 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  242 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  255 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  268 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  281 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  294 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  307 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  320 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  333 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  346 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  359 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  372 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  385 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  398 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  411 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  424 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  437 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  450 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  463 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  476 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  489 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  502 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  515 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  528 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  541 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  554 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  567 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  580 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  593 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  606 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  619 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  632 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  645 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  658 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  671 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  684 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  697 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  710 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  723 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  736 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  749 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  762 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  775 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  788 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  801 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  814 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  827 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  840 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  853 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  866 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  879 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  892 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  905 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  918 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  931 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  944 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  957 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  970 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /*  983 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /*  996 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /* 1009 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /* 1022 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /* 1035 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /* 1048 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /* 1061 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /* 1074 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /* 1087 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1100 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1113 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1126 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1139 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1152 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1165 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1178 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1191 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1204 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1217 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1230 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1243 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1256 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1269 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1282 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1295 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1308 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1321 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1334 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1347 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1360 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1373 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1386 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1399 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1412 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1425 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1438 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1451 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1464 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1477 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1490 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1503 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1516 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1529 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1542 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1555 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1568 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1581 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1594 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1607 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1620 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1633 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1646 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1659 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1672 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1685 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1698 S> */ B(Wide), B(GetNamedProperty), R16(arg0), U16(128), U16(256),
  /* 1715 S> */ B(Wide), B(GetNamedProperty), R16(arg0), U16(128), U16(256),
                B(Star1),
  /* 1715 E> */ B(Wide), B(CallProperty0), R16(1), R16(arg0), U16(258),
  /* 1722 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name384"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name385"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name386"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name387"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name388"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name389"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name390"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name391"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name392"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name393"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name394"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name395"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name396"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name397"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name398"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name399"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name400"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name401"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name402"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name403"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name404"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name405"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name406"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name407"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name408"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name409"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name410"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name411"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name412"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name413"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name414"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name415"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name416"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name417"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name418"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name419"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name420"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name421"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name422"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name423"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name424"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name425"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name426"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name427"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name428"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name429"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name430"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name431"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name432"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name433"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name434"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name435"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name436"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name437"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name438"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name439"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name440"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name441"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name442"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name443"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name444"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name445"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name446"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name447"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name448"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name449"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name450"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name451"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name452"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name453"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name454"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name455"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name456"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name457"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name458"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name459"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name460"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name461"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name462"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name463"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name464"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name465"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name466"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name467"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name468"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name469"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name470"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name471"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name472"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name473"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name474"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name475"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name476"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name477"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name478"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name479"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name480"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name481"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name482"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name483"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name484"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name485"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name486"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name487"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name488"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name489"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name490"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name491"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name492"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name493"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name494"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name495"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name496"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name497"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name498"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name499"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name500"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name501"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name502"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name503"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name504"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name505"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name506"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name507"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name508"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name509"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name510"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name511"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

---
snippet: "
  function f(a) { return a.func(1).func(2).func(3); }
  f(new (function Obj() { this.func = function(a) { return this; }})())
"
frame size: 5
parameter count: 2
bytecode array length: 42
bytecodes: [
  /*   25 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
                B(Star2),
                B(LdaSmi), I8(1),
                B(Star4),
  /*   25 E> */ B(CallProperty1), R(2), R(arg0), R(4), U8(2),
                B(Star2),
  /*   32 E> */ B(GetNamedProperty), R(2), U8(0), U8(4),
                B(Star1),
                B(LdaSmi), I8(2),
                B(Star3),
  /*   33 E> */ B(CallProperty1), R(1), R(2), R(3), U8(6),
                B(Star1),
  /*   40 E> */ B(GetNamedProperty), R(1), U8(0), U8(8),
                B(Star0),
                B(LdaSmi), I8(3),
                B(Star2),
  /*   41 E> */ B(CallProperty1), R(0), R(1), R(2), U8(10),
  /*   49 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["func"],
]
handlers: [
]

