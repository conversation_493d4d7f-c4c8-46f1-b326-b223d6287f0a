// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=de6b935b77168bd9b44f26643c510f360f8b6ebd$
//

#include "libcef_dll/ctocpp/extension_ctocpp.h"
#include "libcef_dll/cpptoc/extension_handler_cpptoc.h"
#include "libcef_dll/ctocpp/dictionary_value_ctocpp.h"
#include "libcef_dll/ctocpp/request_context_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") CefString CefExtensionCToCpp::GetIdentifier() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_identifier))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_identifier(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall") CefString CefExtensionCToCpp::GetPath() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_path))
    return CefString();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_path(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefDictionaryValue> CefExtensionCToCpp::GetManifest() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_manifest))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_dictionary_value_t* _retval = _struct->get_manifest(_struct);

  // Return type: refptr_same
  return CefDictionaryValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
bool CefExtensionCToCpp::IsSame(CefRefPtr<CefExtension> that) {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_same))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: that; type: refptr_same
  DCHECK(that.get());
  if (!that.get())
    return false;

  // Execute
  int _retval = _struct->is_same(_struct, CefExtensionCToCpp::Unwrap(that));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefExtensionHandler> CefExtensionCToCpp::GetHandler() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_handler))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_extension_handler_t* _retval = _struct->get_handler(_struct);

  // Return type: refptr_diff
  return CefExtensionHandlerCppToC::Unwrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefRequestContext> CefExtensionCToCpp::GetLoaderContext() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_loader_context))
    return nullptr;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_request_context_t* _retval = _struct->get_loader_context(_struct);

  // Return type: refptr_same
  return CefRequestContextCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall") bool CefExtensionCToCpp::IsLoaded() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_loaded))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_loaded(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") void CefExtensionCToCpp::Unload() {
  shutdown_checker::AssertNotShutdown();

  cef_extension_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, unload))
    return;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->unload(_struct);
}

// CONSTRUCTOR - Do not edit by hand.

CefExtensionCToCpp::CefExtensionCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefExtensionCToCpp::~CefExtensionCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_extension_t*
CefCToCppRefCounted<CefExtensionCToCpp, CefExtension, cef_extension_t>::
    UnwrapDerived(CefWrapperType type, CefExtension* c) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefExtensionCToCpp,
                                   CefExtension,
                                   cef_extension_t>::kWrapperType =
    WT_EXTENSION;
