# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This test checks the behavior of the reduceRight() method on a number of objects.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS [0,1,2,3].reduceRight(function(a,b){ return a + b; }) is 6
PASS [1,2,3].reduceRight(function(a,b){ return a + b; }) is 6
PASS [0,1,2,3].reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS [1,2,3].reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS toObject([0,1,2,3]).reduceRight(function(a,b){ return a + b; }) is 6
PASS toObject([1,2,3]).reduceRight(function(a,b){ return a + b; }) is 6
PASS toObject([0,1,2,3]).reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS toObject([1,2,3]).reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS toUnorderedObject([0,1,2,3]).reduceRight(function(a,b){ return a + b; }) is 6
PASS toUnorderedObject([1,2,3]).reduceRight(function(a,b){ return a + b; }) is 6
PASS toUnorderedObject([0,1,2,3]).reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS toUnorderedObject([1,2,3]).reduceRight(function(a,b){ return a + b; }, 4) is 10
PASS sparseArray.reduceRight(function(a,b){ return a + b; }, 0) is 10
PASS toObject(sparseArray).reduceRight(function(a,b){ return a + b; }, 0) is 10
PASS sparseArray.reduceRight(function(a,b){ callCount++; }); callCount is 0
PASS toObject(sparseArray).reduceRight(function(a,b){ callCount++; }); callCount is 0
PASS sparseArray.reduceRight(function(a,b){ callCount++; }, 0); callCount is 1
PASS toObject(sparseArray).reduceRight(function(a,b){ callCount++; }, 0); callCount is 1
PASS [0,1,2,3,4].reduceRight(function(a,b){ callCount++; }, 0); callCount is 5
PASS [0,1,2,3,4].reduceRight(function(a,b){ callCount++; }); callCount is 4
PASS [1, 2, 3, 4].reduceRight(function(a,b, i, thisObj){ thisObj.length--; callCount++; return a + b; }, 0); callCount is 4
PASS [1, 2, 3, 4].reduceRight(function(a,b, i, thisObj){ thisObj.length = 1; callCount++; return a + b; }, 0); callCount is 2
PASS [1, 2, 3, 4].reduceRight(function(a,b, i, thisObj){ thisObj.length++; callCount++; return a + b; }, 0); callCount is 4
PASS toObject([1, 2, 3, 4]).reduceRight(function(a,b, i, thisObj){ thisObj.length--; callCount++; return a + b; }, 0); callCount is 4
PASS toObject([1, 2, 3, 4]).reduceRight(function(a,b, i, thisObj){ thisObj.length++; callCount++; return a + b; }, 0); callCount is 4
PASS [[0,1], [2,3], [4,5]].reduceRight(function(a,b) {return a.concat(b);}, []) is [4,5,2,3,0,1]
PASS toObject([[0,1], [2,3], [4,5]]).reduceRight(function(a,b) {return a.concat(b);}, []) is [4,5,2,3,0,1]
PASS toObject([0,1,2,3,4,5]).reduceRight(function(a,b,i) {return a.concat([i,b]);}, []) is [5,5,4,4,3,3,2,2,1,1,0,0]
PASS toUnorderedObject([[0,1], [2,3], [4,5]]).reduceRight(function(a,b) {return a.concat(b);}, []) is [4,5,2,3,0,1]
PASS toUnorderedObject([0,1,2,3,4,5]).reduceRight(function(a,b,i) {return a.concat([i,b]);}, []) is [5,5,4,4,3,3,2,2,1,1,0,0]
PASS [0,1,2,3,4,5].reduceRight(function(a,b,i) {return a.concat([i,b]);}, []) is [5,5,4,4,3,3,2,2,1,1,0,0]
PASS [2,3].reduceRight(function() {'use strict'; return this;}) is undefined
PASS successfullyParsed is true

TEST COMPLETE

