#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  var f;
  f = function f() {};
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
                B(LdaUndefined),
  /*   25 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var f;
  f = function f() { return f; };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
                B(Mov), R(closure), R(0),
  /*   26 S> */ B(Ldar), R(0),
  /*   35 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

