Tests for ES6 class syntax expressions

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS constructorCallCount is 0
PASS A.someStaticMethod() is staticMethodValue
PASS A.someStaticGetter is getterValue
PASS setterValue = undefined; A.someStaticSetter = 123; setterValue is 123
PASS (new A).someInstanceMethod() is instanceMethodValue
PASS constructorCallCount is 1
PASS (new A).someGetter is getterValue
PASS constructorCallCount is 2
PASS (new A).someGetter is getterValue
PASS setterValue = undefined; (new A).someSetter = 789; setterValue is 789
PASS (new A).__proto__ is A.prototype
PASS A.prototype.constructor is A
PASS x = class threw exception SyntaxError: Unexpected end of input.
PASS x = class { threw exception SyntaxError: Unexpected end of input.
PASS x = class { ( } threw exception SyntaxError: Unexpected token '('.
PASS x = class {} did not throw exception.
PASS x = class { constructor() {} constructor() {} } threw exception SyntaxError: A class may only have one constructor.
PASS x = class { get constructor() {} } threw exception SyntaxError: Class constructor may not be an accessor.
PASS x = class { set constructor() {} } threw exception SyntaxError: Class constructor may not be an accessor.
PASS x = class { constructor() {} static constructor() { return staticMethodValue; } } did not throw exception.
PASS x = class { constructor() {} static constructor() { return staticMethodValue; } }; x.constructor() is staticMethodValue
PASS x = class { constructor() {} static prototype() {} } threw exception SyntaxError: Classes may not have a static property named 'prototype'.
PASS x = class { constructor() {} static get prototype() {} } threw exception SyntaxError: Classes may not have a static property named 'prototype'.
PASS x = class { constructor() {} static set prototype() {} } threw exception SyntaxError: Classes may not have a static property named 'prototype'.
PASS x = class  { constructor() {} prototype() { return instanceMethodValue; } } did not throw exception.
PASS x = class { constructor() {} prototype() { return instanceMethodValue; } }; (new x).prototype() is instanceMethodValue
PASS x = class { constructor() {} set foo(a) {} } did not throw exception.
PASS x = class { constructor() {} set foo({x, y}) {} } did not throw exception.
PASS x = class { constructor() {} set foo() {} } threw exception SyntaxError: Setter must have exactly one formal parameter..
PASS x = class { constructor() {} set foo(a, b) {} } threw exception SyntaxError: Setter must have exactly one formal parameter..
PASS x = class { constructor() {} get foo() {} } did not throw exception.
PASS x = class { constructor() {} get foo(x) {} } threw exception SyntaxError: Getter must not have any formal parameters..
PASS x = class { constructor() {} get foo({x, y}) {} } threw exception SyntaxError: Getter must not have any formal parameters..
PASS successfullyParsed is true

TEST COMPLETE
