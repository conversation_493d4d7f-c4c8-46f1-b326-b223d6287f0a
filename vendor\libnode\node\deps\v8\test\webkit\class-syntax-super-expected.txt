Tests for ES6 class syntax "super"

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS (new Base) instanceof Base is true
PASS (new Derived) instanceof Derived is true
PASS (new Derived).callBaseMethod() is baseMethodValue
PASS x = (new Derived).callBaseMethod; x() is baseMethodValue
PASS (new Derived).callBaseMethodInGetter is baseMethodValue
PASS (new Derived).callBaseMethodInSetter = 1; valueInSetter is baseMethodValue
PASS (new Derived).baseMethodInGetterSetter is (new Base).baseMethod
PASS (new Derived).baseMethodInGetterSetter = 1; valueInSetter is (new Base).baseMethod
PASS Derived.staticMethod() is "base3"
PASS (new SecondDerived).chainMethod() is ["base", "derived", "secondDerived"]
PASS x = class extends Base { constructor() { super(); } super() {} } did not throw exception.
PASS x = class extends Base { constructor() { super(); } method() { super() } } threw exception SyntaxError: 'super' keyword unexpected here.
PASS x = class extends Base { constructor() { super(); } method() { super } } threw exception SyntaxError: 'super' keyword unexpected here.
PASS x = class extends Base { constructor() { super(); } method() { return new super } } threw exception SyntaxError: 'super' keyword unexpected here.
PASS x = class extends Base { constructor() { super(); } method1() { delete (super.foo) } method2() { delete super["foo"] } } did not throw exception.
PASS (new x).method1() threw exception ReferenceError: Unsupported reference to 'super'.
PASS (new x).method2() threw exception ReferenceError: Unsupported reference to 'super'.
PASS new (class { constructor() { return undefined; } }) instanceof Object is true
PASS new (class { constructor() { return 1; } }) instanceof Object is true
PASS new (class extends Base { constructor() { return undefined } }) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS new (class extends Base { constructor() { super(); return undefined } }) instanceof Object is true
PASS x = { }; new (class extends Base { constructor() { return x } }); is x
PASS x instanceof Base is false
PASS new (class extends Base { constructor() { } }) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS new (class extends Base { constructor() { return 1; } }) threw exception TypeError: Derived constructors may only return object or undefined.
PASS new (class extends null { constructor() { return undefined } }) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS new (class extends null { constructor() { super(); return undefined } }) threw exception TypeError: Super constructor null of anonymous class is not a constructor.
PASS x = { }; new (class extends null { constructor() { return x } }); is x
PASS x instanceof Object is true
PASS new (class extends null { constructor() { } }) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS new (class extends null { constructor() { return 1; } }) threw exception TypeError: Derived constructors may only return object or undefined.
PASS new (class extends null { constructor() { super() } }) threw exception TypeError: Super constructor null of anonymous class is not a constructor.
PASS new (class { constructor() { super() } }) threw exception SyntaxError: 'super' keyword unexpected here.
PASS function x() { super(); } threw exception SyntaxError: 'super' keyword unexpected here.
PASS new (class extends Object { constructor() { function x() { super() } } }) threw exception SyntaxError: 'super' keyword unexpected here.
PASS new (class extends Object { constructor() { function x() { super.method } } }) threw exception SyntaxError: 'super' keyword unexpected here.
PASS function x() { super.method(); } threw exception SyntaxError: 'super' keyword unexpected here.
PASS function x() { super(); } threw exception SyntaxError: 'super' keyword unexpected here.
PASS successfullyParsed is true

TEST COMPLETE
