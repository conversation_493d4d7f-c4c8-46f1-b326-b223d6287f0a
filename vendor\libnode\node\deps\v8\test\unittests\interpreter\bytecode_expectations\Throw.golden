#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  throw 1;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(1),
  /*   34 E> */ B(Throw),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  throw 'Error';
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaConstant), U8(0),
  /*   34 E> */ B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["Error"],
]
handlers: [
]

---
snippet: "
  var a = 1; if (a) { throw 'Error'; };
"
frame size: 1
parameter count: 1
bytecode array length: 10
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(JumpIfToBooleanFalse), U8(5),
  /*   54 S> */ B(LdaConstant), U8(0),
  /*   54 E> */ B(Throw),
                B(LdaUndefined),
  /*   72 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["Error"],
]
handlers: [
]

