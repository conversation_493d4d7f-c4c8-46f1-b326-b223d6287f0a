# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This test checks the behavior of [[DefineOwnProperty]] applied to Array objects (see ES5.1 15.4.5.1).

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS Object.defineProperty([], 'x', { get:function(){return true;} }).x is true
PASS Object.defineProperty([], 'length', { value: 1 }).length is 1
PASS var a = Object.defineProperty([], 'length', { writable: false }); a[1] = 1; a.length is 0
PASS var a = Object.defineProperty([], 'length', { writable: false }); a.length = 1; a.length is 0
PASS var a = Object.defineProperty([], 'length', {}); a.length = 1; a.length is 1
PASS Object.defineProperty([], 'length', { get:function(){return true;} }) threw exception TypeError: Cannot redefine property: length.
PASS Object.defineProperty([], 'length', { enumerable: true }) threw exception TypeError: Cannot redefine property: length.
PASS Object.defineProperty([], 'length', { configurable: true }) threw exception TypeError: Cannot redefine property: length.
PASS Object.defineProperty(Object.defineProperty([], 'length', { writable: false }), 'length', { writable: true }) threw exception TypeError: Cannot redefine property: length.
PASS var a = Object.defineProperty([], '0', { value: 42 }); a[0] is 42
PASS var a = Object.defineProperty([42], '0', { writable: false }); a[0] = 1; a[0] is 42
PASS var a = Object.defineProperty([42], '0', { enumerable: false }); a[0] + Object.keys(a).length is 42
PASS var a = Object.defineProperty([42], '0', { configurable: false }); a.length = 0; a[0] is 42
PASS var foo = 0; Object.defineProperty([], '0', { set:function(x){foo = x;} })[0] = 42; foo is 42
PASS Object.defineProperty([], '0', { get:function(){return true;} })[0] is true
PASS Object.defineProperty(Object.defineProperty([true], '0', { configurable:true, writable: false }), '0', { writable: true })[0] is true
PASS Object.defineProperty(Object.defineProperty([true], '0', { configurable:false, writable: false }), '0', { writable: true })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: 1, writable:true }), '0', { value: 2 })[0] is 2
PASS Object.defineProperty(Object.defineProperty([], '0', { value: 1 }), '0', { value: 1 })[0] is 1
PASS Object.defineProperty(Object.defineProperty([], '0', { value: Number.NaN }), '0', { value: -Number.NaN })[0] is Number.NaN
PASS Object.defineProperty(Object.defineProperty([], '0', { value: 'okay'.substring(0,2) }), '0', { value: 'not ok'.substring(4,6) })[0] is "ok"
PASS Object.defineProperty(Object.defineProperty([], '0', { value: true }), '0', { value: true })[0] is true
PASS Object.defineProperty(Object.defineProperty([], '0', { value: false }), '0', { value: false })[0] is false
PASS Object.defineProperty(Object.defineProperty([], '0', { value: null }), '0', { value: null })[0] is null
PASS Object.defineProperty(Object.defineProperty([], '0', { value: undefined }), '0', { value: undefined })[0] is undefined
PASS Object.defineProperty(Object.defineProperty([], '0', { value: Math }), '0', { value: Math })[0] is Math
PASS Object.defineProperty(Object.defineProperty([], '0', { value: 1 }), '0', { value: 2 })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: 'okay' }), '0', { value: 'not ok' })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: true }), '0', { value: false })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: false }), '0', { value: true })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: Math }), '0', { value: Object })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: null }), '0', { value: undefined })[0] threw exception TypeError: Cannot redefine property: 0.
PASS Object.defineProperty(Object.defineProperty([], '0', { value: undefined }), '0', { value: null })[0] threw exception TypeError: Cannot redefine property: 0.
PASS arrObj[0] = 42; arrObj.set; is true
PASS arrObj[1] = true; arrObj[1]; is true
PASS successfullyParsed is true

TEST COMPLETE

