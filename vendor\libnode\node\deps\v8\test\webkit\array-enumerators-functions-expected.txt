# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This test checks the behavior of the various array enumeration functions in certain edge case scenarios

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.every.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.forEach.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.some.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.filter.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduce.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnIndex, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], returnIndex, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.map.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnFalse, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], returnFalse, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnTrue, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], returnTrue, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnElem, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], returnElem, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], increaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], increaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], decreaseLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], decreaseLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(simpleArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(emptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeEmptyArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], halveLength, 0) is count=0;lastIndex=-1;Array.prototype.reduceRight.call(toUnorderedObject(largeSparseArray), forwarders[f], halveLength, 0)
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).every(forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).every(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).every(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).every(forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.every.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], decreaseLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).forEach(forwarders[f], halveLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).forEach(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).forEach(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).forEach(forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.forEach.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 1
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 2
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 2
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], decreaseLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).some(forwarders[f], halveLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).some(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).some(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).some(forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.some.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], decreaseLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).filter(forwarders[f], halveLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).filter(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).filter(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).filter(forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.filter.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], decreaseLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduce(forwarders[f], halveLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduce(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduce(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduce(forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduce.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], returnIndex, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], returnIndex, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], returnIndex, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], decreaseLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).map(forwarders[f], halveLength, 0); count is 3
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).map(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).map(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).map(forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.map.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnFalse, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnFalse, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnFalse, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnTrue, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnTrue, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnTrue, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], returnElem, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], returnElem, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], returnElem, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], increaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], increaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], increaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], decreaseLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], decreaseLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], decreaseLength, 0); count is 7
PASS count=0;lastIndex=-1;copyArray(simpleArray).reduceRight(forwarders[f], halveLength, 0); count is 4
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(simpleArray), forwarders[f], halveLength, 0); count is 6
PASS count=0;lastIndex=-1;copyArray(emptyArray).reduceRight(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(emptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeEmptyArray).reduceRight(forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeEmptyArray), forwarders[f], halveLength, 0); count is 0
PASS count=0;lastIndex=-1;copyArray(largeSparseArray).reduceRight(forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS count=0;lastIndex=-1;Array.prototype.reduceRight.call(toObject(largeSparseArray), forwarders[f], halveLength, 0); count is 7
PASS successfullyParsed is true

TEST COMPLETE

