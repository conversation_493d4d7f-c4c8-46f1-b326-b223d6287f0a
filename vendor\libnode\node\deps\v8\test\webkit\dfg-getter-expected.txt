# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests that DFG getter caching does not break the world.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS foo(o) is 0
PASS foo(o) is 1
PASS foo(o) is 2
PASS foo(o) is 3
PASS foo(o) is 4
PASS foo(o) is 5
PASS foo(o) is 6
PASS foo(o) is 7
PASS foo(o) is 8
PASS foo(o) is 9
PASS foo(o) is 10
PASS foo(o) is 11
PASS foo(o) is 12
PASS foo(o) is 13
PASS foo(o) is 14
PASS foo(o) is 15
PASS foo(o) is 16
PASS foo(o) is 17
PASS foo(o) is 18
PASS foo(o) is 19
PASS foo(o) is 20
PASS foo(o) is 21
PASS foo(o) is 22
PASS foo(o) is 23
PASS foo(o) is 24
PASS foo(o) is 25
PASS foo(o) is 26
PASS foo(o) is 27
PASS foo(o) is 28
PASS foo(o) is 29
PASS foo(o) is 30
PASS foo(o) is 31
PASS foo(o) is 32
PASS foo(o) is 33
PASS foo(o) is 34
PASS foo(o) is 35
PASS foo(o) is 36
PASS foo(o) is 37
PASS foo(o) is 38
PASS foo(o) is 39
PASS foo(o) is 40
PASS foo(o) is 41
PASS foo(o) is 42
PASS foo(o) is 43
PASS foo(o) is 44
PASS foo(o) is 45
PASS foo(o) is 46
PASS foo(o) is 47
PASS foo(o) is 48
PASS foo(o) is 49
PASS foo(o) is 50
PASS foo(o) is 51
PASS foo(o) is 52
PASS foo(o) is 53
PASS foo(o) is 54
PASS foo(o) is 55
PASS foo(o) is 56
PASS foo(o) is 57
PASS foo(o) is 58
PASS foo(o) is 59
PASS foo(o) is 60
PASS foo(o) is 61
PASS foo(o) is 62
PASS foo(o) is 63
PASS foo(o) is 64
PASS foo(o) is 65
PASS foo(o) is 66
PASS foo(o) is 67
PASS foo(o) is 68
PASS foo(o) is 69
PASS foo(o) is 70
PASS foo(o) is 71
PASS foo(o) is 72
PASS foo(o) is 73
PASS foo(o) is 74
PASS foo(o) is 75
PASS foo(o) is 76
PASS foo(o) is 77
PASS foo(o) is 78
PASS foo(o) is 79
PASS foo(o) is 80
PASS foo(o) is 81
PASS foo(o) is 82
PASS foo(o) is 83
PASS foo(o) is 84
PASS foo(o) is 85
PASS foo(o) is 86
PASS foo(o) is 87
PASS foo(o) is 88
PASS foo(o) is 89
PASS foo(o) is 90
PASS foo(o) is 91
PASS foo(o) is 92
PASS foo(o) is 93
PASS foo(o) is 94
PASS foo(o) is 95
PASS foo(o) is 96
PASS foo(o) is 97
PASS foo(o) is 98
PASS foo(o) is 99
PASS foo(o) is 100
PASS foo(o) is 101
PASS foo(o) is 102
PASS foo(o) is 103
PASS foo(o) is 104
PASS foo(o) is 105
PASS foo(o) is 106
PASS foo(o) is 107
PASS foo(o) is 108
PASS foo(o) is 109
PASS foo(o) is 110
PASS foo(o) is 111
PASS foo(o) is 112
PASS foo(o) is 113
PASS foo(o) is 114
PASS foo(o) is 115
PASS foo(o) is 116
PASS foo(o) is 117
PASS foo(o) is 118
PASS foo(o) is 119
PASS foo(o) is 120
PASS foo(o) is 121
PASS foo(o) is 122
PASS foo(o) is 123
PASS foo(o) is 124
PASS foo(o) is 125
PASS foo(o) is 126
PASS foo(o) is 127
PASS foo(o) is 128
PASS foo(o) is 129
PASS foo(o) is 130
PASS foo(o) is 131
PASS foo(o) is 132
PASS foo(o) is 133
PASS foo(o) is 134
PASS foo(o) is 135
PASS foo(o) is 136
PASS foo(o) is 137
PASS foo(o) is 138
PASS foo(o) is 139
PASS foo(o) is 140
PASS foo(o) is 141
PASS foo(o) is 142
PASS foo(o) is 143
PASS foo(o) is 144
PASS foo(o) is 145
PASS foo(o) is 146
PASS foo(o) is 147
PASS foo(o) is 148
PASS foo(o) is 149
PASS foo(o) is 150
PASS foo(o) is 151
PASS foo(o) is 152
PASS foo(o) is 153
PASS foo(o) is 154
PASS foo(o) is 155
PASS foo(o) is 156
PASS foo(o) is 157
PASS foo(o) is 158
PASS foo(o) is 159
PASS foo(o) is 160
PASS foo(o) is 161
PASS foo(o) is 162
PASS foo(o) is 163
PASS foo(o) is 164
PASS foo(o) is 165
PASS foo(o) is 166
PASS foo(o) is 167
PASS foo(o) is 168
PASS foo(o) is 169
PASS foo(o) is 170
PASS foo(o) is 171
PASS foo(o) is 172
PASS foo(o) is 173
PASS foo(o) is 174
PASS foo(o) is 175
PASS foo(o) is 176
PASS foo(o) is 177
PASS foo(o) is 178
PASS foo(o) is 179
PASS foo(o) is 180
PASS foo(o) is 181
PASS foo(o) is 182
PASS foo(o) is 183
PASS foo(o) is 184
PASS foo(o) is 185
PASS foo(o) is 186
PASS foo(o) is 187
PASS foo(o) is 188
PASS foo(o) is 189
PASS foo(o) is 190
PASS foo(o) is 191
PASS foo(o) is 192
PASS foo(o) is 193
PASS foo(o) is 194
PASS foo(o) is 195
PASS foo(o) is 196
PASS foo(o) is 197
PASS foo(o) is 198
PASS foo(o) is 199
PASS successfullyParsed is true

TEST COMPLETE

