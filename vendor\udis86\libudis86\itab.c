/* itab.c -- generated by udis86:scripts/ud_itab.py, do no edit */
#include "decode.h"

#define GRO<PERSON>(n) (0x8000 | (n))
#define INVALID  0


const uint16_t ud_itab__0[] = {
  /*  0 */          15,          16,          17,          18,
  /*  4 */          19,          20,  <PERSON><PERSON><PERSON>(426),  <PERSON><PERSON><PERSON>(415),
  /*  8 */         964,         965,         966,         967,
  /*  c */         968,         969,  <PERSON><PERSON><PERSON>(427),   <PERSON><PERSON><PERSON>(17),
  /* 10 */           5,           6,           7,           8,
  /* 14 */           9,          10,  <PERSON><PERSON><PERSON>(428),  G<PERSON><PERSON>(416),
  /* 18 */        1336,        1337,        1338,        1339,
  /* 1c */        1340,        1341,  <PERSON><PERSON><PERSON>(429),  <PERSON><PERSON><PERSON>(417),
  /* 20 */          49,          50,          51,          52,
  /* 24 */          53,          54,     INVALID,  G<PERSON><PERSON>(374),
  /* 28 */        1407,        1408,        1409,        1410,
  /* 2c */        1411,        1412,     <PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON>(375),
  /* 30 */        1487,        1488,        1489,        1490,
  /* 34 */        1491,        1492,     <PERSON><PERSON><PERSON><PERSON>,    <PERSON><PERSON><PERSON>(1),
  /* 38 */         100,         101,         102,         103,
  /* 3c */         104,         105,     <PERSON><PERSON><PERSON>ID,    <PERSON><PERSON><PERSON>(4),
  /* 40 */         699,         700,         701,         702,
  /* 44 */         703,         704,         705,         706,
  /* 48 */         175,         176,         177,         178,
  /* 4c */         179,         180,         181,         182,
  /* 50 */        1246,        1247,        1248,        1249,
  /* 54 */        1250,        1251,        1252,        1253,
  /* 58 */        1101,        1102,        1103,        1104,
  /* 5c */        1105,        1106,        1107,        1108,
  /* 60 */  GROUP(430),  GROUP(419),  GROUP(446),  GROUP(367),
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */        1254,         697,        1256,         698,
  /* 6c */         709,  GROUP(403),         982,  GROUP(414),
  /* 70 */         726,         728,         730,         732,
  /* 74 */         734,         736,         738,         740,
  /* 78 */         742,         744,         746,         748,
  /* 7c */         750,         752,         754,         756,
  /* 80 */    GROUP(5),   GROUP(15),    GROUP(6),   GROUP(16),
  /* 84 */        1433,        1434,        1475,        1476,
  /* 88 */         828,         829,         830,         831,
  /* 8c */         832,         770,         833,  GROUP(418),
  /* 90 */        1477,        1478,        1479,        1480,
  /* 94 */        1481,        1482,        1483,        1484,
  /* 98 */  GROUP(371),  GROUP(373),  GROUP(370),        1470,
  /* 9c */  GROUP(433),  GROUP(422),        1310,         766,
  /* a0 */         834,         835,         836,         837,
  /* a4 */         922,  GROUP(413),         114,  GROUP(372),
  /* a8 */        1435,        1436,        1402,  GROUP(445),
  /* ac */         790,  GROUP(410),        1346,  GROUP(444),
  /* b0 */         838,         839,         840,         841,
  /* b4 */         842,         843,         844,         845,
  /* b8 */         846,         847,         848,         849,
  /* bc */         850,         851,         852,         853,
  /* c0 */  GROUP(437),  GROUP(438),        1301,        1302,
  /* c4 */  GROUP(297),  GROUP(408),  GROUP(411),  GROUP(412),
  /* c8 */         200,         776,        1303,        1304,
  /* cc */         713,         714,  GROUP(404),  GROUP(405),
  /* d0 */  GROUP(439),  GROUP(442),  GROUP(440),  GROUP(441),
  /* d4 */    GROUP(3),    GROUP(2),  GROUP(443),        1486,
  /* d8 */  GROUP(385),  GROUP(379),  GROUP(397),  GROUP(394),
  /* dc */  GROUP(382),  GROUP(400),  GROUP(388),  GROUP(391),
  /* e0 */         794,         795,         796,  GROUP(406),
  /* e4 */         690,         691,         978,         979,
  /* e8 */          72,         763,  GROUP(407),         765,
  /* ec */         692,         693,         980,         981,
  /* f0 */         789,         712,        1299,        1300,
  /* f4 */         687,          83,  GROUP(378),  GROUP(377),
  /* f8 */          77,        1395,          81,        1398,
  /* fc */          78,        1396,  GROUP(376),  GROUP(368),
};

static const uint16_t ud_itab__1[] = {
  /*  0 */           1,     INVALID,
};

static const uint16_t ud_itab__2[] = {
  /*  0 */           2,     INVALID,
};

static const uint16_t ud_itab__3[] = {
  /*  0 */           3,     INVALID,
};

static const uint16_t ud_itab__4[] = {
  /*  0 */           4,     INVALID,
};

static const uint16_t ud_itab__5[] = {
  /*  0 */          21,         970,          11,        1342,
  /*  4 */          55,        1413,        1493,         106,
};

static const uint16_t ud_itab__6[] = {
  /*  0 */    GROUP(8),   GROUP(11),    GROUP(7),   GROUP(12),
  /*  4 */    GROUP(9),   GROUP(13),   GROUP(14),   GROUP(10),
};

static const uint16_t ud_itab__7[] = {
  /*  0 */          12,     INVALID,
};

static const uint16_t ud_itab__8[] = {
  /*  0 */          22,     INVALID,
};

static const uint16_t ud_itab__9[] = {
  /*  0 */          56,     INVALID,
};

static const uint16_t ud_itab__10[] = {
  /*  0 */         107,     INVALID,
};

static const uint16_t ud_itab__11[] = {
  /*  0 */         972,     INVALID,
};

static const uint16_t ud_itab__12[] = {
  /*  0 */        1344,     INVALID,
};

static const uint16_t ud_itab__13[] = {
  /*  0 */        1415,     INVALID,
};

static const uint16_t ud_itab__14[] = {
  /*  0 */        1495,     INVALID,
};

static const uint16_t ud_itab__15[] = {
  /*  0 */          23,         971,          13,        1343,
  /*  4 */          57,        1414,        1494,         108,
};

static const uint16_t ud_itab__16[] = {
  /*  0 */          24,         973,          14,        1345,
  /*  4 */          58,        1416,        1496,         109,
};

static const uint16_t ud_itab__17[] = {
  /*  0 */  GROUP(156),  GROUP(117),         767,         797,
  /*  4 */     INVALID,        1426,          82,        1431,
  /*  8 */         716,        1471,     INVALID,        1444,
  /*  c */     INVALID,  GROUP(224),         430,  GROUP(262),
  /* 10 */  GROUP(188),  GROUP(189),  GROUP(178),  GROUP(181),
  /* 14 */  GROUP(275),  GROUP(274),  GROUP(174),  GROUP(177),
  /* 18 */  GROUP(225),         955,         956,         957,
  /* 1c */         958,         959,         960,         961,
  /* 20 */         854,         855,         856,         857,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */  GROUP(166),  GROUP(167),  GROUP(153),  GROUP(184),
  /* 2c */  GROUP(154),  GROUP(151),  GROUP(273),  GROUP(148),
  /* 30 */        1472,        1297,        1295,        1296,
  /* 34 */  GROUP(269),  GROUP(271),     INVALID,        1514,
  /* 38 */   GROUP(20),     INVALID,   GROUP(82),     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */          84,          85,          86,          87,
  /* 44 */          88,          89,          90,          91,
  /* 48 */          92,          93,          94,          95,
  /* 4c */          96,          97,          98,          99,
  /* 50 */  GROUP(182),  GROUP(267),  GROUP(265),  GROUP(264),
  /* 54 */  GROUP(109),  GROUP(110),  GROUP(191),  GROUP(278),
  /* 58 */   GROUP(18),  GROUP(190),  GROUP(152),  GROUP(150),
  /* 5c */  GROUP(268),  GROUP(160),  GROUP(155),  GROUP(159),
  /* 60 */  GROUP(259),  GROUP(260),  GROUP(261),  GROUP(192),
  /* 64 */  GROUP(209),  GROUP(210),  GROUP(211),  GROUP(194),
  /* 68 */  GROUP(256),  GROUP(257),  GROUP(258),  GROUP(193),
  /* 6c */  GROUP(291),  GROUP(290),  GROUP(168),  GROUP(186),
  /* 70 */  GROUP(227),  GROUP(229),  GROUP(234),  GROUP(239),
  /* 74 */  GROUP(206),  GROUP(207),  GROUP(208),         199,
  /* 78 */  GROUP(276),  GROUP(277),     INVALID,     INVALID,
  /* 7c */  GROUP(292),  GROUP(293),  GROUP(171),  GROUP(187),
  /* 80 */         727,         729,         731,         733,
  /* 84 */         735,         737,         739,         741,
  /* 88 */         743,         745,         747,         749,
  /* 8c */         751,         753,         755,         757,
  /* 90 */        1350,        1351,        1352,        1353,
  /* 94 */        1354,        1355,        1356,        1357,
  /* 98 */        1358,        1359,        1360,        1361,
  /* 9c */        1362,        1363,        1364,        1365,
  /* a0 */        1245,        1100,         131,        1670,
  /* a4 */        1375,        1376,  GROUP(161),  GROUP(279),
  /* a8 */        1244,        1099,        1305,        1675,
  /* ac */        1377,        1378,  GROUP(111),         694,
  /* b0 */         122,         123,         775,        1673,
  /* b4 */         772,         773,         940,         941,
  /* b8 */  GROUP(296),     INVALID,  GROUP(295),        1671,
  /* bc */        1659,        1660,         930,         931,
  /* c0 */        1473,        1474,  GROUP(138),         904,
  /* c4 */  GROUP(213),  GROUP(212),  GROUP(266),  GROUP(139),
  /* c8 */        1661,        1662,        1663,        1664,
  /* cc */        1665,        1666,        1667,        1668,
  /* d0 */   GROUP(19),  GROUP(246),  GROUP(247),  GROUP(248),
  /* d4 */  GROUP(287),  GROUP(222),  GROUP(185),  GROUP(219),
  /* d8 */  GROUP(254),  GROUP(255),  GROUP(218),  GROUP(202),
  /* dc */  GROUP(200),  GROUP(201),  GROUP(216),  GROUP(203),
  /* e0 */  GROUP(204),  GROUP(244),  GROUP(245),  GROUP(205),
  /* e4 */  GROUP(220),  GROUP(221),  GROUP(149),  GROUP(183),
  /* e8 */  GROUP(252),  GROUP(253),  GROUP(217),  GROUP(223),
  /* ec */  GROUP(198),  GROUP(199),  GROUP(215),  GROUP(263),
  /* f0 */  GROUP(294),  GROUP(228),  GROUP(233),  GROUP(238),
  /* f4 */  GROUP(289),  GROUP(214),  GROUP(226),  GROUP(157),
  /* f8 */  GROUP(249),  GROUP(250),  GROUP(251),  GROUP(288),
  /* fc */  GROUP(195),  GROUP(196),  GROUP(197),     INVALID,
};

static const uint16_t ud_itab__18[] = {
  /*  0 */          27,     INVALID,          31,     INVALID,
};

static const uint16_t ud_itab__19[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__20[] = {
  /*  0 */   GROUP(37),   GROUP(38),   GROUP(39),   GROUP(40),
  /*  4 */   GROUP(41),   GROUP(42),   GROUP(43),   GROUP(44),
  /*  8 */   GROUP(45),   GROUP(47),   GROUP(46),   GROUP(48),
  /*  c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 10 */   GROUP(49),     INVALID,     INVALID,     INVALID,
  /* 14 */   GROUP(62),   GROUP(61),     INVALID,   GROUP(78),
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */   GROUP(34),   GROUP(35),   GROUP(36),     INVALID,
  /* 20 */   GROUP(65),   GROUP(66),   GROUP(67),   GROUP(68),
  /* 24 */   GROUP(69),   GROUP(70),     INVALID,     INVALID,
  /* 28 */   GROUP(50),   GROUP(77),   GROUP(63),   GROUP(64),
  /* 2c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 30 */   GROUP(71),   GROUP(72),   GROUP(73),   GROUP(74),
  /* 34 */   GROUP(75),   GROUP(76),     INVALID,   GROUP(79),
  /* 38 */   GROUP(51),   GROUP(52),   GROUP(53),   GROUP(54),
  /* 3c */   GROUP(55),   GROUP(56),   GROUP(58),   GROUP(57),
  /* 40 */   GROUP(59),   GROUP(60),     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 5c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */   GROUP(26),   GROUP(30),     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,   GROUP(25),
  /* dc */   GROUP(23),   GROUP(24),   GROUP(21),   GROUP(22),
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */   GROUP(80),   GROUP(81),     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__21[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__22[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__23[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__24[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__25[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__26[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__27[] = {
  /*  0 */   GROUP(28),   GROUP(29),
};

static const uint16_t ud_itab__28[] = {
  /*  0 */     INVALID,         717,     INVALID,
};

static const uint16_t ud_itab__29[] = {
  /*  0 */     INVALID,         718,     INVALID,
};

static const uint16_t ud_itab__30[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__31[] = {
  /*  0 */   GROUP(32),   GROUP(33),
};

static const uint16_t ud_itab__32[] = {
  /*  0 */     INVALID,         721,     INVALID,
};

static const uint16_t ud_itab__33[] = {
  /*  0 */     INVALID,         722,     INVALID,
};

static const uint16_t ud_itab__34[] = {
  /*  0 */        1573,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__35[] = {
  /*  0 */        1576,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__36[] = {
  /*  0 */        1579,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__37[] = {
  /*  0 */        1582,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__38[] = {
  /*  0 */        1585,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__39[] = {
  /*  0 */        1588,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__40[] = {
  /*  0 */        1591,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__41[] = {
  /*  0 */        1594,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__42[] = {
  /*  0 */        1597,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__43[] = {
  /*  0 */        1600,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__44[] = {
  /*  0 */        1603,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__45[] = {
  /*  0 */        1606,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__46[] = {
  /*  0 */        1609,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__47[] = {
  /*  0 */        1612,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__48[] = {
  /*  0 */        1615,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__49[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__50[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__51[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__52[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__53[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__54[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__55[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__56[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__57[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__58[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__59[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__60[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__61[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__62[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__63[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__64[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__65[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__66[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__67[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__68[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__69[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__70[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__71[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__72[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__73[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__74[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__75[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__76[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__77[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__78[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__79[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__80[] = {
  /*  0 */        1723,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__81[] = {
  /*  0 */        1724,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__82[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */   GROUP(97),   GROUP(98),   GROUP(99),  GROUP(100),
  /*  c */  GROUP(102),  GROUP(101),  GROUP(103),   GROUP(96),
  /* 10 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 14 */   GROUP(87),   GROUP(90),   GROUP(88),   GROUP(86),
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */   GROUP(91),   GROUP(95),   GROUP(92),     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 2c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */   GROUP(85),   GROUP(84),  GROUP(104),     INVALID,
  /* 44 */   GROUP(94),     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 5c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 60 */  GROUP(106),  GROUP(105),  GROUP(108),  GROUP(107),
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,   GROUP(83),
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__83[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__84[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__85[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__86[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__87[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__88[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__89[] = {
  /*  0 */     INVALID,        1049,        1051,
};

static const uint16_t ud_itab__90[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__91[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__92[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__93[] = {
  /*  0 */     INVALID,        1063,        1064,
};

static const uint16_t ud_itab__94[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__95[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__96[] = {
  /*  0 */        1618,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__97[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__98[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__99[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__100[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__101[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__102[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__103[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__104[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__105[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__106[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__107[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__108[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__109[] = {
  /*  0 */          61,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__110[] = {
  /*  0 */          65,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__111[] = {
  /*  0 */  GROUP(112),  GROUP(113),
};

static const uint16_t ud_itab__112[] = {
  /*  0 */         683,         682,         768,        1400,
  /*  4 */        1507,        1506,     INVALID,          79,
};

static const uint16_t ud_itab__113[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,  GROUP(114),  GROUP(115),  GROUP(116),
};

static const uint16_t ud_itab__114[] = {
  /*  0 */         777,         778,         779,         780,
  /*  4 */         781,         782,         783,         784,
};

static const uint16_t ud_itab__115[] = {
  /*  0 */         808,         809,         810,         811,
  /*  4 */         812,         813,         814,         815,
};

static const uint16_t ud_itab__116[] = {
  /*  0 */        1366,        1367,        1368,        1369,
  /*  4 */        1370,        1371,        1372,        1373,
};

static const uint16_t ud_itab__117[] = {
  /*  0 */  GROUP(137),  GROUP(118),
};

static const uint16_t ud_itab__118[] = {
  /*  0 */  GROUP(131),  GROUP(128),  GROUP(136),  GROUP(119),
  /*  4 */        1386,     INVALID,         788,  GROUP(129),
};

static const uint16_t ud_itab__119[] = {
  /*  0 */  GROUP(124),  GROUP(125),  GROUP(126),  GROUP(127),
  /*  4 */  GROUP(122),  GROUP(120),  GROUP(123),  GROUP(121),
};

static const uint16_t ud_itab__120[] = {
  /*  0 */          80,     INVALID,     INVALID,
};

static const uint16_t ud_itab__121[] = {
  /*  0 */         720,     INVALID,     INVALID,
};

static const uint16_t ud_itab__122[] = {
  /*  0 */        1397,     INVALID,     INVALID,
};

static const uint16_t ud_itab__123[] = {
  /*  0 */        1399,     INVALID,     INVALID,
};

static const uint16_t ud_itab__124[] = {
  /*  0 */        1466,     INVALID,     INVALID,
};

static const uint16_t ud_itab__125[] = {
  /*  0 */        1467,     INVALID,     INVALID,
};

static const uint16_t ud_itab__126[] = {
  /*  0 */        1468,     INVALID,     INVALID,
};

static const uint16_t ud_itab__127[] = {
  /*  0 */        1469,     INVALID,     INVALID,
};

static const uint16_t ud_itab__128[] = {
  /*  0 */         824,         952,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__129[] = {
  /*  0 */        1425,  GROUP(130),     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__130[] = {
  /*  0 */        1298,     INVALID,     INVALID,
};

static const uint16_t ud_itab__131[] = {
  /*  0 */     INVALID,  GROUP(132),  GROUP(133),  GROUP(134),
  /*  4 */  GROUP(135),     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__132[] = {
  /*  0 */     INVALID,        1455,     INVALID,
};

static const uint16_t ud_itab__133[] = {
  /*  0 */     INVALID,        1461,     INVALID,
};

static const uint16_t ud_itab__134[] = {
  /*  0 */     INVALID,        1462,     INVALID,
};

static const uint16_t ud_itab__135[] = {
  /*  0 */     INVALID,        1463,     INVALID,
};

static const uint16_t ud_itab__136[] = {
  /*  0 */        1485,        1508,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__137[] = {
  /*  0 */        1374,        1383,         785,         774,
  /*  4 */        1385,     INVALID,         787,         719,
};

static const uint16_t ud_itab__138[] = {
  /*  0 */         112,     INVALID,         120,     INVALID,
};

static const uint16_t ud_itab__139[] = {
  /*  0 */  GROUP(140),  GROUP(147),
};

static const uint16_t ud_itab__140[] = {
  /*  0 */     INVALID,  GROUP(141),     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,  GROUP(142),  GROUP(146),
};

static const uint16_t ud_itab__141[] = {
  /*  0 */     INVALID,         125,         126,
};

static const uint16_t ud_itab__142[] = {
  /*  0 */  GROUP(145),     INVALID,  GROUP(144),     INVALID,
};

static const uint16_t ud_itab__143[] = {
  /*  0 */     INVALID,        1457,     INVALID,
};

static const uint16_t ud_itab__144[] = {
  /*  0 */     INVALID,        1458,     INVALID,
};

static const uint16_t ud_itab__145[] = {
  /*  0 */     INVALID,        1459,     INVALID,
};

static const uint16_t ud_itab__146[] = {
  /*  0 */     INVALID,        1460,     INVALID,
};

static const uint16_t ud_itab__147[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,        1456,     INVALID,
};

static const uint16_t ud_itab__148[] = {
  /*  0 */         129,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__149[] = {
  /*  0 */     INVALID,     INVALID,         132,     INVALID,
};

static const uint16_t ud_itab__150[] = {
  /*  0 */         134,     INVALID,         163,     INVALID,
};

static const uint16_t ud_itab__151[] = {
  /*  0 */         147,     INVALID,         158,     INVALID,
};

static const uint16_t ud_itab__152[] = {
  /*  0 */         145,     INVALID,         156,     INVALID,
};

static const uint16_t ud_itab__153[] = {
  /*  0 */         141,     INVALID,         154,     INVALID,
};

static const uint16_t ud_itab__154[] = {
  /*  0 */         165,     INVALID,         168,     INVALID,
};

static const uint16_t ud_itab__155[] = {
  /*  0 */         189,     INVALID,         193,     INVALID,
};

static const uint16_t ud_itab__156[] = {
  /*  0 */        1384,        1406,         786,         798,
  /*  4 */        1453,        1454,     INVALID,     INVALID,
};

static const uint16_t ud_itab__157[] = {
  /*  0 */     INVALID,  GROUP(158),
};

static const uint16_t ud_itab__158[] = {
  /*  0 */         799,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__159[] = {
  /*  0 */         802,     INVALID,         806,     INVALID,
};

static const uint16_t ud_itab__160[] = {
  /*  0 */         818,     INVALID,         822,     INVALID,
};

static const uint16_t ud_itab__161[] = {
  /*  0 */     INVALID,  GROUP(162),
};

static const uint16_t ud_itab__162[] = {
  /*  0 */  GROUP(163),  GROUP(164),  GROUP(165),     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__163[] = {
  /*  0 */         825,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__164[] = {
  /*  0 */        1509,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__165[] = {
  /*  0 */        1510,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__166[] = {
  /*  0 */         862,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__167[] = {
  /*  0 */         864,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__168[] = {
  /*  0 */  GROUP(169),     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__169[] = {
  /*  0 */     INVALID,         867,         910,
};

static const uint16_t ud_itab__170[] = {
  /*  0 */     INVALID,         870,         911,
};

static const uint16_t ud_itab__171[] = {
  /*  0 */  GROUP(172),     INVALID,         916,     INVALID,
};

static const uint16_t ud_itab__172[] = {
  /*  0 */     INVALID,         873,         913,
};

static const uint16_t ud_itab__173[] = {
  /*  0 */     INVALID,         876,         914,
};

static const uint16_t ud_itab__174[] = {
  /*  0 */  GROUP(175),  GROUP(176),
};

static const uint16_t ud_itab__175[] = {
  /*  0 */         882,     INVALID,        1567,     INVALID,
};

static const uint16_t ud_itab__176[] = {
  /*  0 */         886,     INVALID,        1565,     INVALID,
};

static const uint16_t ud_itab__177[] = {
  /*  0 */         884,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__178[] = {
  /*  0 */  GROUP(179),  GROUP(180),
};

static const uint16_t ud_itab__179[] = {
  /*  0 */         892,     INVALID,        1571,     INVALID,
};

static const uint16_t ud_itab__180[] = {
  /*  0 */         896,     INVALID,        1569,     INVALID,
};

static const uint16_t ud_itab__181[] = {
  /*  0 */         894,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__182[] = {
  /*  0 */         900,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__183[] = {
  /*  0 */         909,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__184[] = {
  /*  0 */         907,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__185[] = {
  /*  0 */     INVALID,     INVALID,        1526,     INVALID,
};

static const uint16_t ud_itab__186[] = {
  /*  0 */         920,     INVALID,        1522,     INVALID,
};

static const uint16_t ud_itab__187[] = {
  /*  0 */         921,     INVALID,        1524,     INVALID,
};

static const uint16_t ud_itab__188[] = {
  /*  0 */         936,     INVALID,         928,     INVALID,
};

static const uint16_t ud_itab__189[] = {
  /*  0 */         938,     INVALID,         929,     INVALID,
};

static const uint16_t ud_itab__190[] = {
  /*  0 */         946,     INVALID,         950,     INVALID,
};

static const uint16_t ud_itab__191[] = {
  /*  0 */         976,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__192[] = {
  /*  0 */         987,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__193[] = {
  /*  0 */         990,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__194[] = {
  /*  0 */         993,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__195[] = {
  /*  0 */         996,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__196[] = {
  /*  0 */         997,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__197[] = {
  /*  0 */        1000,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__198[] = {
  /*  0 */        1003,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__199[] = {
  /*  0 */        1006,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__200[] = {
  /*  0 */        1009,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__201[] = {
  /*  0 */        1012,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__202[] = {
  /*  0 */        1017,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__203[] = {
  /*  0 */        1020,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__204[] = {
  /*  0 */        1023,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__205[] = {
  /*  0 */        1026,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__206[] = {
  /*  0 */        1027,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__207[] = {
  /*  0 */        1030,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__208[] = {
  /*  0 */        1033,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__209[] = {
  /*  0 */        1038,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__210[] = {
  /*  0 */        1041,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__211[] = {
  /*  0 */        1044,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__212[] = {
  /*  0 */        1055,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__213[] = {
  /*  0 */        1059,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__214[] = {
  /*  0 */        1069,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__215[] = {
  /*  0 */        1074,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__216[] = {
  /*  0 */        1075,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__217[] = {
  /*  0 */        1080,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__218[] = {
  /*  0 */        1083,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__219[] = {
  /*  0 */        1086,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__220[] = {
  /*  0 */        1087,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__221[] = {
  /*  0 */        1092,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__222[] = {
  /*  0 */        1093,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__223[] = {
  /*  0 */        1118,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__224[] = {
  /*  0 */        1119,        1120,        1121,        1122,
  /*  4 */        1123,        1124,        1125,        1126,
};

static const uint16_t ud_itab__225[] = {
  /*  0 */        1127,        1128,        1129,        1130,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__226[] = {
  /*  0 */        1133,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__227[] = {
  /*  0 */        1134,     INVALID,        1535,     INVALID,
};

static const uint16_t ud_itab__228[] = {
  /*  0 */        1136,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__229[] = {
  /*  0 */     INVALID,     INVALID,  GROUP(232),     INVALID,
  /*  4 */  GROUP(231),     INVALID,  GROUP(230),     INVALID,
};

static const uint16_t ud_itab__230[] = {
  /*  0 */        1138,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__231[] = {
  /*  0 */        1152,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__232[] = {
  /*  0 */        1159,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__233[] = {
  /*  0 */        1140,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__234[] = {
  /*  0 */     INVALID,     INVALID,  GROUP(237),     INVALID,
  /*  4 */  GROUP(236),     INVALID,  GROUP(235),     INVALID,
};

static const uint16_t ud_itab__235[] = {
  /*  0 */        1142,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__236[] = {
  /*  0 */        1153,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__237[] = {
  /*  0 */        1165,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__238[] = {
  /*  0 */        1144,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__239[] = {
  /*  0 */     INVALID,     INVALID,  GROUP(241),  GROUP(243),
  /*  4 */     INVALID,     INVALID,  GROUP(240),  GROUP(242),
};

static const uint16_t ud_itab__240[] = {
  /*  0 */        1146,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__241[] = {
  /*  0 */        1171,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__242[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__243[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__244[] = {
  /*  0 */        1147,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__245[] = {
  /*  0 */        1156,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__246[] = {
  /*  0 */        1160,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__247[] = {
  /*  0 */        1166,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__248[] = {
  /*  0 */        1172,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__249[] = {
  /*  0 */        1179,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__250[] = {
  /*  0 */        1182,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__251[] = {
  /*  0 */        1183,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__252[] = {
  /*  0 */        1186,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__253[] = {
  /*  0 */        1189,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__254[] = {
  /*  0 */        1192,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__255[] = {
  /*  0 */        1195,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__256[] = {
  /*  0 */        1200,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__257[] = {
  /*  0 */        1203,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__258[] = {
  /*  0 */        1206,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__259[] = {
  /*  0 */        1209,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__260[] = {
  /*  0 */        1212,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__261[] = {
  /*  0 */        1215,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__262[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  c */        1216,        1217,     INVALID,     INVALID,
  /* 10 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 14 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */        1218,        1219,     INVALID,     INVALID,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 2c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 5c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,        1220,     INVALID,
  /* 8c */     INVALID,     INVALID,        1221,     INVALID,
  /* 90 */        1222,     INVALID,     INVALID,     INVALID,
  /* 94 */        1223,     INVALID,        1224,        1225,
  /* 98 */     INVALID,     INVALID,        1226,     INVALID,
  /* 9c */     INVALID,     INVALID,        1227,     INVALID,
  /* a0 */        1228,     INVALID,     INVALID,     INVALID,
  /* a4 */        1229,     INVALID,        1230,        1231,
  /* a8 */     INVALID,     INVALID,        1232,     INVALID,
  /* ac */     INVALID,     INVALID,        1233,     INVALID,
  /* b0 */        1234,     INVALID,     INVALID,     INVALID,
  /* b4 */        1235,     INVALID,        1236,        1237,
  /* b8 */     INVALID,     INVALID,     INVALID,        1238,
  /* bc */     INVALID,     INVALID,     INVALID,        1239,
  /* c0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__263[] = {
  /*  0 */        1266,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__264[] = {
  /*  0 */        1291,     INVALID,        1293,     INVALID,
};

static const uint16_t ud_itab__265[] = {
  /*  0 */        1306,     INVALID,        1308,     INVALID,
};

static const uint16_t ud_itab__266[] = {
  /*  0 */        1381,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__267[] = {
  /*  0 */        1387,     INVALID,        1393,     INVALID,
};

static const uint16_t ud_itab__268[] = {
  /*  0 */        1419,     INVALID,        1423,     INVALID,
};

static const uint16_t ud_itab__269[] = {
  /*  0 */        1427,  GROUP(270),
};

static const uint16_t ud_itab__270[] = {
  /*  0 */     INVALID,        1428,     INVALID,
};

static const uint16_t ud_itab__271[] = {
  /*  0 */        1429,  GROUP(272),
};

static const uint16_t ud_itab__272[] = {
  /*  0 */     INVALID,        1430,     INVALID,
};

static const uint16_t ud_itab__273[] = {
  /*  0 */        1442,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__274[] = {
  /*  0 */        1447,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__275[] = {
  /*  0 */        1449,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__276[] = {
  /*  0 */     INVALID,        1464,     INVALID,
};

static const uint16_t ud_itab__277[] = {
  /*  0 */     INVALID,        1465,     INVALID,
};

static const uint16_t ud_itab__278[] = {
  /*  0 */        1499,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__279[] = {
  /*  0 */     INVALID,  GROUP(280),
};

static const uint16_t ud_itab__280[] = {
  /*  0 */  GROUP(286),  GROUP(281),  GROUP(282),  GROUP(283),
  /*  4 */  GROUP(284),  GROUP(285),     INVALID,     INVALID,
};

static const uint16_t ud_itab__281[] = {
  /*  0 */        1501,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__282[] = {
  /*  0 */        1502,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__283[] = {
  /*  0 */        1503,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__284[] = {
  /*  0 */        1504,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__285[] = {
  /*  0 */        1505,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__286[] = {
  /*  0 */        1511,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__287[] = {
  /*  0 */        1527,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__288[] = {
  /*  0 */        1532,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__289[] = {
  /*  0 */        1533,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__290[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__291[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__292[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__293[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__294[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__295[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */        1669,        1676,        1674,        1672,
};

static const uint16_t ud_itab__296[] = {
  /*  0 */     INVALID,     INVALID,        1710,     INVALID,
};

static const uint16_t ud_itab__297[] = {
  /*  0 */  GROUP(366),  GROUP(314),     INVALID,     INVALID,
  /*  4 */     INVALID,  GROUP(298),  GROUP(329),  GROUP(341),
  /*  8 */     INVALID,  GROUP(324),     INVALID,     INVALID,
  /*  c */     INVALID,  GROUP(320),     INVALID,     INVALID,
};

static const uint16_t ud_itab__298[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 10 */         933,         935,  GROUP(302),         891,
  /* 14 */        1452,        1446,  GROUP(301),         881,
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */         859,         861,     INVALID,         906,
  /* 2c */     INVALID,     INVALID,        1441,         128,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */         899,        1390,     INVALID,     INVALID,
  /* 54 */          60,          64,         975,        1498,
  /* 58 */          26,         945,         140,         144,
  /* 5c */        1418,         817,         188,         801,
  /* 60 */        1208,        1211,        1214,         986,
  /* 64 */        1037,        1040,        1043,         992,
  /* 68 */        1199,        1202,        1205,         989,
  /* 6c */        1548,        1546,  GROUP(299),        1518,
  /* 70 */        1540,  GROUP(304),  GROUP(306),  GROUP(308),
  /* 74 */        1029,        1032,        1035,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */        1550,        1554,  GROUP(300),        1516,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,         111,     INVALID,
  /* c4 */        1061,        1054,        1380,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */          34,        1162,        1168,        1174,
  /* d4 */        1529,        1095,         919,  GROUP(303),
  /* d8 */        1194,        1197,        1082,        1016,
  /* dc */        1011,        1014,        1077,        1019,
  /* e0 */        1022,        1149,        1155,        1025,
  /* e4 */        1089,        1091,         161,         903,
  /* e8 */        1188,        1191,        1079,        1117,
  /* ec */        1005,        1008,        1073,        1265,
  /* f0 */     INVALID,  GROUP(311),  GROUP(312),  GROUP(313),
  /* f4 */     INVALID,        1071,        1132,  GROUP(310),
  /* f8 */        1178,        1181,        1185,        1531,
  /* fc */         995,         999,        1002,     INVALID,
};

static const uint16_t ud_itab__299[] = {
  /*  0 */     INVALID,         871,         912,
};

static const uint16_t ud_itab__300[] = {
  /*  0 */     INVALID,         877,         915,
};

static const uint16_t ud_itab__301[] = {
  /*  0 */         879,     INVALID,
};

static const uint16_t ud_itab__302[] = {
  /*  0 */         889,     INVALID,
};

static const uint16_t ud_itab__303[] = {
  /*  0 */        1085,     INVALID,
};

static const uint16_t ud_itab__304[] = {
  /*  0 */     INVALID,     INVALID,        1164,     INVALID,
  /*  4 */        1151,     INVALID,  GROUP(305),     INVALID,
};

static const uint16_t ud_itab__305[] = {
  /*  0 */        1756,     INVALID,
};

static const uint16_t ud_itab__306[] = {
  /*  0 */     INVALID,     INVALID,        1170,     INVALID,
  /*  4 */        1158,     INVALID,  GROUP(307),     INVALID,
};

static const uint16_t ud_itab__307[] = {
  /*  0 */        1758,     INVALID,
};

static const uint16_t ud_itab__308[] = {
  /*  0 */     INVALID,     INVALID,        1176,        1544,
  /*  4 */     INVALID,     INVALID,  GROUP(309),        1542,
};

static const uint16_t ud_itab__309[] = {
  /*  0 */        1760,     INVALID,
};

static const uint16_t ud_itab__310[] = {
  /*  0 */     INVALID,        1520,
};

static const uint16_t ud_itab__311[] = {
  /*  0 */        1755,     INVALID,
};

static const uint16_t ud_itab__312[] = {
  /*  0 */        1757,     INVALID,
};

static const uint16_t ud_itab__313[] = {
  /*  0 */        1759,     INVALID,
};

static const uint16_t ud_itab__314[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 10 */         937,         939,  GROUP(316),         895,
  /* 14 */        1450,        1448,  GROUP(315),         885,
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */         863,         865,     INVALID,         908,
  /* 2c */     INVALID,     INVALID,        1443,         130,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */         901,        1388,        1307,        1292,
  /* 54 */          62,          66,         977,        1500,
  /* 58 */          28,         947,         146,         135,
  /* 5c */        1420,         819,         190,         803,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,  GROUP(319),
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,  GROUP(317),     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,         113,     INVALID,
  /* c4 */     INVALID,     INVALID,        1382,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__315[] = {
  /*  0 */         883,         887,
};

static const uint16_t ud_itab__316[] = {
  /*  0 */         893,         897,
};

static const uint16_t ud_itab__317[] = {
  /*  0 */  GROUP(318),     INVALID,
};

static const uint16_t ud_itab__318[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,        1401,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__319[] = {
  /*  0 */        1742,        1743,
};

static const uint16_t ud_itab__320[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 10 */  GROUP(322),  GROUP(323),  GROUP(321),     INVALID,
  /* 14 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,     INVALID,         153,     INVALID,
  /* 2c */         167,         149,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,        1392,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */          30,         949,         151,     INVALID,
  /* 5c */        1422,         821,         192,         805,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */        1538,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */        1552,        1556,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,         118,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */          36,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,         137,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */        1560,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__321[] = {
  /*  0 */        1564,        1562,
};

static const uint16_t ud_itab__322[] = {
  /*  0 */        1747,        1746,
};

static const uint16_t ud_itab__323[] = {
  /*  0 */        1749,        1748,
};

static const uint16_t ud_itab__324[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 10 */  GROUP(327),  GROUP(328),  GROUP(326),     INVALID,
  /* 14 */     INVALID,     INVALID,  GROUP(325),     INVALID,
  /* 18 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,     INVALID,         155,     INVALID,
  /* 2c */         169,         159,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,        1394,        1309,        1294,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */          32,         951,         157,         164,
  /* 5c */        1424,         823,         194,         807,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,        1523,
  /* 70 */        1536,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,         917,        1525,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,         121,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,         133,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__325[] = {
  /*  0 */        1568,        1566,
};

static const uint16_t ud_itab__326[] = {
  /*  0 */        1572,        1570,
};

static const uint16_t ud_itab__327[] = {
  /*  0 */        1751,        1750,
};

static const uint16_t ud_itab__328[] = {
  /*  0 */        1753,        1752,
};

static const uint16_t ud_itab__329[] = {
  /*  0 */        1584,        1587,        1590,        1593,
  /*  4 */        1596,        1599,        1602,        1605,
  /*  8 */        1608,        1614,        1611,        1617,
  /*  c */  GROUP(338),  GROUP(337),  GROUP(339),  GROUP(340),
  /* 10 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 14 */     INVALID,     INVALID,     INVALID,        1712,
  /* 18 */  GROUP(330),  GROUP(331),     INVALID,     INVALID,
  /* 1c */        1575,        1578,        1581,     INVALID,
  /* 20 */        1686,        1688,        1690,        1692,
  /* 24 */        1694,     INVALID,     INVALID,     INVALID,
  /* 28 */        1623,        1709,        1682,        1684,
  /* 2c */  GROUP(333),  GROUP(335),  GROUP(334),  GROUP(336),
  /* 30 */        1697,        1699,        1701,        1703,
  /* 34 */        1705,        1707,     INVALID,        1718,
  /* 38 */        1625,        1627,        1629,        1631,
  /* 3c */        1633,        1635,        1639,        1637,
  /* 40 */        1641,        1643,     INVALID,     INVALID,
  /* 44 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 4c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 5c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 60 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,          46,
  /* dc */          42,          44,          38,          40,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__330[] = {
  /*  0 */        1727,     INVALID,
};

static const uint16_t ud_itab__331[] = {
  /*  0 */  GROUP(332),     INVALID,
};

static const uint16_t ud_itab__332[] = {
  /*  0 */     INVALID,        1728,
};

static const uint16_t ud_itab__333[] = {
  /*  0 */        1731,     INVALID,
};

static const uint16_t ud_itab__334[] = {
  /*  0 */        1732,     INVALID,
};

static const uint16_t ud_itab__335[] = {
  /*  0 */        1733,     INVALID,
};

static const uint16_t ud_itab__336[] = {
  /*  0 */        1734,     INVALID,
};

static const uint16_t ud_itab__337[] = {
  /*  0 */        1735,     INVALID,
};

static const uint16_t ud_itab__338[] = {
  /*  0 */        1737,     INVALID,
};

static const uint16_t ud_itab__339[] = {
  /*  0 */        1740,     INVALID,
};

static const uint16_t ud_itab__340[] = {
  /*  0 */        1741,     INVALID,
};

static const uint16_t ud_itab__341[] = {
  /*  0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /*  4 */  GROUP(360),  GROUP(359),  GROUP(361),     INVALID,
  /*  8 */        1645,        1647,        1649,        1651,
  /*  c */        1655,        1653,        1678,        1620,
  /* 10 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 14 */  GROUP(342),        1057,  GROUP(343),         202,
  /* 18 */  GROUP(357),  GROUP(355),     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */  GROUP(347),        1558,  GROUP(349),     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 2c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 40 */         198,         196,        1680,     INVALID,
  /* 44 */        1513,     INVALID,     INVALID,     INVALID,
  /* 48 */     INVALID,     INVALID,  GROUP(364),  GROUP(363),
  /* 4c */  GROUP(365),     INVALID,     INVALID,     INVALID,
  /* 50 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 54 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 58 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 5c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 60 */        1716,        1714,        1722,        1720,
  /* 64 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 68 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 6c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 70 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 74 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 78 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 7c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 80 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 84 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 88 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 8c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 90 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 94 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 98 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 9c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* a8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ac */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* b8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* bc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* c8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* cc */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* d8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* dc */     INVALID,     INVALID,     INVALID,          48,
  /* e0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* e8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* ec */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f0 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f4 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* f8 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* fc */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__342[] = {
  /*  0 */        1046,     INVALID,
};

static const uint16_t ud_itab__343[] = {
  /*  0 */     INVALID,  GROUP(345),  GROUP(346),
};

static const uint16_t ud_itab__344[] = {
  /*  0 */        1048,     INVALID,
};

static const uint16_t ud_itab__345[] = {
  /*  0 */        1050,     INVALID,
};

static const uint16_t ud_itab__346[] = {
  /*  0 */     INVALID,        1052,
};

static const uint16_t ud_itab__347[] = {
  /*  0 */  GROUP(348),     INVALID,
};

static const uint16_t ud_itab__348[] = {
  /*  0 */        1065,     INVALID,
};

static const uint16_t ud_itab__349[] = {
  /*  0 */  GROUP(350),  GROUP(352),
};

static const uint16_t ud_itab__350[] = {
  /*  0 */  GROUP(351),     INVALID,
};

static const uint16_t ud_itab__351[] = {
  /*  0 */        1066,     INVALID,
};

static const uint16_t ud_itab__352[] = {
  /*  0 */  GROUP(353),  GROUP(354),
};

static const uint16_t ud_itab__353[] = {
  /*  0 */        1067,     INVALID,
};

static const uint16_t ud_itab__354[] = {
  /*  0 */        1068,     INVALID,
};

static const uint16_t ud_itab__355[] = {
  /*  0 */  GROUP(356),     INVALID,
};

static const uint16_t ud_itab__356[] = {
  /*  0 */     INVALID,        1729,
};

static const uint16_t ud_itab__357[] = {
  /*  0 */  GROUP(358),     INVALID,
};

static const uint16_t ud_itab__358[] = {
  /*  0 */     INVALID,        1730,
};

static const uint16_t ud_itab__359[] = {
  /*  0 */        1736,     INVALID,
};

static const uint16_t ud_itab__360[] = {
  /*  0 */        1738,     INVALID,
};

static const uint16_t ud_itab__361[] = {
  /*  0 */  GROUP(362),     INVALID,
};

static const uint16_t ud_itab__362[] = {
  /*  0 */     INVALID,        1739,
};

static const uint16_t ud_itab__363[] = {
  /*  0 */        1744,     INVALID,
};

static const uint16_t ud_itab__364[] = {
  /*  0 */        1745,     INVALID,
};

static const uint16_t ud_itab__365[] = {
  /*  0 */        1754,     INVALID,
};

static const uint16_t ud_itab__366[] = {
  /*  0 */         771,     INVALID,
};

static const uint16_t ud_itab__367[] = {
  /*  0 */          67,          68,
};

static const uint16_t ud_itab__368[] = {
  /*  0 */         707,         184,  GROUP(369),          71,
  /*  4 */         761,         762,        1255,     INVALID,
};

static const uint16_t ud_itab__369[] = {
  /*  0 */          69,          70,
};

static const uint16_t ud_itab__370[] = {
  /*  0 */          73,     INVALID,
};

static const uint16_t ud_itab__371[] = {
  /*  0 */     INVALID,          75,          76,
};

static const uint16_t ud_itab__372[] = {
  /*  0 */     INVALID,         116,         119,
};

static const uint16_t ud_itab__373[] = {
  /*  0 */     INVALID,         171,         172,
};

static const uint16_t ud_itab__374[] = {
  /*  0 */         173,     INVALID,
};

static const uint16_t ud_itab__375[] = {
  /*  0 */         174,     INVALID,
};

static const uint16_t ud_itab__376[] = {
  /*  0 */         708,         183,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__377[] = {
  /*  0 */        1438,        1439,         963,         954,
  /*  4 */         943,         696,         185,         688,
};

static const uint16_t ud_itab__378[] = {
  /*  0 */        1432,        1437,         962,         953,
  /*  4 */         942,         695,         186,         689,
};

static const uint16_t ud_itab__379[] = {
  /*  0 */  GROUP(381),  GROUP(380),
};

static const uint16_t ud_itab__380[] = {
  /*  0 */         477,         478,         479,         480,
  /*  4 */         481,         482,         483,         484,
  /*  8 */         658,         659,         660,         661,
  /*  c */         662,         663,         664,         665,
  /* 10 */         522,     INVALID,     INVALID,     INVALID,
  /* 14 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 18 */         549,         550,         551,         552,
  /* 1c */         553,         554,         555,         556,
  /* 20 */         233,         204,     INVALID,     INVALID,
  /* 24 */         639,         657,     INVALID,     INVALID,
  /* 28 */         485,         486,         487,         488,
  /* 2c */         489,         490,         491,     INVALID,
  /* 30 */         203,         685,         529,         526,
  /* 34 */         684,         528,         377,         454,
  /* 38 */         527,         686,         537,         536,
  /* 3c */         530,         534,         535,         376,
};

static const uint16_t ud_itab__381[] = {
  /*  0 */         476,     INVALID,         573,         540,
  /*  4 */         493,         492,         584,         583,
};

static const uint16_t ud_itab__382[] = {
  /*  0 */  GROUP(383),  GROUP(384),
};

static const uint16_t ud_itab__383[] = {
  /*  0 */         205,         494,         308,         358,
  /*  4 */         588,         613,         378,         404,
};

static const uint16_t ud_itab__384[] = {
  /*  0 */         207,         208,         209,         210,
  /*  4 */         211,         212,         213,         214,
  /*  8 */         495,         496,         497,         498,
  /*  c */         499,         500,         501,         502,
  /* 10 */         317,         318,         319,         320,
  /* 14 */         321,         322,         323,         324,
  /* 18 */         325,         326,         327,         328,
  /* 1c */         329,         330,         331,         332,
  /* 20 */         622,         623,         624,         625,
  /* 24 */         626,         627,         628,         629,
  /* 28 */         597,         598,         599,         600,
  /* 2c */         601,         602,         603,         604,
  /* 30 */         405,         406,         407,         408,
  /* 34 */         409,         410,         411,         412,
  /* 38 */         379,         380,         381,         382,
  /* 3c */         383,         384,         385,         386,
};

static const uint16_t ud_itab__385[] = {
  /*  0 */  GROUP(386),  GROUP(387),
};

static const uint16_t ud_itab__386[] = {
  /*  0 */         206,         503,         307,         357,
  /*  4 */         587,         630,         387,         413,
};

static const uint16_t ud_itab__387[] = {
  /*  0 */         215,         216,         217,         218,
  /*  4 */         219,         220,         221,         222,
  /*  8 */         504,         505,         506,         507,
  /*  c */         508,         509,         510,         511,
  /* 10 */         309,         310,         311,         312,
  /* 14 */         313,         314,         315,         316,
  /* 18 */         359,         360,         361,         362,
  /* 1c */         363,         364,         365,         366,
  /* 20 */         589,         590,         591,         592,
  /* 24 */         593,         594,         595,         596,
  /* 28 */         614,         615,         616,         617,
  /* 2c */         618,         619,         620,         621,
  /* 30 */         388,         389,         390,         391,
  /* 34 */         392,         393,         394,         395,
  /* 38 */         414,         415,         416,         417,
  /* 3c */         418,         419,         420,         421,
};

static const uint16_t ud_itab__388[] = {
  /*  0 */  GROUP(390),  GROUP(389),
};

static const uint16_t ud_itab__389[] = {
  /*  0 */         223,         224,         225,         226,
  /*  4 */         227,         228,         229,         230,
  /*  8 */         512,         513,         514,         515,
  /*  c */         516,         517,         518,         519,
  /* 10 */         367,         368,         369,         370,
  /* 14 */         371,         372,         373,         374,
  /* 18 */     INVALID,         375,     INVALID,     INVALID,
  /* 1c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 20 */         631,         632,         633,         634,
  /* 24 */         635,         636,         637,         638,
  /* 28 */         605,         606,         607,         608,
  /* 2c */         609,         610,         611,         612,
  /* 30 */         422,         423,         424,         425,
  /* 34 */         426,         427,         428,         429,
  /* 38 */         396,         397,         398,         399,
  /* 3c */         400,         401,         402,         403,
};

static const uint16_t ud_itab__390[] = {
  /*  0 */         457,         521,         447,         449,
  /*  4 */         463,         465,         461,         459,
};

static const uint16_t ud_itab__391[] = {
  /*  0 */  GROUP(392),  GROUP(393),
};

static const uint16_t ud_itab__392[] = {
  /*  0 */         451,         473,         466,         468,
  /*  4 */         231,         452,         232,         469,
};

static const uint16_t ud_itab__393[] = {
  /*  0 */         439,         440,         441,         442,
  /*  4 */         443,         444,         445,         446,
  /*  8 */         674,         675,         676,         677,
  /*  c */         678,         679,         680,         681,
  /* 10 */         557,         558,         559,         560,
  /* 14 */         561,         562,         563,         564,
  /* 18 */         565,         566,         567,         568,
  /* 1c */         569,         570,         571,         572,
  /* 20 */         586,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */         341,         342,         343,         344,
  /* 2c */         345,         346,         347,         348,
  /* 30 */         349,         350,         351,         352,
  /* 34 */         353,         354,         355,         356,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__394[] = {
  /*  0 */  GROUP(396),  GROUP(395),
};

static const uint16_t ud_itab__395[] = {
  /*  0 */         267,         268,         269,         270,
  /*  4 */         271,         272,         273,         274,
  /*  8 */         275,         276,         277,         278,
  /*  c */         279,         280,         281,         282,
  /* 10 */         283,         284,         285,         286,
  /* 14 */         287,         288,         289,         290,
  /* 18 */         291,         292,         293,         294,
  /* 1c */         295,         296,         297,         298,
  /* 20 */         524,         523,         234,         455,
  /* 24 */         525,         532,     INVALID,     INVALID,
  /* 28 */         299,         300,         301,         302,
  /* 2c */         303,         304,         305,         306,
  /* 30 */         333,         334,         335,         336,
  /* 34 */         337,         338,         339,         340,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__396[] = {
  /*  0 */         453,         471,         467,         470,
  /*  4 */     INVALID,         474,     INVALID,         538,
};

static const uint16_t ud_itab__397[] = {
  /*  0 */  GROUP(399),  GROUP(398),
};

static const uint16_t ud_itab__398[] = {
  /*  0 */         235,         236,         237,         238,
  /*  4 */         239,         240,         241,         242,
  /*  8 */         243,         244,         245,         246,
  /*  c */         247,         248,         249,         250,
  /* 10 */         251,         252,         253,         254,
  /* 14 */         255,         256,         257,         258,
  /* 18 */         259,         260,         261,         262,
  /* 1c */         263,         264,         265,         266,
  /* 20 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 24 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 28 */     INVALID,         656,     INVALID,     INVALID,
  /* 2c */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__399[] = {
  /*  0 */         456,         520,         448,         450,
  /*  4 */         462,         464,         460,         458,
};

static const uint16_t ud_itab__400[] = {
  /*  0 */  GROUP(402),  GROUP(401),
};

static const uint16_t ud_itab__401[] = {
  /*  0 */         431,         432,         433,         434,
  /*  4 */         435,         436,         437,         438,
  /*  8 */         666,         667,         668,         669,
  /*  c */         670,         671,         672,         673,
  /* 10 */         575,         576,         577,         578,
  /* 14 */         579,         580,         581,         582,
  /* 18 */         541,         542,         543,         544,
  /* 1c */         545,         546,         547,         548,
  /* 20 */         640,         641,         642,         643,
  /* 24 */         644,         645,         646,         647,
  /* 28 */         648,         649,         650,         651,
  /* 2c */         652,         653,         654,         655,
  /* 30 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 34 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 38 */     INVALID,     INVALID,     INVALID,     INVALID,
  /* 3c */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__402[] = {
  /*  0 */         475,         472,         574,         539,
  /*  4 */         531,     INVALID,         533,         585,
};

static const uint16_t ud_itab__403[] = {
  /*  0 */     INVALID,         711,     INVALID,
};

static const uint16_t ud_itab__404[] = {
  /*  0 */         715,     INVALID,
};

static const uint16_t ud_itab__405[] = {
  /*  0 */     INVALID,         724,         725,
};

static const uint16_t ud_itab__406[] = {
  /*  0 */     INVALID,         759,         760,
};

static const uint16_t ud_itab__407[] = {
  /*  0 */         764,     INVALID,
};

static const uint16_t ud_itab__408[] = {
  /*  0 */  GROUP(409),  GROUP(314),     INVALID,     INVALID,
  /*  4 */     INVALID,  GROUP(298),  GROUP(329),  GROUP(341),
  /*  8 */     INVALID,  GROUP(324),     INVALID,     INVALID,
  /*  c */     INVALID,  GROUP(320),     INVALID,     INVALID,
};

static const uint16_t ud_itab__409[] = {
  /*  0 */         769,     INVALID,
};

static const uint16_t ud_itab__410[] = {
  /*  0 */     INVALID,         792,         793,
};

static const uint16_t ud_itab__411[] = {
  /*  0 */         826,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__412[] = {
  /*  0 */         827,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__413[] = {
  /*  0 */     INVALID,         924,         927,
};

static const uint16_t ud_itab__414[] = {
  /*  0 */     INVALID,         984,     INVALID,
};

static const uint16_t ud_itab__415[] = {
  /*  0 */        1096,     INVALID,
};

static const uint16_t ud_itab__416[] = {
  /*  0 */        1097,     INVALID,
};

static const uint16_t ud_itab__417[] = {
  /*  0 */        1098,     INVALID,
};

static const uint16_t ud_itab__418[] = {
  /*  0 */        1109,     INVALID,     INVALID,     INVALID,
  /*  4 */     INVALID,     INVALID,     INVALID,     INVALID,
};

static const uint16_t ud_itab__419[] = {
  /*  0 */     INVALID,  GROUP(421),     INVALID,
};

static const uint16_t ud_itab__420[] = {
  /*  0 */        1110,     INVALID,
};

static const uint16_t ud_itab__421[] = {
  /*  0 */        1111,     INVALID,
};

static const uint16_t ud_itab__422[] = {
  /*  0 */     INVALID,  GROUP(424),  GROUP(425),
};

static const uint16_t ud_itab__423[] = {
  /*  0 */        1112,     INVALID,
};

static const uint16_t ud_itab__424[] = {
  /*  0 */        1113,        1114,
};

static const uint16_t ud_itab__425[] = {
  /*  0 */     INVALID,        1115,
};

static const uint16_t ud_itab__426[] = {
  /*  0 */        1240,     INVALID,
};

static const uint16_t ud_itab__427[] = {
  /*  0 */        1241,     INVALID,
};

static const uint16_t ud_itab__428[] = {
  /*  0 */        1242,     INVALID,
};

static const uint16_t ud_itab__429[] = {
  /*  0 */        1243,     INVALID,
};

static const uint16_t ud_itab__430[] = {
  /*  0 */     INVALID,  GROUP(432),     INVALID,
};

static const uint16_t ud_itab__431[] = {
  /*  0 */        1257,     INVALID,
};

static const uint16_t ud_itab__432[] = {
  /*  0 */        1258,     INVALID,
};

static const uint16_t ud_itab__433[] = {
  /*  0 */     INVALID,  GROUP(435),  GROUP(436),
};

static const uint16_t ud_itab__434[] = {
  /*  0 */        1259,        1260,
};

static const uint16_t ud_itab__435[] = {
  /*  0 */        1261,        1262,
};

static const uint16_t ud_itab__436[] = {
  /*  0 */     INVALID,        1263,
};

static const uint16_t ud_itab__437[] = {
  /*  0 */        1279,        1286,        1267,        1275,
  /*  4 */        1327,        1334,        1318,        1313,
};

static const uint16_t ud_itab__438[] = {
  /*  0 */        1284,        1287,        1268,        1274,
  /*  4 */        1323,        1330,        1319,        1315,
};

static const uint16_t ud_itab__439[] = {
  /*  0 */        1280,        1285,        1269,        1273,
  /*  4 */        1326,        1333,        1320,        1314,
};

static const uint16_t ud_itab__440[] = {
  /*  0 */        1282,        1289,        1270,        1277,
  /*  4 */        1324,        1331,        1321,        1316,
};

static const uint16_t ud_itab__441[] = {
  /*  0 */        1283,        1290,        1271,        1278,
  /*  4 */        1328,        1335,        1322,        1317,
};

static const uint16_t ud_itab__442[] = {
  /*  0 */        1281,        1288,        1272,        1276,
  /*  4 */        1325,        1332,        1329,        1312,
};

static const uint16_t ud_itab__443[] = {
  /*  0 */        1311,     INVALID,
};

static const uint16_t ud_itab__444[] = {
  /*  0 */     INVALID,        1348,        1349,
};

static const uint16_t ud_itab__445[] = {
  /*  0 */     INVALID,        1404,        1405,
};

static const uint16_t ud_itab__446[] = {
  /*  0 */        1658,     INVALID,
};


struct ud_lookup_table_list_entry ud_lookup_table_list[] = {
    /* 000 */ { ud_itab__0, UD_TAB__OPC_TABLE, "opctbl" },
    /* 001 */ { ud_itab__1, UD_TAB__OPC_MODE, "/m" },
    /* 002 */ { ud_itab__2, UD_TAB__OPC_MODE, "/m" },
    /* 003 */ { ud_itab__3, UD_TAB__OPC_MODE, "/m" },
    /* 004 */ { ud_itab__4, UD_TAB__OPC_MODE, "/m" },
    /* 005 */ { ud_itab__5, UD_TAB__OPC_REG, "/reg" },
    /* 006 */ { ud_itab__6, UD_TAB__OPC_REG, "/reg" },
    /* 007 */ { ud_itab__7, UD_TAB__OPC_MODE, "/m" },
    /* 008 */ { ud_itab__8, UD_TAB__OPC_MODE, "/m" },
    /* 009 */ { ud_itab__9, UD_TAB__OPC_MODE, "/m" },
    /* 010 */ { ud_itab__10, UD_TAB__OPC_MODE, "/m" },
    /* 011 */ { ud_itab__11, UD_TAB__OPC_MODE, "/m" },
    /* 012 */ { ud_itab__12, UD_TAB__OPC_MODE, "/m" },
    /* 013 */ { ud_itab__13, UD_TAB__OPC_MODE, "/m" },
    /* 014 */ { ud_itab__14, UD_TAB__OPC_MODE, "/m" },
    /* 015 */ { ud_itab__15, UD_TAB__OPC_REG, "/reg" },
    /* 016 */ { ud_itab__16, UD_TAB__OPC_REG, "/reg" },
    /* 017 */ { ud_itab__17, UD_TAB__OPC_TABLE, "opctbl" },
    /* 018 */ { ud_itab__18, UD_TAB__OPC_SSE, "/sse" },
    /* 019 */ { ud_itab__19, UD_TAB__OPC_SSE, "/sse" },
    /* 020 */ { ud_itab__20, UD_TAB__OPC_TABLE, "opctbl" },
    /* 021 */ { ud_itab__21, UD_TAB__OPC_SSE, "/sse" },
    /* 022 */ { ud_itab__22, UD_TAB__OPC_SSE, "/sse" },
    /* 023 */ { ud_itab__23, UD_TAB__OPC_SSE, "/sse" },
    /* 024 */ { ud_itab__24, UD_TAB__OPC_SSE, "/sse" },
    /* 025 */ { ud_itab__25, UD_TAB__OPC_SSE, "/sse" },
    /* 026 */ { ud_itab__26, UD_TAB__OPC_SSE, "/sse" },
    /* 027 */ { ud_itab__27, UD_TAB__OPC_MODE, "/m" },
    /* 028 */ { ud_itab__28, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 029 */ { ud_itab__29, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 030 */ { ud_itab__30, UD_TAB__OPC_SSE, "/sse" },
    /* 031 */ { ud_itab__31, UD_TAB__OPC_MODE, "/m" },
    /* 032 */ { ud_itab__32, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 033 */ { ud_itab__33, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 034 */ { ud_itab__34, UD_TAB__OPC_SSE, "/sse" },
    /* 035 */ { ud_itab__35, UD_TAB__OPC_SSE, "/sse" },
    /* 036 */ { ud_itab__36, UD_TAB__OPC_SSE, "/sse" },
    /* 037 */ { ud_itab__37, UD_TAB__OPC_SSE, "/sse" },
    /* 038 */ { ud_itab__38, UD_TAB__OPC_SSE, "/sse" },
    /* 039 */ { ud_itab__39, UD_TAB__OPC_SSE, "/sse" },
    /* 040 */ { ud_itab__40, UD_TAB__OPC_SSE, "/sse" },
    /* 041 */ { ud_itab__41, UD_TAB__OPC_SSE, "/sse" },
    /* 042 */ { ud_itab__42, UD_TAB__OPC_SSE, "/sse" },
    /* 043 */ { ud_itab__43, UD_TAB__OPC_SSE, "/sse" },
    /* 044 */ { ud_itab__44, UD_TAB__OPC_SSE, "/sse" },
    /* 045 */ { ud_itab__45, UD_TAB__OPC_SSE, "/sse" },
    /* 046 */ { ud_itab__46, UD_TAB__OPC_SSE, "/sse" },
    /* 047 */ { ud_itab__47, UD_TAB__OPC_SSE, "/sse" },
    /* 048 */ { ud_itab__48, UD_TAB__OPC_SSE, "/sse" },
    /* 049 */ { ud_itab__49, UD_TAB__OPC_SSE, "/sse" },
    /* 050 */ { ud_itab__50, UD_TAB__OPC_SSE, "/sse" },
    /* 051 */ { ud_itab__51, UD_TAB__OPC_SSE, "/sse" },
    /* 052 */ { ud_itab__52, UD_TAB__OPC_SSE, "/sse" },
    /* 053 */ { ud_itab__53, UD_TAB__OPC_SSE, "/sse" },
    /* 054 */ { ud_itab__54, UD_TAB__OPC_SSE, "/sse" },
    /* 055 */ { ud_itab__55, UD_TAB__OPC_SSE, "/sse" },
    /* 056 */ { ud_itab__56, UD_TAB__OPC_SSE, "/sse" },
    /* 057 */ { ud_itab__57, UD_TAB__OPC_SSE, "/sse" },
    /* 058 */ { ud_itab__58, UD_TAB__OPC_SSE, "/sse" },
    /* 059 */ { ud_itab__59, UD_TAB__OPC_SSE, "/sse" },
    /* 060 */ { ud_itab__60, UD_TAB__OPC_SSE, "/sse" },
    /* 061 */ { ud_itab__61, UD_TAB__OPC_SSE, "/sse" },
    /* 062 */ { ud_itab__62, UD_TAB__OPC_SSE, "/sse" },
    /* 063 */ { ud_itab__63, UD_TAB__OPC_SSE, "/sse" },
    /* 064 */ { ud_itab__64, UD_TAB__OPC_SSE, "/sse" },
    /* 065 */ { ud_itab__65, UD_TAB__OPC_SSE, "/sse" },
    /* 066 */ { ud_itab__66, UD_TAB__OPC_SSE, "/sse" },
    /* 067 */ { ud_itab__67, UD_TAB__OPC_SSE, "/sse" },
    /* 068 */ { ud_itab__68, UD_TAB__OPC_SSE, "/sse" },
    /* 069 */ { ud_itab__69, UD_TAB__OPC_SSE, "/sse" },
    /* 070 */ { ud_itab__70, UD_TAB__OPC_SSE, "/sse" },
    /* 071 */ { ud_itab__71, UD_TAB__OPC_SSE, "/sse" },
    /* 072 */ { ud_itab__72, UD_TAB__OPC_SSE, "/sse" },
    /* 073 */ { ud_itab__73, UD_TAB__OPC_SSE, "/sse" },
    /* 074 */ { ud_itab__74, UD_TAB__OPC_SSE, "/sse" },
    /* 075 */ { ud_itab__75, UD_TAB__OPC_SSE, "/sse" },
    /* 076 */ { ud_itab__76, UD_TAB__OPC_SSE, "/sse" },
    /* 077 */ { ud_itab__77, UD_TAB__OPC_SSE, "/sse" },
    /* 078 */ { ud_itab__78, UD_TAB__OPC_SSE, "/sse" },
    /* 079 */ { ud_itab__79, UD_TAB__OPC_SSE, "/sse" },
    /* 080 */ { ud_itab__80, UD_TAB__OPC_SSE, "/sse" },
    /* 081 */ { ud_itab__81, UD_TAB__OPC_SSE, "/sse" },
    /* 082 */ { ud_itab__82, UD_TAB__OPC_TABLE, "opctbl" },
    /* 083 */ { ud_itab__83, UD_TAB__OPC_SSE, "/sse" },
    /* 084 */ { ud_itab__84, UD_TAB__OPC_SSE, "/sse" },
    /* 085 */ { ud_itab__85, UD_TAB__OPC_SSE, "/sse" },
    /* 086 */ { ud_itab__86, UD_TAB__OPC_SSE, "/sse" },
    /* 087 */ { ud_itab__87, UD_TAB__OPC_SSE, "/sse" },
    /* 088 */ { ud_itab__88, UD_TAB__OPC_SSE, "/sse" },
    /* 089 */ { ud_itab__89, UD_TAB__OPC_OSIZE, "/o" },
    /* 090 */ { ud_itab__90, UD_TAB__OPC_SSE, "/sse" },
    /* 091 */ { ud_itab__91, UD_TAB__OPC_SSE, "/sse" },
    /* 092 */ { ud_itab__92, UD_TAB__OPC_SSE, "/sse" },
    /* 093 */ { ud_itab__93, UD_TAB__OPC_OSIZE, "/o" },
    /* 094 */ { ud_itab__94, UD_TAB__OPC_SSE, "/sse" },
    /* 095 */ { ud_itab__95, UD_TAB__OPC_SSE, "/sse" },
    /* 096 */ { ud_itab__96, UD_TAB__OPC_SSE, "/sse" },
    /* 097 */ { ud_itab__97, UD_TAB__OPC_SSE, "/sse" },
    /* 098 */ { ud_itab__98, UD_TAB__OPC_SSE, "/sse" },
    /* 099 */ { ud_itab__99, UD_TAB__OPC_SSE, "/sse" },
    /* 100 */ { ud_itab__100, UD_TAB__OPC_SSE, "/sse" },
    /* 101 */ { ud_itab__101, UD_TAB__OPC_SSE, "/sse" },
    /* 102 */ { ud_itab__102, UD_TAB__OPC_SSE, "/sse" },
    /* 103 */ { ud_itab__103, UD_TAB__OPC_SSE, "/sse" },
    /* 104 */ { ud_itab__104, UD_TAB__OPC_SSE, "/sse" },
    /* 105 */ { ud_itab__105, UD_TAB__OPC_SSE, "/sse" },
    /* 106 */ { ud_itab__106, UD_TAB__OPC_SSE, "/sse" },
    /* 107 */ { ud_itab__107, UD_TAB__OPC_SSE, "/sse" },
    /* 108 */ { ud_itab__108, UD_TAB__OPC_SSE, "/sse" },
    /* 109 */ { ud_itab__109, UD_TAB__OPC_SSE, "/sse" },
    /* 110 */ { ud_itab__110, UD_TAB__OPC_SSE, "/sse" },
    /* 111 */ { ud_itab__111, UD_TAB__OPC_MOD, "/mod" },
    /* 112 */ { ud_itab__112, UD_TAB__OPC_REG, "/reg" },
    /* 113 */ { ud_itab__113, UD_TAB__OPC_REG, "/reg" },
    /* 114 */ { ud_itab__114, UD_TAB__OPC_RM, "/rm" },
    /* 115 */ { ud_itab__115, UD_TAB__OPC_RM, "/rm" },
    /* 116 */ { ud_itab__116, UD_TAB__OPC_RM, "/rm" },
    /* 117 */ { ud_itab__117, UD_TAB__OPC_MOD, "/mod" },
    /* 118 */ { ud_itab__118, UD_TAB__OPC_REG, "/reg" },
    /* 119 */ { ud_itab__119, UD_TAB__OPC_RM, "/rm" },
    /* 120 */ { ud_itab__120, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 121 */ { ud_itab__121, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 122 */ { ud_itab__122, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 123 */ { ud_itab__123, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 124 */ { ud_itab__124, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 125 */ { ud_itab__125, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 126 */ { ud_itab__126, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 127 */ { ud_itab__127, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 128 */ { ud_itab__128, UD_TAB__OPC_RM, "/rm" },
    /* 129 */ { ud_itab__129, UD_TAB__OPC_RM, "/rm" },
    /* 130 */ { ud_itab__130, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 131 */ { ud_itab__131, UD_TAB__OPC_RM, "/rm" },
    /* 132 */ { ud_itab__132, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 133 */ { ud_itab__133, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 134 */ { ud_itab__134, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 135 */ { ud_itab__135, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 136 */ { ud_itab__136, UD_TAB__OPC_RM, "/rm" },
    /* 137 */ { ud_itab__137, UD_TAB__OPC_REG, "/reg" },
    /* 138 */ { ud_itab__138, UD_TAB__OPC_SSE, "/sse" },
    /* 139 */ { ud_itab__139, UD_TAB__OPC_MOD, "/mod" },
    /* 140 */ { ud_itab__140, UD_TAB__OPC_REG, "/reg" },
    /* 141 */ { ud_itab__141, UD_TAB__OPC_OSIZE, "/o" },
    /* 142 */ { ud_itab__142, UD_TAB__OPC_SSE, "/sse" },
    /* 143 */ { ud_itab__143, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 144 */ { ud_itab__144, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 145 */ { ud_itab__145, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 146 */ { ud_itab__146, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 147 */ { ud_itab__147, UD_TAB__OPC_REG, "/reg" },
    /* 148 */ { ud_itab__148, UD_TAB__OPC_SSE, "/sse" },
    /* 149 */ { ud_itab__149, UD_TAB__OPC_SSE, "/sse" },
    /* 150 */ { ud_itab__150, UD_TAB__OPC_SSE, "/sse" },
    /* 151 */ { ud_itab__151, UD_TAB__OPC_SSE, "/sse" },
    /* 152 */ { ud_itab__152, UD_TAB__OPC_SSE, "/sse" },
    /* 153 */ { ud_itab__153, UD_TAB__OPC_SSE, "/sse" },
    /* 154 */ { ud_itab__154, UD_TAB__OPC_SSE, "/sse" },
    /* 155 */ { ud_itab__155, UD_TAB__OPC_SSE, "/sse" },
    /* 156 */ { ud_itab__156, UD_TAB__OPC_REG, "/reg" },
    /* 157 */ { ud_itab__157, UD_TAB__OPC_MOD, "/mod" },
    /* 158 */ { ud_itab__158, UD_TAB__OPC_SSE, "/sse" },
    /* 159 */ { ud_itab__159, UD_TAB__OPC_SSE, "/sse" },
    /* 160 */ { ud_itab__160, UD_TAB__OPC_SSE, "/sse" },
    /* 161 */ { ud_itab__161, UD_TAB__OPC_MOD, "/mod" },
    /* 162 */ { ud_itab__162, UD_TAB__OPC_REG, "/reg" },
    /* 163 */ { ud_itab__163, UD_TAB__OPC_RM, "/rm" },
    /* 164 */ { ud_itab__164, UD_TAB__OPC_RM, "/rm" },
    /* 165 */ { ud_itab__165, UD_TAB__OPC_RM, "/rm" },
    /* 166 */ { ud_itab__166, UD_TAB__OPC_SSE, "/sse" },
    /* 167 */ { ud_itab__167, UD_TAB__OPC_SSE, "/sse" },
    /* 168 */ { ud_itab__168, UD_TAB__OPC_SSE, "/sse" },
    /* 169 */ { ud_itab__169, UD_TAB__OPC_OSIZE, "/o" },
    /* 170 */ { ud_itab__170, UD_TAB__OPC_OSIZE, "/o" },
    /* 171 */ { ud_itab__171, UD_TAB__OPC_SSE, "/sse" },
    /* 172 */ { ud_itab__172, UD_TAB__OPC_OSIZE, "/o" },
    /* 173 */ { ud_itab__173, UD_TAB__OPC_OSIZE, "/o" },
    /* 174 */ { ud_itab__174, UD_TAB__OPC_MOD, "/mod" },
    /* 175 */ { ud_itab__175, UD_TAB__OPC_SSE, "/sse" },
    /* 176 */ { ud_itab__176, UD_TAB__OPC_SSE, "/sse" },
    /* 177 */ { ud_itab__177, UD_TAB__OPC_SSE, "/sse" },
    /* 178 */ { ud_itab__178, UD_TAB__OPC_MOD, "/mod" },
    /* 179 */ { ud_itab__179, UD_TAB__OPC_SSE, "/sse" },
    /* 180 */ { ud_itab__180, UD_TAB__OPC_SSE, "/sse" },
    /* 181 */ { ud_itab__181, UD_TAB__OPC_SSE, "/sse" },
    /* 182 */ { ud_itab__182, UD_TAB__OPC_SSE, "/sse" },
    /* 183 */ { ud_itab__183, UD_TAB__OPC_SSE, "/sse" },
    /* 184 */ { ud_itab__184, UD_TAB__OPC_SSE, "/sse" },
    /* 185 */ { ud_itab__185, UD_TAB__OPC_SSE, "/sse" },
    /* 186 */ { ud_itab__186, UD_TAB__OPC_SSE, "/sse" },
    /* 187 */ { ud_itab__187, UD_TAB__OPC_SSE, "/sse" },
    /* 188 */ { ud_itab__188, UD_TAB__OPC_SSE, "/sse" },
    /* 189 */ { ud_itab__189, UD_TAB__OPC_SSE, "/sse" },
    /* 190 */ { ud_itab__190, UD_TAB__OPC_SSE, "/sse" },
    /* 191 */ { ud_itab__191, UD_TAB__OPC_SSE, "/sse" },
    /* 192 */ { ud_itab__192, UD_TAB__OPC_SSE, "/sse" },
    /* 193 */ { ud_itab__193, UD_TAB__OPC_SSE, "/sse" },
    /* 194 */ { ud_itab__194, UD_TAB__OPC_SSE, "/sse" },
    /* 195 */ { ud_itab__195, UD_TAB__OPC_SSE, "/sse" },
    /* 196 */ { ud_itab__196, UD_TAB__OPC_SSE, "/sse" },
    /* 197 */ { ud_itab__197, UD_TAB__OPC_SSE, "/sse" },
    /* 198 */ { ud_itab__198, UD_TAB__OPC_SSE, "/sse" },
    /* 199 */ { ud_itab__199, UD_TAB__OPC_SSE, "/sse" },
    /* 200 */ { ud_itab__200, UD_TAB__OPC_SSE, "/sse" },
    /* 201 */ { ud_itab__201, UD_TAB__OPC_SSE, "/sse" },
    /* 202 */ { ud_itab__202, UD_TAB__OPC_SSE, "/sse" },
    /* 203 */ { ud_itab__203, UD_TAB__OPC_SSE, "/sse" },
    /* 204 */ { ud_itab__204, UD_TAB__OPC_SSE, "/sse" },
    /* 205 */ { ud_itab__205, UD_TAB__OPC_SSE, "/sse" },
    /* 206 */ { ud_itab__206, UD_TAB__OPC_SSE, "/sse" },
    /* 207 */ { ud_itab__207, UD_TAB__OPC_SSE, "/sse" },
    /* 208 */ { ud_itab__208, UD_TAB__OPC_SSE, "/sse" },
    /* 209 */ { ud_itab__209, UD_TAB__OPC_SSE, "/sse" },
    /* 210 */ { ud_itab__210, UD_TAB__OPC_SSE, "/sse" },
    /* 211 */ { ud_itab__211, UD_TAB__OPC_SSE, "/sse" },
    /* 212 */ { ud_itab__212, UD_TAB__OPC_SSE, "/sse" },
    /* 213 */ { ud_itab__213, UD_TAB__OPC_SSE, "/sse" },
    /* 214 */ { ud_itab__214, UD_TAB__OPC_SSE, "/sse" },
    /* 215 */ { ud_itab__215, UD_TAB__OPC_SSE, "/sse" },
    /* 216 */ { ud_itab__216, UD_TAB__OPC_SSE, "/sse" },
    /* 217 */ { ud_itab__217, UD_TAB__OPC_SSE, "/sse" },
    /* 218 */ { ud_itab__218, UD_TAB__OPC_SSE, "/sse" },
    /* 219 */ { ud_itab__219, UD_TAB__OPC_SSE, "/sse" },
    /* 220 */ { ud_itab__220, UD_TAB__OPC_SSE, "/sse" },
    /* 221 */ { ud_itab__221, UD_TAB__OPC_SSE, "/sse" },
    /* 222 */ { ud_itab__222, UD_TAB__OPC_SSE, "/sse" },
    /* 223 */ { ud_itab__223, UD_TAB__OPC_SSE, "/sse" },
    /* 224 */ { ud_itab__224, UD_TAB__OPC_REG, "/reg" },
    /* 225 */ { ud_itab__225, UD_TAB__OPC_REG, "/reg" },
    /* 226 */ { ud_itab__226, UD_TAB__OPC_SSE, "/sse" },
    /* 227 */ { ud_itab__227, UD_TAB__OPC_SSE, "/sse" },
    /* 228 */ { ud_itab__228, UD_TAB__OPC_SSE, "/sse" },
    /* 229 */ { ud_itab__229, UD_TAB__OPC_REG, "/reg" },
    /* 230 */ { ud_itab__230, UD_TAB__OPC_SSE, "/sse" },
    /* 231 */ { ud_itab__231, UD_TAB__OPC_SSE, "/sse" },
    /* 232 */ { ud_itab__232, UD_TAB__OPC_SSE, "/sse" },
    /* 233 */ { ud_itab__233, UD_TAB__OPC_SSE, "/sse" },
    /* 234 */ { ud_itab__234, UD_TAB__OPC_REG, "/reg" },
    /* 235 */ { ud_itab__235, UD_TAB__OPC_SSE, "/sse" },
    /* 236 */ { ud_itab__236, UD_TAB__OPC_SSE, "/sse" },
    /* 237 */ { ud_itab__237, UD_TAB__OPC_SSE, "/sse" },
    /* 238 */ { ud_itab__238, UD_TAB__OPC_SSE, "/sse" },
    /* 239 */ { ud_itab__239, UD_TAB__OPC_REG, "/reg" },
    /* 240 */ { ud_itab__240, UD_TAB__OPC_SSE, "/sse" },
    /* 241 */ { ud_itab__241, UD_TAB__OPC_SSE, "/sse" },
    /* 242 */ { ud_itab__242, UD_TAB__OPC_SSE, "/sse" },
    /* 243 */ { ud_itab__243, UD_TAB__OPC_SSE, "/sse" },
    /* 244 */ { ud_itab__244, UD_TAB__OPC_SSE, "/sse" },
    /* 245 */ { ud_itab__245, UD_TAB__OPC_SSE, "/sse" },
    /* 246 */ { ud_itab__246, UD_TAB__OPC_SSE, "/sse" },
    /* 247 */ { ud_itab__247, UD_TAB__OPC_SSE, "/sse" },
    /* 248 */ { ud_itab__248, UD_TAB__OPC_SSE, "/sse" },
    /* 249 */ { ud_itab__249, UD_TAB__OPC_SSE, "/sse" },
    /* 250 */ { ud_itab__250, UD_TAB__OPC_SSE, "/sse" },
    /* 251 */ { ud_itab__251, UD_TAB__OPC_SSE, "/sse" },
    /* 252 */ { ud_itab__252, UD_TAB__OPC_SSE, "/sse" },
    /* 253 */ { ud_itab__253, UD_TAB__OPC_SSE, "/sse" },
    /* 254 */ { ud_itab__254, UD_TAB__OPC_SSE, "/sse" },
    /* 255 */ { ud_itab__255, UD_TAB__OPC_SSE, "/sse" },
    /* 256 */ { ud_itab__256, UD_TAB__OPC_SSE, "/sse" },
    /* 257 */ { ud_itab__257, UD_TAB__OPC_SSE, "/sse" },
    /* 258 */ { ud_itab__258, UD_TAB__OPC_SSE, "/sse" },
    /* 259 */ { ud_itab__259, UD_TAB__OPC_SSE, "/sse" },
    /* 260 */ { ud_itab__260, UD_TAB__OPC_SSE, "/sse" },
    /* 261 */ { ud_itab__261, UD_TAB__OPC_SSE, "/sse" },
    /* 262 */ { ud_itab__262, UD_TAB__OPC_3DNOW, "/3dnow" },
    /* 263 */ { ud_itab__263, UD_TAB__OPC_SSE, "/sse" },
    /* 264 */ { ud_itab__264, UD_TAB__OPC_SSE, "/sse" },
    /* 265 */ { ud_itab__265, UD_TAB__OPC_SSE, "/sse" },
    /* 266 */ { ud_itab__266, UD_TAB__OPC_SSE, "/sse" },
    /* 267 */ { ud_itab__267, UD_TAB__OPC_SSE, "/sse" },
    /* 268 */ { ud_itab__268, UD_TAB__OPC_SSE, "/sse" },
    /* 269 */ { ud_itab__269, UD_TAB__OPC_MODE, "/m" },
    /* 270 */ { ud_itab__270, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 271 */ { ud_itab__271, UD_TAB__OPC_MODE, "/m" },
    /* 272 */ { ud_itab__272, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 273 */ { ud_itab__273, UD_TAB__OPC_SSE, "/sse" },
    /* 274 */ { ud_itab__274, UD_TAB__OPC_SSE, "/sse" },
    /* 275 */ { ud_itab__275, UD_TAB__OPC_SSE, "/sse" },
    /* 276 */ { ud_itab__276, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 277 */ { ud_itab__277, UD_TAB__OPC_VENDOR, "/vendor" },
    /* 278 */ { ud_itab__278, UD_TAB__OPC_SSE, "/sse" },
    /* 279 */ { ud_itab__279, UD_TAB__OPC_MOD, "/mod" },
    /* 280 */ { ud_itab__280, UD_TAB__OPC_REG, "/reg" },
    /* 281 */ { ud_itab__281, UD_TAB__OPC_RM, "/rm" },
    /* 282 */ { ud_itab__282, UD_TAB__OPC_RM, "/rm" },
    /* 283 */ { ud_itab__283, UD_TAB__OPC_RM, "/rm" },
    /* 284 */ { ud_itab__284, UD_TAB__OPC_RM, "/rm" },
    /* 285 */ { ud_itab__285, UD_TAB__OPC_RM, "/rm" },
    /* 286 */ { ud_itab__286, UD_TAB__OPC_RM, "/rm" },
    /* 287 */ { ud_itab__287, UD_TAB__OPC_SSE, "/sse" },
    /* 288 */ { ud_itab__288, UD_TAB__OPC_SSE, "/sse" },
    /* 289 */ { ud_itab__289, UD_TAB__OPC_SSE, "/sse" },
    /* 290 */ { ud_itab__290, UD_TAB__OPC_SSE, "/sse" },
    /* 291 */ { ud_itab__291, UD_TAB__OPC_SSE, "/sse" },
    /* 292 */ { ud_itab__292, UD_TAB__OPC_SSE, "/sse" },
    /* 293 */ { ud_itab__293, UD_TAB__OPC_SSE, "/sse" },
    /* 294 */ { ud_itab__294, UD_TAB__OPC_SSE, "/sse" },
    /* 295 */ { ud_itab__295, UD_TAB__OPC_REG, "/reg" },
    /* 296 */ { ud_itab__296, UD_TAB__OPC_SSE, "/sse" },
    /* 297 */ { ud_itab__297, UD_TAB__OPC_VEX, "/vex" },
    /* 298 */ { ud_itab__298, UD_TAB__OPC_TABLE, "opctbl" },
    /* 299 */ { ud_itab__299, UD_TAB__OPC_OSIZE, "/o" },
    /* 300 */ { ud_itab__300, UD_TAB__OPC_OSIZE, "/o" },
    /* 301 */ { ud_itab__301, UD_TAB__OPC_MOD, "/mod" },
    /* 302 */ { ud_itab__302, UD_TAB__OPC_MOD, "/mod" },
    /* 303 */ { ud_itab__303, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 304 */ { ud_itab__304, UD_TAB__OPC_REG, "/reg" },
    /* 305 */ { ud_itab__305, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 306 */ { ud_itab__306, UD_TAB__OPC_REG, "/reg" },
    /* 307 */ { ud_itab__307, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 308 */ { ud_itab__308, UD_TAB__OPC_REG, "/reg" },
    /* 309 */ { ud_itab__309, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 310 */ { ud_itab__310, UD_TAB__OPC_MOD, "/mod" },
    /* 311 */ { ud_itab__311, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 312 */ { ud_itab__312, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 313 */ { ud_itab__313, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 314 */ { ud_itab__314, UD_TAB__OPC_TABLE, "opctbl" },
    /* 315 */ { ud_itab__315, UD_TAB__OPC_MOD, "/mod" },
    /* 316 */ { ud_itab__316, UD_TAB__OPC_MOD, "/mod" },
    /* 317 */ { ud_itab__317, UD_TAB__OPC_MOD, "/mod" },
    /* 318 */ { ud_itab__318, UD_TAB__OPC_REG, "/reg" },
    /* 319 */ { ud_itab__319, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 320 */ { ud_itab__320, UD_TAB__OPC_TABLE, "opctbl" },
    /* 321 */ { ud_itab__321, UD_TAB__OPC_MOD, "/mod" },
    /* 322 */ { ud_itab__322, UD_TAB__OPC_MOD, "/mod" },
    /* 323 */ { ud_itab__323, UD_TAB__OPC_MOD, "/mod" },
    /* 324 */ { ud_itab__324, UD_TAB__OPC_TABLE, "opctbl" },
    /* 325 */ { ud_itab__325, UD_TAB__OPC_MOD, "/mod" },
    /* 326 */ { ud_itab__326, UD_TAB__OPC_MOD, "/mod" },
    /* 327 */ { ud_itab__327, UD_TAB__OPC_MOD, "/mod" },
    /* 328 */ { ud_itab__328, UD_TAB__OPC_MOD, "/mod" },
    /* 329 */ { ud_itab__329, UD_TAB__OPC_TABLE, "opctbl" },
    /* 330 */ { ud_itab__330, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 331 */ { ud_itab__331, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 332 */ { ud_itab__332, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 333 */ { ud_itab__333, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 334 */ { ud_itab__334, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 335 */ { ud_itab__335, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 336 */ { ud_itab__336, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 337 */ { ud_itab__337, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 338 */ { ud_itab__338, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 339 */ { ud_itab__339, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 340 */ { ud_itab__340, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 341 */ { ud_itab__341, UD_TAB__OPC_TABLE, "opctbl" },
    /* 342 */ { ud_itab__342, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 343 */ { ud_itab__343, UD_TAB__OPC_OSIZE, "/o" },
    /* 344 */ { ud_itab__344, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 345 */ { ud_itab__345, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 346 */ { ud_itab__346, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 347 */ { ud_itab__347, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 348 */ { ud_itab__348, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 349 */ { ud_itab__349, UD_TAB__OPC_MODE, "/m" },
    /* 350 */ { ud_itab__350, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 351 */ { ud_itab__351, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 352 */ { ud_itab__352, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 353 */ { ud_itab__353, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 354 */ { ud_itab__354, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 355 */ { ud_itab__355, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 356 */ { ud_itab__356, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 357 */ { ud_itab__357, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 358 */ { ud_itab__358, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 359 */ { ud_itab__359, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 360 */ { ud_itab__360, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 361 */ { ud_itab__361, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 362 */ { ud_itab__362, UD_TAB__OPC_VEX_L, "/vexl" },
    /* 363 */ { ud_itab__363, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 364 */ { ud_itab__364, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 365 */ { ud_itab__365, UD_TAB__OPC_VEX_W, "/vexw" },
    /* 366 */ { ud_itab__366, UD_TAB__OPC_MODE, "/m" },
    /* 367 */ { ud_itab__367, UD_TAB__OPC_MODE, "/m" },
    /* 368 */ { ud_itab__368, UD_TAB__OPC_REG, "/reg" },
    /* 369 */ { ud_itab__369, UD_TAB__OPC_MODE, "/m" },
    /* 370 */ { ud_itab__370, UD_TAB__OPC_MODE, "/m" },
    /* 371 */ { ud_itab__371, UD_TAB__OPC_OSIZE, "/o" },
    /* 372 */ { ud_itab__372, UD_TAB__OPC_OSIZE, "/o" },
    /* 373 */ { ud_itab__373, UD_TAB__OPC_OSIZE, "/o" },
    /* 374 */ { ud_itab__374, UD_TAB__OPC_MODE, "/m" },
    /* 375 */ { ud_itab__375, UD_TAB__OPC_MODE, "/m" },
    /* 376 */ { ud_itab__376, UD_TAB__OPC_REG, "/reg" },
    /* 377 */ { ud_itab__377, UD_TAB__OPC_REG, "/reg" },
    /* 378 */ { ud_itab__378, UD_TAB__OPC_REG, "/reg" },
    /* 379 */ { ud_itab__379, UD_TAB__OPC_MOD, "/mod" },
    /* 380 */ { ud_itab__380, UD_TAB__OPC_X87, "/x87" },
    /* 381 */ { ud_itab__381, UD_TAB__OPC_REG, "/reg" },
    /* 382 */ { ud_itab__382, UD_TAB__OPC_MOD, "/mod" },
    /* 383 */ { ud_itab__383, UD_TAB__OPC_REG, "/reg" },
    /* 384 */ { ud_itab__384, UD_TAB__OPC_X87, "/x87" },
    /* 385 */ { ud_itab__385, UD_TAB__OPC_MOD, "/mod" },
    /* 386 */ { ud_itab__386, UD_TAB__OPC_REG, "/reg" },
    /* 387 */ { ud_itab__387, UD_TAB__OPC_X87, "/x87" },
    /* 388 */ { ud_itab__388, UD_TAB__OPC_MOD, "/mod" },
    /* 389 */ { ud_itab__389, UD_TAB__OPC_X87, "/x87" },
    /* 390 */ { ud_itab__390, UD_TAB__OPC_REG, "/reg" },
    /* 391 */ { ud_itab__391, UD_TAB__OPC_MOD, "/mod" },
    /* 392 */ { ud_itab__392, UD_TAB__OPC_REG, "/reg" },
    /* 393 */ { ud_itab__393, UD_TAB__OPC_X87, "/x87" },
    /* 394 */ { ud_itab__394, UD_TAB__OPC_MOD, "/mod" },
    /* 395 */ { ud_itab__395, UD_TAB__OPC_X87, "/x87" },
    /* 396 */ { ud_itab__396, UD_TAB__OPC_REG, "/reg" },
    /* 397 */ { ud_itab__397, UD_TAB__OPC_MOD, "/mod" },
    /* 398 */ { ud_itab__398, UD_TAB__OPC_X87, "/x87" },
    /* 399 */ { ud_itab__399, UD_TAB__OPC_REG, "/reg" },
    /* 400 */ { ud_itab__400, UD_TAB__OPC_MOD, "/mod" },
    /* 401 */ { ud_itab__401, UD_TAB__OPC_X87, "/x87" },
    /* 402 */ { ud_itab__402, UD_TAB__OPC_REG, "/reg" },
    /* 403 */ { ud_itab__403, UD_TAB__OPC_OSIZE, "/o" },
    /* 404 */ { ud_itab__404, UD_TAB__OPC_MODE, "/m" },
    /* 405 */ { ud_itab__405, UD_TAB__OPC_OSIZE, "/o" },
    /* 406 */ { ud_itab__406, UD_TAB__OPC_ASIZE, "/a" },
    /* 407 */ { ud_itab__407, UD_TAB__OPC_MODE, "/m" },
    /* 408 */ { ud_itab__408, UD_TAB__OPC_VEX, "/vex" },
    /* 409 */ { ud_itab__409, UD_TAB__OPC_MODE, "/m" },
    /* 410 */ { ud_itab__410, UD_TAB__OPC_OSIZE, "/o" },
    /* 411 */ { ud_itab__411, UD_TAB__OPC_REG, "/reg" },
    /* 412 */ { ud_itab__412, UD_TAB__OPC_REG, "/reg" },
    /* 413 */ { ud_itab__413, UD_TAB__OPC_OSIZE, "/o" },
    /* 414 */ { ud_itab__414, UD_TAB__OPC_OSIZE, "/o" },
    /* 415 */ { ud_itab__415, UD_TAB__OPC_MODE, "/m" },
    /* 416 */ { ud_itab__416, UD_TAB__OPC_MODE, "/m" },
    /* 417 */ { ud_itab__417, UD_TAB__OPC_MODE, "/m" },
    /* 418 */ { ud_itab__418, UD_TAB__OPC_REG, "/reg" },
    /* 419 */ { ud_itab__419, UD_TAB__OPC_OSIZE, "/o" },
    /* 420 */ { ud_itab__420, UD_TAB__OPC_MODE, "/m" },
    /* 421 */ { ud_itab__421, UD_TAB__OPC_MODE, "/m" },
    /* 422 */ { ud_itab__422, UD_TAB__OPC_OSIZE, "/o" },
    /* 423 */ { ud_itab__423, UD_TAB__OPC_MODE, "/m" },
    /* 424 */ { ud_itab__424, UD_TAB__OPC_MODE, "/m" },
    /* 425 */ { ud_itab__425, UD_TAB__OPC_MODE, "/m" },
    /* 426 */ { ud_itab__426, UD_TAB__OPC_MODE, "/m" },
    /* 427 */ { ud_itab__427, UD_TAB__OPC_MODE, "/m" },
    /* 428 */ { ud_itab__428, UD_TAB__OPC_MODE, "/m" },
    /* 429 */ { ud_itab__429, UD_TAB__OPC_MODE, "/m" },
    /* 430 */ { ud_itab__430, UD_TAB__OPC_OSIZE, "/o" },
    /* 431 */ { ud_itab__431, UD_TAB__OPC_MODE, "/m" },
    /* 432 */ { ud_itab__432, UD_TAB__OPC_MODE, "/m" },
    /* 433 */ { ud_itab__433, UD_TAB__OPC_OSIZE, "/o" },
    /* 434 */ { ud_itab__434, UD_TAB__OPC_MODE, "/m" },
    /* 435 */ { ud_itab__435, UD_TAB__OPC_MODE, "/m" },
    /* 436 */ { ud_itab__436, UD_TAB__OPC_MODE, "/m" },
    /* 437 */ { ud_itab__437, UD_TAB__OPC_REG, "/reg" },
    /* 438 */ { ud_itab__438, UD_TAB__OPC_REG, "/reg" },
    /* 439 */ { ud_itab__439, UD_TAB__OPC_REG, "/reg" },
    /* 440 */ { ud_itab__440, UD_TAB__OPC_REG, "/reg" },
    /* 441 */ { ud_itab__441, UD_TAB__OPC_REG, "/reg" },
    /* 442 */ { ud_itab__442, UD_TAB__OPC_REG, "/reg" },
    /* 443 */ { ud_itab__443, UD_TAB__OPC_MODE, "/m" },
    /* 444 */ { ud_itab__444, UD_TAB__OPC_OSIZE, "/o" },
    /* 445 */ { ud_itab__445, UD_TAB__OPC_OSIZE, "/o" },
    /* 446 */ { ud_itab__446, UD_TAB__OPC_MODE, "/m" },
};

/* itab entry operand definitions (for readability) */
#define O_AL      { OP_AL,       SZ_B     }
#define O_AX      { OP_AX,       SZ_W     }
#define O_Av      { OP_A,        SZ_V     }
#define O_C       { OP_C,        SZ_NA    }
#define O_CL      { OP_CL,       SZ_B     }
#define O_CS      { OP_CS,       SZ_NA    }
#define O_CX      { OP_CX,       SZ_W     }
#define O_D       { OP_D,        SZ_NA    }
#define O_DL      { OP_DL,       SZ_B     }
#define O_DS      { OP_DS,       SZ_NA    }
#define O_DX      { OP_DX,       SZ_W     }
#define O_E       { OP_E,        SZ_NA    }
#define O_ES      { OP_ES,       SZ_NA    }
#define O_Eb      { OP_E,        SZ_B     }
#define O_Ed      { OP_E,        SZ_D     }
#define O_Eq      { OP_E,        SZ_Q     }
#define O_Ev      { OP_E,        SZ_V     }
#define O_Ew      { OP_E,        SZ_W     }
#define O_Ey      { OP_E,        SZ_Y     }
#define O_Ez      { OP_E,        SZ_Z     }
#define O_FS      { OP_FS,       SZ_NA    }
#define O_Fv      { OP_F,        SZ_V     }
#define O_G       { OP_G,        SZ_NA    }
#define O_GS      { OP_GS,       SZ_NA    }
#define O_Gb      { OP_G,        SZ_B     }
#define O_Gd      { OP_G,        SZ_D     }
#define O_Gq      { OP_G,        SZ_Q     }
#define O_Gv      { OP_G,        SZ_V     }
#define O_Gw      { OP_G,        SZ_W     }
#define O_Gy      { OP_G,        SZ_Y     }
#define O_Gz      { OP_G,        SZ_Z     }
#define O_H       { OP_H,        SZ_X     }
#define O_Hqq     { OP_H,        SZ_QQ    }
#define O_Hx      { OP_H,        SZ_X     }
#define O_I1      { OP_I1,       SZ_NA    }
#define O_I3      { OP_I3,       SZ_NA    }
#define O_Ib      { OP_I,        SZ_B     }
#define O_Iv      { OP_I,        SZ_V     }
#define O_Iw      { OP_I,        SZ_W     }
#define O_Iz      { OP_I,        SZ_Z     }
#define O_Jb      { OP_J,        SZ_B     }
#define O_Jv      { OP_J,        SZ_V     }
#define O_Jz      { OP_J,        SZ_Z     }
#define O_L       { OP_L,        SZ_O     }
#define O_Lx      { OP_L,        SZ_X     }
#define O_M       { OP_M,        SZ_NA    }
#define O_Mb      { OP_M,        SZ_B     }
#define O_MbRd    { OP_MR,       SZ_BD    }
#define O_MbRv    { OP_MR,       SZ_BV    }
#define O_Md      { OP_M,        SZ_D     }
#define O_MdRy    { OP_MR,       SZ_DY    }
#define O_MdU     { OP_MU,       SZ_DO    }
#define O_Mdq     { OP_M,        SZ_DQ    }
#define O_Mo      { OP_M,        SZ_O     }
#define O_Mq      { OP_M,        SZ_Q     }
#define O_MqU     { OP_MU,       SZ_QO    }
#define O_Ms      { OP_M,        SZ_W     }
#define O_Mt      { OP_M,        SZ_T     }
#define O_Mv      { OP_M,        SZ_V     }
#define O_Mw      { OP_M,        SZ_W     }
#define O_MwRd    { OP_MR,       SZ_WD    }
#define O_MwRv    { OP_MR,       SZ_WV    }
#define O_MwRy    { OP_MR,       SZ_WY    }
#define O_MwU     { OP_MU,       SZ_WO    }
#define O_N       { OP_N,        SZ_Q     }
#define O_NONE    { OP_NONE,     SZ_NA    }
#define O_Ob      { OP_O,        SZ_B     }
#define O_Ov      { OP_O,        SZ_V     }
#define O_Ow      { OP_O,        SZ_W     }
#define O_P       { OP_P,        SZ_Q     }
#define O_Q       { OP_Q,        SZ_Q     }
#define O_R       { OP_R,        SZ_RDQ   }
#define O_R0b     { OP_R0,       SZ_B     }
#define O_R0v     { OP_R0,       SZ_V     }
#define O_R0w     { OP_R0,       SZ_W     }
#define O_R0y     { OP_R0,       SZ_Y     }
#define O_R0z     { OP_R0,       SZ_Z     }
#define O_R1b     { OP_R1,       SZ_B     }
#define O_R1v     { OP_R1,       SZ_V     }
#define O_R1w     { OP_R1,       SZ_W     }
#define O_R1y     { OP_R1,       SZ_Y     }
#define O_R1z     { OP_R1,       SZ_Z     }
#define O_R2b     { OP_R2,       SZ_B     }
#define O_R2v     { OP_R2,       SZ_V     }
#define O_R2w     { OP_R2,       SZ_W     }
#define O_R2y     { OP_R2,       SZ_Y     }
#define O_R2z     { OP_R2,       SZ_Z     }
#define O_R3b     { OP_R3,       SZ_B     }
#define O_R3v     { OP_R3,       SZ_V     }
#define O_R3w     { OP_R3,       SZ_W     }
#define O_R3y     { OP_R3,       SZ_Y     }
#define O_R3z     { OP_R3,       SZ_Z     }
#define O_R4b     { OP_R4,       SZ_B     }
#define O_R4v     { OP_R4,       SZ_V     }
#define O_R4w     { OP_R4,       SZ_W     }
#define O_R4y     { OP_R4,       SZ_Y     }
#define O_R4z     { OP_R4,       SZ_Z     }
#define O_R5b     { OP_R5,       SZ_B     }
#define O_R5v     { OP_R5,       SZ_V     }
#define O_R5w     { OP_R5,       SZ_W     }
#define O_R5y     { OP_R5,       SZ_Y     }
#define O_R5z     { OP_R5,       SZ_Z     }
#define O_R6b     { OP_R6,       SZ_B     }
#define O_R6v     { OP_R6,       SZ_V     }
#define O_R6w     { OP_R6,       SZ_W     }
#define O_R6y     { OP_R6,       SZ_Y     }
#define O_R6z     { OP_R6,       SZ_Z     }
#define O_R7b     { OP_R7,       SZ_B     }
#define O_R7v     { OP_R7,       SZ_V     }
#define O_R7w     { OP_R7,       SZ_W     }
#define O_R7y     { OP_R7,       SZ_Y     }
#define O_R7z     { OP_R7,       SZ_Z     }
#define O_S       { OP_S,        SZ_W     }
#define O_SS      { OP_SS,       SZ_NA    }
#define O_ST0     { OP_ST0,      SZ_NA    }
#define O_ST1     { OP_ST1,      SZ_NA    }
#define O_ST2     { OP_ST2,      SZ_NA    }
#define O_ST3     { OP_ST3,      SZ_NA    }
#define O_ST4     { OP_ST4,      SZ_NA    }
#define O_ST5     { OP_ST5,      SZ_NA    }
#define O_ST6     { OP_ST6,      SZ_NA    }
#define O_ST7     { OP_ST7,      SZ_NA    }
#define O_U       { OP_U,        SZ_O     }
#define O_Ux      { OP_U,        SZ_X     }
#define O_V       { OP_V,        SZ_DQ    }
#define O_Vdq     { OP_V,        SZ_DQ    }
#define O_Vqq     { OP_V,        SZ_QQ    }
#define O_Vsd     { OP_V,        SZ_Q     }
#define O_Vx      { OP_V,        SZ_X     }
#define O_W       { OP_W,        SZ_DQ    }
#define O_Wdq     { OP_W,        SZ_DQ    }
#define O_Wqq     { OP_W,        SZ_QQ    }
#define O_Wsd     { OP_W,        SZ_Q     }
#define O_Wx      { OP_W,        SZ_X     }
#define O_eAX     { OP_eAX,      SZ_Z     }
#define O_eCX     { OP_eCX,      SZ_Z     }
#define O_eDX     { OP_eDX,      SZ_Z     }
#define O_rAX     { OP_rAX,      SZ_V     }
#define O_rCX     { OP_rCX,      SZ_V     }
#define O_rDX     { OP_rDX,      SZ_V     }
#define O_sIb     { OP_sI,       SZ_B     }
#define O_sIv     { OP_sI,       SZ_V     }
#define O_sIz     { OP_sI,       SZ_Z     }

struct ud_itab_entry ud_itab[] = {
  /* 0000 */ { UD_Iinvalid, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0001 */ { UD_Iaaa, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0002 */ { UD_Iaad, O_Ib, O_NONE, O_NONE, O_NONE, P_none },
  /* 0003 */ { UD_Iaam, O_Ib, O_NONE, O_NONE, O_NONE, P_none },
  /* 0004 */ { UD_Iaas, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0005 */ { UD_Iadc, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0006 */ { UD_Iadc, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0007 */ { UD_Iadc, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0008 */ { UD_Iadc, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0009 */ { UD_Iadc, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0010 */ { UD_Iadc, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0011 */ { UD_Iadc, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0012 */ { UD_Iadc, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 0013 */ { UD_Iadc, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0014 */ { UD_Iadc, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0015 */ { UD_Iadd, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0016 */ { UD_Iadd, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0017 */ { UD_Iadd, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0018 */ { UD_Iadd, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0019 */ { UD_Iadd, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0020 */ { UD_Iadd, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0021 */ { UD_Iadd, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0022 */ { UD_Iadd, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 0023 */ { UD_Iadd, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0024 */ { UD_Iadd, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0025 */ { UD_Iaddpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0026 */ { UD_Ivaddpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0027 */ { UD_Iaddps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0028 */ { UD_Ivaddps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0029 */ { UD_Iaddsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0030 */ { UD_Ivaddsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0031 */ { UD_Iaddss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0032 */ { UD_Ivaddss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0033 */ { UD_Iaddsubpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0034 */ { UD_Ivaddsubpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0035 */ { UD_Iaddsubps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0036 */ { UD_Ivaddsubps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0037 */ { UD_Iaesdec, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0038 */ { UD_Ivaesdec, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0039 */ { UD_Iaesdeclast, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0040 */ { UD_Ivaesdeclast, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0041 */ { UD_Iaesenc, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0042 */ { UD_Ivaesenc, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0043 */ { UD_Iaesenclast, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0044 */ { UD_Ivaesenclast, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0045 */ { UD_Iaesimc, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0046 */ { UD_Ivaesimc, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0047 */ { UD_Iaeskeygenassist, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0048 */ { UD_Ivaeskeygenassist, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0049 */ { UD_Iand, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0050 */ { UD_Iand, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0051 */ { UD_Iand, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0052 */ { UD_Iand, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0053 */ { UD_Iand, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0054 */ { UD_Iand, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0055 */ { UD_Iand, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0056 */ { UD_Iand, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 0057 */ { UD_Iand, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0058 */ { UD_Iand, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0059 */ { UD_Iandpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0060 */ { UD_Ivandpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0061 */ { UD_Iandps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0062 */ { UD_Ivandps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0063 */ { UD_Iandnpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0064 */ { UD_Ivandnpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0065 */ { UD_Iandnps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0066 */ { UD_Ivandnps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0067 */ { UD_Iarpl, O_Ew, O_Gw, O_NONE, O_NONE, P_aso },
  /* 0068 */ { UD_Imovsxd, O_Gq, O_Ed, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexx|P_rexr|P_rexb },
  /* 0069 */ { UD_Icall, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0070 */ { UD_Icall, O_Eq, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 0071 */ { UD_Icall, O_Fv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0072 */ { UD_Icall, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0073 */ { UD_Icall, O_Av, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0074 */ { UD_Icbw, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0075 */ { UD_Icwde, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0076 */ { UD_Icdqe, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0077 */ { UD_Iclc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0078 */ { UD_Icld, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0079 */ { UD_Iclflush, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0080 */ { UD_Iclgi, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0081 */ { UD_Icli, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0082 */ { UD_Iclts, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0083 */ { UD_Icmc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0084 */ { UD_Icmovo, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0085 */ { UD_Icmovno, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0086 */ { UD_Icmovb, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0087 */ { UD_Icmovae, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0088 */ { UD_Icmovz, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0089 */ { UD_Icmovnz, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0090 */ { UD_Icmovbe, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0091 */ { UD_Icmova, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0092 */ { UD_Icmovs, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0093 */ { UD_Icmovns, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0094 */ { UD_Icmovp, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0095 */ { UD_Icmovnp, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0096 */ { UD_Icmovl, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0097 */ { UD_Icmovge, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0098 */ { UD_Icmovle, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0099 */ { UD_Icmovg, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0100 */ { UD_Icmp, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0101 */ { UD_Icmp, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0102 */ { UD_Icmp, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0103 */ { UD_Icmp, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0104 */ { UD_Icmp, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0105 */ { UD_Icmp, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0106 */ { UD_Icmp, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0107 */ { UD_Icmp, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 0108 */ { UD_Icmp, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0109 */ { UD_Icmp, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0110 */ { UD_Icmppd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0111 */ { UD_Ivcmppd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0112 */ { UD_Icmpps, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0113 */ { UD_Ivcmpps, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0114 */ { UD_Icmpsb, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_seg },
  /* 0115 */ { UD_Icmpsw, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw|P_seg },
  /* 0116 */ { UD_Icmpsd, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw|P_seg },
  /* 0117 */ { UD_Icmpsd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0118 */ { UD_Ivcmpsd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0119 */ { UD_Icmpsq, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw|P_seg },
  /* 0120 */ { UD_Icmpss, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0121 */ { UD_Ivcmpss, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0122 */ { UD_Icmpxchg, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0123 */ { UD_Icmpxchg, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0124 */ { UD_Icmpxchg8b, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0125 */ { UD_Icmpxchg8b, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0126 */ { UD_Icmpxchg16b, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0127 */ { UD_Icomisd, O_Vsd, O_Wsd, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0128 */ { UD_Ivcomisd, O_Vsd, O_Wsd, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0129 */ { UD_Icomiss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0130 */ { UD_Ivcomiss, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0131 */ { UD_Icpuid, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0132 */ { UD_Icvtdq2pd, O_V, O_Wdq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0133 */ { UD_Ivcvtdq2pd, O_Vx, O_Wdq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0134 */ { UD_Icvtdq2ps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0135 */ { UD_Ivcvtdq2ps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0136 */ { UD_Icvtpd2dq, O_Vdq, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0137 */ { UD_Ivcvtpd2dq, O_Vdq, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0138 */ { UD_Icvtpd2pi, O_P, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0139 */ { UD_Icvtpd2ps, O_Vdq, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0140 */ { UD_Ivcvtpd2ps, O_Vdq, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0141 */ { UD_Icvtpi2ps, O_V, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0142 */ { UD_Icvtpi2pd, O_V, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0143 */ { UD_Icvtps2dq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0144 */ { UD_Ivcvtps2dq, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0145 */ { UD_Icvtps2pd, O_V, O_Wdq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0146 */ { UD_Ivcvtps2pd, O_Vx, O_Wdq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0147 */ { UD_Icvtps2pi, O_P, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0148 */ { UD_Icvtsd2si, O_Gy, O_MqU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0149 */ { UD_Ivcvtsd2si, O_Gy, O_MqU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0150 */ { UD_Icvtsd2ss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0151 */ { UD_Ivcvtsd2ss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0152 */ { UD_Icvtsi2sd, O_V, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0153 */ { UD_Ivcvtsi2sd, O_Vx, O_Hx, O_Ey, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0154 */ { UD_Icvtsi2ss, O_V, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0155 */ { UD_Ivcvtsi2ss, O_Vx, O_Hx, O_Ey, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0156 */ { UD_Icvtss2sd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0157 */ { UD_Ivcvtss2sd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0158 */ { UD_Icvtss2si, O_Gy, O_MdU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0159 */ { UD_Ivcvtss2si, O_Gy, O_MdU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0160 */ { UD_Icvttpd2dq, O_Vdq, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0161 */ { UD_Ivcvttpd2dq, O_Vdq, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0162 */ { UD_Icvttpd2pi, O_P, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0163 */ { UD_Icvttps2dq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0164 */ { UD_Ivcvttps2dq, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0165 */ { UD_Icvttps2pi, O_P, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0166 */ { UD_Icvttsd2si, O_Gy, O_MqU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0167 */ { UD_Ivcvttsd2si, O_Gy, O_MqU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0168 */ { UD_Icvttss2si, O_Gy, O_MdU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0169 */ { UD_Ivcvttss2si, O_Gy, O_MdU, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0170 */ { UD_Icwd, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0171 */ { UD_Icdq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0172 */ { UD_Icqo, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0173 */ { UD_Idaa, O_NONE, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 0174 */ { UD_Idas, O_NONE, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 0175 */ { UD_Idec, O_R0z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0176 */ { UD_Idec, O_R1z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0177 */ { UD_Idec, O_R2z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0178 */ { UD_Idec, O_R3z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0179 */ { UD_Idec, O_R4z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0180 */ { UD_Idec, O_R5z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0181 */ { UD_Idec, O_R6z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0182 */ { UD_Idec, O_R7z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0183 */ { UD_Idec, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0184 */ { UD_Idec, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0185 */ { UD_Idiv, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0186 */ { UD_Idiv, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0187 */ { UD_Idivpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0188 */ { UD_Ivdivpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0189 */ { UD_Idivps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0190 */ { UD_Ivdivps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0191 */ { UD_Idivsd, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0192 */ { UD_Ivdivsd, O_Vx, O_Hx, O_MqU, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0193 */ { UD_Idivss, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0194 */ { UD_Ivdivss, O_Vx, O_Hx, O_MdU, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0195 */ { UD_Idppd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0196 */ { UD_Ivdppd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0197 */ { UD_Idpps, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0198 */ { UD_Ivdpps, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0199 */ { UD_Iemms, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0200 */ { UD_Ienter, O_Iw, O_Ib, O_NONE, O_NONE, P_def64 },
  /* 0201 */ { UD_Iextractps, O_MdRy, O_V, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 0202 */ { UD_Ivextractps, O_MdRy, O_Vx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 0203 */ { UD_If2xm1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0204 */ { UD_Ifabs, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0205 */ { UD_Ifadd, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0206 */ { UD_Ifadd, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0207 */ { UD_Ifadd, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0208 */ { UD_Ifadd, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0209 */ { UD_Ifadd, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0210 */ { UD_Ifadd, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0211 */ { UD_Ifadd, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0212 */ { UD_Ifadd, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0213 */ { UD_Ifadd, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0214 */ { UD_Ifadd, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0215 */ { UD_Ifadd, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0216 */ { UD_Ifadd, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0217 */ { UD_Ifadd, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0218 */ { UD_Ifadd, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0219 */ { UD_Ifadd, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0220 */ { UD_Ifadd, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0221 */ { UD_Ifadd, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0222 */ { UD_Ifadd, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0223 */ { UD_Ifaddp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0224 */ { UD_Ifaddp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0225 */ { UD_Ifaddp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0226 */ { UD_Ifaddp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0227 */ { UD_Ifaddp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0228 */ { UD_Ifaddp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0229 */ { UD_Ifaddp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0230 */ { UD_Ifaddp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0231 */ { UD_Ifbld, O_Mt, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0232 */ { UD_Ifbstp, O_Mt, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0233 */ { UD_Ifchs, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0234 */ { UD_Ifclex, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0235 */ { UD_Ifcmovb, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0236 */ { UD_Ifcmovb, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0237 */ { UD_Ifcmovb, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0238 */ { UD_Ifcmovb, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0239 */ { UD_Ifcmovb, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0240 */ { UD_Ifcmovb, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0241 */ { UD_Ifcmovb, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0242 */ { UD_Ifcmovb, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0243 */ { UD_Ifcmove, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0244 */ { UD_Ifcmove, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0245 */ { UD_Ifcmove, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0246 */ { UD_Ifcmove, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0247 */ { UD_Ifcmove, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0248 */ { UD_Ifcmove, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0249 */ { UD_Ifcmove, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0250 */ { UD_Ifcmove, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0251 */ { UD_Ifcmovbe, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0252 */ { UD_Ifcmovbe, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0253 */ { UD_Ifcmovbe, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0254 */ { UD_Ifcmovbe, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0255 */ { UD_Ifcmovbe, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0256 */ { UD_Ifcmovbe, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0257 */ { UD_Ifcmovbe, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0258 */ { UD_Ifcmovbe, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0259 */ { UD_Ifcmovu, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0260 */ { UD_Ifcmovu, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0261 */ { UD_Ifcmovu, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0262 */ { UD_Ifcmovu, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0263 */ { UD_Ifcmovu, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0264 */ { UD_Ifcmovu, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0265 */ { UD_Ifcmovu, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0266 */ { UD_Ifcmovu, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0267 */ { UD_Ifcmovnb, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0268 */ { UD_Ifcmovnb, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0269 */ { UD_Ifcmovnb, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0270 */ { UD_Ifcmovnb, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0271 */ { UD_Ifcmovnb, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0272 */ { UD_Ifcmovnb, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0273 */ { UD_Ifcmovnb, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0274 */ { UD_Ifcmovnb, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0275 */ { UD_Ifcmovne, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0276 */ { UD_Ifcmovne, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0277 */ { UD_Ifcmovne, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0278 */ { UD_Ifcmovne, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0279 */ { UD_Ifcmovne, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0280 */ { UD_Ifcmovne, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0281 */ { UD_Ifcmovne, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0282 */ { UD_Ifcmovne, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0283 */ { UD_Ifcmovnbe, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0284 */ { UD_Ifcmovnbe, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0285 */ { UD_Ifcmovnbe, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0286 */ { UD_Ifcmovnbe, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0287 */ { UD_Ifcmovnbe, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0288 */ { UD_Ifcmovnbe, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0289 */ { UD_Ifcmovnbe, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0290 */ { UD_Ifcmovnbe, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0291 */ { UD_Ifcmovnu, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0292 */ { UD_Ifcmovnu, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0293 */ { UD_Ifcmovnu, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0294 */ { UD_Ifcmovnu, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0295 */ { UD_Ifcmovnu, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0296 */ { UD_Ifcmovnu, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0297 */ { UD_Ifcmovnu, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0298 */ { UD_Ifcmovnu, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0299 */ { UD_Ifucomi, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0300 */ { UD_Ifucomi, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0301 */ { UD_Ifucomi, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0302 */ { UD_Ifucomi, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0303 */ { UD_Ifucomi, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0304 */ { UD_Ifucomi, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0305 */ { UD_Ifucomi, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0306 */ { UD_Ifucomi, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0307 */ { UD_Ifcom, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0308 */ { UD_Ifcom, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0309 */ { UD_Ifcom, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0310 */ { UD_Ifcom, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0311 */ { UD_Ifcom, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0312 */ { UD_Ifcom, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0313 */ { UD_Ifcom, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0314 */ { UD_Ifcom, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0315 */ { UD_Ifcom, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0316 */ { UD_Ifcom, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0317 */ { UD_Ifcom2, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0318 */ { UD_Ifcom2, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0319 */ { UD_Ifcom2, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0320 */ { UD_Ifcom2, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0321 */ { UD_Ifcom2, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0322 */ { UD_Ifcom2, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0323 */ { UD_Ifcom2, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0324 */ { UD_Ifcom2, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0325 */ { UD_Ifcomp3, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0326 */ { UD_Ifcomp3, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0327 */ { UD_Ifcomp3, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0328 */ { UD_Ifcomp3, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0329 */ { UD_Ifcomp3, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0330 */ { UD_Ifcomp3, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0331 */ { UD_Ifcomp3, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0332 */ { UD_Ifcomp3, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0333 */ { UD_Ifcomi, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0334 */ { UD_Ifcomi, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0335 */ { UD_Ifcomi, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0336 */ { UD_Ifcomi, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0337 */ { UD_Ifcomi, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0338 */ { UD_Ifcomi, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0339 */ { UD_Ifcomi, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0340 */ { UD_Ifcomi, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0341 */ { UD_Ifucomip, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0342 */ { UD_Ifucomip, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0343 */ { UD_Ifucomip, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0344 */ { UD_Ifucomip, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0345 */ { UD_Ifucomip, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0346 */ { UD_Ifucomip, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0347 */ { UD_Ifucomip, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0348 */ { UD_Ifucomip, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0349 */ { UD_Ifcomip, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0350 */ { UD_Ifcomip, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0351 */ { UD_Ifcomip, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0352 */ { UD_Ifcomip, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0353 */ { UD_Ifcomip, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0354 */ { UD_Ifcomip, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0355 */ { UD_Ifcomip, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0356 */ { UD_Ifcomip, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0357 */ { UD_Ifcomp, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0358 */ { UD_Ifcomp, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0359 */ { UD_Ifcomp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0360 */ { UD_Ifcomp, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0361 */ { UD_Ifcomp, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0362 */ { UD_Ifcomp, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0363 */ { UD_Ifcomp, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0364 */ { UD_Ifcomp, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0365 */ { UD_Ifcomp, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0366 */ { UD_Ifcomp, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0367 */ { UD_Ifcomp5, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0368 */ { UD_Ifcomp5, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0369 */ { UD_Ifcomp5, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0370 */ { UD_Ifcomp5, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0371 */ { UD_Ifcomp5, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0372 */ { UD_Ifcomp5, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0373 */ { UD_Ifcomp5, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0374 */ { UD_Ifcomp5, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0375 */ { UD_Ifcompp, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0376 */ { UD_Ifcos, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0377 */ { UD_Ifdecstp, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0378 */ { UD_Ifdiv, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0379 */ { UD_Ifdiv, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0380 */ { UD_Ifdiv, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0381 */ { UD_Ifdiv, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0382 */ { UD_Ifdiv, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0383 */ { UD_Ifdiv, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0384 */ { UD_Ifdiv, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0385 */ { UD_Ifdiv, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0386 */ { UD_Ifdiv, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0387 */ { UD_Ifdiv, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0388 */ { UD_Ifdiv, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0389 */ { UD_Ifdiv, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0390 */ { UD_Ifdiv, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0391 */ { UD_Ifdiv, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0392 */ { UD_Ifdiv, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0393 */ { UD_Ifdiv, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0394 */ { UD_Ifdiv, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0395 */ { UD_Ifdiv, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0396 */ { UD_Ifdivp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0397 */ { UD_Ifdivp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0398 */ { UD_Ifdivp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0399 */ { UD_Ifdivp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0400 */ { UD_Ifdivp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0401 */ { UD_Ifdivp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0402 */ { UD_Ifdivp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0403 */ { UD_Ifdivp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0404 */ { UD_Ifdivr, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0405 */ { UD_Ifdivr, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0406 */ { UD_Ifdivr, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0407 */ { UD_Ifdivr, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0408 */ { UD_Ifdivr, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0409 */ { UD_Ifdivr, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0410 */ { UD_Ifdivr, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0411 */ { UD_Ifdivr, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0412 */ { UD_Ifdivr, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0413 */ { UD_Ifdivr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0414 */ { UD_Ifdivr, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0415 */ { UD_Ifdivr, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0416 */ { UD_Ifdivr, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0417 */ { UD_Ifdivr, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0418 */ { UD_Ifdivr, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0419 */ { UD_Ifdivr, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0420 */ { UD_Ifdivr, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0421 */ { UD_Ifdivr, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0422 */ { UD_Ifdivrp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0423 */ { UD_Ifdivrp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0424 */ { UD_Ifdivrp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0425 */ { UD_Ifdivrp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0426 */ { UD_Ifdivrp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0427 */ { UD_Ifdivrp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0428 */ { UD_Ifdivrp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0429 */ { UD_Ifdivrp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0430 */ { UD_Ifemms, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0431 */ { UD_Iffree, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0432 */ { UD_Iffree, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0433 */ { UD_Iffree, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0434 */ { UD_Iffree, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0435 */ { UD_Iffree, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0436 */ { UD_Iffree, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0437 */ { UD_Iffree, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0438 */ { UD_Iffree, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0439 */ { UD_Iffreep, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0440 */ { UD_Iffreep, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0441 */ { UD_Iffreep, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0442 */ { UD_Iffreep, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0443 */ { UD_Iffreep, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0444 */ { UD_Iffreep, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0445 */ { UD_Iffreep, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0446 */ { UD_Iffreep, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0447 */ { UD_Ificom, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0448 */ { UD_Ificom, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0449 */ { UD_Ificomp, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0450 */ { UD_Ificomp, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0451 */ { UD_Ifild, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0452 */ { UD_Ifild, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0453 */ { UD_Ifild, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0454 */ { UD_Ifincstp, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0455 */ { UD_Ifninit, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0456 */ { UD_Ifiadd, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0457 */ { UD_Ifiadd, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0458 */ { UD_Ifidivr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0459 */ { UD_Ifidivr, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0460 */ { UD_Ifidiv, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0461 */ { UD_Ifidiv, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0462 */ { UD_Ifisub, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0463 */ { UD_Ifisub, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0464 */ { UD_Ifisubr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0465 */ { UD_Ifisubr, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0466 */ { UD_Ifist, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0467 */ { UD_Ifist, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0468 */ { UD_Ifistp, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0469 */ { UD_Ifistp, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0470 */ { UD_Ifistp, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0471 */ { UD_Ifisttp, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0472 */ { UD_Ifisttp, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0473 */ { UD_Ifisttp, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0474 */ { UD_Ifld, O_Mt, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0475 */ { UD_Ifld, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0476 */ { UD_Ifld, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0477 */ { UD_Ifld, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0478 */ { UD_Ifld, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0479 */ { UD_Ifld, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0480 */ { UD_Ifld, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0481 */ { UD_Ifld, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0482 */ { UD_Ifld, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0483 */ { UD_Ifld, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0484 */ { UD_Ifld, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0485 */ { UD_Ifld1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0486 */ { UD_Ifldl2t, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0487 */ { UD_Ifldl2e, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0488 */ { UD_Ifldpi, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0489 */ { UD_Ifldlg2, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0490 */ { UD_Ifldln2, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0491 */ { UD_Ifldz, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0492 */ { UD_Ifldcw, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0493 */ { UD_Ifldenv, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0494 */ { UD_Ifmul, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0495 */ { UD_Ifmul, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0496 */ { UD_Ifmul, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0497 */ { UD_Ifmul, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0498 */ { UD_Ifmul, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0499 */ { UD_Ifmul, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0500 */ { UD_Ifmul, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0501 */ { UD_Ifmul, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0502 */ { UD_Ifmul, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0503 */ { UD_Ifmul, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0504 */ { UD_Ifmul, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0505 */ { UD_Ifmul, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0506 */ { UD_Ifmul, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0507 */ { UD_Ifmul, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0508 */ { UD_Ifmul, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0509 */ { UD_Ifmul, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0510 */ { UD_Ifmul, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0511 */ { UD_Ifmul, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0512 */ { UD_Ifmulp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0513 */ { UD_Ifmulp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0514 */ { UD_Ifmulp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0515 */ { UD_Ifmulp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0516 */ { UD_Ifmulp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0517 */ { UD_Ifmulp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0518 */ { UD_Ifmulp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0519 */ { UD_Ifmulp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0520 */ { UD_Ifimul, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0521 */ { UD_Ifimul, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0522 */ { UD_Ifnop, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0523 */ { UD_Ifndisi, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0524 */ { UD_Ifneni, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0525 */ { UD_Ifnsetpm, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0526 */ { UD_Ifpatan, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0527 */ { UD_Ifprem, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0528 */ { UD_Ifprem1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0529 */ { UD_Ifptan, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0530 */ { UD_Ifrndint, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0531 */ { UD_Ifrstor, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0532 */ { UD_Ifrstpm, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0533 */ { UD_Ifnsave, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0534 */ { UD_Ifscale, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0535 */ { UD_Ifsin, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0536 */ { UD_Ifsincos, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0537 */ { UD_Ifsqrt, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0538 */ { UD_Ifstp, O_Mt, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0539 */ { UD_Ifstp, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0540 */ { UD_Ifstp, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0541 */ { UD_Ifstp, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0542 */ { UD_Ifstp, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0543 */ { UD_Ifstp, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0544 */ { UD_Ifstp, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0545 */ { UD_Ifstp, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0546 */ { UD_Ifstp, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0547 */ { UD_Ifstp, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0548 */ { UD_Ifstp, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0549 */ { UD_Ifstp1, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0550 */ { UD_Ifstp1, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0551 */ { UD_Ifstp1, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0552 */ { UD_Ifstp1, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0553 */ { UD_Ifstp1, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0554 */ { UD_Ifstp1, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0555 */ { UD_Ifstp1, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0556 */ { UD_Ifstp1, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0557 */ { UD_Ifstp8, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0558 */ { UD_Ifstp8, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0559 */ { UD_Ifstp8, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0560 */ { UD_Ifstp8, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0561 */ { UD_Ifstp8, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0562 */ { UD_Ifstp8, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0563 */ { UD_Ifstp8, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0564 */ { UD_Ifstp8, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0565 */ { UD_Ifstp9, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0566 */ { UD_Ifstp9, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0567 */ { UD_Ifstp9, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0568 */ { UD_Ifstp9, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0569 */ { UD_Ifstp9, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0570 */ { UD_Ifstp9, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0571 */ { UD_Ifstp9, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0572 */ { UD_Ifstp9, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0573 */ { UD_Ifst, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0574 */ { UD_Ifst, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0575 */ { UD_Ifst, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0576 */ { UD_Ifst, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0577 */ { UD_Ifst, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0578 */ { UD_Ifst, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0579 */ { UD_Ifst, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0580 */ { UD_Ifst, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0581 */ { UD_Ifst, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0582 */ { UD_Ifst, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0583 */ { UD_Ifnstcw, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0584 */ { UD_Ifnstenv, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0585 */ { UD_Ifnstsw, O_Mw, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0586 */ { UD_Ifnstsw, O_AX, O_NONE, O_NONE, O_NONE, P_none },
  /* 0587 */ { UD_Ifsub, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0588 */ { UD_Ifsub, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0589 */ { UD_Ifsub, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0590 */ { UD_Ifsub, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0591 */ { UD_Ifsub, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0592 */ { UD_Ifsub, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0593 */ { UD_Ifsub, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0594 */ { UD_Ifsub, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0595 */ { UD_Ifsub, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0596 */ { UD_Ifsub, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0597 */ { UD_Ifsub, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0598 */ { UD_Ifsub, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0599 */ { UD_Ifsub, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0600 */ { UD_Ifsub, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0601 */ { UD_Ifsub, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0602 */ { UD_Ifsub, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0603 */ { UD_Ifsub, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0604 */ { UD_Ifsub, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0605 */ { UD_Ifsubp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0606 */ { UD_Ifsubp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0607 */ { UD_Ifsubp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0608 */ { UD_Ifsubp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0609 */ { UD_Ifsubp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0610 */ { UD_Ifsubp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0611 */ { UD_Ifsubp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0612 */ { UD_Ifsubp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0613 */ { UD_Ifsubr, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0614 */ { UD_Ifsubr, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0615 */ { UD_Ifsubr, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0616 */ { UD_Ifsubr, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0617 */ { UD_Ifsubr, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0618 */ { UD_Ifsubr, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0619 */ { UD_Ifsubr, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0620 */ { UD_Ifsubr, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0621 */ { UD_Ifsubr, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0622 */ { UD_Ifsubr, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0623 */ { UD_Ifsubr, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0624 */ { UD_Ifsubr, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0625 */ { UD_Ifsubr, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0626 */ { UD_Ifsubr, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0627 */ { UD_Ifsubr, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0628 */ { UD_Ifsubr, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0629 */ { UD_Ifsubr, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0630 */ { UD_Ifsubr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0631 */ { UD_Ifsubrp, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0632 */ { UD_Ifsubrp, O_ST1, O_ST0, O_NONE, O_NONE, P_none },
  /* 0633 */ { UD_Ifsubrp, O_ST2, O_ST0, O_NONE, O_NONE, P_none },
  /* 0634 */ { UD_Ifsubrp, O_ST3, O_ST0, O_NONE, O_NONE, P_none },
  /* 0635 */ { UD_Ifsubrp, O_ST4, O_ST0, O_NONE, O_NONE, P_none },
  /* 0636 */ { UD_Ifsubrp, O_ST5, O_ST0, O_NONE, O_NONE, P_none },
  /* 0637 */ { UD_Ifsubrp, O_ST6, O_ST0, O_NONE, O_NONE, P_none },
  /* 0638 */ { UD_Ifsubrp, O_ST7, O_ST0, O_NONE, O_NONE, P_none },
  /* 0639 */ { UD_Iftst, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0640 */ { UD_Ifucom, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0641 */ { UD_Ifucom, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0642 */ { UD_Ifucom, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0643 */ { UD_Ifucom, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0644 */ { UD_Ifucom, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0645 */ { UD_Ifucom, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0646 */ { UD_Ifucom, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0647 */ { UD_Ifucom, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0648 */ { UD_Ifucomp, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0649 */ { UD_Ifucomp, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0650 */ { UD_Ifucomp, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0651 */ { UD_Ifucomp, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0652 */ { UD_Ifucomp, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0653 */ { UD_Ifucomp, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0654 */ { UD_Ifucomp, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0655 */ { UD_Ifucomp, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0656 */ { UD_Ifucompp, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0657 */ { UD_Ifxam, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0658 */ { UD_Ifxch, O_ST0, O_ST0, O_NONE, O_NONE, P_none },
  /* 0659 */ { UD_Ifxch, O_ST0, O_ST1, O_NONE, O_NONE, P_none },
  /* 0660 */ { UD_Ifxch, O_ST0, O_ST2, O_NONE, O_NONE, P_none },
  /* 0661 */ { UD_Ifxch, O_ST0, O_ST3, O_NONE, O_NONE, P_none },
  /* 0662 */ { UD_Ifxch, O_ST0, O_ST4, O_NONE, O_NONE, P_none },
  /* 0663 */ { UD_Ifxch, O_ST0, O_ST5, O_NONE, O_NONE, P_none },
  /* 0664 */ { UD_Ifxch, O_ST0, O_ST6, O_NONE, O_NONE, P_none },
  /* 0665 */ { UD_Ifxch, O_ST0, O_ST7, O_NONE, O_NONE, P_none },
  /* 0666 */ { UD_Ifxch4, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0667 */ { UD_Ifxch4, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0668 */ { UD_Ifxch4, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0669 */ { UD_Ifxch4, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0670 */ { UD_Ifxch4, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0671 */ { UD_Ifxch4, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0672 */ { UD_Ifxch4, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0673 */ { UD_Ifxch4, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0674 */ { UD_Ifxch7, O_ST0, O_NONE, O_NONE, O_NONE, P_none },
  /* 0675 */ { UD_Ifxch7, O_ST1, O_NONE, O_NONE, O_NONE, P_none },
  /* 0676 */ { UD_Ifxch7, O_ST2, O_NONE, O_NONE, O_NONE, P_none },
  /* 0677 */ { UD_Ifxch7, O_ST3, O_NONE, O_NONE, O_NONE, P_none },
  /* 0678 */ { UD_Ifxch7, O_ST4, O_NONE, O_NONE, O_NONE, P_none },
  /* 0679 */ { UD_Ifxch7, O_ST5, O_NONE, O_NONE, O_NONE, P_none },
  /* 0680 */ { UD_Ifxch7, O_ST6, O_NONE, O_NONE, O_NONE, P_none },
  /* 0681 */ { UD_Ifxch7, O_ST7, O_NONE, O_NONE, O_NONE, P_none },
  /* 0682 */ { UD_Ifxrstor, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0683 */ { UD_Ifxsave, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0684 */ { UD_Ifxtract, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0685 */ { UD_Ifyl2x, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0686 */ { UD_Ifyl2xp1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0687 */ { UD_Ihlt, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0688 */ { UD_Iidiv, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0689 */ { UD_Iidiv, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0690 */ { UD_Iin, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0691 */ { UD_Iin, O_eAX, O_Ib, O_NONE, O_NONE, P_oso },
  /* 0692 */ { UD_Iin, O_AL, O_DX, O_NONE, O_NONE, P_none },
  /* 0693 */ { UD_Iin, O_eAX, O_DX, O_NONE, O_NONE, P_oso },
  /* 0694 */ { UD_Iimul, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0695 */ { UD_Iimul, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0696 */ { UD_Iimul, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0697 */ { UD_Iimul, O_Gv, O_Ev, O_Iz, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0698 */ { UD_Iimul, O_Gv, O_Ev, O_sIb, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0699 */ { UD_Iinc, O_R0z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0700 */ { UD_Iinc, O_R1z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0701 */ { UD_Iinc, O_R2z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0702 */ { UD_Iinc, O_R3z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0703 */ { UD_Iinc, O_R4z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0704 */ { UD_Iinc, O_R5z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0705 */ { UD_Iinc, O_R6z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0706 */ { UD_Iinc, O_R7z, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0707 */ { UD_Iinc, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0708 */ { UD_Iinc, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0709 */ { UD_Iinsb, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg },
  /* 0710 */ { UD_Iinsw, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_oso|P_seg },
  /* 0711 */ { UD_Iinsd, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_oso|P_seg },
  /* 0712 */ { UD_Iint1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0713 */ { UD_Iint3, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0714 */ { UD_Iint, O_Ib, O_NONE, O_NONE, O_NONE, P_none },
  /* 0715 */ { UD_Iinto, O_NONE, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 0716 */ { UD_Iinvd, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0717 */ { UD_Iinvept, O_Gd, O_Mo, O_NONE, O_NONE, P_none },
  /* 0718 */ { UD_Iinvept, O_Gq, O_Mo, O_NONE, O_NONE, P_none },
  /* 0719 */ { UD_Iinvlpg, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0720 */ { UD_Iinvlpga, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0721 */ { UD_Iinvvpid, O_Gd, O_Mo, O_NONE, O_NONE, P_none },
  /* 0722 */ { UD_Iinvvpid, O_Gq, O_Mo, O_NONE, O_NONE, P_none },
  /* 0723 */ { UD_Iiretw, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0724 */ { UD_Iiretd, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0725 */ { UD_Iiretq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0726 */ { UD_Ijo, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0727 */ { UD_Ijo, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0728 */ { UD_Ijno, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0729 */ { UD_Ijno, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0730 */ { UD_Ijb, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0731 */ { UD_Ijb, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0732 */ { UD_Ijae, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0733 */ { UD_Ijae, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0734 */ { UD_Ijz, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0735 */ { UD_Ijz, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0736 */ { UD_Ijnz, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0737 */ { UD_Ijnz, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0738 */ { UD_Ijbe, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0739 */ { UD_Ijbe, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0740 */ { UD_Ija, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0741 */ { UD_Ija, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0742 */ { UD_Ijs, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0743 */ { UD_Ijs, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0744 */ { UD_Ijns, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0745 */ { UD_Ijns, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0746 */ { UD_Ijp, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0747 */ { UD_Ijp, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0748 */ { UD_Ijnp, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0749 */ { UD_Ijnp, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0750 */ { UD_Ijl, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0751 */ { UD_Ijl, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0752 */ { UD_Ijge, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0753 */ { UD_Ijge, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0754 */ { UD_Ijle, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0755 */ { UD_Ijle, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0756 */ { UD_Ijg, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0757 */ { UD_Ijg, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0758 */ { UD_Ijcxz, O_Jb, O_NONE, O_NONE, O_NONE, P_aso },
  /* 0759 */ { UD_Ijecxz, O_Jb, O_NONE, O_NONE, O_NONE, P_aso },
  /* 0760 */ { UD_Ijrcxz, O_Jb, O_NONE, O_NONE, O_NONE, P_aso },
  /* 0761 */ { UD_Ijmp, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 0762 */ { UD_Ijmp, O_Fv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0763 */ { UD_Ijmp, O_Jz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 0764 */ { UD_Ijmp, O_Av, O_NONE, O_NONE, O_NONE, P_oso },
  /* 0765 */ { UD_Ijmp, O_Jb, O_NONE, O_NONE, O_NONE, P_def64 },
  /* 0766 */ { UD_Ilahf, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0767 */ { UD_Ilar, O_Gv, O_Ew, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0768 */ { UD_Ildmxcsr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0769 */ { UD_Ilds, O_Gv, O_M, O_NONE, O_NONE, P_aso|P_oso },
  /* 0770 */ { UD_Ilea, O_Gv, O_M, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0771 */ { UD_Iles, O_Gv, O_M, O_NONE, O_NONE, P_aso|P_oso },
  /* 0772 */ { UD_Ilfs, O_Gz, O_M, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0773 */ { UD_Ilgs, O_Gz, O_M, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0774 */ { UD_Ilidt, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0775 */ { UD_Ilss, O_Gv, O_M, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0776 */ { UD_Ileave, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0777 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0778 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0779 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0780 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0781 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0782 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0783 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0784 */ { UD_Ilfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0785 */ { UD_Ilgdt, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0786 */ { UD_Illdt, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0787 */ { UD_Ilmsw, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0788 */ { UD_Ilmsw, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0789 */ { UD_Ilock, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0790 */ { UD_Ilodsb, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg },
  /* 0791 */ { UD_Ilodsw, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0792 */ { UD_Ilodsd, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0793 */ { UD_Ilodsq, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0794 */ { UD_Iloopne, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0795 */ { UD_Iloope, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0796 */ { UD_Iloop, O_Jb, O_NONE, O_NONE, O_NONE, P_none },
  /* 0797 */ { UD_Ilsl, O_Gv, O_Ew, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0798 */ { UD_Iltr, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0799 */ { UD_Imaskmovq, O_P, O_N, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0800 */ { UD_Imaxpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0801 */ { UD_Ivmaxpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0802 */ { UD_Imaxps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0803 */ { UD_Ivmaxps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0804 */ { UD_Imaxsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0805 */ { UD_Ivmaxsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0806 */ { UD_Imaxss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0807 */ { UD_Ivmaxss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0808 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0809 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0810 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0811 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0812 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0813 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0814 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0815 */ { UD_Imfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0816 */ { UD_Iminpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0817 */ { UD_Ivminpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0818 */ { UD_Iminps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0819 */ { UD_Ivminps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0820 */ { UD_Iminsd, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0821 */ { UD_Ivminsd, O_Vx, O_Hx, O_MqU, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0822 */ { UD_Iminss, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0823 */ { UD_Ivminss, O_Vx, O_Hx, O_MdU, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0824 */ { UD_Imonitor, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0825 */ { UD_Imontmul, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0826 */ { UD_Imov, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0827 */ { UD_Imov, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0828 */ { UD_Imov, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0829 */ { UD_Imov, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0830 */ { UD_Imov, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0831 */ { UD_Imov, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0832 */ { UD_Imov, O_MwRv, O_S, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0833 */ { UD_Imov, O_S, O_MwRv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0834 */ { UD_Imov, O_AL, O_Ob, O_NONE, O_NONE, P_none },
  /* 0835 */ { UD_Imov, O_rAX, O_Ov, O_NONE, O_NONE, P_aso|P_oso|P_rexw },
  /* 0836 */ { UD_Imov, O_Ob, O_AL, O_NONE, O_NONE, P_none },
  /* 0837 */ { UD_Imov, O_Ov, O_rAX, O_NONE, O_NONE, P_aso|P_oso|P_rexw },
  /* 0838 */ { UD_Imov, O_R0b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0839 */ { UD_Imov, O_R1b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0840 */ { UD_Imov, O_R2b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0841 */ { UD_Imov, O_R3b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0842 */ { UD_Imov, O_R4b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0843 */ { UD_Imov, O_R5b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0844 */ { UD_Imov, O_R6b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0845 */ { UD_Imov, O_R7b, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 0846 */ { UD_Imov, O_R0v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0847 */ { UD_Imov, O_R1v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0848 */ { UD_Imov, O_R2v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0849 */ { UD_Imov, O_R3v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0850 */ { UD_Imov, O_R4v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0851 */ { UD_Imov, O_R5v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0852 */ { UD_Imov, O_R6v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0853 */ { UD_Imov, O_R7v, O_Iv, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 0854 */ { UD_Imov, O_R, O_C, O_NONE, O_NONE, P_rexr|P_rexw|P_rexb },
  /* 0855 */ { UD_Imov, O_R, O_D, O_NONE, O_NONE, P_rexr|P_rexw|P_rexb },
  /* 0856 */ { UD_Imov, O_C, O_R, O_NONE, O_NONE, P_rexr|P_rexw|P_rexb },
  /* 0857 */ { UD_Imov, O_D, O_R, O_NONE, O_NONE, P_rexr|P_rexw|P_rexb },
  /* 0858 */ { UD_Imovapd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0859 */ { UD_Ivmovapd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0860 */ { UD_Imovapd, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0861 */ { UD_Ivmovapd, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0862 */ { UD_Imovaps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0863 */ { UD_Ivmovaps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0864 */ { UD_Imovaps, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0865 */ { UD_Ivmovaps, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0866 */ { UD_Imovd, O_P, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0867 */ { UD_Imovd, O_P, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0868 */ { UD_Imovd, O_V, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0869 */ { UD_Ivmovd, O_Vx, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0870 */ { UD_Imovd, O_V, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0871 */ { UD_Ivmovd, O_Vx, O_Ey, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0872 */ { UD_Imovd, O_Ey, O_P, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0873 */ { UD_Imovd, O_Ey, O_P, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0874 */ { UD_Imovd, O_Ey, O_V, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0875 */ { UD_Ivmovd, O_Ey, O_Vx, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0876 */ { UD_Imovd, O_Ey, O_V, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0877 */ { UD_Ivmovd, O_Ey, O_Vx, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0878 */ { UD_Imovhpd, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0879 */ { UD_Ivmovhpd, O_Vx, O_Hx, O_M, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0880 */ { UD_Imovhpd, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0881 */ { UD_Ivmovhpd, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0882 */ { UD_Imovhps, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0883 */ { UD_Ivmovhps, O_Vx, O_Hx, O_M, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0884 */ { UD_Imovhps, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0885 */ { UD_Ivmovhps, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0886 */ { UD_Imovlhps, O_V, O_U, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0887 */ { UD_Ivmovlhps, O_Vx, O_Hx, O_Ux, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0888 */ { UD_Imovlpd, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0889 */ { UD_Ivmovlpd, O_Vx, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0890 */ { UD_Imovlpd, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0891 */ { UD_Ivmovlpd, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0892 */ { UD_Imovlps, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0893 */ { UD_Ivmovlps, O_Vx, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0894 */ { UD_Imovlps, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0895 */ { UD_Ivmovlps, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0896 */ { UD_Imovhlps, O_V, O_U, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0897 */ { UD_Ivmovhlps, O_Vx, O_Ux, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0898 */ { UD_Imovmskpd, O_Gd, O_U, O_NONE, O_NONE, P_oso|P_rexr|P_rexb },
  /* 0899 */ { UD_Ivmovmskpd, O_Gd, O_Ux, O_NONE, O_NONE, P_oso|P_rexr|P_rexb|P_vexl },
  /* 0900 */ { UD_Imovmskps, O_Gd, O_U, O_NONE, O_NONE, P_oso|P_rexr|P_rexb },
  /* 0901 */ { UD_Ivmovmskps, O_Gd, O_Ux, O_NONE, O_NONE, P_oso|P_rexr|P_rexb },
  /* 0902 */ { UD_Imovntdq, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0903 */ { UD_Ivmovntdq, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0904 */ { UD_Imovnti, O_M, O_Gy, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0905 */ { UD_Imovntpd, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0906 */ { UD_Ivmovntpd, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0907 */ { UD_Imovntps, O_M, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0908 */ { UD_Ivmovntps, O_M, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0909 */ { UD_Imovntq, O_M, O_P, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0910 */ { UD_Imovq, O_P, O_Eq, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0911 */ { UD_Imovq, O_V, O_Eq, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0912 */ { UD_Ivmovq, O_Vx, O_Eq, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0913 */ { UD_Imovq, O_Eq, O_P, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0914 */ { UD_Imovq, O_Eq, O_V, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0915 */ { UD_Ivmovq, O_Eq, O_Vx, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0916 */ { UD_Imovq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0917 */ { UD_Ivmovq, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0918 */ { UD_Imovq, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0919 */ { UD_Ivmovq, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0920 */ { UD_Imovq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0921 */ { UD_Imovq, O_Q, O_P, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0922 */ { UD_Imovsb, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg },
  /* 0923 */ { UD_Imovsw, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0924 */ { UD_Imovsd, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0925 */ { UD_Imovsd, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0926 */ { UD_Imovsd, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0927 */ { UD_Imovsq, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 0928 */ { UD_Imovss, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0929 */ { UD_Imovss, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0930 */ { UD_Imovsx, O_Gv, O_Eb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0931 */ { UD_Imovsx, O_Gy, O_Ew, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0932 */ { UD_Imovupd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0933 */ { UD_Ivmovupd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0934 */ { UD_Imovupd, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0935 */ { UD_Ivmovupd, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0936 */ { UD_Imovups, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0937 */ { UD_Ivmovups, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0938 */ { UD_Imovups, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0939 */ { UD_Ivmovups, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0940 */ { UD_Imovzx, O_Gv, O_Eb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0941 */ { UD_Imovzx, O_Gy, O_Ew, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0942 */ { UD_Imul, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0943 */ { UD_Imul, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0944 */ { UD_Imulpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0945 */ { UD_Ivmulpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0946 */ { UD_Imulps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0947 */ { UD_Ivmulps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0948 */ { UD_Imulsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0949 */ { UD_Ivmulsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0950 */ { UD_Imulss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0951 */ { UD_Ivmulss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0952 */ { UD_Imwait, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 0953 */ { UD_Ineg, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0954 */ { UD_Ineg, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0955 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0956 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0957 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0958 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0959 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0960 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0961 */ { UD_Inop, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0962 */ { UD_Inot, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0963 */ { UD_Inot, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0964 */ { UD_Ior, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0965 */ { UD_Ior, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0966 */ { UD_Ior, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0967 */ { UD_Ior, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0968 */ { UD_Ior, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 0969 */ { UD_Ior, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 0970 */ { UD_Ior, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0971 */ { UD_Ior, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0972 */ { UD_Ior, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0973 */ { UD_Ior, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 0974 */ { UD_Iorpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0975 */ { UD_Ivorpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0976 */ { UD_Iorps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0977 */ { UD_Ivorps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0978 */ { UD_Iout, O_Ib, O_AL, O_NONE, O_NONE, P_none },
  /* 0979 */ { UD_Iout, O_Ib, O_eAX, O_NONE, O_NONE, P_oso },
  /* 0980 */ { UD_Iout, O_DX, O_AL, O_NONE, O_NONE, P_none },
  /* 0981 */ { UD_Iout, O_DX, O_eAX, O_NONE, O_NONE, P_oso },
  /* 0982 */ { UD_Ioutsb, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg },
  /* 0983 */ { UD_Ioutsw, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_oso|P_seg },
  /* 0984 */ { UD_Ioutsd, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_oso|P_seg },
  /* 0985 */ { UD_Ipacksswb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0986 */ { UD_Ivpacksswb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0987 */ { UD_Ipacksswb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0988 */ { UD_Ipackssdw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0989 */ { UD_Ivpackssdw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0990 */ { UD_Ipackssdw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0991 */ { UD_Ipackuswb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0992 */ { UD_Ivpackuswb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0993 */ { UD_Ipackuswb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0994 */ { UD_Ipaddb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0995 */ { UD_Ivpaddb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 0996 */ { UD_Ipaddb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0997 */ { UD_Ipaddw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0998 */ { UD_Ipaddw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 0999 */ { UD_Ivpaddw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1000 */ { UD_Ipaddd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1001 */ { UD_Ipaddd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1002 */ { UD_Ivpaddd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1003 */ { UD_Ipaddsb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1004 */ { UD_Ipaddsb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1005 */ { UD_Ivpaddsb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1006 */ { UD_Ipaddsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1007 */ { UD_Ipaddsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1008 */ { UD_Ivpaddsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1009 */ { UD_Ipaddusb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1010 */ { UD_Ipaddusb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1011 */ { UD_Ivpaddusb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1012 */ { UD_Ipaddusw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1013 */ { UD_Ipaddusw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1014 */ { UD_Ivpaddusw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1015 */ { UD_Ipand, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1016 */ { UD_Ivpand, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1017 */ { UD_Ipand, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1018 */ { UD_Ipandn, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1019 */ { UD_Ivpandn, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1020 */ { UD_Ipandn, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1021 */ { UD_Ipavgb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1022 */ { UD_Ivpavgb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1023 */ { UD_Ipavgb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1024 */ { UD_Ipavgw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1025 */ { UD_Ivpavgw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1026 */ { UD_Ipavgw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1027 */ { UD_Ipcmpeqb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1028 */ { UD_Ipcmpeqb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1029 */ { UD_Ivpcmpeqb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1030 */ { UD_Ipcmpeqw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1031 */ { UD_Ipcmpeqw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1032 */ { UD_Ivpcmpeqw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1033 */ { UD_Ipcmpeqd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1034 */ { UD_Ipcmpeqd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1035 */ { UD_Ivpcmpeqd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1036 */ { UD_Ipcmpgtb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1037 */ { UD_Ivpcmpgtb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1038 */ { UD_Ipcmpgtb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1039 */ { UD_Ipcmpgtw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1040 */ { UD_Ivpcmpgtw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1041 */ { UD_Ipcmpgtw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1042 */ { UD_Ipcmpgtd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1043 */ { UD_Ivpcmpgtd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1044 */ { UD_Ipcmpgtd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1045 */ { UD_Ipextrb, O_MbRv, O_V, O_Ib, O_NONE, P_aso|P_rexx|P_rexr|P_rexb|P_def64 },
  /* 1046 */ { UD_Ivpextrb, O_MbRv, O_Vx, O_Ib, O_NONE, P_aso|P_rexx|P_rexr|P_rexb|P_def64 },
  /* 1047 */ { UD_Ipextrd, O_Ed, O_V, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexw|P_rexb },
  /* 1048 */ { UD_Ivpextrd, O_Ed, O_Vx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexw|P_rexb },
  /* 1049 */ { UD_Ipextrd, O_Ed, O_V, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexw|P_rexb },
  /* 1050 */ { UD_Ivpextrd, O_Ed, O_Vx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexw|P_rexb },
  /* 1051 */ { UD_Ipextrq, O_Eq, O_V, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexb|P_def64 },
  /* 1052 */ { UD_Ivpextrq, O_Eq, O_Vx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexb|P_def64 },
  /* 1053 */ { UD_Ipextrw, O_Gd, O_U, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexb },
  /* 1054 */ { UD_Ivpextrw, O_Gd, O_Ux, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexb },
  /* 1055 */ { UD_Ipextrw, O_Gd, O_N, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1056 */ { UD_Ipextrw, O_MwRd, O_V, O_Ib, O_NONE, P_aso|P_rexw|P_rexx|P_rexr|P_rexb },
  /* 1057 */ { UD_Ivpextrw, O_MwRd, O_Vx, O_Ib, O_NONE, P_aso|P_rexw|P_rexx|P_rexr|P_rexb },
  /* 1058 */ { UD_Ipinsrb, O_V, O_MbRd, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1059 */ { UD_Ipinsrw, O_P, O_MwRy, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1060 */ { UD_Ipinsrw, O_V, O_MwRy, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1061 */ { UD_Ivpinsrw, O_Vx, O_MwRy, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1062 */ { UD_Ipinsrd, O_V, O_Ed, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1063 */ { UD_Ipinsrd, O_V, O_Ed, O_Ib, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1064 */ { UD_Ipinsrq, O_V, O_Eq, O_Ib, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1065 */ { UD_Ivpinsrb, O_V, O_H, O_MbRd, O_Ib, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1066 */ { UD_Ivpinsrd, O_V, O_H, O_Ed, O_Ib, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1067 */ { UD_Ivpinsrd, O_V, O_H, O_Ed, O_Ib, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1068 */ { UD_Ivpinsrq, O_V, O_H, O_Eq, O_Ib, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1069 */ { UD_Ipmaddwd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1070 */ { UD_Ipmaddwd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1071 */ { UD_Ivpmaddwd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1072 */ { UD_Ipmaxsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1073 */ { UD_Ivpmaxsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1074 */ { UD_Ipmaxsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1075 */ { UD_Ipmaxub, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1076 */ { UD_Ipmaxub, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1077 */ { UD_Ivpmaxub, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1078 */ { UD_Ipminsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1079 */ { UD_Ivpminsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1080 */ { UD_Ipminsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1081 */ { UD_Ipminub, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1082 */ { UD_Ivpminub, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1083 */ { UD_Ipminub, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1084 */ { UD_Ipmovmskb, O_Gd, O_U, O_NONE, O_NONE, P_oso|P_rexr|P_rexw|P_rexb },
  /* 1085 */ { UD_Ivpmovmskb, O_Gd, O_Ux, O_NONE, O_NONE, P_oso|P_rexr|P_rexw|P_rexb },
  /* 1086 */ { UD_Ipmovmskb, O_Gd, O_N, O_NONE, O_NONE, P_oso|P_rexr|P_rexw|P_rexb },
  /* 1087 */ { UD_Ipmulhuw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1088 */ { UD_Ipmulhuw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1089 */ { UD_Ivpmulhuw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1090 */ { UD_Ipmulhw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1091 */ { UD_Ivpmulhw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1092 */ { UD_Ipmulhw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1093 */ { UD_Ipmullw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1094 */ { UD_Ipmullw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1095 */ { UD_Ivpmullw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1096 */ { UD_Ipop, O_ES, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1097 */ { UD_Ipop, O_SS, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1098 */ { UD_Ipop, O_DS, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1099 */ { UD_Ipop, O_GS, O_NONE, O_NONE, O_NONE, P_none },
  /* 1100 */ { UD_Ipop, O_FS, O_NONE, O_NONE, O_NONE, P_none },
  /* 1101 */ { UD_Ipop, O_R0v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1102 */ { UD_Ipop, O_R1v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1103 */ { UD_Ipop, O_R2v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1104 */ { UD_Ipop, O_R3v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1105 */ { UD_Ipop, O_R4v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1106 */ { UD_Ipop, O_R5v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1107 */ { UD_Ipop, O_R6v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1108 */ { UD_Ipop, O_R7v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1109 */ { UD_Ipop, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1110 */ { UD_Ipopa, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_inv64 },
  /* 1111 */ { UD_Ipopad, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_inv64 },
  /* 1112 */ { UD_Ipopfw, O_NONE, O_NONE, O_NONE, O_NONE, P_oso },
  /* 1113 */ { UD_Ipopfd, O_NONE, O_NONE, O_NONE, O_NONE, P_oso },
  /* 1114 */ { UD_Ipopfq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 1115 */ { UD_Ipopfq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 1116 */ { UD_Ipor, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1117 */ { UD_Ivpor, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1118 */ { UD_Ipor, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1119 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1120 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1121 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1122 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1123 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1124 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1125 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1126 */ { UD_Iprefetch, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1127 */ { UD_Iprefetchnta, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1128 */ { UD_Iprefetcht0, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1129 */ { UD_Iprefetcht1, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1130 */ { UD_Iprefetcht2, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1131 */ { UD_Ipsadbw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1132 */ { UD_Ivpsadbw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1133 */ { UD_Ipsadbw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1134 */ { UD_Ipshufw, O_P, O_Q, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1135 */ { UD_Ipsllw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1136 */ { UD_Ipsllw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1137 */ { UD_Ipsllw, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1138 */ { UD_Ipsllw, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1139 */ { UD_Ipslld, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1140 */ { UD_Ipslld, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1141 */ { UD_Ipslld, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1142 */ { UD_Ipslld, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1143 */ { UD_Ipsllq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1144 */ { UD_Ipsllq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1145 */ { UD_Ipsllq, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1146 */ { UD_Ipsllq, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1147 */ { UD_Ipsraw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1148 */ { UD_Ipsraw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1149 */ { UD_Ivpsraw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1150 */ { UD_Ipsraw, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1151 */ { UD_Ivpsraw, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1152 */ { UD_Ipsraw, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1153 */ { UD_Ipsrad, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1154 */ { UD_Ipsrad, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1155 */ { UD_Ivpsrad, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1156 */ { UD_Ipsrad, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1157 */ { UD_Ipsrad, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1158 */ { UD_Ivpsrad, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1159 */ { UD_Ipsrlw, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1160 */ { UD_Ipsrlw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1161 */ { UD_Ipsrlw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1162 */ { UD_Ivpsrlw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1163 */ { UD_Ipsrlw, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1164 */ { UD_Ivpsrlw, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1165 */ { UD_Ipsrld, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1166 */ { UD_Ipsrld, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1167 */ { UD_Ipsrld, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1168 */ { UD_Ivpsrld, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1169 */ { UD_Ipsrld, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1170 */ { UD_Ivpsrld, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1171 */ { UD_Ipsrlq, O_N, O_Ib, O_NONE, O_NONE, P_none },
  /* 1172 */ { UD_Ipsrlq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1173 */ { UD_Ipsrlq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1174 */ { UD_Ivpsrlq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1175 */ { UD_Ipsrlq, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1176 */ { UD_Ivpsrlq, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1177 */ { UD_Ipsubb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1178 */ { UD_Ivpsubb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1179 */ { UD_Ipsubb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1180 */ { UD_Ipsubw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1181 */ { UD_Ivpsubw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1182 */ { UD_Ipsubw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1183 */ { UD_Ipsubd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1184 */ { UD_Ipsubd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1185 */ { UD_Ivpsubd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1186 */ { UD_Ipsubsb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1187 */ { UD_Ipsubsb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1188 */ { UD_Ivpsubsb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1189 */ { UD_Ipsubsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1190 */ { UD_Ipsubsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1191 */ { UD_Ivpsubsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1192 */ { UD_Ipsubusb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1193 */ { UD_Ipsubusb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1194 */ { UD_Ivpsubusb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1195 */ { UD_Ipsubusw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1196 */ { UD_Ipsubusw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1197 */ { UD_Ivpsubusw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1198 */ { UD_Ipunpckhbw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1199 */ { UD_Ivpunpckhbw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1200 */ { UD_Ipunpckhbw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1201 */ { UD_Ipunpckhwd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1202 */ { UD_Ivpunpckhwd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1203 */ { UD_Ipunpckhwd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1204 */ { UD_Ipunpckhdq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1205 */ { UD_Ivpunpckhdq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1206 */ { UD_Ipunpckhdq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1207 */ { UD_Ipunpcklbw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1208 */ { UD_Ivpunpcklbw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1209 */ { UD_Ipunpcklbw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1210 */ { UD_Ipunpcklwd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1211 */ { UD_Ivpunpcklwd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1212 */ { UD_Ipunpcklwd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1213 */ { UD_Ipunpckldq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1214 */ { UD_Ivpunpckldq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1215 */ { UD_Ipunpckldq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1216 */ { UD_Ipi2fw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1217 */ { UD_Ipi2fd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1218 */ { UD_Ipf2iw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1219 */ { UD_Ipf2id, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1220 */ { UD_Ipfnacc, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1221 */ { UD_Ipfpnacc, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1222 */ { UD_Ipfcmpge, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1223 */ { UD_Ipfmin, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1224 */ { UD_Ipfrcp, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1225 */ { UD_Ipfrsqrt, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1226 */ { UD_Ipfsub, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1227 */ { UD_Ipfadd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1228 */ { UD_Ipfcmpgt, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1229 */ { UD_Ipfmax, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1230 */ { UD_Ipfrcpit1, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1231 */ { UD_Ipfrsqit1, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1232 */ { UD_Ipfsubr, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1233 */ { UD_Ipfacc, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1234 */ { UD_Ipfcmpeq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1235 */ { UD_Ipfmul, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1236 */ { UD_Ipfrcpit2, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1237 */ { UD_Ipmulhrw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1238 */ { UD_Ipswapd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1239 */ { UD_Ipavgusb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1240 */ { UD_Ipush, O_ES, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1241 */ { UD_Ipush, O_CS, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1242 */ { UD_Ipush, O_SS, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1243 */ { UD_Ipush, O_DS, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1244 */ { UD_Ipush, O_GS, O_NONE, O_NONE, O_NONE, P_none },
  /* 1245 */ { UD_Ipush, O_FS, O_NONE, O_NONE, O_NONE, P_none },
  /* 1246 */ { UD_Ipush, O_R0v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1247 */ { UD_Ipush, O_R1v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1248 */ { UD_Ipush, O_R2v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1249 */ { UD_Ipush, O_R3v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1250 */ { UD_Ipush, O_R4v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1251 */ { UD_Ipush, O_R5v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1252 */ { UD_Ipush, O_R6v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1253 */ { UD_Ipush, O_R7v, O_NONE, O_NONE, O_NONE, P_oso|P_rexb|P_def64 },
  /* 1254 */ { UD_Ipush, O_sIz, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 1255 */ { UD_Ipush, O_Ev, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1256 */ { UD_Ipush, O_sIb, O_NONE, O_NONE, O_NONE, P_oso|P_def64 },
  /* 1257 */ { UD_Ipusha, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_inv64 },
  /* 1258 */ { UD_Ipushad, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_inv64 },
  /* 1259 */ { UD_Ipushfw, O_NONE, O_NONE, O_NONE, O_NONE, P_oso },
  /* 1260 */ { UD_Ipushfw, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_def64 },
  /* 1261 */ { UD_Ipushfd, O_NONE, O_NONE, O_NONE, O_NONE, P_oso },
  /* 1262 */ { UD_Ipushfq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_def64 },
  /* 1263 */ { UD_Ipushfq, O_NONE, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_def64 },
  /* 1264 */ { UD_Ipxor, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1265 */ { UD_Ivpxor, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1266 */ { UD_Ipxor, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1267 */ { UD_Ircl, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1268 */ { UD_Ircl, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1269 */ { UD_Ircl, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1270 */ { UD_Ircl, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1271 */ { UD_Ircl, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1272 */ { UD_Ircl, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1273 */ { UD_Ircr, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1274 */ { UD_Ircr, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1275 */ { UD_Ircr, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1276 */ { UD_Ircr, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1277 */ { UD_Ircr, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1278 */ { UD_Ircr, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1279 */ { UD_Irol, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1280 */ { UD_Irol, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1281 */ { UD_Irol, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1282 */ { UD_Irol, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1283 */ { UD_Irol, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1284 */ { UD_Irol, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1285 */ { UD_Iror, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1286 */ { UD_Iror, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1287 */ { UD_Iror, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1288 */ { UD_Iror, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1289 */ { UD_Iror, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1290 */ { UD_Iror, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1291 */ { UD_Ircpps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1292 */ { UD_Ivrcpps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1293 */ { UD_Ircpss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1294 */ { UD_Ivrcpss, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1295 */ { UD_Irdmsr, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1296 */ { UD_Irdpmc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1297 */ { UD_Irdtsc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1298 */ { UD_Irdtscp, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1299 */ { UD_Irepne, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1300 */ { UD_Irep, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1301 */ { UD_Iret, O_Iw, O_NONE, O_NONE, O_NONE, P_none },
  /* 1302 */ { UD_Iret, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1303 */ { UD_Iretf, O_Iw, O_NONE, O_NONE, O_NONE, P_none },
  /* 1304 */ { UD_Iretf, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1305 */ { UD_Irsm, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1306 */ { UD_Irsqrtps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1307 */ { UD_Ivrsqrtps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1308 */ { UD_Irsqrtss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1309 */ { UD_Ivrsqrtss, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1310 */ { UD_Isahf, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1311 */ { UD_Isalc, O_NONE, O_NONE, O_NONE, O_NONE, P_inv64 },
  /* 1312 */ { UD_Isar, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1313 */ { UD_Isar, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1314 */ { UD_Isar, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1315 */ { UD_Isar, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1316 */ { UD_Isar, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1317 */ { UD_Isar, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1318 */ { UD_Ishl, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1319 */ { UD_Ishl, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1320 */ { UD_Ishl, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1321 */ { UD_Ishl, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1322 */ { UD_Ishl, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1323 */ { UD_Ishl, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1324 */ { UD_Ishl, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1325 */ { UD_Ishl, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1326 */ { UD_Ishl, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1327 */ { UD_Ishl, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1328 */ { UD_Ishl, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1329 */ { UD_Ishl, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1330 */ { UD_Ishr, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1331 */ { UD_Ishr, O_Eb, O_CL, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1332 */ { UD_Ishr, O_Ev, O_I1, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1333 */ { UD_Ishr, O_Eb, O_I1, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1334 */ { UD_Ishr, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1335 */ { UD_Ishr, O_Ev, O_CL, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1336 */ { UD_Isbb, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1337 */ { UD_Isbb, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1338 */ { UD_Isbb, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1339 */ { UD_Isbb, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1340 */ { UD_Isbb, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 1341 */ { UD_Isbb, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 1342 */ { UD_Isbb, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1343 */ { UD_Isbb, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1344 */ { UD_Isbb, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 1345 */ { UD_Isbb, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1346 */ { UD_Iscasb, O_NONE, O_NONE, O_NONE, O_NONE, P_strz },
  /* 1347 */ { UD_Iscasw, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw },
  /* 1348 */ { UD_Iscasd, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw },
  /* 1349 */ { UD_Iscasq, O_NONE, O_NONE, O_NONE, O_NONE, P_strz|P_oso|P_rexw },
  /* 1350 */ { UD_Iseto, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1351 */ { UD_Isetno, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1352 */ { UD_Isetb, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1353 */ { UD_Isetae, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1354 */ { UD_Isetz, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1355 */ { UD_Isetnz, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1356 */ { UD_Isetbe, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1357 */ { UD_Iseta, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1358 */ { UD_Isets, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1359 */ { UD_Isetns, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1360 */ { UD_Isetp, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1361 */ { UD_Isetnp, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1362 */ { UD_Isetl, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1363 */ { UD_Isetge, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1364 */ { UD_Isetle, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1365 */ { UD_Isetg, O_Eb, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1366 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1367 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1368 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1369 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1370 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1371 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1372 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1373 */ { UD_Isfence, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1374 */ { UD_Isgdt, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1375 */ { UD_Ishld, O_Ev, O_Gv, O_Ib, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1376 */ { UD_Ishld, O_Ev, O_Gv, O_CL, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1377 */ { UD_Ishrd, O_Ev, O_Gv, O_Ib, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1378 */ { UD_Ishrd, O_Ev, O_Gv, O_CL, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1379 */ { UD_Ishufpd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1380 */ { UD_Ivshufpd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1381 */ { UD_Ishufps, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1382 */ { UD_Ivshufps, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1383 */ { UD_Isidt, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1384 */ { UD_Isldt, O_MwRv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1385 */ { UD_Ismsw, O_MwRv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1386 */ { UD_Ismsw, O_MwRv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1387 */ { UD_Isqrtps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1388 */ { UD_Ivsqrtps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1389 */ { UD_Isqrtpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1390 */ { UD_Ivsqrtpd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1391 */ { UD_Isqrtsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1392 */ { UD_Ivsqrtsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1393 */ { UD_Isqrtss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1394 */ { UD_Ivsqrtss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1395 */ { UD_Istc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1396 */ { UD_Istd, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1397 */ { UD_Istgi, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1398 */ { UD_Isti, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1399 */ { UD_Iskinit, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1400 */ { UD_Istmxcsr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1401 */ { UD_Ivstmxcsr, O_Md, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1402 */ { UD_Istosb, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg },
  /* 1403 */ { UD_Istosw, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 1404 */ { UD_Istosd, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 1405 */ { UD_Istosq, O_NONE, O_NONE, O_NONE, O_NONE, P_str|P_seg|P_oso|P_rexw },
  /* 1406 */ { UD_Istr, O_MwRv, O_NONE, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1407 */ { UD_Isub, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1408 */ { UD_Isub, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1409 */ { UD_Isub, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1410 */ { UD_Isub, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1411 */ { UD_Isub, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 1412 */ { UD_Isub, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 1413 */ { UD_Isub, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1414 */ { UD_Isub, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1415 */ { UD_Isub, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 1416 */ { UD_Isub, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1417 */ { UD_Isubpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1418 */ { UD_Ivsubpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1419 */ { UD_Isubps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1420 */ { UD_Ivsubps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1421 */ { UD_Isubsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1422 */ { UD_Ivsubsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1423 */ { UD_Isubss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1424 */ { UD_Ivsubss, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1425 */ { UD_Iswapgs, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1426 */ { UD_Isyscall, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1427 */ { UD_Isysenter, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1428 */ { UD_Isysenter, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1429 */ { UD_Isysexit, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1430 */ { UD_Isysexit, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1431 */ { UD_Isysret, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1432 */ { UD_Itest, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1433 */ { UD_Itest, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1434 */ { UD_Itest, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1435 */ { UD_Itest, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 1436 */ { UD_Itest, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 1437 */ { UD_Itest, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1438 */ { UD_Itest, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1439 */ { UD_Itest, O_Ev, O_Iz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1440 */ { UD_Iucomisd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1441 */ { UD_Ivucomisd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1442 */ { UD_Iucomiss, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1443 */ { UD_Ivucomiss, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1444 */ { UD_Iud2, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1445 */ { UD_Iunpckhpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1446 */ { UD_Ivunpckhpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1447 */ { UD_Iunpckhps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1448 */ { UD_Ivunpckhps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1449 */ { UD_Iunpcklps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1450 */ { UD_Ivunpcklps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1451 */ { UD_Iunpcklpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1452 */ { UD_Ivunpcklpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1453 */ { UD_Iverr, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1454 */ { UD_Iverw, O_Ew, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1455 */ { UD_Ivmcall, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1456 */ { UD_Irdrand, O_R, O_NONE, O_NONE, O_NONE, P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1457 */ { UD_Ivmclear, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1458 */ { UD_Ivmxon, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1459 */ { UD_Ivmptrld, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1460 */ { UD_Ivmptrst, O_Mq, O_NONE, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1461 */ { UD_Ivmlaunch, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1462 */ { UD_Ivmresume, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1463 */ { UD_Ivmxoff, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1464 */ { UD_Ivmread, O_Ey, O_Gy, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1465 */ { UD_Ivmwrite, O_Gy, O_Ey, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_def64 },
  /* 1466 */ { UD_Ivmrun, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1467 */ { UD_Ivmmcall, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1468 */ { UD_Ivmload, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1469 */ { UD_Ivmsave, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1470 */ { UD_Iwait, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1471 */ { UD_Iwbinvd, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1472 */ { UD_Iwrmsr, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1473 */ { UD_Ixadd, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexx|P_rexb },
  /* 1474 */ { UD_Ixadd, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1475 */ { UD_Ixchg, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1476 */ { UD_Ixchg, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1477 */ { UD_Ixchg, O_R0v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1478 */ { UD_Ixchg, O_R1v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1479 */ { UD_Ixchg, O_R2v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1480 */ { UD_Ixchg, O_R3v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1481 */ { UD_Ixchg, O_R4v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1482 */ { UD_Ixchg, O_R5v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1483 */ { UD_Ixchg, O_R6v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1484 */ { UD_Ixchg, O_R7v, O_rAX, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1485 */ { UD_Ixgetbv, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1486 */ { UD_Ixlatb, O_NONE, O_NONE, O_NONE, O_NONE, P_rexw|P_seg },
  /* 1487 */ { UD_Ixor, O_Eb, O_Gb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1488 */ { UD_Ixor, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1489 */ { UD_Ixor, O_Gb, O_Eb, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1490 */ { UD_Ixor, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1491 */ { UD_Ixor, O_AL, O_Ib, O_NONE, O_NONE, P_none },
  /* 1492 */ { UD_Ixor, O_rAX, O_sIz, O_NONE, O_NONE, P_oso|P_rexw },
  /* 1493 */ { UD_Ixor, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1494 */ { UD_Ixor, O_Ev, O_sIz, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1495 */ { UD_Ixor, O_Eb, O_Ib, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_inv64 },
  /* 1496 */ { UD_Ixor, O_Ev, O_sIb, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1497 */ { UD_Ixorpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1498 */ { UD_Ivxorpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1499 */ { UD_Ixorps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1500 */ { UD_Ivxorps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1501 */ { UD_Ixcryptecb, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1502 */ { UD_Ixcryptcbc, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1503 */ { UD_Ixcryptctr, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1504 */ { UD_Ixcryptcfb, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1505 */ { UD_Ixcryptofb, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1506 */ { UD_Ixrstor, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1507 */ { UD_Ixsave, O_M, O_NONE, O_NONE, O_NONE, P_aso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1508 */ { UD_Ixsetbv, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1509 */ { UD_Ixsha1, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1510 */ { UD_Ixsha256, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1511 */ { UD_Ixstore, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1512 */ { UD_Ipclmulqdq, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1513 */ { UD_Ivpclmulqdq, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1514 */ { UD_Igetsec, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1515 */ { UD_Imovdqa, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1516 */ { UD_Ivmovdqa, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1517 */ { UD_Imovdqa, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1518 */ { UD_Ivmovdqa, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1519 */ { UD_Imaskmovdqu, O_V, O_U, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1520 */ { UD_Ivmaskmovdqu, O_Vx, O_Ux, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1521 */ { UD_Imovdq2q, O_P, O_U, O_NONE, O_NONE, P_aso|P_rexb },
  /* 1522 */ { UD_Imovdqu, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1523 */ { UD_Ivmovdqu, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1524 */ { UD_Imovdqu, O_W, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1525 */ { UD_Ivmovdqu, O_Wx, O_Vx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1526 */ { UD_Imovq2dq, O_V, O_N, O_NONE, O_NONE, P_aso|P_rexr },
  /* 1527 */ { UD_Ipaddq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1528 */ { UD_Ipaddq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1529 */ { UD_Ivpaddq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1530 */ { UD_Ipsubq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1531 */ { UD_Ivpsubq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1532 */ { UD_Ipsubq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1533 */ { UD_Ipmuludq, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1534 */ { UD_Ipmuludq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1535 */ { UD_Ipshufhw, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1536 */ { UD_Ivpshufhw, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1537 */ { UD_Ipshuflw, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1538 */ { UD_Ivpshuflw, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1539 */ { UD_Ipshufd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1540 */ { UD_Ivpshufd, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1541 */ { UD_Ipslldq, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1542 */ { UD_Ivpslldq, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1543 */ { UD_Ipsrldq, O_U, O_Ib, O_NONE, O_NONE, P_rexb },
  /* 1544 */ { UD_Ivpsrldq, O_Hx, O_Ux, O_Ib, O_NONE, P_rexb },
  /* 1545 */ { UD_Ipunpckhqdq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1546 */ { UD_Ivpunpckhqdq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1547 */ { UD_Ipunpcklqdq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1548 */ { UD_Ivpunpcklqdq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1549 */ { UD_Ihaddpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1550 */ { UD_Ivhaddpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1551 */ { UD_Ihaddps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1552 */ { UD_Ivhaddps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1553 */ { UD_Ihsubpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1554 */ { UD_Ivhsubpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1555 */ { UD_Ihsubps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1556 */ { UD_Ivhsubps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1557 */ { UD_Iinsertps, O_V, O_Md, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1558 */ { UD_Ivinsertps, O_Vx, O_Hx, O_Md, O_Ib, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1559 */ { UD_Ilddqu, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1560 */ { UD_Ivlddqu, O_Vx, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1561 */ { UD_Imovddup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1562 */ { UD_Ivmovddup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1563 */ { UD_Imovddup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1564 */ { UD_Ivmovddup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1565 */ { UD_Imovshdup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1566 */ { UD_Ivmovshdup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1567 */ { UD_Imovshdup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1568 */ { UD_Ivmovshdup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1569 */ { UD_Imovsldup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1570 */ { UD_Ivmovsldup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1571 */ { UD_Imovsldup, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1572 */ { UD_Ivmovsldup, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1573 */ { UD_Ipabsb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1574 */ { UD_Ipabsb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1575 */ { UD_Ivpabsb, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1576 */ { UD_Ipabsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1577 */ { UD_Ipabsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1578 */ { UD_Ivpabsw, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1579 */ { UD_Ipabsd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1580 */ { UD_Ipabsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1581 */ { UD_Ivpabsd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1582 */ { UD_Ipshufb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1583 */ { UD_Ipshufb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1584 */ { UD_Ivpshufb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1585 */ { UD_Iphaddw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1586 */ { UD_Iphaddw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1587 */ { UD_Ivphaddw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1588 */ { UD_Iphaddd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1589 */ { UD_Iphaddd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1590 */ { UD_Ivphaddd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1591 */ { UD_Iphaddsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1592 */ { UD_Iphaddsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1593 */ { UD_Ivphaddsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1594 */ { UD_Ipmaddubsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1595 */ { UD_Ipmaddubsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1596 */ { UD_Ivpmaddubsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1597 */ { UD_Iphsubw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1598 */ { UD_Iphsubw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1599 */ { UD_Ivphsubw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1600 */ { UD_Iphsubd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1601 */ { UD_Iphsubd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1602 */ { UD_Ivphsubd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1603 */ { UD_Iphsubsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1604 */ { UD_Iphsubsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1605 */ { UD_Ivphsubsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1606 */ { UD_Ipsignb, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1607 */ { UD_Ipsignb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1608 */ { UD_Ivpsignb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1609 */ { UD_Ipsignd, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1610 */ { UD_Ipsignd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1611 */ { UD_Ivpsignd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1612 */ { UD_Ipsignw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1613 */ { UD_Ipsignw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1614 */ { UD_Ivpsignw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1615 */ { UD_Ipmulhrsw, O_P, O_Q, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1616 */ { UD_Ipmulhrsw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1617 */ { UD_Ivpmulhrsw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1618 */ { UD_Ipalignr, O_P, O_Q, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1619 */ { UD_Ipalignr, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1620 */ { UD_Ivpalignr, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1621 */ { UD_Ipblendvb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1622 */ { UD_Ipmuldq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1623 */ { UD_Ivpmuldq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1624 */ { UD_Ipminsb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1625 */ { UD_Ivpminsb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1626 */ { UD_Ipminsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1627 */ { UD_Ivpminsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1628 */ { UD_Ipminuw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1629 */ { UD_Ivpminuw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1630 */ { UD_Ipminud, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1631 */ { UD_Ivpminud, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1632 */ { UD_Ipmaxsb, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1633 */ { UD_Ivpmaxsb, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1634 */ { UD_Ipmaxsd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1635 */ { UD_Ivpmaxsd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1636 */ { UD_Ipmaxud, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1637 */ { UD_Ivpmaxud, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1638 */ { UD_Ipmaxuw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1639 */ { UD_Ivpmaxuw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1640 */ { UD_Ipmulld, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1641 */ { UD_Ivpmulld, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1642 */ { UD_Iphminposuw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1643 */ { UD_Ivphminposuw, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1644 */ { UD_Iroundps, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1645 */ { UD_Ivroundps, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1646 */ { UD_Iroundpd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1647 */ { UD_Ivroundpd, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1648 */ { UD_Iroundss, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1649 */ { UD_Ivroundss, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1650 */ { UD_Iroundsd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1651 */ { UD_Ivroundsd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1652 */ { UD_Iblendpd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1653 */ { UD_Ivblendpd, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1654 */ { UD_Iblendps, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1655 */ { UD_Ivblendps, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1656 */ { UD_Iblendvpd, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1657 */ { UD_Iblendvps, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1658 */ { UD_Ibound, O_Gv, O_M, O_NONE, O_NONE, P_aso|P_oso },
  /* 1659 */ { UD_Ibsf, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1660 */ { UD_Ibsr, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1661 */ { UD_Ibswap, O_R0y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1662 */ { UD_Ibswap, O_R1y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1663 */ { UD_Ibswap, O_R2y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1664 */ { UD_Ibswap, O_R3y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1665 */ { UD_Ibswap, O_R4y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1666 */ { UD_Ibswap, O_R5y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1667 */ { UD_Ibswap, O_R6y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1668 */ { UD_Ibswap, O_R7y, O_NONE, O_NONE, O_NONE, P_oso|P_rexw|P_rexb },
  /* 1669 */ { UD_Ibt, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1670 */ { UD_Ibt, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1671 */ { UD_Ibtc, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1672 */ { UD_Ibtc, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1673 */ { UD_Ibtr, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1674 */ { UD_Ibtr, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1675 */ { UD_Ibts, O_Ev, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1676 */ { UD_Ibts, O_Ev, O_Ib, O_NONE, O_NONE, P_aso|P_oso|P_rexw|P_rexr|P_rexx|P_rexb },
  /* 1677 */ { UD_Ipblendw, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1678 */ { UD_Ivpblendw, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1679 */ { UD_Impsadbw, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1680 */ { UD_Ivmpsadbw, O_Vx, O_Hx, O_Wx, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1681 */ { UD_Imovntdqa, O_V, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1682 */ { UD_Ivmovntdqa, O_Vx, O_M, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb|P_vexl },
  /* 1683 */ { UD_Ipackusdw, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1684 */ { UD_Ivpackusdw, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb|P_vexl },
  /* 1685 */ { UD_Ipmovsxbw, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1686 */ { UD_Ivpmovsxbw, O_Vx, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1687 */ { UD_Ipmovsxbd, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1688 */ { UD_Ivpmovsxbd, O_Vx, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1689 */ { UD_Ipmovsxbq, O_V, O_MwU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1690 */ { UD_Ivpmovsxbq, O_Vx, O_MwU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1691 */ { UD_Ipmovsxwd, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1692 */ { UD_Ivpmovsxwd, O_Vx, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1693 */ { UD_Ipmovsxwq, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1694 */ { UD_Ivpmovsxwq, O_Vx, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1695 */ { UD_Ipmovsxdq, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1696 */ { UD_Ipmovzxbw, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1697 */ { UD_Ivpmovzxbw, O_Vx, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1698 */ { UD_Ipmovzxbd, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1699 */ { UD_Ivpmovzxbd, O_Vx, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1700 */ { UD_Ipmovzxbq, O_V, O_MwU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1701 */ { UD_Ivpmovzxbq, O_Vx, O_MwU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1702 */ { UD_Ipmovzxwd, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1703 */ { UD_Ivpmovzxwd, O_Vx, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1704 */ { UD_Ipmovzxwq, O_V, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1705 */ { UD_Ivpmovzxwq, O_Vx, O_MdU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1706 */ { UD_Ipmovzxdq, O_V, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1707 */ { UD_Ivpmovzxdq, O_Vx, O_MqU, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1708 */ { UD_Ipcmpeqq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1709 */ { UD_Ivpcmpeqq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1710 */ { UD_Ipopcnt, O_Gv, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1711 */ { UD_Iptest, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1712 */ { UD_Ivptest, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb|P_vexl },
  /* 1713 */ { UD_Ipcmpestri, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1714 */ { UD_Ivpcmpestri, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1715 */ { UD_Ipcmpestrm, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1716 */ { UD_Ivpcmpestrm, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1717 */ { UD_Ipcmpgtq, O_V, O_W, O_NONE, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1718 */ { UD_Ivpcmpgtq, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1719 */ { UD_Ipcmpistri, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1720 */ { UD_Ivpcmpistri, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1721 */ { UD_Ipcmpistrm, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1722 */ { UD_Ivpcmpistrm, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1723 */ { UD_Imovbe, O_Gv, O_Mv, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1724 */ { UD_Imovbe, O_Mv, O_Gv, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1725 */ { UD_Icrc32, O_Gy, O_Eb, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1726 */ { UD_Icrc32, O_Gy, O_Ev, O_NONE, O_NONE, P_aso|P_oso|P_rexr|P_rexw|P_rexx|P_rexb },
  /* 1727 */ { UD_Ivbroadcastss, O_V, O_Md, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1728 */ { UD_Ivbroadcastsd, O_Vqq, O_Mq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1729 */ { UD_Ivextractf128, O_Wdq, O_Vqq, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1730 */ { UD_Ivinsertf128, O_Vqq, O_Hqq, O_Wdq, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1731 */ { UD_Ivmaskmovps, O_V, O_H, O_M, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1732 */ { UD_Ivmaskmovps, O_M, O_H, O_V, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1733 */ { UD_Ivmaskmovpd, O_V, O_H, O_M, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1734 */ { UD_Ivmaskmovpd, O_M, O_H, O_V, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1735 */ { UD_Ivpermilpd, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1736 */ { UD_Ivpermilpd, O_V, O_W, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1737 */ { UD_Ivpermilps, O_Vx, O_Hx, O_Wx, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1738 */ { UD_Ivpermilps, O_Vx, O_Wx, O_Ib, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1739 */ { UD_Ivperm2f128, O_Vqq, O_Hqq, O_Wqq, O_Ib, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1740 */ { UD_Ivtestps, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1741 */ { UD_Ivtestpd, O_Vx, O_Wx, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1742 */ { UD_Ivzeroupper, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1743 */ { UD_Ivzeroall, O_NONE, O_NONE, O_NONE, O_NONE, P_none },
  /* 1744 */ { UD_Ivblendvpd, O_Vx, O_Hx, O_Wx, O_Lx, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1745 */ { UD_Ivblendvps, O_Vx, O_Hx, O_Wx, O_Lx, P_aso|P_rexr|P_rexx|P_rexb|P_vexl },
  /* 1746 */ { UD_Ivmovsd, O_V, O_H, O_U, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1747 */ { UD_Ivmovsd, O_V, O_Mq, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1748 */ { UD_Ivmovsd, O_U, O_H, O_V, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1749 */ { UD_Ivmovsd, O_Mq, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1750 */ { UD_Ivmovss, O_V, O_H, O_U, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1751 */ { UD_Ivmovss, O_V, O_Md, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1752 */ { UD_Ivmovss, O_U, O_H, O_V, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1753 */ { UD_Ivmovss, O_Md, O_V, O_NONE, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1754 */ { UD_Ivpblendvb, O_V, O_H, O_W, O_L, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1755 */ { UD_Ivpsllw, O_V, O_H, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1756 */ { UD_Ivpsllw, O_H, O_V, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1757 */ { UD_Ivpslld, O_V, O_H, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1758 */ { UD_Ivpslld, O_H, O_V, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1759 */ { UD_Ivpsllq, O_V, O_H, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
  /* 1760 */ { UD_Ivpsllq, O_H, O_V, O_W, O_NONE, P_aso|P_rexr|P_rexx|P_rexb },
};


const char* ud_mnemonics_str[] = {
    "aaa",
    "aad",
    "aam",
    "aas",
    "adc",
    "add",
    "addpd",
    "addps",
    "addsd",
    "addss",
    "addsubpd",
    "addsubps",
    "aesdec",
    "aesdeclast",
    "aesenc",
    "aesenclast",
    "aesimc",
    "aeskeygenassist",
    "and",
    "andnpd",
    "andnps",
    "andpd",
    "andps",
    "arpl",
    "blendpd",
    "blendps",
    "blendvpd",
    "blendvps",
    "bound",
    "bsf",
    "bsr",
    "bswap",
    "bt",
    "btc",
    "btr",
    "bts",
    "call",
    "cbw",
    "cdq",
    "cdqe",
    "clc",
    "cld",
    "clflush",
    "clgi",
    "cli",
    "clts",
    "cmc",
    "cmova",
    "cmovae",
    "cmovb",
    "cmovbe",
    "cmovg",
    "cmovge",
    "cmovl",
    "cmovle",
    "cmovno",
    "cmovnp",
    "cmovns",
    "cmovnz",
    "cmovo",
    "cmovp",
    "cmovs",
    "cmovz",
    "cmp",
    "cmppd",
    "cmpps",
    "cmpsb",
    "cmpsd",
    "cmpsq",
    "cmpss",
    "cmpsw",
    "cmpxchg",
    "cmpxchg16b",
    "cmpxchg8b",
    "comisd",
    "comiss",
    "cpuid",
    "cqo",
    "crc32",
    "cvtdq2pd",
    "cvtdq2ps",
    "cvtpd2dq",
    "cvtpd2pi",
    "cvtpd2ps",
    "cvtpi2pd",
    "cvtpi2ps",
    "cvtps2dq",
    "cvtps2pd",
    "cvtps2pi",
    "cvtsd2si",
    "cvtsd2ss",
    "cvtsi2sd",
    "cvtsi2ss",
    "cvtss2sd",
    "cvtss2si",
    "cvttpd2dq",
    "cvttpd2pi",
    "cvttps2dq",
    "cvttps2pi",
    "cvttsd2si",
    "cvttss2si",
    "cwd",
    "cwde",
    "daa",
    "das",
    "dec",
    "div",
    "divpd",
    "divps",
    "divsd",
    "divss",
    "dppd",
    "dpps",
    "emms",
    "enter",
    "extractps",
    "f2xm1",
    "fabs",
    "fadd",
    "faddp",
    "fbld",
    "fbstp",
    "fchs",
    "fclex",
    "fcmovb",
    "fcmovbe",
    "fcmove",
    "fcmovnb",
    "fcmovnbe",
    "fcmovne",
    "fcmovnu",
    "fcmovu",
    "fcom",
    "fcom2",
    "fcomi",
    "fcomip",
    "fcomp",
    "fcomp3",
    "fcomp5",
    "fcompp",
    "fcos",
    "fdecstp",
    "fdiv",
    "fdivp",
    "fdivr",
    "fdivrp",
    "femms",
    "ffree",
    "ffreep",
    "fiadd",
    "ficom",
    "ficomp",
    "fidiv",
    "fidivr",
    "fild",
    "fimul",
    "fincstp",
    "fist",
    "fistp",
    "fisttp",
    "fisub",
    "fisubr",
    "fld",
    "fld1",
    "fldcw",
    "fldenv",
    "fldl2e",
    "fldl2t",
    "fldlg2",
    "fldln2",
    "fldpi",
    "fldz",
    "fmul",
    "fmulp",
    "fndisi",
    "fneni",
    "fninit",
    "fnop",
    "fnsave",
    "fnsetpm",
    "fnstcw",
    "fnstenv",
    "fnstsw",
    "fpatan",
    "fprem",
    "fprem1",
    "fptan",
    "frndint",
    "frstor",
    "frstpm",
    "fscale",
    "fsin",
    "fsincos",
    "fsqrt",
    "fst",
    "fstp",
    "fstp1",
    "fstp8",
    "fstp9",
    "fsub",
    "fsubp",
    "fsubr",
    "fsubrp",
    "ftst",
    "fucom",
    "fucomi",
    "fucomip",
    "fucomp",
    "fucompp",
    "fxam",
    "fxch",
    "fxch4",
    "fxch7",
    "fxrstor",
    "fxsave",
    "fxtract",
    "fyl2x",
    "fyl2xp1",
    "getsec",
    "haddpd",
    "haddps",
    "hlt",
    "hsubpd",
    "hsubps",
    "idiv",
    "imul",
    "in",
    "inc",
    "insb",
    "insd",
    "insertps",
    "insw",
    "int",
    "int1",
    "int3",
    "into",
    "invd",
    "invept",
    "invlpg",
    "invlpga",
    "invvpid",
    "iretd",
    "iretq",
    "iretw",
    "ja",
    "jae",
    "jb",
    "jbe",
    "jcxz",
    "jecxz",
    "jg",
    "jge",
    "jl",
    "jle",
    "jmp",
    "jno",
    "jnp",
    "jns",
    "jnz",
    "jo",
    "jp",
    "jrcxz",
    "js",
    "jz",
    "lahf",
    "lar",
    "lddqu",
    "ldmxcsr",
    "lds",
    "lea",
    "leave",
    "les",
    "lfence",
    "lfs",
    "lgdt",
    "lgs",
    "lidt",
    "lldt",
    "lmsw",
    "lock",
    "lodsb",
    "lodsd",
    "lodsq",
    "lodsw",
    "loop",
    "loope",
    "loopne",
    "lsl",
    "lss",
    "ltr",
    "maskmovdqu",
    "maskmovq",
    "maxpd",
    "maxps",
    "maxsd",
    "maxss",
    "mfence",
    "minpd",
    "minps",
    "minsd",
    "minss",
    "monitor",
    "montmul",
    "mov",
    "movapd",
    "movaps",
    "movbe",
    "movd",
    "movddup",
    "movdq2q",
    "movdqa",
    "movdqu",
    "movhlps",
    "movhpd",
    "movhps",
    "movlhps",
    "movlpd",
    "movlps",
    "movmskpd",
    "movmskps",
    "movntdq",
    "movntdqa",
    "movnti",
    "movntpd",
    "movntps",
    "movntq",
    "movq",
    "movq2dq",
    "movsb",
    "movsd",
    "movshdup",
    "movsldup",
    "movsq",
    "movss",
    "movsw",
    "movsx",
    "movsxd",
    "movupd",
    "movups",
    "movzx",
    "mpsadbw",
    "mul",
    "mulpd",
    "mulps",
    "mulsd",
    "mulss",
    "mwait",
    "neg",
    "nop",
    "not",
    "or",
    "orpd",
    "orps",
    "out",
    "outsb",
    "outsd",
    "outsw",
    "pabsb",
    "pabsd",
    "pabsw",
    "packssdw",
    "packsswb",
    "packusdw",
    "packuswb",
    "paddb",
    "paddd",
    "paddq",
    "paddsb",
    "paddsw",
    "paddusb",
    "paddusw",
    "paddw",
    "palignr",
    "pand",
    "pandn",
    "pavgb",
    "pavgusb",
    "pavgw",
    "pblendvb",
    "pblendw",
    "pclmulqdq",
    "pcmpeqb",
    "pcmpeqd",
    "pcmpeqq",
    "pcmpeqw",
    "pcmpestri",
    "pcmpestrm",
    "pcmpgtb",
    "pcmpgtd",
    "pcmpgtq",
    "pcmpgtw",
    "pcmpistri",
    "pcmpistrm",
    "pextrb",
    "pextrd",
    "pextrq",
    "pextrw",
    "pf2id",
    "pf2iw",
    "pfacc",
    "pfadd",
    "pfcmpeq",
    "pfcmpge",
    "pfcmpgt",
    "pfmax",
    "pfmin",
    "pfmul",
    "pfnacc",
    "pfpnacc",
    "pfrcp",
    "pfrcpit1",
    "pfrcpit2",
    "pfrsqit1",
    "pfrsqrt",
    "pfsub",
    "pfsubr",
    "phaddd",
    "phaddsw",
    "phaddw",
    "phminposuw",
    "phsubd",
    "phsubsw",
    "phsubw",
    "pi2fd",
    "pi2fw",
    "pinsrb",
    "pinsrd",
    "pinsrq",
    "pinsrw",
    "pmaddubsw",
    "pmaddwd",
    "pmaxsb",
    "pmaxsd",
    "pmaxsw",
    "pmaxub",
    "pmaxud",
    "pmaxuw",
    "pminsb",
    "pminsd",
    "pminsw",
    "pminub",
    "pminud",
    "pminuw",
    "pmovmskb",
    "pmovsxbd",
    "pmovsxbq",
    "pmovsxbw",
    "pmovsxdq",
    "pmovsxwd",
    "pmovsxwq",
    "pmovzxbd",
    "pmovzxbq",
    "pmovzxbw",
    "pmovzxdq",
    "pmovzxwd",
    "pmovzxwq",
    "pmuldq",
    "pmulhrsw",
    "pmulhrw",
    "pmulhuw",
    "pmulhw",
    "pmulld",
    "pmullw",
    "pmuludq",
    "pop",
    "popa",
    "popad",
    "popcnt",
    "popfd",
    "popfq",
    "popfw",
    "por",
    "prefetch",
    "prefetchnta",
    "prefetcht0",
    "prefetcht1",
    "prefetcht2",
    "psadbw",
    "pshufb",
    "pshufd",
    "pshufhw",
    "pshuflw",
    "pshufw",
    "psignb",
    "psignd",
    "psignw",
    "pslld",
    "pslldq",
    "psllq",
    "psllw",
    "psrad",
    "psraw",
    "psrld",
    "psrldq",
    "psrlq",
    "psrlw",
    "psubb",
    "psubd",
    "psubq",
    "psubsb",
    "psubsw",
    "psubusb",
    "psubusw",
    "psubw",
    "pswapd",
    "ptest",
    "punpckhbw",
    "punpckhdq",
    "punpckhqdq",
    "punpckhwd",
    "punpcklbw",
    "punpckldq",
    "punpcklqdq",
    "punpcklwd",
    "push",
    "pusha",
    "pushad",
    "pushfd",
    "pushfq",
    "pushfw",
    "pxor",
    "rcl",
    "rcpps",
    "rcpss",
    "rcr",
    "rdmsr",
    "rdpmc",
    "rdrand",
    "rdtsc",
    "rdtscp",
    "rep",
    "repne",
    "ret",
    "retf",
    "rol",
    "ror",
    "roundpd",
    "roundps",
    "roundsd",
    "roundss",
    "rsm",
    "rsqrtps",
    "rsqrtss",
    "sahf",
    "salc",
    "sar",
    "sbb",
    "scasb",
    "scasd",
    "scasq",
    "scasw",
    "seta",
    "setae",
    "setb",
    "setbe",
    "setg",
    "setge",
    "setl",
    "setle",
    "setno",
    "setnp",
    "setns",
    "setnz",
    "seto",
    "setp",
    "sets",
    "setz",
    "sfence",
    "sgdt",
    "shl",
    "shld",
    "shr",
    "shrd",
    "shufpd",
    "shufps",
    "sidt",
    "skinit",
    "sldt",
    "smsw",
    "sqrtpd",
    "sqrtps",
    "sqrtsd",
    "sqrtss",
    "stc",
    "std",
    "stgi",
    "sti",
    "stmxcsr",
    "stosb",
    "stosd",
    "stosq",
    "stosw",
    "str",
    "sub",
    "subpd",
    "subps",
    "subsd",
    "subss",
    "swapgs",
    "syscall",
    "sysenter",
    "sysexit",
    "sysret",
    "test",
    "ucomisd",
    "ucomiss",
    "ud2",
    "unpckhpd",
    "unpckhps",
    "unpcklpd",
    "unpcklps",
    "vaddpd",
    "vaddps",
    "vaddsd",
    "vaddss",
    "vaddsubpd",
    "vaddsubps",
    "vaesdec",
    "vaesdeclast",
    "vaesenc",
    "vaesenclast",
    "vaesimc",
    "vaeskeygenassist",
    "vandnpd",
    "vandnps",
    "vandpd",
    "vandps",
    "vblendpd",
    "vblendps",
    "vblendvpd",
    "vblendvps",
    "vbroadcastsd",
    "vbroadcastss",
    "vcmppd",
    "vcmpps",
    "vcmpsd",
    "vcmpss",
    "vcomisd",
    "vcomiss",
    "vcvtdq2pd",
    "vcvtdq2ps",
    "vcvtpd2dq",
    "vcvtpd2ps",
    "vcvtps2dq",
    "vcvtps2pd",
    "vcvtsd2si",
    "vcvtsd2ss",
    "vcvtsi2sd",
    "vcvtsi2ss",
    "vcvtss2sd",
    "vcvtss2si",
    "vcvttpd2dq",
    "vcvttps2dq",
    "vcvttsd2si",
    "vcvttss2si",
    "vdivpd",
    "vdivps",
    "vdivsd",
    "vdivss",
    "vdppd",
    "vdpps",
    "verr",
    "verw",
    "vextractf128",
    "vextractps",
    "vhaddpd",
    "vhaddps",
    "vhsubpd",
    "vhsubps",
    "vinsertf128",
    "vinsertps",
    "vlddqu",
    "vmaskmovdqu",
    "vmaskmovpd",
    "vmaskmovps",
    "vmaxpd",
    "vmaxps",
    "vmaxsd",
    "vmaxss",
    "vmcall",
    "vmclear",
    "vminpd",
    "vminps",
    "vminsd",
    "vminss",
    "vmlaunch",
    "vmload",
    "vmmcall",
    "vmovapd",
    "vmovaps",
    "vmovd",
    "vmovddup",
    "vmovdqa",
    "vmovdqu",
    "vmovhlps",
    "vmovhpd",
    "vmovhps",
    "vmovlhps",
    "vmovlpd",
    "vmovlps",
    "vmovmskpd",
    "vmovmskps",
    "vmovntdq",
    "vmovntdqa",
    "vmovntpd",
    "vmovntps",
    "vmovq",
    "vmovsd",
    "vmovshdup",
    "vmovsldup",
    "vmovss",
    "vmovupd",
    "vmovups",
    "vmpsadbw",
    "vmptrld",
    "vmptrst",
    "vmread",
    "vmresume",
    "vmrun",
    "vmsave",
    "vmulpd",
    "vmulps",
    "vmulsd",
    "vmulss",
    "vmwrite",
    "vmxoff",
    "vmxon",
    "vorpd",
    "vorps",
    "vpabsb",
    "vpabsd",
    "vpabsw",
    "vpackssdw",
    "vpacksswb",
    "vpackusdw",
    "vpackuswb",
    "vpaddb",
    "vpaddd",
    "vpaddq",
    "vpaddsb",
    "vpaddsw",
    "vpaddusb",
    "vpaddusw",
    "vpaddw",
    "vpalignr",
    "vpand",
    "vpandn",
    "vpavgb",
    "vpavgw",
    "vpblendvb",
    "vpblendw",
    "vpclmulqdq",
    "vpcmpeqb",
    "vpcmpeqd",
    "vpcmpeqq",
    "vpcmpeqw",
    "vpcmpestri",
    "vpcmpestrm",
    "vpcmpgtb",
    "vpcmpgtd",
    "vpcmpgtq",
    "vpcmpgtw",
    "vpcmpistri",
    "vpcmpistrm",
    "vperm2f128",
    "vpermilpd",
    "vpermilps",
    "vpextrb",
    "vpextrd",
    "vpextrq",
    "vpextrw",
    "vphaddd",
    "vphaddsw",
    "vphaddw",
    "vphminposuw",
    "vphsubd",
    "vphsubsw",
    "vphsubw",
    "vpinsrb",
    "vpinsrd",
    "vpinsrq",
    "vpinsrw",
    "vpmaddubsw",
    "vpmaddwd",
    "vpmaxsb",
    "vpmaxsd",
    "vpmaxsw",
    "vpmaxub",
    "vpmaxud",
    "vpmaxuw",
    "vpminsb",
    "vpminsd",
    "vpminsw",
    "vpminub",
    "vpminud",
    "vpminuw",
    "vpmovmskb",
    "vpmovsxbd",
    "vpmovsxbq",
    "vpmovsxbw",
    "vpmovsxwd",
    "vpmovsxwq",
    "vpmovzxbd",
    "vpmovzxbq",
    "vpmovzxbw",
    "vpmovzxdq",
    "vpmovzxwd",
    "vpmovzxwq",
    "vpmuldq",
    "vpmulhrsw",
    "vpmulhuw",
    "vpmulhw",
    "vpmulld",
    "vpmullw",
    "vpor",
    "vpsadbw",
    "vpshufb",
    "vpshufd",
    "vpshufhw",
    "vpshuflw",
    "vpsignb",
    "vpsignd",
    "vpsignw",
    "vpslld",
    "vpslldq",
    "vpsllq",
    "vpsllw",
    "vpsrad",
    "vpsraw",
    "vpsrld",
    "vpsrldq",
    "vpsrlq",
    "vpsrlw",
    "vpsubb",
    "vpsubd",
    "vpsubq",
    "vpsubsb",
    "vpsubsw",
    "vpsubusb",
    "vpsubusw",
    "vpsubw",
    "vptest",
    "vpunpckhbw",
    "vpunpckhdq",
    "vpunpckhqdq",
    "vpunpckhwd",
    "vpunpcklbw",
    "vpunpckldq",
    "vpunpcklqdq",
    "vpunpcklwd",
    "vpxor",
    "vrcpps",
    "vrcpss",
    "vroundpd",
    "vroundps",
    "vroundsd",
    "vroundss",
    "vrsqrtps",
    "vrsqrtss",
    "vshufpd",
    "vshufps",
    "vsqrtpd",
    "vsqrtps",
    "vsqrtsd",
    "vsqrtss",
    "vstmxcsr",
    "vsubpd",
    "vsubps",
    "vsubsd",
    "vsubss",
    "vtestpd",
    "vtestps",
    "vucomisd",
    "vucomiss",
    "vunpckhpd",
    "vunpckhps",
    "vunpcklpd",
    "vunpcklps",
    "vxorpd",
    "vxorps",
    "vzeroall",
    "vzeroupper",
    "wait",
    "wbinvd",
    "wrmsr",
    "xadd",
    "xchg",
    "xcryptcbc",
    "xcryptcfb",
    "xcryptctr",
    "xcryptecb",
    "xcryptofb",
    "xgetbv",
    "xlatb",
    "xor",
    "xorpd",
    "xorps",
    "xrstor",
    "xsave",
    "xsetbv",
    "xsha1",
    "xsha256",
    "xstore",
    "invalid",
    "3dnow",
    "none",
    "db",
    "pause"
};
