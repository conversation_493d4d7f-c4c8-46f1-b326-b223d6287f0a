// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=b3339627f92d31a68d36574fdd7c85db0842ea63$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_V8CONTEXT_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_V8CONTEXT_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_v8_capi.h"
#include "include/cef_v8.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefV8ContextCToCpp : public CefCToCppRefCounted<CefV8ContextCToCpp,
                                                      CefV8Context,
                                                      cef_v8context_t> {
 public:
  CefV8ContextCToCpp();
  virtual ~CefV8ContextCToCpp();

  // CefV8Context methods.
  CefRefPtr<CefTaskRunner> GetTaskRunner() override;
  bool IsValid() override;
  CefRefPtr<CefBrowser> GetBrowser() override;
  CefRefPtr<CefFrame> GetFrame() override;
  CefRefPtr<CefV8Value> GetGlobal() override;
  bool Enter() override;
  bool Exit() override;
  bool IsSame(CefRefPtr<CefV8Context> that) override;
  bool Eval(const CefString& code,
            const CefString& script_url,
            int start_line,
            CefRefPtr<CefV8Value>& retval,
            CefRefPtr<CefV8Exception>& exception) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_V8CONTEXT_CTOCPP_H_
