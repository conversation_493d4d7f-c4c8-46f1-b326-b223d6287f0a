#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var a = 1;
  switch(a) {
   case 1: return 2;
   case 2: return 3;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 30
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(7),
                B(Jump), U8(8),
  /*   66 S> */ B(LdaSmi), I8(2),
  /*   75 S> */ B(Return),
  /*   85 S> */ B(LdaSmi), I8(3),
  /*   94 S> */ B(Return),
                B(LdaUndefined),
  /*   97 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case 1: a = 2; break;
   case 2: a = 3; break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 34
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(9),
                B(Jump), U8(12),
  /*   66 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   73 S> */ B(Jump), U8(7),
  /*   89 S> */ B(LdaSmi), I8(3),
                B(Star0),
  /*   96 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  105 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case 1: a = 2; // fall-through
   case 2: a = 3; break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 32
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(7),
                B(Jump), U8(10),
  /*   66 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   98 S> */ B(LdaSmi), I8(3),
                B(Star0),
  /*  105 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  114 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case 2: break;
   case 3: break;
   default: a = 1; break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 33
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(2),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(3),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(6),
                B(Jump), U8(6),
  /*   66 S> */ B(Jump), U8(9),
  /*   82 S> */ B(Jump), U8(7),
  /*   99 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*  106 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  115 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(typeof(a)) {
   case 2: a = 1; break;
   case 3: a = 2; break;
   default: a = 3; break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 38
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(TypeOf),
                B(Star1),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(3),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(9),
                B(Jump), U8(12),
  /*   74 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   81 S> */ B(Jump), U8(12),
  /*   97 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  104 S> */ B(Jump), U8(7),
  /*  121 S> */ B(LdaSmi), I8(3),
                B(Star0),
  /*  128 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  137 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case typeof(a): a = 1; break;
   default: a = 2; break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 26
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(TypeOf),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(4),
                B(Jump), U8(7),
  /*   74 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   81 S> */ B(Jump), U8(7),
  /*   98 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  105 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  114 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case 1:
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    a = 2;
    break;
   case 2:
    a = 3;
    break;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 223
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(198),
                B(Jump), U8(201),
  /*   68 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   77 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   86 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   95 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  104 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  113 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  122 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  131 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  140 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  149 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  158 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  167 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  176 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  185 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  194 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  203 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  212 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  221 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  230 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  239 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  248 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  257 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  266 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  275 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  284 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  293 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  302 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  311 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  320 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  329 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  338 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  347 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  356 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  365 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  374 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  383 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  392 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  401 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  410 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  419 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  428 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  437 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  446 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  455 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  464 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  473 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  482 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  491 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  500 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  509 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  518 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  527 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  536 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  545 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  554 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  563 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  572 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  581 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  590 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  599 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  608 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  617 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  626 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  635 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  644 S> */ B(Jump), U8(7),
  /*  662 S> */ B(LdaSmi), I8(3),
                B(Star0),
  /*  671 S> */ B(Jump), U8(2),
                B(LdaUndefined),
  /*  680 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1;
  switch(a) {
   case 1: 
     switch(a + 1) {
        case 2 : a = 1; break;
        default : a = 2; break;
     }  // fall-through
   case 2: a = 3;
  }
"
frame size: 2
parameter count: 1
bytecode array length: 52
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
                B(TestEqualStrict), R(0), U8(0),
                B(Mov), R(0), R(1),
                B(JumpIfTrue), U8(11),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(0),
                B(JumpIfTrue), U8(29),
                B(Jump), U8(30),
  /*   70 S> */ B(Ldar), R(0),
  /*   79 E> */ B(AddSmi), I8(1), U8(1),
                B(Star1),
                B(LdaSmi), I8(2),
                B(TestEqualStrict), R(1), U8(2),
                B(JumpIfTrue), U8(4),
                B(Jump), U8(7),
  /*  101 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*  108 S> */ B(Jump), U8(7),
  /*  131 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*  138 S> */ B(Jump), U8(2),
  /*  176 S> */ B(LdaSmi), I8(3),
                B(Star0),
                B(LdaUndefined),
  /*  185 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

