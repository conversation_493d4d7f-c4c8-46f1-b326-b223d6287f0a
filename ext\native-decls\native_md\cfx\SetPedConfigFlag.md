---
ns: CFX
apiset: server
---
## SET_PED_CONFIG_FLAG

```c
void SET_PED_CONFIG_FLAG(Ped ped, int flagId, BOOL value);
```

```cpp
// Potential names and hash collisions included as comments
enum ePedConfigFlags {
CPED_CONFIG_FLAG_CreatedByFactory = 0,
CPED_CONFIG_FLAG_CanBeShotInVehicle = 1,
CPED_CONFIG_FLAG_NoCriticalHits = 2,
CPED_CONFIG_FLAG_DrownsInWater = 3,
CPED_CONFIG_FLAG_DrownsInSinkingVehicle = 4,
CPED_CONFIG_FLAG_DiesInstantlyWhenSwimming = 5,
CPED_CONFIG_FLAG_HasBulletProofVest = 6,
CPED_CONFIG_FLAG_UpperBodyDamageAnimsOnly = 7,
CPED_CONFIG_FLAG_NeverFallOffSkis = 8,
CPED_CONFIG_FLAG_NeverEverTargetThisPed = 9,
CPED_CONFIG_FLAG_ThisPedIsATargetPriority = 10,
CPED_CONFIG_FLAG_TargettableWithNoLos = 11,
CPED_CONFIG_FLAG_DoesntListenToPlayerGroupCommands = 12,
CPED_CONFIG_FLAG_NeverLeavesGroup = 13,
CPED_CONFIG_FLAG_DoesntDropWeaponsWhenDead = 14,
CPED_CONFIG_FLAG_SetDelayedWeaponAsCurrent = 15,
CPED_CONFIG_FLAG_KeepTasksAfterCleanUp = 16,
CPED_CONFIG_FLAG_BlockNonTemporaryEvents = 17,
CPED_CONFIG_FLAG_HasAScriptBrain = 18,
CPED_CONFIG_FLAG_WaitingForScriptBrainToLoad = 19,
CPED_CONFIG_FLAG_AllowMedicsToReviveMe = 20,
CPED_CONFIG_FLAG_MoneyHasBeenGivenByScript = 21,
CPED_CONFIG_FLAG_NotAllowedToCrouch = 22,
CPED_CONFIG_FLAG_DeathPickupsPersist = 23,
CPED_CONFIG_FLAG_IgnoreSeenMelee = 24,
CPED_CONFIG_FLAG_ForceDieIfInjured = 25,
CPED_CONFIG_FLAG_DontDragMeOutCar = 26,
CPED_CONFIG_FLAG_StayInCarOnJack = 27,
CPED_CONFIG_FLAG_ForceDieInCar = 28,
CPED_CONFIG_FLAG_GetOutUndriveableVehicle = 29,
CPED_CONFIG_FLAG_WillRemainOnBoatAfterMissionEnds = 30,
CPED_CONFIG_FLAG_DontStoreAsPersistent = 31,
CPED_CONFIG_FLAG_WillFlyThroughWindscreen = 32,
CPED_CONFIG_FLAG_DieWhenRagdoll = 33,
CPED_CONFIG_FLAG_HasHelmet = 34,
CPED_CONFIG_FLAG_UseHelmet = 35,
CPED_CONFIG_FLAG_DontTakeOffHelmet = 36,
CPED_CONFIG_FLAG_HideInCutscene = 37,
CPED_CONFIG_FLAG_PedIsEnemyToPlayer = 38,
CPED_CONFIG_FLAG_DisableEvasiveDives = 39,
CPED_CONFIG_FLAG_PedGeneratesDeadBodyEvents = 40,
CPED_CONFIG_FLAG_DontAttackPlayerWithoutWantedLevel = 41,
CPED_CONFIG_FLAG_DontInfluenceWantedLevel = 42,
CPED_CONFIG_FLAG_DisablePlayerLockon = 43,
CPED_CONFIG_FLAG_DisableLockonToRandomPeds = 44,
CPED_CONFIG_FLAG_AllowLockonToFriendlyPlayers = 45,
_0xDB115BFA = 46,
CPED_CONFIG_FLAG_PedBeingDeleted = 47,
CPED_CONFIG_FLAG_BlockWeaponSwitching = 48,
CPED_CONFIG_FLAG_BlockGroupPedAimedAtResponse = 49,
CPED_CONFIG_FLAG_WillFollowLeaderAnyMeans = 50,
CPED_CONFIG_FLAG_BlippedByScript = 51,
CPED_CONFIG_FLAG_DrawRadarVisualField = 52,
CPED_CONFIG_FLAG_StopWeaponFiringOnImpact = 53,
CPED_CONFIG_FLAG_DissableAutoFallOffTests = 54,
CPED_CONFIG_FLAG_SteerAroundDeadBodies = 55,
CPED_CONFIG_FLAG_ConstrainToNavMesh = 56,
CPED_CONFIG_FLAG_SyncingAnimatedProps = 57,
CPED_CONFIG_FLAG_IsFiring = 58,
CPED_CONFIG_FLAG_WasFiring = 59,
CPED_CONFIG_FLAG_IsStanding = 60,
CPED_CONFIG_FLAG_WasStanding = 61,
CPED_CONFIG_FLAG_InVehicle = 62,
CPED_CONFIG_FLAG_OnMount = 63,
CPED_CONFIG_FLAG_AttachedToVehicle = 64,
CPED_CONFIG_FLAG_IsSwimming = 65,
CPED_CONFIG_FLAG_WasSwimming = 66,
CPED_CONFIG_FLAG_IsSkiing = 67,
CPED_CONFIG_FLAG_IsSitting = 68,
CPED_CONFIG_FLAG_KilledByStealth = 69,
CPED_CONFIG_FLAG_KilledByTakedown = 70,
CPED_CONFIG_FLAG_Knockedout = 71,
CPED_CONFIG_FLAG_ClearRadarBlipOnDeath = 72,
CPED_CONFIG_FLAG_JustGotOffTrain = 73,
CPED_CONFIG_FLAG_JustGotOnTrain = 74,
CPED_CONFIG_FLAG_UsingCoverPoint = 75,
CPED_CONFIG_FLAG_IsInTheAir = 76,
CPED_CONFIG_FLAG_KnockedUpIntoAir = 77,
CPED_CONFIG_FLAG_IsAimingGun = 78,
CPED_CONFIG_FLAG_HasJustLeftCar = 79,
CPED_CONFIG_FLAG_TargetWhenInjuredAllowed = 80,
CPED_CONFIG_FLAG_CurrLeftFootCollNM = 81,
CPED_CONFIG_FLAG_PrevLeftFootCollNM = 82,
CPED_CONFIG_FLAG_CurrRightFootCollNM = 83,
CPED_CONFIG_FLAG_PrevRightFootCollNM = 84,
CPED_CONFIG_FLAG_HasBeenBumpedInCar = 85,
CPED_CONFIG_FLAG_InWaterTaskQuitToClimbLadder = 86,
CPED_CONFIG_FLAG_NMTwoHandedWeaponBothHandsConstrained = 87,
CPED_CONFIG_FLAG_CreatedBloodPoolTimer = 88,
CPED_CONFIG_FLAG_DontActivateRagdollFromAnyPedImpact = 89,
CPED_CONFIG_FLAG_GroupPedFailedToEnterCover = 90,
CPED_CONFIG_FLAG_AlreadyChattedOnPhone = 91,
CPED_CONFIG_FLAG_AlreadyReactedToPedOnRoof = 92,
CPED_CONFIG_FLAG_ForcePedLoadCover = 93,
CPED_CONFIG_FLAG_BlockCoweringInCover = 94,
CPED_CONFIG_FLAG_BlockPeekingInCover = 95,
CPED_CONFIG_FLAG_JustLeftCarNotCheckedForDoors = 96,
CPED_CONFIG_FLAG_VaultFromCover = 97,
CPED_CONFIG_FLAG_AutoConversationLookAts = 98,
CPED_CONFIG_FLAG_UsingCrouchedPedCapsule = 99,
CPED_CONFIG_FLAG_HasDeadPedBeenReported = 100,
CPED_CONFIG_FLAG_ForcedAim = 101,
CPED_CONFIG_FLAG_SteersAroundPeds = 102,
CPED_CONFIG_FLAG_SteersAroundObjects = 103,
CPED_CONFIG_FLAG_OpenDoorArmIK = 104,
CPED_CONFIG_FLAG_ForceReload = 105,
CPED_CONFIG_FLAG_DontActivateRagdollFromVehicleImpact = 106,
CPED_CONFIG_FLAG_DontActivateRagdollFromBulletImpact = 107,
CPED_CONFIG_FLAG_DontActivateRagdollFromExplosions = 108,
CPED_CONFIG_FLAG_DontActivateRagdollFromFire = 109,
CPED_CONFIG_FLAG_DontActivateRagdollFromElectrocution = 110,
CPED_CONFIG_FLAG_IsBeingDraggedToSafety = 111,
CPED_CONFIG_FLAG_HasBeenDraggedToSafety = 112,
CPED_CONFIG_FLAG_KeepWeaponHolsteredUnlessFired = 113,
CPED_CONFIG_FLAG_ForceScriptControlledKnockout = 114,
CPED_CONFIG_FLAG_FallOutOfVehicleWhenKilled = 115,
CPED_CONFIG_FLAG_GetOutBurningVehicle = 116,
CPED_CONFIG_FLAG_BumpedByPlayer = 117,
CPED_CONFIG_FLAG_RunFromFiresAndExplosions = 118,
CPED_CONFIG_FLAG_TreatAsPlayerDuringTargeting = 119,
CPED_CONFIG_FLAG_IsHandCuffed = 120,
CPED_CONFIG_FLAG_IsAnkleCuffed = 121,
CPED_CONFIG_FLAG_DisableMelee = 122,
CPED_CONFIG_FLAG_DisableUnarmedDrivebys = 123,
CPED_CONFIG_FLAG_JustGetsPulledOutWhenElectrocuted = 124,
CPED_CONFIG_FLAG_UNUSED_REPLACE_ME = 125,
CPED_CONFIG_FLAG_WillNotHotwireLawEnforcementVehicle = 126,
CPED_CONFIG_FLAG_WillCommandeerRatherThanJack = 127,
CPED_CONFIG_FLAG_CanBeAgitated = 128,
CPED_CONFIG_FLAG_ForcePedToFaceLeftInCover = 129,
CPED_CONFIG_FLAG_ForcePedToFaceRightInCover = 130,
CPED_CONFIG_FLAG_BlockPedFromTurningInCover = 131,
CPED_CONFIG_FLAG_KeepRelationshipGroupAfterCleanUp = 132,
CPED_CONFIG_FLAG_ForcePedToBeDragged = 133,
CPED_CONFIG_FLAG_PreventPedFromReactingToBeingJacked = 134,
CPED_CONFIG_FLAG_IsScuba = 135,
CPED_CONFIG_FLAG_WillArrestRatherThanJack = 136,
CPED_CONFIG_FLAG_RemoveDeadExtraFarAway = 137,
CPED_CONFIG_FLAG_RidingTrain = 138,
CPED_CONFIG_FLAG_ArrestResult = 139,
CPED_CONFIG_FLAG_CanAttackFriendly = 140,
CPED_CONFIG_FLAG_WillJackAnyPlayer = 141,
CPED_CONFIG_FLAG_BumpedByPlayerVehicle = 142,
CPED_CONFIG_FLAG_DodgedPlayerVehicle = 143,
CPED_CONFIG_FLAG_WillJackWantedPlayersRatherThanStealCar = 144,
CPED_CONFIG_FLAG_NoCopWantedAggro = 145,
CPED_CONFIG_FLAG_DisableLadderClimbing = 146,
CPED_CONFIG_FLAG_StairsDetected = 147,
CPED_CONFIG_FLAG_SlopeDetected = 148,
CPED_CONFIG_FLAG_HelmetHasBeenShot = 149,
CPED_CONFIG_FLAG_CowerInsteadOfFlee = 150,
CPED_CONFIG_FLAG_CanActivateRagdollWhenVehicleUpsideDown = 151,
CPED_CONFIG_FLAG_AlwaysRespondToCriesForHelp = 152,
CPED_CONFIG_FLAG_DisableBloodPoolCreation = 153,
CPED_CONFIG_FLAG_ShouldFixIfNoCollision = 154,
CPED_CONFIG_FLAG_CanPerformArrest = 155,
CPED_CONFIG_FLAG_CanPerformUncuff = 156,
CPED_CONFIG_FLAG_CanBeArrested = 157,
CPED_CONFIG_FLAG_MoverConstrictedByOpposingCollisions = 158,
CPED_CONFIG_FLAG_PlayerPreferFrontSeatMP = 159,
CPED_CONFIG_FLAG_DontActivateRagdollFromImpactObject = 160,
CPED_CONFIG_FLAG_DontActivateRagdollFromMelee = 161,
CPED_CONFIG_FLAG_DontActivateRagdollFromWaterJet = 162,
CPED_CONFIG_FLAG_DontActivateRagdollFromDrowning = 163,
CPED_CONFIG_FLAG_DontActivateRagdollFromFalling = 164,
CPED_CONFIG_FLAG_DontActivateRagdollFromRubberBullet = 165,
CPED_CONFIG_FLAG_IsInjured = 166,
CPED_CONFIG_FLAG_DontEnterVehiclesInPlayersGroup = 167,
CPED_CONFIG_FLAG_SwimmingTasksRunning = 168,
CPED_CONFIG_FLAG_PreventAllMeleeTaunts = 169,
CPED_CONFIG_FLAG_ForceDirectEntry = 170,
CPED_CONFIG_FLAG_AlwaysSeeApproachingVehicles = 171,
CPED_CONFIG_FLAG_CanDiveAwayFromApproachingVehicles = 172,
CPED_CONFIG_FLAG_AllowPlayerToInterruptVehicleEntryExit = 173,
CPED_CONFIG_FLAG_OnlyAttackLawIfPlayerIsWanted = 174,
CPED_CONFIG_FLAG_PlayerInContactWithKinematicPed = 175,
CPED_CONFIG_FLAG_PlayerInContactWithSomethingOtherThanKinematicPed = 176,
CPED_CONFIG_FLAG_PedsJackingMeDontGetIn = 177,
CPED_CONFIG_FLAG_AdditionalRappellingPed = 178,
CPED_CONFIG_FLAG_PedIgnoresAnimInterruptEvents = 179,
CPED_CONFIG_FLAG_IsInCustody = 180,
CPED_CONFIG_FLAG_ForceStandardBumpReactionThresholds = 181,
CPED_CONFIG_FLAG_LawWillOnlyAttackIfPlayerIsWanted = 182,
CPED_CONFIG_FLAG_IsAgitated = 183,
CPED_CONFIG_FLAG_PreventAutoShuffleToDriversSeat = 184,
CPED_CONFIG_FLAG_UseKinematicModeWhenStationary = 185,
CPED_CONFIG_FLAG_EnableWeaponBlocking = 186,
CPED_CONFIG_FLAG_HasHurtStarted = 187,
CPED_CONFIG_FLAG_DisableHurt = 188,
CPED_CONFIG_FLAG_PlayerIsWeird = 189,
CPED_CONFIG_FLAG_PedHadPhoneConversation = 190,
CPED_CONFIG_FLAG_BeganCrossingRoad = 191,
CPED_CONFIG_FLAG_WarpIntoLeadersVehicle = 192,
CPED_CONFIG_FLAG_DoNothingWhenOnFootByDefault = 193,
CPED_CONFIG_FLAG_UsingScenario = 194,
CPED_CONFIG_FLAG_VisibleOnScreen = 195,
CPED_CONFIG_FLAG_DontCollideWithKinematic = 196,
CPED_CONFIG_FLAG_ActivateOnSwitchFromLowPhysicsLod = 197,
CPED_CONFIG_FLAG_DontActivateRagdollOnPedCollisionWhenDead = 198,
CPED_CONFIG_FLAG_DontActivateRagdollOnVehicleCollisionWhenDead = 199,
CPED_CONFIG_FLAG_HasBeenInArmedCombat = 200,
CPED_CONFIG_FLAG_UseDiminishingAmmoRate = 201,
CPED_CONFIG_FLAG_Avoidance_Ignore_All = 202,
CPED_CONFIG_FLAG_Avoidance_Ignored_by_All = 203,
CPED_CONFIG_FLAG_Avoidance_Ignore_Group1 = 204,
CPED_CONFIG_FLAG_Avoidance_Member_of_Group1 = 205,
CPED_CONFIG_FLAG_ForcedToUseSpecificGroupSeatIndex = 206,
CPED_CONFIG_FLAG_LowPhysicsLodMayPlaceOnNavMesh = 207,
CPED_CONFIG_FLAG_DisableExplosionReactions = 208,
CPED_CONFIG_FLAG_DodgedPlayer = 209,
CPED_CONFIG_FLAG_WaitingForPlayerControlInterrupt = 210,
CPED_CONFIG_FLAG_ForcedToStayInCover = 211,
CPED_CONFIG_FLAG_GeneratesSoundEvents = 212,
CPED_CONFIG_FLAG_ListensToSoundEvents = 213,
CPED_CONFIG_FLAG_AllowToBeTargetedInAVehicle = 214,
CPED_CONFIG_FLAG_WaitForDirectEntryPointToBeFreeWhenExiting = 215,
CPED_CONFIG_FLAG_OnlyRequireOnePressToExitVehicle = 216,
CPED_CONFIG_FLAG_ForceExitToSkyDive = 217,
CPED_CONFIG_FLAG_SteersAroundVehicles = 218,
CPED_CONFIG_FLAG_AllowPedInVehiclesOverrideTaskFlags = 219,
CPED_CONFIG_FLAG_DontEnterLeadersVehicle = 220,
CPED_CONFIG_FLAG_DisableExitToSkyDive = 221,
CPED_CONFIG_FLAG_ScriptHasDisabledCollision = 222,
CPED_CONFIG_FLAG_UseAmbientModelScaling = 223,
CPED_CONFIG_FLAG_DontWatchFirstOnNextHurryAway = 224,
CPED_CONFIG_FLAG_DisablePotentialToBeWalkedIntoResponse = 225,
CPED_CONFIG_FLAG_DisablePedAvoidance = 226,
CPED_CONFIG_FLAG_ForceRagdollUponDeath = 227,
CPED_CONFIG_FLAG_CanLosePropsOnDamage = 228,
CPED_CONFIG_FLAG_DisablePanicInVehicle = 229,
CPED_CONFIG_FLAG_AllowedToDetachTrailer = 230,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromFront = 231,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromBack = 232,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromLeft = 233,
CPED_CONFIG_FLAG_HasShotBeenReactedToFromRight = 234,
CPED_CONFIG_FLAG_AllowBlockDeadPedRagdollActivation = 235,
CPED_CONFIG_FLAG_IsHoldingProp = 236,
CPED_CONFIG_FLAG_BlocksPathingWhenDead = 237,
CPED_CONFIG_FLAG_ForcePlayNormalScenarioExitOnNextScriptCommand = 238,
CPED_CONFIG_FLAG_ForcePlayImmediateScenarioExitOnNextScriptCommand = 239,
CPED_CONFIG_FLAG_ForceSkinCharacterCloth = 240,
CPED_CONFIG_FLAG_LeaveEngineOnWhenExitingVehicles = 241,
CPED_CONFIG_FLAG_PhoneDisableTextingAnimations = 242,
CPED_CONFIG_FLAG_PhoneDisableTalkingAnimations = 243,
CPED_CONFIG_FLAG_PhoneDisableCameraAnimations = 244,
CPED_CONFIG_FLAG_DisableBlindFiringInShotReactions = 245,
CPED_CONFIG_FLAG_AllowNearbyCoverUsage = 246,
CPED_CONFIG_FLAG_InStrafeTransition = 247,
CPED_CONFIG_FLAG_CanPlayInCarIdles = 248,
CPED_CONFIG_FLAG_CanAttackNonWantedPlayerAsLaw = 249,
CPED_CONFIG_FLAG_WillTakeDamageWhenVehicleCrashes = 250,
CPED_CONFIG_FLAG_AICanDrivePlayerAsRearPassenger = 251,
CPED_CONFIG_FLAG_PlayerCanJackFriendlyPlayers = 252,
CPED_CONFIG_FLAG_OnStairs = 253,
CPED_CONFIG_FLAG_SimulatingAiming = 254,
CPED_CONFIG_FLAG_AIDriverAllowFriendlyPassengerSeatEntry = 255,
CPED_CONFIG_FLAG_ParentCarIsBeingRemoved = 256,
CPED_CONFIG_FLAG_AllowMissionPedToUseInjuredMovement = 257,
CPED_CONFIG_FLAG_CanLoseHelmetOnDamage = 258,
CPED_CONFIG_FLAG_NeverDoScenarioExitProbeChecks = 259,
CPED_CONFIG_FLAG_SuppressLowLODRagdollSwitchWhenCorpseSettles = 260,
CPED_CONFIG_FLAG_PreventUsingLowerPrioritySeats = 261,
CPED_CONFIG_FLAG_JustLeftVehicleNeedsReset = 262,
CPED_CONFIG_FLAG_TeleportIfCantReachPlayer = 263,
CPED_CONFIG_FLAG_PedsInVehiclePositionNeedsReset = 264,
CPED_CONFIG_FLAG_PedsFullyInSeat = 265,
CPED_CONFIG_FLAG_AllowPlayerLockOnIfFriendly = 266,
CPED_CONFIG_FLAG_UseCameraHeadingForDesiredDirectionLockOnTest = 267,
CPED_CONFIG_FLAG_TeleportToLeaderVehicle = 268,
CPED_CONFIG_FLAG_Avoidance_Ignore_WeirdPedBuffer = 269,
CPED_CONFIG_FLAG_OnStairSlope = 270,
CPED_CONFIG_FLAG_HasPlayedNMGetup = 271,
CPED_CONFIG_FLAG_DontBlipCop = 272,
CPED_CONFIG_FLAG_SpawnedAtExtendedRangeScenario = 273,
CPED_CONFIG_FLAG_WalkAlongsideLeaderWhenClose = 274,
CPED_CONFIG_FLAG_KillWhenTrapped = 275,
CPED_CONFIG_FLAG_EdgeDetected = 276,
CPED_CONFIG_FLAG_AlwaysWakeUpPhysicsOfIntersectedPeds = 277,
CPED_CONFIG_FLAG_EquippedAmbientLoadOutWeapon = 278,
CPED_CONFIG_FLAG_AvoidTearGas = 279,
CPED_CONFIG_FLAG_StoppedSpeechUponFreezing = 280,
CPED_CONFIG_FLAG_DisableGoToWritheWhenInjured = 281,
CPED_CONFIG_FLAG_OnlyUseForcedSeatWhenEnteringHeliInGroup = 282,
CPED_CONFIG_FLAG_ThrownFromVehicleDueToExhaustion = 283,
CPED_CONFIG_FLAG_UpdateEnclosedSearchRegion = 284,
CPED_CONFIG_FLAG_DisableWeirdPedEvents = 285,
CPED_CONFIG_FLAG_ShouldChargeNow = 286,
CPED_CONFIG_FLAG_RagdollingOnBoat = 287,
CPED_CONFIG_FLAG_HasBrandishedWeapon = 288,
CPED_CONFIG_FLAG_AllowMinorReactionsAsMissionPed = 289,
CPED_CONFIG_FLAG_BlockDeadBodyShockingEventsWhenDead = 290,
CPED_CONFIG_FLAG_PedHasBeenSeen = 291,
CPED_CONFIG_FLAG_PedIsInReusePool = 292,
CPED_CONFIG_FLAG_PedWasReused = 293,
CPED_CONFIG_FLAG_DisableShockingEvents = 294,
CPED_CONFIG_FLAG_MovedUsingLowLodPhysicsSinceLastActive = 295,
CPED_CONFIG_FLAG_NeverReactToPedOnRoof = 296,
CPED_CONFIG_FLAG_ForcePlayFleeScenarioExitOnNextScriptCommand = 297,
CPED_CONFIG_FLAG_JustBumpedIntoVehicle = 298,
CPED_CONFIG_FLAG_DisableShockingDrivingOnPavementEvents = 299,
CPED_CONFIG_FLAG_ShouldThrowSmokeNow = 300,
CPED_CONFIG_FLAG_DisablePedConstraints = 301,
CPED_CONFIG_FLAG_ForceInitialPeekInCover = 302,
CPED_CONFIG_FLAG_CreatedByDispatch = 303,
CPED_CONFIG_FLAG_PointGunLeftHandSupporting = 304,
CPED_CONFIG_FLAG_DisableJumpingFromVehiclesAfterLeader = 305,
CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerPedImpact = 306,
CPED_CONFIG_FLAG_DontActivateRagdollFromAiRagdollImpact = 307,
CPED_CONFIG_FLAG_DontActivateRagdollFromPlayerRagdollImpact = 308,
CPED_CONFIG_FLAG_DisableQuadrupedSpring = 309,
CPED_CONFIG_FLAG_IsInCluster = 310,
CPED_CONFIG_FLAG_ShoutToGroupOnPlayerMelee = 311,
CPED_CONFIG_FLAG_IgnoredByAutoOpenDoors = 312,
CPED_CONFIG_FLAG_PreferInjuredGetup = 313,
CPED_CONFIG_FLAG_ForceIgnoreMeleeActiveCombatant = 314,
CPED_CONFIG_FLAG_CheckLoSForSoundEvents = 315,
CPED_CONFIG_FLAG_JackedAbandonedCar = 316,
CPED_CONFIG_FLAG_CanSayFollowedByPlayerAudio = 317,
CPED_CONFIG_FLAG_ActivateRagdollFromMinorPlayerContact = 318,
CPED_CONFIG_FLAG_HasPortablePickupAttached = 319,
CPED_CONFIG_FLAG_ForcePoseCharacterCloth = 320,
CPED_CONFIG_FLAG_HasClothCollisionBounds = 321,
CPED_CONFIG_FLAG_HasHighHeels = 322,
CPED_CONFIG_FLAG_TreatAsAmbientPedForDriverLockOn = 323,
CPED_CONFIG_FLAG_DontBehaveLikeLaw = 324,
CPED_CONFIG_FLAG_SpawnedAtScenario = 325,
CPED_CONFIG_FLAG_DisablePoliceInvestigatingBody = 326,
CPED_CONFIG_FLAG_DisableWritheShootFromGround = 327,
CPED_CONFIG_FLAG_LowerPriorityOfWarpSeats = 328,
CPED_CONFIG_FLAG_DisableTalkTo = 329,
CPED_CONFIG_FLAG_DontBlip = 330,
CPED_CONFIG_FLAG_IsSwitchingWeapon = 331,
CPED_CONFIG_FLAG_IgnoreLegIkRestrictions = 332,
CPED_CONFIG_FLAG_ScriptForceNoTimesliceIntelligenceUpdate = 333,
CPED_CONFIG_FLAG_JackedOutOfMyVehicle = 334,
CPED_CONFIG_FLAG_WentIntoCombatAfterBeingJacked = 335,
CPED_CONFIG_FLAG_DontActivateRagdollForVehicleGrab = 336,
CPED_CONFIG_FLAG_ForcePackageCharacterCloth = 337,
CPED_CONFIG_FLAG_DontRemoveWithValidOrder = 338,
CPED_CONFIG_FLAG_AllowTaskDoNothingTimeslicing = 339,
CPED_CONFIG_FLAG_ForcedToStayInCoverDueToPlayerSwitch = 340,
CPED_CONFIG_FLAG_ForceProneCharacterCloth = 341,
CPED_CONFIG_FLAG_NotAllowedToJackAnyPlayers = 342,
CPED_CONFIG_FLAG_InToStrafeTransition = 343,
CPED_CONFIG_FLAG_KilledByStandardMelee = 344,
CPED_CONFIG_FLAG_AlwaysLeaveTrainUponArrival = 345,
CPED_CONFIG_FLAG_ForcePlayDirectedNormalScenarioExitOnNextScriptCommand = 346,
CPED_CONFIG_FLAG_OnlyWritheFromWeaponDamage = 347,
CPED_CONFIG_FLAG_UseSloMoBloodVfx = 348,
CPED_CONFIG_FLAG_EquipJetpack = 349,
CPED_CONFIG_FLAG_PreventDraggedOutOfCarThreatResponse = 350,
CPED_CONFIG_FLAG_ScriptHasCompletelyDisabledCollision = 351,
CPED_CONFIG_FLAG_NeverDoScenarioNavChecks = 352,
CPED_CONFIG_FLAG_ForceSynchronousScenarioExitChecking = 353,
CPED_CONFIG_FLAG_ThrowingGrenadeWhileAiming = 354,
CPED_CONFIG_FLAG_HeadbobToRadioEnabled = 355,
CPED_CONFIG_FLAG_ForceDeepSurfaceCheck = 356,
CPED_CONFIG_FLAG_DisableDeepSurfaceAnims = 357,
CPED_CONFIG_FLAG_DontBlipNotSynced = 358,
CPED_CONFIG_FLAG_IsDuckingInVehicle = 359,
CPED_CONFIG_FLAG_PreventAutoShuffleToTurretSeat = 360,
CPED_CONFIG_FLAG_DisableEventInteriorStatusCheck = 361,
CPED_CONFIG_FLAG_HasReserveParachute = 362,
CPED_CONFIG_FLAG_UseReserveParachute = 363,
CPED_CONFIG_FLAG_TreatDislikeAsHateWhenInCombat = 364,
CPED_CONFIG_FLAG_OnlyUpdateTargetWantedIfSeen = 365,
CPED_CONFIG_FLAG_AllowAutoShuffleToDriversSeat = 366,
CPED_CONFIG_FLAG_DontActivateRagdollFromSmokeGrenade = 367,
CPED_CONFIG_FLAG_LinkMBRToOwnerOnChain = 368,
CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayer = 369,
CPED_CONFIG_FLAG_AmbientFriendBumpedByPlayerVehicle = 370,
CPED_CONFIG_FLAG_InFPSUnholsterTransition = 371,
CPED_CONFIG_FLAG_PreventReactingToSilencedCloneBullets = 372,
CPED_CONFIG_FLAG_DisableInjuredCryForHelpEvents = 373,
CPED_CONFIG_FLAG_NeverLeaveTrain = 374,
CPED_CONFIG_FLAG_DontDropJetpackOnDeath = 375,
CPED_CONFIG_FLAG_UseFPSUnholsterTransitionDuringCombatRoll = 376,
CPED_CONFIG_FLAG_ExitingFPSCombatRoll = 377,
CPED_CONFIG_FLAG_ScriptHasControlOfPlayer = 378,
CPED_CONFIG_FLAG_PlayFPSIdleFidgetsForProjectile = 379,
CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInBikes = 380,
CPED_CONFIG_FLAG_DisableAutoEquipHelmetsInAircraft = 381,
CPED_CONFIG_FLAG_WasPlayingFPSGetup = 382,
CPED_CONFIG_FLAG_WasPlayingFPSMeleeActionResult = 383,
CPED_CONFIG_FLAG_PreferNoPriorityRemoval = 384,
CPED_CONFIG_FLAG_FPSFidgetsAbortedOnFire = 385,
CPED_CONFIG_FLAG_ForceFPSIKWithUpperBodyAnim = 386,
CPED_CONFIG_FLAG_SwitchingCharactersInFirstPerson = 387,
CPED_CONFIG_FLAG_IsClimbingLadder = 388,
CPED_CONFIG_FLAG_HasBareFeet = 389,
CPED_CONFIG_FLAG_UNUSED_REPLACE_ME_2 = 390,
CPED_CONFIG_FLAG_GoOnWithoutVehicleIfItIsUnableToGetBackToRoad = 391,
CPED_CONFIG_FLAG_BlockDroppingHealthSnacksOnDeath = 392,
CPED_CONFIG_FLAG_ResetLastVehicleOnVehicleExit = 393,
CPED_CONFIG_FLAG_ForceThreatResponseToNonFriendToFriendMeleeActions = 394,
CPED_CONFIG_FLAG_DontRespondToRandomPedsDamage = 395,
CPED_CONFIG_FLAG_AllowContinuousThreatResponseWantedLevelUpdates = 396,
CPED_CONFIG_FLAG_KeepTargetLossResponseOnCleanup = 397,
CPED_CONFIG_FLAG_PlayersDontDragMeOutOfCar = 398,
CPED_CONFIG_FLAG_BroadcastRepondedToThreatWhenGoingToPointShooting = 399,
CPED_CONFIG_FLAG_IgnorePedTypeForIsFriendlyWith = 400,
CPED_CONFIG_FLAG_TreatNonFriendlyAsHateWhenInCombat = 401,
CPED_CONFIG_FLAG_DontLeaveVehicleIfLeaderNotInVehicle = 402,
CPED_CONFIG_FLAG_ChangeFromPermanentToAmbientPopTypeOnMigration = 403,
CPED_CONFIG_FLAG_AllowMeleeReactionIfMeleeProofIsOn = 404,
CPED_CONFIG_FLAG_UsingLowriderLeans = 405,
CPED_CONFIG_FLAG_UsingAlternateLowriderLeans = 406,
CPED_CONFIG_FLAG_UseNormalExplosionDamageWhenBlownUpInVehicle = 407,
CPED_CONFIG_FLAG_DisableHomingMissileLockForVehiclePedInside = 408,
CPED_CONFIG_FLAG_DisableTakeOffScubaGear = 409,
CPED_CONFIG_FLAG_IgnoreMeleeFistWeaponDamageMult = 410,
CPED_CONFIG_FLAG_LawPedsCanFleeFromNonWantedPlayer = 411,
CPED_CONFIG_FLAG_ForceBlipSecurityPedsIfPlayerIsWanted = 412,
CPED_CONFIG_FLAG_IsHolsteringWeapon = 413,
CPED_CONFIG_FLAG_UseGoToPointForScenarioNavigation = 414,
CPED_CONFIG_FLAG_DontClearLocalPassengersWantedLevel = 415,
CPED_CONFIG_FLAG_BlockAutoSwapOnWeaponPickups = 416,
CPED_CONFIG_FLAG_ThisPedIsATargetPriorityForAI = 417,
CPED_CONFIG_FLAG_IsSwitchingHelmetVisor = 418,
CPED_CONFIG_FLAG_ForceHelmetVisorSwitch = 419,
CPED_CONFIG_FLAG_IsPerformingVehicleMelee = 420,
CPED_CONFIG_FLAG_UseOverrideFootstepPtFx = 421,
CPED_CONFIG_FLAG_DisableVehicleCombat = 422,
CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamage = 423,
CPED_CONFIG_FLAG_AllowBikeAlternateAnimations = 424,
CPED_CONFIG_FLAG_TreatAsFriendlyForTargetingAndDamageNonSynced = 425,
CPED_CONFIG_FLAG_UseLockpickVehicleEntryAnimations = 426,
CPED_CONFIG_FLAG_IgnoreInteriorCheckForSprinting = 427,
CPED_CONFIG_FLAG_SwatHeliSpawnWithinLastSpottedLocation = 428,
CPED_CONFIG_FLAG_DisableStartEngine = 429,
CPED_CONFIG_FLAG_IgnoreBeingOnFire = 430,
CPED_CONFIG_FLAG_DisableTurretOrRearSeatPreference = 431,
CPED_CONFIG_FLAG_DisableWantedHelicopterSpawning = 432,
CPED_CONFIG_FLAG_UseTargetPerceptionForCreatingAimedAtEvents = 433,
CPED_CONFIG_FLAG_DisableHomingMissileLockon = 434,
CPED_CONFIG_FLAG_ForceIgnoreMaxMeleeActiveSupportCombatants = 435,
CPED_CONFIG_FLAG_StayInDefensiveAreaWhenInVehicle = 436,
CPED_CONFIG_FLAG_DontShoutTargetPosition = 437,
CPED_CONFIG_FLAG_DisableHelmetArmor = 438,
CPED_CONFIG_FLAG_CreatedByConcealedPlayer = 439,
CPED_CONFIG_FLAG_PermanentlyDisablePotentialToBeWalkedIntoResponse = 440,
CPED_CONFIG_FLAG_PreventVehExitDueToInvalidWeapon = 441,
CPED_CONFIG_FLAG_IgnoreNetSessionFriendlyFireCheckForAllowDamage = 442,
CPED_CONFIG_FLAG_DontLeaveCombatIfTargetPlayerIsAttackedByPolice = 443,
CPED_CONFIG_FLAG_CheckLockedBeforeWarp = 444,
CPED_CONFIG_FLAG_DontShuffleInVehicleToMakeRoom = 445,
CPED_CONFIG_FLAG_GiveWeaponOnGetup = 446,
CPED_CONFIG_FLAG_DontHitVehicleWithProjectiles = 447,
CPED_CONFIG_FLAG_DisableForcedEntryForOpenVehiclesFromTryLockedDoor = 448,
CPED_CONFIG_FLAG_FiresDummyRockets = 449,
CPED_CONFIG_FLAG_PedIsArresting = 450,
CPED_CONFIG_FLAG_IsDecoyPed = 451,
CPED_CONFIG_FLAG_HasEstablishedDecoy = 452,
CPED_CONFIG_FLAG_BlockDispatchedHelicoptersFromLanding = 453,
CPED_CONFIG_FLAG_DontCryForHelpOnStun = 454,
CPED_CONFIG_FLAG_HitByTranqWeapon = 455,
CPED_CONFIG_FLAG_CanBeIncapacitated = 456,
CPED_CONFIG_FLAG_ForcedAimFromArrest = 457,
CPED_CONFIG_FLAG_DontChangeTargetFromMelee = 458,
_0x4376ABF2 = 459,
CPED_CONFIG_FLAG_RagdollFloatsIndefinitely = 460,
CPED_CONFIG_FLAG_BlockElectricWeaponDamage = 461,
_0x262A3B8E = 462,
_0x1AA79A25 = 463,
}
```

**This is the server-side RPC native equivalent of the client native [SET\_PED\_CONFIG\_FLAG](?_0x1913FE4CBF41C463).**

## Parameters
* **ped**: 
* **flagId**: 
* **value**: 

