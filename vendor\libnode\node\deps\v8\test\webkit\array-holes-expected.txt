# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that arrays have holes that you can see the prototype through, not just missing values.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS var a = []; a.length = 1; showHoles(a) is '[hole]'
PASS var a = []; a[0] = undefined; showHoles(a) is '[undefined]'
PASS var a = []; a[0] = undefined; delete a[0]; showHoles(a) is '[hole]'
PASS showHoles([0, , 2]) is '[0, hole, 2]'
PASS showHoles([0, 1, ,]) is '[0, 1, hole]'
PASS showHoles([0, , 2].concat([3, , 5])) is '[0, hole, 2, 3, hole, 5]'
PASS showHoles([0, , 2, 3].reverse()) is '[3, 2, hole, 0]'
PASS a = [0, , 2, 3]; a.shift(); showHoles(a) is '[hole, 2, 3]'
PASS showHoles([0, , 2, 3].slice(0, 3)) is '[0, hole, 2]'
PASS showHoles([0, , 2, 3].sort()) is '[0, 2, 3, hole]'
PASS showHoles([0, undefined, 2, 3].sort()) is '[0, 2, 3, undefined]'
PASS a = [0, , 2, 3]; a.splice(2, 3, 5, 6); showHoles(a) is '[0, hole, 5, 6]'
PASS a = [0, , 2, 3]; a.unshift(4); showHoles(a) is '[4, 0, hole, 2, 3]'
PASS showHoles([0, , 2, 3].filter(returnTrue)) is '[0, 2, 3]'
PASS showHoles([0, undefined, 2, 3].filter(returnTrue)) is '[0, undefined, 2, 3]'
PASS showHoles([0, , 2, 3].map(returnTrue)) is '[true, hole, true, true]'
PASS showHoles([0, undefined, 2, 3].map(returnTrue)) is '[true, true, true, true]'
PASS a = []; [0, , 2, 3].every(addToArrayReturnTrue); showHoles(a) is '[0, 2, 3]'
PASS a = []; [0, undefined, 2, 3].every(addToArrayReturnTrue); showHoles(a) is '[0, undefined, 2, 3]'
PASS a = []; [0, , 2, 3].forEach(addToArray); showHoles(a) is '[0, 2, 3]'
PASS a = []; [0, undefined, 2, 3].forEach(addToArray); showHoles(a) is '[0, undefined, 2, 3]'
PASS a = []; [0, , 2, 3].some(addToArrayReturnFalse); showHoles(a) is '[0, 2, 3]'
PASS a = []; [0, undefined, 2, 3].some(addToArrayReturnFalse); showHoles(a) is '[0, undefined, 2, 3]'
PASS [0, , 2, 3].indexOf() is -1
PASS [0, undefined, 2, 3].indexOf() is 1
PASS [0, , 2, 3].lastIndexOf() is -1
PASS [0, undefined, 2, 3].lastIndexOf() is 1
PASS showHoles([0, , 2]) is '[0, hole, 2]'
PASS showHoles([0, 1, ,]) is '[0, 1, hole]'
PASS showHoles([0, , 2].concat([3, , 5])) is '[0, peekaboo, 2, 3, peekaboo, 5]'
PASS showHoles([0, , 2, 3].reverse()) is '[3, 2, peekaboo, 0]'
PASS a = [0, , 2, 3]; a.shift(); showHoles(a) is '[peekaboo, 2, 3]'
PASS showHoles([0, , 2, 3].slice(0, 3)) is '[0, peekaboo, 2]'
PASS showHoles([0, , 2, 3].sort()) is '[0, 2, 3, peekaboo]'
PASS showHoles([0, undefined, 2, 3].sort()) is '[0, 2, 3, undefined]'
PASS a = [0, , 2, 3]; a.splice(2, 3, 5, 6); showHoles(a) is '[0, hole, 5, 6]'
PASS a = [0, , 2, 3]; a.unshift(4); showHoles(a) is '[4, 0, peekaboo, 2, 3]'
PASS showHoles([0, , 2, 3].filter(returnTrue)) is '[0, peekaboo, 2, 3]'
PASS showHoles([0, undefined, 2, 3].filter(returnTrue)) is '[0, undefined, 2, 3]'
PASS showHoles([0, , 2, 3].map(returnTrue)) is '[true, true, true, true]'
PASS showHoles([0, undefined, 2, 3].map(returnTrue)) is '[true, true, true, true]'
PASS a = []; [0, , 2, 3].every(addToArrayReturnTrue); showHoles(a) is '[0, peekaboo, 2, 3]'
PASS a = []; [0, undefined, 2, 3].every(addToArrayReturnTrue); showHoles(a) is '[0, undefined, 2, 3]'
PASS a = []; [0, , 2, 3].forEach(addToArray); showHoles(a) is '[0, peekaboo, 2, 3]'
PASS a = []; [0, undefined, 2, 3].forEach(addToArray); showHoles(a) is '[0, undefined, 2, 3]'
PASS a = []; [0, , 2, 3].some(addToArrayReturnFalse); showHoles(a) is '[0, peekaboo, 2, 3]'
PASS a = []; [0, undefined, 2, 3].some(addToArrayReturnFalse); showHoles(a) is '[0, undefined, 2, 3]'
PASS [0, , 2, 3].indexOf() is -1
PASS [0, , 2, 3].indexOf('peekaboo') is 1
PASS [0, undefined, 2, 3].indexOf() is 1
PASS [0, , 2, 3].lastIndexOf() is -1
PASS [0, , 2, 3].lastIndexOf('peekaboo') is 1
PASS [0, undefined, 2, 3].lastIndexOf() is 1
PASS successfullyParsed is true

TEST COMPLETE

