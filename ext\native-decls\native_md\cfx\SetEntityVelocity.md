---
ns: CFX
apiset: server
---
## SET_ENTITY_VELOCITY

```c
void SET_ENTITY_VELOCITY(Entity entity, float x, float y, float z);
```

```
Note that the third parameter(denoted as z) is "up and down" with positive numbers encouraging upwards movement.
```

**This is the server-side RPC native equivalent of the client native [SET\_ENTITY\_VELOCITY](?_0x1C99BB7B6E96D16F).**

## Parameters
* **entity**: 
* **x**: 
* **y**: 
* **z**: 

