
# xpidlyacc.py
# This file is automatically generated. Do not edit.
# pylint: disable=W,C,R
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = "left|leftLSHIFTRSHIFTleft+-left*leftUMINUSATTRIBUTE CDATA CONST HEXNUM IDENTIFIER IID IN INCLUDE INOUT INTERFACE LSHIFT NATIVE NATIVEID NUMBER OUT RAISES READONLY RSHIFT TYPEDEFidlfile : productionsproductions : productions : CDATA productionsproductions : INCLUDE productionsproductions : interface productions\n                       | typedef productions\n                       | native productionstypedef : TYPEDEF IDENTIFIER IDENTIFIER ';'native : attributes NATIVE IDENTIFIER afternativeid '(' NATIVEID ')' ';'afternativeid : anyident : IDENTIFIER\n                    | CONSTattributes : '[' attlist ']'\n                      | attlist : attributeattlist : attribute ',' attlistattribute : anyident attributevalattributeval : '(' IDENTIFIER ')'\n                        | '(' IID ')'\n                        | interface : attributes INTERFACE IDENTIFIER ifacebase ifacebody ';'ifacebody : '{' members '}'\n                     | ifacebase : ':' IDENTIFIER\n                     | members : members : member membersmember : CDATAmember : CONST IDENTIFIER IDENTIFIER '=' number ';' number : NUMBERnumber : HEXNUMnumber : IDENTIFIERnumber : '(' number ')'number : '-' number %prec UMINUSnumber : number '+' number\n                  | number '-' number\n                  | number '*' numbernumber : number LSHIFT number\n                  | number RSHIFT numbernumber : number '|' numbermember : attributes optreadonly ATTRIBUTE IDENTIFIER IDENTIFIER ';'member : attributes IDENTIFIER IDENTIFIER '(' paramlist ')' raises ';'paramlist : param moreparams\n                     | moreparams :moreparams : ',' param moreparamsparam : attributes paramtype IDENTIFIER IDENTIFIERparamtype : IN\n                     | INOUT\n                     | OUToptreadonly : READONLY\n                       | raises : RAISES '(' idlist ')'\n                  | idlist : IDENTIFIERidlist : IDENTIFIER ',' idlist"
    
_lr_action_items = {'$end':([0,1,2,3,4,5,6,7,11,12,13,14,15,34,44,61,],[-2,0,-1,-2,-2,-2,-2,-2,-3,-4,-5,-6,-7,-8,-21,-9,]),'CDATA':([0,3,4,5,6,7,34,39,44,46,47,61,75,84,104,],[3,3,3,3,3,3,-8,47,-21,47,-28,-9,-29,-41,-42,]),'INCLUDE':([0,3,4,5,6,7,34,44,61,],[4,4,4,4,4,4,-8,-21,-9,]),'TYPEDEF':([0,3,4,5,6,7,34,44,61,],[9,9,9,9,9,9,-8,-21,-9,]),'[':([0,3,4,5,6,7,34,39,44,46,47,61,64,75,84,91,104,],[10,10,10,10,10,10,-8,10,-21,10,-28,-9,10,-29,-41,10,-42,]),'INTERFACE':([0,3,4,5,6,7,8,27,34,44,61,],[-14,-14,-14,-14,-14,-14,16,-13,-8,-21,-9,]),'NATIVE':([0,3,4,5,6,7,8,27,34,44,61,],[-14,-14,-14,-14,-14,-14,17,-13,-8,-21,-9,]),'IDENTIFIER':([9,10,16,17,18,27,28,30,32,39,46,47,48,49,53,55,59,62,63,69,70,75,76,77,78,79,80,81,84,85,86,87,88,99,104,105,110,],[18,22,24,25,26,-13,22,36,40,-14,-14,-28,53,55,58,60,63,65,71,65,65,-29,65,65,65,65,65,65,-41,99,-48,-49,-50,103,-42,108,108,]),'CONST':([10,28,39,46,47,75,84,104,],[23,23,48,48,-28,-29,-41,-42,]),']':([19,20,21,22,23,29,35,42,43,],[27,-15,-20,-11,-12,-17,-16,-18,-19,]),',':([20,21,22,23,29,42,43,74,102,103,108,],[28,-20,-11,-12,-17,-18,-19,91,91,-47,110,]),'(':([21,22,23,25,33,60,62,69,70,76,77,78,79,80,81,101,],[30,-11,-12,-10,41,64,69,69,69,69,69,69,69,69,69,105,]),':':([24,],[32,]),'{':([24,31,40,],[-25,39,-24,]),';':([24,26,31,38,40,51,57,65,66,67,68,71,83,89,92,93,94,95,96,97,98,100,109,],[-25,34,-23,44,-24,-22,61,-32,75,-30,-31,84,-34,-54,-35,-36,-37,-38,-39,-40,-33,104,-53,]),'READONLY':([27,39,46,47,49,75,84,104,],[-13,-14,-14,-28,56,-29,-41,-42,]),'ATTRIBUTE':([27,39,46,47,49,54,56,75,84,104,],[-13,-14,-14,-28,-52,59,-51,-29,-41,-42,]),'IN':([27,64,72,91,],[-13,-14,86,-14,]),'INOUT':([27,64,72,91,],[-13,-14,87,-14,]),'OUT':([27,64,72,91,],[-13,-14,88,-14,]),'IID':([30,],[37,]),')':([36,37,50,64,65,67,68,73,74,82,83,90,92,93,94,95,96,97,98,102,103,106,107,108,111,],[42,43,57,-44,-32,-30,-31,89,-45,98,-34,-43,-35,-36,-37,-38,-39,-40,-33,-45,-47,-46,109,-55,-56,]),'}':([39,45,46,47,52,75,84,104,],[-26,51,-26,-28,-27,-29,-41,-42,]),'NATIVEID':([41,],[50,]),'=':([58,],[62,]),'NUMBER':([62,69,70,76,77,78,79,80,81,],[67,67,67,67,67,67,67,67,67,]),'HEXNUM':([62,69,70,76,77,78,79,80,81,],[68,68,68,68,68,68,68,68,68,]),'-':([62,65,66,67,68,69,70,76,77,78,79,80,81,82,83,92,93,94,95,96,97,98,],[70,-32,77,-30,-31,70,70,70,70,70,70,70,70,77,-34,-35,-36,-37,77,77,77,-33,]),'+':([65,66,67,68,82,83,92,93,94,95,96,97,98,],[-32,76,-30,-31,76,-34,-35,-36,-37,76,76,76,-33,]),'*':([65,66,67,68,82,83,92,93,94,95,96,97,98,],[-32,78,-30,-31,78,-34,78,78,-37,78,78,78,-33,]),'LSHIFT':([65,66,67,68,82,83,92,93,94,95,96,97,98,],[-32,79,-30,-31,79,-34,-35,-36,-37,-38,-39,79,-33,]),'RSHIFT':([65,66,67,68,82,83,92,93,94,95,96,97,98,],[-32,80,-30,-31,80,-34,-35,-36,-37,-38,-39,80,-33,]),'|':([65,66,67,68,82,83,92,93,94,95,96,97,98,],[-32,81,-30,-31,81,-34,-35,-36,-37,-38,-39,-40,-33,]),'RAISES':([89,],[101,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'idlfile':([0,],[1,]),'productions':([0,3,4,5,6,7,],[2,11,12,13,14,15,]),'interface':([0,3,4,5,6,7,],[5,5,5,5,5,5,]),'typedef':([0,3,4,5,6,7,],[6,6,6,6,6,6,]),'native':([0,3,4,5,6,7,],[7,7,7,7,7,7,]),'attributes':([0,3,4,5,6,7,39,46,64,91,],[8,8,8,8,8,8,49,49,72,72,]),'attlist':([10,28,],[19,35,]),'attribute':([10,28,],[20,20,]),'anyident':([10,28,],[21,21,]),'attributeval':([21,],[29,]),'ifacebase':([24,],[31,]),'afternativeid':([25,],[33,]),'ifacebody':([31,],[38,]),'members':([39,46,],[45,52,]),'member':([39,46,],[46,46,]),'optreadonly':([49,],[54,]),'number':([62,69,70,76,77,78,79,80,81,],[66,82,83,92,93,94,95,96,97,]),'paramlist':([64,],[73,]),'param':([64,91,],[74,102,]),'paramtype':([72,],[85,]),'moreparams':([74,102,],[90,106,]),'raises':([89,],[100,]),'idlist':([105,110,],[107,111,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> idlfile","S'",1,None,None,None),
  ('idlfile -> productions','idlfile',1,'p_idlfile','xpidl.py',1138),
  ('productions -> <empty>','productions',0,'p_productions_start','xpidl.py',1142),
  ('productions -> CDATA productions','productions',2,'p_productions_cdata','xpidl.py',1146),
  ('productions -> INCLUDE productions','productions',2,'p_productions_include','xpidl.py',1151),
  ('productions -> interface productions','productions',2,'p_productions_interface','xpidl.py',1156),
  ('productions -> typedef productions','productions',2,'p_productions_interface','xpidl.py',1157),
  ('productions -> native productions','productions',2,'p_productions_interface','xpidl.py',1158),
  ('typedef -> TYPEDEF IDENTIFIER IDENTIFIER ;','typedef',4,'p_typedef','xpidl.py',1163),
  ('native -> attributes NATIVE IDENTIFIER afternativeid ( NATIVEID ) ;','native',8,'p_native','xpidl.py',1170),
  ('afternativeid -> <empty>','afternativeid',0,'p_afternativeid','xpidl.py',1177),
  ('anyident -> IDENTIFIER','anyident',1,'p_anyident','xpidl.py',1183),
  ('anyident -> CONST','anyident',1,'p_anyident','xpidl.py',1184),
  ('attributes -> [ attlist ]','attributes',3,'p_attributes','xpidl.py',1189),
  ('attributes -> <empty>','attributes',0,'p_attributes','xpidl.py',1190),
  ('attlist -> attribute','attlist',1,'p_attlist_start','xpidl.py',1198),
  ('attlist -> attribute , attlist','attlist',3,'p_attlist_continue','xpidl.py',1202),
  ('attribute -> anyident attributeval','attribute',2,'p_attribute','xpidl.py',1207),
  ('attributeval -> ( IDENTIFIER )','attributeval',3,'p_attributeval','xpidl.py',1211),
  ('attributeval -> ( IID )','attributeval',3,'p_attributeval','xpidl.py',1212),
  ('attributeval -> <empty>','attributeval',0,'p_attributeval','xpidl.py',1213),
  ('interface -> attributes INTERFACE IDENTIFIER ifacebase ifacebody ;','interface',6,'p_interface','xpidl.py',1218),
  ('ifacebody -> { members }','ifacebody',3,'p_ifacebody','xpidl.py',1247),
  ('ifacebody -> <empty>','ifacebody',0,'p_ifacebody','xpidl.py',1248),
  ('ifacebase -> : IDENTIFIER','ifacebase',2,'p_ifacebase','xpidl.py',1253),
  ('ifacebase -> <empty>','ifacebase',0,'p_ifacebase','xpidl.py',1254),
  ('members -> <empty>','members',0,'p_members_start','xpidl.py',1259),
  ('members -> member members','members',2,'p_members_continue','xpidl.py',1263),
  ('member -> CDATA','member',1,'p_member_cdata','xpidl.py',1268),
  ('member -> CONST IDENTIFIER IDENTIFIER = number ;','member',6,'p_member_const','xpidl.py',1272),
  ('number -> NUMBER','number',1,'p_number_decimal','xpidl.py',1280),
  ('number -> HEXNUM','number',1,'p_number_hex','xpidl.py',1285),
  ('number -> IDENTIFIER','number',1,'p_number_identifier','xpidl.py',1290),
  ('number -> ( number )','number',3,'p_number_paren','xpidl.py',1296),
  ('number -> - number','number',2,'p_number_neg','xpidl.py',1300),
  ('number -> number + number','number',3,'p_number_add','xpidl.py',1305),
  ('number -> number - number','number',3,'p_number_add','xpidl.py',1306),
  ('number -> number * number','number',3,'p_number_add','xpidl.py',1307),
  ('number -> number LSHIFT number','number',3,'p_number_shift','xpidl.py',1318),
  ('number -> number RSHIFT number','number',3,'p_number_shift','xpidl.py',1319),
  ('number -> number | number','number',3,'p_number_bitor','xpidl.py',1328),
  ('member -> attributes optreadonly ATTRIBUTE IDENTIFIER IDENTIFIER ;','member',6,'p_member_att','xpidl.py',1334),
  ('member -> attributes IDENTIFIER IDENTIFIER ( paramlist ) raises ;','member',8,'p_member_method','xpidl.py',1350),
  ('paramlist -> param moreparams','paramlist',2,'p_paramlist','xpidl.py',1365),
  ('paramlist -> <empty>','paramlist',0,'p_paramlist','xpidl.py',1366),
  ('moreparams -> <empty>','moreparams',0,'p_moreparams_start','xpidl.py',1374),
  ('moreparams -> , param moreparams','moreparams',3,'p_moreparams_continue','xpidl.py',1378),
  ('param -> attributes paramtype IDENTIFIER IDENTIFIER','param',4,'p_param','xpidl.py',1383),
  ('paramtype -> IN','paramtype',1,'p_paramtype','xpidl.py',1391),
  ('paramtype -> INOUT','paramtype',1,'p_paramtype','xpidl.py',1392),
  ('paramtype -> OUT','paramtype',1,'p_paramtype','xpidl.py',1393),
  ('optreadonly -> READONLY','optreadonly',1,'p_optreadonly','xpidl.py',1397),
  ('optreadonly -> <empty>','optreadonly',0,'p_optreadonly','xpidl.py',1398),
  ('raises -> RAISES ( idlist )','raises',4,'p_raises','xpidl.py',1405),
  ('raises -> <empty>','raises',0,'p_raises','xpidl.py',1406),
  ('idlist -> IDENTIFIER','idlist',1,'p_idlist','xpidl.py',1413),
  ('idlist -> IDENTIFIER , idlist','idlist',3,'p_idlist_continue','xpidl.py',1417),
]
