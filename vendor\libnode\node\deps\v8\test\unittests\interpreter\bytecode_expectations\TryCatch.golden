#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  try { return 1; } catch(e) { return 2; }
"
frame size: 2
parameter count: 1
bytecode array length: 20
bytecodes: [
                B(Mov), R(context), R(0),
  /*   40 S> */ B(LdaSmi), I8(1),
  /*   49 S> */ B(Return),
                B(Star1),
                B(CreateCatchContext), R(1), U8(0),
                B(Star0),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(0),
                B(PushContext), R(1),
  /*   63 S> */ B(LdaSmi), I8(2),
  /*   72 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
  [3, 6, 6],
]

---
snippet: "
  var a;
  try { a = 1 } catch(e1) {};
  try { a = 2 } catch(e2) { a = 3 }
"
frame size: 3
parameter count: 1
bytecode array length: 47
bytecodes: [
                B(Mov), R(context), R(1),
  /*   47 S> */ B(LdaSmi), I8(1),
                B(Star0),
                B(Jump), U8(15),
                B(Star2),
  /*   49 E> */ B(CreateCatchContext), R(2), U8(0),
                B(Star1),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(1),
                B(PushContext), R(2),
                B(PopContext), R(2),
                B(Mov), R(context), R(1),
  /*   75 S> */ B(LdaSmi), I8(2),
                B(Star0),
                B(Jump), U8(18),
                B(Star2),
  /*   77 E> */ B(CreateCatchContext), R(2), U8(1),
                B(Star1),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(1),
                B(PushContext), R(2),
  /*   95 S> */ B(LdaSmi), I8(3),
                B(Star0),
                B(PopContext), R(2),
                B(LdaUndefined),
  /*  103 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  SCOPE_INFO_TYPE,
]
handlers: [
  [3, 6, 8],
  [24, 27, 29],
]

