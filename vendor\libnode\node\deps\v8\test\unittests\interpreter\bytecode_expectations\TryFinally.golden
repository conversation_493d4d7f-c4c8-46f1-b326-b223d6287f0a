#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var a = 1;
  try { a = 2; } finally { a = 3; }
"
frame size: 4
parameter count: 1
bytecode array length: 37
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
                B(Mov), R(context), R(3),
  /*   51 S> */ B(LdaSmi), I8(2),
                B(Star0),
                B(LdaSmi), I8(-1),
                B(Star2),
                B(Star1),
                B(Jump), U8(5),
                B(Star2),
                B(LdaZero),
                B(Star1),
                B(LdaTheHole),
  /*   53 E> */ B(SetPendingMessage),
                B(Star3),
  /*   70 S> */ B(LdaSmi), I8(3),
                B(Star0),
                B(Ldar), R(3),
  /*   72 E> */ B(SetPendingMessage),
                B(LdaZero),
                B(TestReferenceEqual), R(1),
                B(JumpIfFalse), U8(5),
                B(Ldar), R(2),
                B(ReThrow),
                B(LdaUndefined),
  /*   79 S> */ B(Return),
]
constant pool: [
]
handlers: [
  [6, 9, 15],
]

---
snippet: "
  var a = 1;
  try { a = 2; } catch(e) { a = 20 } finally { a = 3; }
"
frame size: 6
parameter count: 1
bytecode array length: 58
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
                B(Mov), R(context), R(3),
                B(Mov), R(context), R(4),
  /*   51 S> */ B(LdaSmi), I8(2),
                B(Star0),
                B(Jump), U8(18),
                B(Star5),
  /*   53 E> */ B(CreateCatchContext), R(5), U8(0),
                B(Star4),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(4),
                B(PushContext), R(5),
  /*   71 S> */ B(LdaSmi), I8(20),
                B(Star0),
                B(PopContext), R(5),
                B(LdaSmi), I8(-1),
                B(Star2),
                B(Star1),
                B(Jump), U8(5),
                B(Star2),
                B(LdaZero),
                B(Star1),
                B(LdaTheHole),
  /*   73 E> */ B(SetPendingMessage),
                B(Star3),
  /*   90 S> */ B(LdaSmi), I8(3),
                B(Star0),
                B(Ldar), R(3),
  /*   92 E> */ B(SetPendingMessage),
                B(LdaZero),
                B(TestReferenceEqual), R(1),
                B(JumpIfFalse), U8(5),
                B(Ldar), R(2),
                B(ReThrow),
                B(LdaUndefined),
  /*   99 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
  [6, 30, 36],
  [9, 12, 14],
]

---
snippet: "
  var a; try {
    try { a = 1 } catch(e) { a = 2 }
  } catch(e) { a = 20 } finally { a = 3; }
"
frame size: 7
parameter count: 1
bytecode array length: 76
bytecodes: [
                B(Mov), R(context), R(3),
                B(Mov), R(context), R(4),
                B(Mov), R(context), R(5),
  /*   55 S> */ B(LdaSmi), I8(1),
                B(Star0),
                B(Jump), U8(18),
                B(Star6),
  /*   57 E> */ B(CreateCatchContext), R(6), U8(0),
                B(Star5),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(5),
                B(PushContext), R(6),
  /*   74 S> */ B(LdaSmi), I8(2),
                B(Star0),
                B(PopContext), R(6),
                B(Jump), U8(18),
                B(Star5),
  /*   76 E> */ B(CreateCatchContext), R(5), U8(1),
                B(Star4),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(4),
                B(PushContext), R(5),
  /*   95 S> */ B(LdaSmi), I8(20),
                B(Star0),
                B(PopContext), R(5),
                B(LdaSmi), I8(-1),
                B(Star2),
                B(Star1),
                B(Jump), U8(5),
                B(Star2),
                B(LdaZero),
                B(Star1),
                B(LdaTheHole),
  /*   97 E> */ B(SetPendingMessage),
                B(Star3),
  /*  114 S> */ B(LdaSmi), I8(3),
                B(Star0),
                B(Ldar), R(3),
  /*  116 E> */ B(SetPendingMessage),
                B(LdaZero),
                B(TestReferenceEqual), R(1),
                B(JumpIfFalse), U8(5),
                B(Ldar), R(2),
                B(ReThrow),
                B(LdaUndefined),
  /*  123 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  SCOPE_INFO_TYPE,
]
handlers: [
  [3, 48, 54],
  [6, 30, 32],
  [9, 12, 14],
]

