declare function AbortAllGarageActivity(): void;

declare function AbortScriptedConversation(abort: boolean): number;

declare function Absf(value: number): number;

declare function Absi(value: number): number;

declare function Acos(Unk496: number): number;

declare function ActivateCheat(cheat: number): void;

declare function ActivateDamageTrackerOnNetworkId(Unk897: number, Unk898: number): void;

declare function ActivateFrontend(): void;

declare function ActivateHeliSpeedCheat(heli: number, cheat: number): void;

declare function ActivateInterior(interior: number, unknownTrue: boolean): void;

declare function ActivateMenuItem(menuid: number, item: number, activate: boolean): void;

declare function ActivateNetworkSettingsMenu(): void;

declare function ActivateReplayMenu(): void;

declare function ActivateSaveMenu(): void;

declare function ActivateScriptPopulationZone(): void;

declare function ActivateScriptedCams(Unk538: boolean, Unk539: boolean): void;

/**
 * Activates built-in timecycle editing tool.
 */
declare function ActivateTimecycleEditor(): void;

declare function ActivateViewport(viewportid: number, activate: boolean): void;

declare function AddAdditionalPopulationModel(model: number): void;

declare function AddAmmoToChar(ped: number, weapon: number, amount: number): void;

declare function AddArmourToChar(ped: number, amount: number): void;

/**
 * Adds an output for the specified audio submix.
 * @param submixId The input submix.
 * @param outputSubmixId The output submix. Use `0` for the master game submix.
 */
declare function AddAudioSubmixOutput(submixId: number, outputSubmixId: number): void;

declare function AddBlipForCar(vehicle: number, pBlip?: number): number;

declare function AddBlipForChar(ped: number, pBlip?: number): number;

declare function AddBlipForContact(x: number, y: number, z: number, pBlip?: number): number;

declare function AddBlipForCoord(x: number, y: number, z: number, pBlip?: number): number;

declare function AddBlipForGangTerritory(x0: number, y0: number, x1: number, y1: number, colour: number, blip?: number): number;

declare function AddBlipForObject(obj: number, pBlip?: number): number;

declare function AddBlipForPickup(pickup: number, pBlip?: number): number;

declare function AddBlipForRadius(x: number, y: number, z: number, _type: number, blip?: number): number;

declare function AddBlipForWeapon(x: number, y: number, z: number, blip?: number): number;

declare function AddCamSplineNode(cam: number, camnode: number): void;

declare function AddCarToMissionDeletionList(car: number): void;

declare function AddCharDecisionMakerEventResponse(dm: number, eventid: number, responseid: number, param1: number, param2: number, param3: number, param4: number, unknown0_1: number, unknown1_1: number): void;

/**
 * Adds a listener for Console Variable changes.
 * The function called expects to match the following signature:
 * ```ts
 * function ConVarChangeListener(conVarName: string, reserved: any);
 * ```
 * *   **conVarName**: The ConVar that changed.
 * *   **reserved**: Currently unused.
 * @param conVarFilter The Console Variable to listen for, this can be a pattern like "test:\*", or null for any
 * @param handler The handler function.
 * @return A cookie to remove the change handler.
 */
declare function AddConvarChangeListener(conVarFilter: string, handler: Function): number;

declare function AddCoverBlockingArea(Unk110: number, Unk111: number, Unk112: number, Unk113: number, Unk114: number, Unk115: number, Unk116: number, Unk117: number, Unk118: number): void;

declare function AddCoverPoint(Unk119: number, Unk120: number, Unk121: number, Unk122: number, Unk123: number, Unk124: number, Unk125: number, Unk126: number): void;

declare function AddExplosion(x: number, y: number, z: number, exptype: number, radius: number, playsound: boolean, novisual: boolean, camshake: number): void;

declare function AddFirstNCharactersOfStringToHtmlScriptObject(htmlobj: number, str: string, n: number): void;

declare function AddFollowNavmeshToPhoneTask(ped: number, Unk127: number, Unk128: number, Unk129: number): void;

declare function AddGroupDecisionMakerEventResponse(dm: number, eventid: number, responseid: number, param1: number, param2: number, param3: number, param4: number, unknown0_1: number, unknown1_1: number): void;

declare function AddGroupToNetworkRestartNodeGroupList(Unk899: number): void;

declare function AddHospitalRestart(x: number, y: number, z: number, radius: number, islandnum: number): void;

declare function AddLineToConversation(Unk522: number, Unk523: number, Unk524: number, Unk525: number, Unk526: number): void;

declare function AddLineToMobilePhoneCall(id: number, name: string, text: string): void;

declare function AddLineToScriptedConversation(conversation: number, Unk527: number, Unk528: number): void;

declare function AddNavmeshRequiredRegion(x: number, y: number, z: number): number;

declare function AddNeededAtPosn(x: number, y: number, z: number): void;

declare function AddNewConversationSpeaker(id: number, Unk529: number, Unk530?: number): number;

declare function AddNewFrontendConversationSpeaker(Unk531: number, Unk532: number): void;

declare function AddNextMessageToPreviousBriefs(add: boolean): void;

declare function AddObjectToInteriorRoomByKey(obj: number, roomKey: number): void;

declare function AddObjectToInteriorRoomByName(obj: number, room_name: string): void;

declare function AddPedToCinematographyAi(Unk28: number, ped: number): void;

declare function AddPedToMissionDeletionList(ped: number, Unk29: boolean): void;

declare function AddPickupToInteriorRoomByKey(pickup: number, room_hash: number): void;

declare function AddPickupToInteriorRoomByName(pickup: number, roomName: string): void;

declare function AddPointToGpsRaceTrack(): number[];

declare function AddPoliceRestart(x: number, y: number, z: number, radius: number, islandnum: number): void;

declare function AddScenarioBlockingArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function AddScore(playerIndex: number, score: number): void;

declare function AddSimpleBlipForPickup(pickup: number): void;

declare function AddSpawnBlockingArea(Unk900: number, Unk901: number, Unk902: number, Unk903: number): void;

declare function AddSpawnBlockingDisc(Unk904: number, Unk905: number, Unk906: number, Unk907: number, Unk908: number): void;

/**
 * Adds a handler for changes to a state bag.
 * The function called expects to match the following signature:
 * ```ts
 * function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
 * ```
 * *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
 * or `localEntity:Handle`.
 * *   **key**: The changed key.
 * *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
 * *   **reserved**: Currently unused.
 * *   **replicated**: Whether the set is meant to be replicated.
 * At this time, the change handler can't opt to reject changes.
 * If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](#\_0x4BDF1867) to get the entity handle
 * If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](#\_0xA56135E0) to get the player handle
 * @param keyFilter The key to check for, or null for no filter.
 * @param bagFilter The bag ID to check for such as `entity:65535`, or null for no filter.
 * @param handler The handler function.
 * @return A cookie to remove the change handler.
 */
declare function AddStateBagChangeHandler(keyFilter: string, bagFilter: string, handler: Function): number;

declare function AddStringToHtmlScriptObject(htmlobj: number, str: string): void;

declare function AddStringToNewsScrollbar(str: string): void;

declare function AddStringWithThisTextLabelToPreviousBrief(gxtname: string): void;

declare function AddStuckCarCheck(car: number, stuckdif: number, timeout: number): void;

declare function AddStuckCarCheckWithWarp(car: number, stuckdif: number, time: number, flag0: boolean, flag1: boolean, flag2: boolean, flag3: boolean): void;

declare function AddStuntJump(x: number, y: number, z: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, x3: number, y3: number, z3: number, reward: number): void;

/**
 * ADD_TEXT_ENTRY
 */
declare function AddTextEntry(entryKey: string, entryText: string): void;

/**
 * ADD_TEXT_ENTRY_BY_HASH
 */
declare function AddTextEntryByHash(entryKey: string | number, entryText: string): void;

declare function AddTextWidget(Unk1089: number): string;

declare function AddTickerToPreviousBriefWithUnderscore(Unk625: number, Unk626: number, Unk627: number, Unk628: number, Unk629: number, Unk630: number, Unk631: number): void;

declare function AddToHtmlScriptObject(htmlobj: number, htmlcode: string): void;

declare function AddToPreviousBrief(gxtentry: string): void;

declare function AddToPreviousBriefWithUnderscore(gxtentry: string): void;

declare function AddToWidgetCombo(Unk1091: number): void;

declare function AddUpsidedownCarCheck(vehicle: number): void;

declare function AddWidgetFloatReadOnly(Unk1092: number, Unk1093: number): void;

declare function AddWidgetFloatSlider(Unk1094: number, Unk1095: number, Unk1096: number, Unk1097: number, Unk1098: number): void;

declare function AddWidgetReadOnly(Unk1099: number, Unk1100: number): void;

declare function AddWidgetSlider(Unk1101: number, Unk1102: number, Unk1103: number, Unk1104: number, Unk1105: number): void;

declare function AddWidgetString(Unk1106: number): void;

declare function AddWidgetToggle(Unk1107: number, Unk1108: number): void;

declare function AllocateScriptToObject(ScriptName: string, model: number, Unk602: number, radius: number, UnkTime: number): void;

declare function AllocateScriptToRandomPed(ScriptName: string, model: number, Unk603: number, flag: boolean): void;

declare function AllowAutoConversationLookats(ped: number, allow: boolean): void;

declare function AllowEmergencyServices(allow: boolean): void;

declare function AllowGameToPauseForStreaming(allow: boolean): void;

declare function AllowGangRelationshipsToBeChangedByNextCommand(value: boolean): void;

declare function AllowLockonToFriendlyPlayers(player: number, allow: boolean): void;

declare function AllowLockonToRandomPeds(player: number, allow: boolean): void;

declare function AllowMultipleDrivebyPickups(allow: boolean): void;

declare function AllowOneTimeOnlyCommandsToRun(): boolean;

declare function AllowPlayerToCarryNonMissionObjects(playerIndex: number, allow: boolean): void;

declare function AllowReactionAnims(ped: number, allow: boolean): void;

declare function AllowScenarioPedsToBeReturnedByNextCommand(value: boolean): void;

declare function AllowStuntJumpsToTrigger(allow: boolean): void;

declare function AllowTargetWhenInjured(ped: number, allow: boolean): void;

declare function AllowThisScriptToBePaused(allows: boolean): void;

declare function AlterWantedLevel(playerIndex: number, level: number): void;

declare function AlterWantedLevelNoDrop(playerIndex: number, level: number): void;

declare function AlwaysUseHeadOnHornAnimWhenDeadInCar(ped: number, use: boolean): void;

declare function AmbientAudioBankNoLongerNeeded(): void;

declare function AnchorBoat(boat: number, anchor: boolean): void;

declare function AnchorObject(obj: number, anchor: boolean, flags: number): void;

declare function ApplyForceToCar(vehicle: number, unknown0_3: number, x: number, y: number, z: number, spinX: number, spinY: number, spinZ: number, unknown4_0: number, unknown5_1: number, unknown6_1: number, unknown7_1: number): void;

declare function ApplyForceToObject(obj: number, uk0_3: number, pX: number, pY: number, pZ: number, spinX: number, spinY: number, spinZ: number, uk4_0: number, uk5_1: number, uk6_1: number, uk7_1: number): void;

declare function ApplyForceToPed(ped: number, unknown0_3: number, x: number, y: number, z: number, spinX: number, spinY: number, spinZ: number, unknown4_0: number, unknown5_1: number, unknown6_1: number, unknown7_1: number): void;

declare function ApplyWantedLevelChangeNow(playerIndex: number): void;

/**
 * APPLY_WEATHER_CYCLES
 * @param numEntries The number of cycle entries. Must be between 1 and 256
 * @param msPerCycle The duration in milliseconds of each cycle. Must be between 1000 and 86400000 (24 hours)
 * @return Returns true if all parameters were valid, otherwise false.
 */
declare function ApplyWeatherCycles(numEntries: number, msPerCycle: number): boolean;

declare function AreAllNavmeshRegionsLoaded(): boolean;

declare function AreAnyCharsNearChar(ped: number, radius: number): boolean;

declare function AreCreditsFinished(): boolean;

declare function AreEnemyPedsInArea(ped: number, x: number, y: number, z: number, radius: number): boolean;

declare function AreTaxiLightsOn(vehicle: number): boolean;

declare function AreWidescreenBordersActive(): boolean;

declare function AsciiIntToString(ascii: number): string;

declare function Asin(value: number): number;

declare function Atan(value: number): number;

declare function Atan2(Unk497: number, Unk498: number): number;

declare function AttachAnimsToModel(model: number, anims: string): void;

declare function AttachCamToObject(cam: number, obj: number): void;

declare function AttachCamToPed(cam: number, ped: number): void;

declare function AttachCamToVehicle(cam: number, veh: number): void;

declare function AttachCamToViewport(cam: number, viewportid: number): void;

declare function AttachCarToCar(car0: number, car1: number, Unk51: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function AttachCarToCarPhysically(vehid1: number, vehid2: number, Unk52: boolean, Unk53: number, xoffset: number, yoffset: number, zoffset: number, xbuffer: number, ybuffer: number, zbuffer: number, xrotateveh1: number, yrotateveh1: number, Unk54: number, Unk55: number, Unk56: number): void;

declare function AttachCarToObject(car: number, obj: number, Unk57: number, Unk58: number, Unk59: number, Unk60: number, Unk61: number, Unk62: number, Unk63: number): void;

declare function AttachObjectToCar(obj: number, v: number, unknown0_0: number, pX: number, pY: number, pZ: number, rX: number, rY: number, rZ: number): void;

declare function AttachObjectToCarPhysically(obj: number, car: number, Unk79: number, Unk80: number, Unk81: number, Unk82: number, Unk83: number, Unk84: number, Unk85: number, Unk86: number, Unk87: number, Unk88: number, Unk89: number, Unk90: number, flag: boolean): void;

declare function AttachObjectToObject(obj0: number, obj1_attach_to: number, Unk91: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function AttachObjectToPed(obj: number, c: number, bone: number, pX: number, pY: number, pZ: number, rX: number, rY: number, rZ: number, unknown1_0: number): void;

declare function AttachObjectToPedPhysically(obj: number, c: number, unknown: boolean, bone: number, pX: number, pY: number, pZ: number, rX: number, rY: number, rZ: number, unknown1_0: number, unknown2_0: number): void;

declare function AttachParachuteModelToPlayer(ped: number, obj: number): void;

declare function AttachPedToCar(ped: number, vehicle: number, unknown0_0: number, offsetX: number, offsetY: number, offsetZ: number, rotX: number, rotY: number, Unk64: boolean, Unk65: boolean): void;

declare function AttachPedToCarPhysically(ped: number, car: number, pedbone: number, x: number, y: number, z: number, angle: number, Unk30: number, Unk31: boolean, Unk32: boolean): void;

declare function AttachPedToObject(ped: number, obj: number, pedbone: number, x: number, y: number, z: number, angle: number, Unk33: number, Unk34: boolean, Unk35: boolean): void;

declare function AttachPedToObjectPhysically(ped: number, obj: number, pedbone: number, x: number, y: number, z: number, angle: number, Unk36: number, Unk37: boolean, Unk38: boolean): void;

declare function AttachPedToShimmyEdge(ped: number, x: number, y: number, z: number, Unk39: number): void;

declare function AwardAchievement(achievement: number): number;

declare function AwardPlayerMissionRespect(respect: number): void;

declare function BeginCamCommands(Unk540: number): void;

declare function BeginCharSearchCriteria(): void;

declare function BlendFromNmWithAnim(ped: number, AnimName0: string, AnimName1: string, Unk1: number, x: number, y: number, z: number): void;

declare function BlendOutCharMoveAnims(ped: number): void;

declare function BlockCharAmbientAnims(ped: number, block: boolean): void;

declare function BlockCharGestureAnims(ped: number, value: boolean): void;

declare function BlockCharHeadIk(ped: number, block: boolean): void;

declare function BlockCharVisemeAnims(ped: number, block: boolean): void;

declare function BlockCoweringInCover(ped: number, _set: boolean): void;

declare function BlockPedWeaponSwitching(ped: number, value: boolean): void;

declare function BlockPeekingInCover(ped: number, _set: boolean): void;

declare function BlockStatsMenuActions(player: number): void;

declare function BreakCarDoor(vehicle: number, door: number, unknownFalse: boolean): void;

declare function BurstCarTyre(vehicle: number, tyre: number): void;

declare function CalculateChecksum(Unk1006: number, Unk1007: number): number;

declare function CalculateTravelDistanceBetweenNodes(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): number;

declare function CamIsSphereVisible(camera: number, pX: number, pY: number, pZ: number, radius: number): boolean;

declare function CamProcess(cam: number): void;

declare function CamRestore(): void;

declare function CamRestoreJumpcut(): void;

declare function CamSequenceClose(): void;

declare function CamSequenceGetProgress(Unk541: number, progress?: number): number;

declare function CamSequenceOpen(Unk542: number): void;

declare function CamSequenceRemove(Unk543: number): void;

declare function CamSequenceStart(Unk544: number): void;

declare function CamSequenceStop(Unk545: number): void;

declare function CamSequenceWait(cam: number, time: number): void;

declare function CamSetCinematic(veh: number, _set: boolean): void;

declare function CamSetDollyZoomLock(cam: number, _set: boolean): void;

declare function CamSetInterpGraphPos(cam: number, Unk547: number): void;

declare function CamSetInterpGraphRot(cam: number, val: number): void;

declare function CamSetInterpStateSrc(cam: number, Unk548: number): void;

declare function CamSetInterpStateSrc(Unk549: number, Unk550: number): void;

declare function CamSetInterpolationDetails(Unk546: number): void;

declare function CanBeDescribedAsACar(veh: number): boolean;

declare function CanCharSeeDeadChar(ped: number, pednext: number): boolean;

declare function CanCreateRandomChar(flag0: boolean, flag1: boolean): boolean;

declare function CanFontBeLoaded(fontid: number): boolean;

declare function CanPedShimmyInDirection(ped: number, direction: number): boolean;

declare function CanPhoneBeSeenOnScreen(): boolean;

declare function CanPlayerStartMission(player: number): boolean;

declare function CanRegisterMissionObject(): boolean;

declare function CanRegisterMissionPed(): boolean;

declare function CanRegisterMissionVehicle(): boolean;

declare function CanStartMissionPassedTune(): boolean;

declare function CanTheStatHaveString(stat: number): boolean;

declare function CancelCurrentlyPlayingAmbientSpeech(ped: number): void;

declare function CancelCurrentlyPlayingAmbientSpeech(ped: number): void;

/**
 * Cancels the currently executing event.
 */
declare function CancelEvent(): void;

declare function CancelOverrideRestart(): void;

declare function Ceil(value: number): number;

declare function CellCamActivate(Unk551: boolean, Unk552: boolean): void;

declare function CellCamIsCharVisible(ped: number): boolean;

declare function CellCamIsCharVisibleNoFaceCheck(ped: number): boolean;

declare function CellCamSetCentrePos(x: number, y: number): void;

declare function CellCamSetColourBrightness(Unk553: number, Unk554: number, Unk555: number, Unk556: number): void;

declare function CellCamSetZoom(zoom: number): void;

declare function ChangeBlipAlpha(blip: number, alpha: number): void;

declare function ChangeBlipColour(blip: number, colour: number): void;

declare function ChangeBlipDisplay(blip: number, display: number): void;

declare function ChangeBlipNameFromAscii(blip: number, blipName: string): void;

declare function ChangeBlipNameFromTextFile(blip: number, gxtName: string): void;

declare function ChangeBlipNameToPlayerName(blip: number, playerid: number): void;

declare function ChangeBlipPriority(blip: number, priority: number): void;

declare function ChangeBlipRotation(blip: number, rotation: number): void;

declare function ChangeBlipScale(blip: number, scale: number): void;

declare function ChangeBlipSprite(blip: number, sprite: number): void;

declare function ChangeBlipTeamRelevance(blip: number, relevance: number): void;

declare function ChangeCarColour(vehicle: number, colour1: number, colour2: number): void;

declare function ChangeCharSitIdleAnim(ped: number, Unk2: number, Unk3: number, Unk4: number): void;

declare function ChangeGarageType(garage: number, _type: number): void;

declare function ChangePickupBlipColour(colour: number): void;

declare function ChangePickupBlipDisplay(display: number): void;

declare function ChangePickupBlipPriority(priority: number): void;

declare function ChangePickupBlipScale(scale: number): void;

declare function ChangePickupBlipSprite(sprite: number): void;

declare function ChangePlaybackToUseAi(car: number): void;

declare function ChangePlayerModel(playerIndex: number, model: number): void;

declare function ChangePlayerPhoneModel(player: number, model: number): void;

declare function ChangePlayerPhoneModelOffsets(player: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function ChangeTerritoryBlipScale(blip: number, Unk632: number, Unk633: number): void;

declare function CheatHappenedRecently(cheat: number, time: number): boolean;

declare function CheckNmFeedback(ped: number, id: number, Unk13: boolean): boolean;

declare function CheckStuckTimer(car: number, timernum: number, timeout: number): boolean;

declare function ClearAdditionalText(textid: number, Unk634: boolean): void;

declare function ClearAllCharProps(ped: number): void;

declare function ClearAllCharRelationships(ped: number, relgroup: number): void;

declare function ClearAngledAreaOfCars(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, radius: number): void;

declare function ClearArea(x: number, y: number, z: number, radius: number, unknown: boolean): void;

declare function ClearAreaOfCars(x: number, y: number, z: number, radius: number): void;

declare function ClearAreaOfChars(x: number, y: number, z: number, radius: number): void;

declare function ClearAreaOfCops(x: number, y: number, z: number, radius: number): void;

declare function ClearAreaOfObjects(x: number, y: number, z: number, radius: number): void;

declare function ClearBit(bit: number): number;

declare function ClearBrief(): void;

declare function ClearCarLastDamageEntity(vehicle: number): void;

declare function ClearCarLastWeaponDamage(vehicle: number): void;

declare function ClearCharLastDamageBone(ped: number): void;

declare function ClearCharLastDamageEntity(ped: number): void;

declare function ClearCharLastWeaponDamage(ped: number): void;

declare function ClearCharProp(ped: number, unknown: boolean): void;

declare function ClearCharRelationship(ped: number, reltype: number, relgroup: number): void;

declare function ClearCharSecondaryTask(ped: number): void;

declare function ClearCharTasks(ped: number): void;

declare function ClearCharTasksImmediately(ped: number): void;

declare function ClearCutscene(): void;

declare function ClearGroupDecisionMakerEventResponse(dm: number, eventid: number): void;

declare function ClearHelp(): void;

declare function ClearNamedCutscene(name: string): void;

declare function ClearNetworkRestartNodeGroupList(): void;

declare function ClearNewsScrollbar(): void;

declare function ClearObjectLastDamageEntity(obj: number): void;

declare function ClearObjectLastWeaponDamage(obj: number): void;

declare function ClearOnscreenCounter(counterid: number): void;

declare function ClearOnscreenTimer(timerid: number): void;

declare function ClearPedNonCreationArea(): void;

declare function ClearPedNonRemovalArea(): void;

declare function ClearPlayerHasDamagedAtLeastOnePed(playerIndex: number): void;

declare function ClearPlayerHasDamagedAtLeastOneVehicle(player: number): void;

declare function ClearPrints(): void;

declare function ClearRelationship(p0: number, p1: number, p2: number): void;

declare function ClearRoomForCar(vehicle: number): void;

declare function ClearRoomForChar(ped: number): void;

declare function ClearRoomForObject(obj: number): void;

declare function ClearRoomForViewport(viewportid: number): void;

declare function ClearScriptArrayFromScratchpad(Unk909: number): void;

declare function ClearScriptedConversionCentre(): void;

declare function ClearSequenceTask(taskSequence: number): void;

declare function ClearShakePlayerpadWhenControllerDisabled(): void;

declare function ClearSmallPrints(): void;

declare function ClearTextLabel(label: string): void;

declare function ClearThisBigPrint(gxtentry: string): void;

declare function ClearThisPrint(gxtentry: string): void;

declare function ClearThisPrintBigNow(Unk635: boolean): void;

declare function ClearTimecycleModifier(): void;

declare function ClearWantedLevel(playerIndex: number): void;

declare function CloneCam(cam: number, camcopy?: number): number;

/**
 * CLONE_TIMECYCLE_MODIFIER
 * @param sourceModifierName The source timecycle name.
 * @param clonedModifierName The clone timecycle name, must be unique.
 * @return The cloned timecycle modifier index, or -1 if failed.
 */
declare function CloneTimecycleModifier(sourceModifierName: string, clonedModifierName: string): number;

declare function CloseAllCarDoors(vehicle: number): void;

declare function CloseDebugFile(): void;

declare function CloseGarage(garageName: string): void;

declare function CloseMicPed(id: number, ped: number): void;

declare function CloseSequenceTask(taskSequence: number): void;

declare function CodeWantsMobilePhoneRemoved(): boolean;

declare function CodeWantsMobilePhoneRemovedForWeaponSwitching(): boolean;

declare function CompareString(str0: string, str1: string): number;

declare function CompareTwoDates(date0_0: number, date0_1: number, date1_0: number, date1_1: number): number;

declare function ConnectLods(obj0: number, obj1: number): void;

declare function ControlCarDoor(vehicle: number, door: number, unknown_maybe_open: number, angle: number): void;

declare function ConvertIntToPlayerindex(playerId: number): number;

declare function ConvertMetresToFeet(metres: number): number;

declare function ConvertMetresToFeetInt(metres: number): number;

declare function ConvertThenAddStringToHtmlScriptObject(htmlobj: number, strgxtkey: string): void;

declare function CopyAnimations(ped: number, pednext: number, speed: number): void;

declare function CopyCharDecisionMaker(_type: number, pDM?: number): number;

declare function CopyCombatDecisionMaker(_type: number, pDM?: number): number;

declare function CopyGroupCharDecisionMaker(_type: number, pDM?: number): number;

declare function CopyGroupCombatDecisionMaker(_type: number, pDM?: number): number;

declare function CopySharedCharDecisionMaker(_type: number, pDM?: number): number;

declare function CopySharedCombatDecisionMaker(_type: number, pDM?: number): number;

declare function Cos(value: number): number;

declare function CountPickupsOfType(_type: number): number;

declare function CountScriptCams(): number;

declare function CountScriptCamsByTypeAndOrState(_type: number, Unk536: number, Unk537: number): number;

/**
 * Creates an audio submix with the specified name, or gets the existing audio submix by that name.
 * @param name The audio submix name.
 * @return A submix ID, or -1 if the submix could not be created.
 */
declare function CreateAudioSubmix(name: string): number;

declare function CreateCam(camtype_usually14: number, camera?: number): number;

declare function CreateCar(nameHash: number, x: number, y: number, z: number, unknownTrue: boolean): number;

declare function CreateCarGenerator(x: number, y: number, z: number, yaw: number, pitch: number, roll: number, model: number, color1: number, color2: number, spec1: number, spec2: number, Unk66: number, alarm: boolean, doorlock: boolean, handle?: number): number;

declare function CreateCarsOnGeneratorsInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function CreateChar(_type: number, model: number, x: number, y: number, z: number, unknownTrue: boolean): number;

declare function CreateCharAsPassenger(vehicle: number, charType: number, model: number, passengerIndex: number, pPed?: number): number;

declare function CreateCharInsideCar(vehicle: number, charType: number, model: number, pPed?: number): number;

declare function CreateCheckpoint(_type: number, x: number, y: number, z: number, Unk709: number, Unk710: number): number;

/**
 * Creates a DUI browser. This can be used to draw on a runtime texture using CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.
 * @param url The initial URL to load in the browser.
 * @param width The width of the backing surface.
 * @param height The height of the backing surface.
 * @return A DUI object.
 */
declare function CreateDui(url: string, width: number, height: number): number;

declare function CreateEmergencyServicesCar(model: number, x: number, y: number, z: number): boolean;

declare function CreateEmergencyServicesCarReturnDriver(model: number, x: number, y: number, z: number): [boolean, number, number, number];

declare function CreateEmergencyServicesCarThenWalk(model: number, x: number, y: number, z: number): boolean;

declare function CreateGroup(unknownFalse: boolean, unknownTrue: boolean): number;

declare function CreateHtmlScriptObject(objname: string): number;

declare function CreateHtmlViewport(htmlviewport: number): void;

declare function CreateMenu(gxtentry: string, Unk859: number, Unk860: number, Unk861: number, Unk862: number, Unk863: number, Unk864: number, Unk865: number, menuid?: number): number;

declare function CreateMissionTrain(unknown1: number, x: number, y: number, z: number, unknown2: boolean, pTrain?: number): number;

declare function CreateMobilePhone(Unk799: number): void;

declare function CreateMoneyPickup(x: number, y: number, z: number, amount: number, unknownTrue: boolean, pPickup?: number): number;

declare function CreateNmMessage(Unk40: boolean, id: number): void;

declare function CreateObject(model: number, x: number, y: number, z: number, unknownTrue: boolean): number;

declare function CreateObjectNoOffset(model: number, x: number, y: number, z: number, unknownTrue: boolean): number;

declare function CreatePickup(model: number, pickupType: number, x: number, y: number, z: number, unknownFalse: boolean): number;

declare function CreatePickupRotate(model: number, pickupType: number, unknown: number, x: number, y: number, z: number, rX: number, rY: number, rZ: number, pPickup?: number): number;

declare function CreatePickupWithAmmo(model: number, pickupType: number, unknown: number, x: number, y: number, z: number, pPickup?: number): number;

declare function CreatePlayer(playerId: number, x: number, y: number, z: number, pPlayerIndex?: number): number;

declare function CreateRandomCarForCarPark(x: number, y: number, z: number, radius: number): void;

declare function CreateRandomChar(x: number, y: number, z: number, pPed?: number): number;

declare function CreateRandomCharAsDriver(vehicle: number, pPed?: number): number;

declare function CreateRandomCharAsPassenger(vehicle: number, seat: number, pPed?: number): number;

declare function CreateRandomFemaleChar(x: number, y: number, z: number, pPed?: number): number;

declare function CreateRandomMaleChar(x: number, y: number, z: number, pPed?: number): number;

declare function CreateTemporaryRadarBlipsForPickupsInArea(x: number, y: number, z: number, radius: number, bliptype: number): void;

/**
 * Create a clean timecycle modifier. See [`SET_TIMECYCLE_MODIFIER_VAR`](#\_0x6E0A422B) to add variables.
 * @param modifierName The new timecycle name, must be unique.
 * @return The created timecycle modifier index, or -1 if failed.
 */
declare function CreateTimecycleModifier(modifierName: string): number;

declare function CreateViewport(viewport: number): void;

declare function CreateWidgetGroup(Unk1109: number): void;

declare function DamageCar(car: number, x: number, y: number, z: number, unkforce0: number, unkforce1: number, flag: boolean): void;

declare function DamageChar(ped: number, hitPoints: number, unknown: boolean): void;

declare function DamagePedBodyPart(ped: number, part: number, hitPoints: number): void;

declare function DeactivateFrontend(): void;

declare function DeactivateNetworkSettingsMenu(): void;

declare function DeactivateScriptPopulationZone(): void;

declare function DebugOff(): void;

declare function DecrementFloatStat(stat: number, val: number): void;

declare function DecrementIntStat(stat: number, amount: number): void;

declare function DefinePedGenerationConstraintArea(x: number, y: number, z: number, radius: number): void;

declare function DeleteAllHtmlScriptObjects(): void;

declare function DeleteAllTrains(): void;

declare function DeleteCar(pVehicle: number): void;

declare function DeleteCarGenerator(handle: number): void;

declare function DeleteChar(pPed: number): void;

declare function DeleteCheckpoint(checkpoint: number): void;

/**
 * DELETE_FUNCTION_REFERENCE
 */
declare function DeleteFunctionReference(referenceIdentity: string): void;

declare function DeleteHtmlScriptObject(htmlobj: number): void;

declare function DeleteMenu(menuid: number): void;

declare function DeleteMissionTrain(pTrain: number): void;

declare function DeleteMissionTrains(): void;

declare function DeleteObject(pObj: number): void;

declare function DeletePlayer(): void;

/**
 * DELETE_RESOURCE_KVP
 * @param key The key to delete
 */
declare function DeleteResourceKvp(key: string): void;

/**
 * Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to delete
 */
declare function DeleteResourceKvpNoSync(key: string): void;

declare function DeleteWidget(Unk1110: number): void;

declare function DeleteWidgetGroup(Unk1111: number): void;

declare function DestroyAllCams(): void;

declare function DestroyAllScriptViewports(): void;

declare function DestroyCam(camera: number): void;

/**
 * Destroys a DUI browser.
 * @param duiObject The DUI browser handle.
 */
declare function DestroyDui(duiObject: number): void;

declare function DestroyMobilePhone(): void;

declare function DestroyPedGenerationConstraintArea(): void;

declare function DestroyThread(ScriptHandle: number): void;

declare function DestroyViewport(viewportid: number): void;

declare function DetachCamFromViewport(Unk557: number): void;

declare function DetachCar(vehicle: number): void;

declare function DetachObject(obj: number, unknown: boolean): void;

declare function DetachObjectNoCollide(obj: number, flag: boolean): void;

declare function DetachPed(ped: number, unknown: boolean): void;

declare function DidSaveCompleteSuccessfully(): boolean;

declare function DimBlip(blip: number, unknownTrue: boolean): void;

declare function DisableCarGenerators(flag0: boolean, flag1: boolean): void;

declare function DisableCarGeneratorsWithHeli(disable: boolean): void;

declare function DisableEndCreditsFade(): void;

declare function DisableFrontendRadio(): void;

declare function DisableGps(disable: boolean): void;

declare function DisableHeliChaseCamBonnetNitroFix(): void;

declare function DisableHeliChaseCamThisUpdate(): void;

declare function DisableIntermezzoCams(): void;

declare function DisableLocalPlayerPickups(disable: boolean): void;

declare function DisablePauseMenu(disabled: boolean): void;

declare function DisablePlayerAutoVehicleExit(ped: number, disable: boolean): void;

declare function DisablePlayerLockon(playerIndex: number, disabled: boolean): void;

declare function DisablePlayerSprint(playerIndex: number, disabled: boolean): void;

declare function DisablePlayerVehicleEntry(player: number, disable: boolean): void;

declare function DisablePoliceScanner(): void;

/**
 * Disables the specified `rawKeyIndex`, making it not trigger the regular `IS_RAW_KEY_*` natives.
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
declare function DisableRawKeyThisFrame(rawKeyIndex: number): boolean;

declare function DisableStickyBombActiveSound(ped: number, disable: boolean): void;

/**
 * Disables the game's world horizon lods rendering (see `farlods.#dd`).
 * Using the island hopper natives might also affect this state.
 * @param state On/Off
 */
declare function DisableWorldhorizonRendering(state: boolean): void;

declare function DisplayAltimeterThisFrame(): void;

declare function DisplayAmmo(display: boolean): void;

declare function DisplayAreaName(display: boolean): void;

declare function DisplayCash(display: boolean): void;

declare function DisplayFrontendMapBlips(display: boolean): void;

declare function DisplayGrimeThisFrame(): void;

declare function DisplayHelpTextThisFrame(gxtkey: string, Unk636: boolean): void;

declare function DisplayHud(display: boolean): void;

declare function DisplayLoadingThisFrameWithScriptSprites(): void;

declare function DisplayNonMinigameHelpMessages(Unk637: boolean): void;

declare function DisplayNthOnscreenCounterWithString(Unk638: number, Unk639: number, Unk640: number, str: string): void;

declare function DisplayOnscreenTimerWithString(timerid: number, Unk641: boolean, str: string): void;

declare function DisplayPlayerNames(Unk910: number): void;

declare function DisplayRadar(display: boolean): void;

declare function DisplaySniperScopeThisFrame(): void;

declare function DisplayText(x: number, y: number, gxtName: string): void;

declare function DisplayTextSubstring(Unk642: number, Unk643: number, Unk644: number, Unk645: number, Unk646: number, Unk647: number, Unk648: number): void;

declare function DisplayTextWithBlipName(x: number, y: number, str: string, blip: number): void;

declare function DisplayTextWithFloat(x: number, y: number, gxtName: string, value: number, unknown: number): void;

declare function DisplayTextWithLiteralString(x: number, y: number, gxtName: string, literalStr: string): void;

declare function DisplayTextWithLiteralSubstring(Unk652: number, Unk653: number, Unk654: number, Unk655: number, Unk656: number, Unk657: number): void;

declare function DisplayTextWithNumber(x: number, y: number, gxtName: string, value: number): void;

declare function DisplayTextWithString(x: number, y: number, gxtName: string, gxtStringName: string): void;

declare function DisplayTextWithStringAndInt(x: number, y: number, gxtname: string, gxtnamenext: string, val: number): void;

declare function DisplayTextWithSubstringGivenHashKey(x: number, y: number, gxtkey: string, gxtkey0: number): void;

declare function DisplayTextWithTwoLiteralStrings(x: number, y: number, gxtName: string, literalStr1: string, literalStr2: string): void;

declare function DisplayTextWithTwoStrings(x: number, y: number, gxtName: string, gxtStringName1: string, gxtStringName2: string): void;

declare function DisplayTextWithTwoSubstringsGivenHashKeys(x: number, y: number, gxtkey: string, gxtkey0: number, gxtkey1: number): void;

declare function DisplayTextWith_2Numbers(x: number, y: number, gxtName: string, number1: number, number2: number): void;

declare function DisplayTextWith_3Numbers(x: number, y: number, gxtentry: string, Unk649: number, Unk650: number, Unk651: number): void;

declare function DoAutoSave(): void;

declare function DoScreenFadeIn(timeMS: number): void;

declare function DoScreenFadeInUnhacked(timeMS: number): void;

declare function DoScreenFadeOut(timeMS: number): void;

declare function DoScreenFadeOutUnhacked(timeMS: number): void;

declare function DoesBlipExist(blip: number): boolean;

declare function DoesCamExist(camera: number): boolean;

declare function DoesCarHaveHydraulics(car: number): boolean;

declare function DoesCarHaveRoof(vehicle: number): boolean;

declare function DoesCarHaveStuckCarCheck(vehicle: number): boolean;

declare function DoesCharExist(ped: number): boolean;

declare function DoesDecisionMakerExist(dm: number): boolean;

declare function DoesGameCodeWantToLeaveNetworkSession(): boolean;

declare function DoesGroupExist(group: number): boolean;

declare function DoesObjectExist(obj: number): boolean;

declare function DoesObjectExistWithNetworkId(netid: number): boolean;

declare function DoesObjectHavePhysics(obj: number): boolean;

declare function DoesObjectHaveThisModel(obj: number, model: number): boolean;

declare function DoesObjectOfTypeExistAtCoords(x: number, y: number, z: number, radius: number, model: number): boolean;

declare function DoesPedExistWithNetworkId(netid: number): boolean;

declare function DoesPickupExist(pickup: number): boolean;

declare function DoesPlayerHaveControlOfNetworkId(player: number, id: number): boolean;

declare function DoesScenarioExistInArea(Unk104: number, Unk105: number, Unk106: number, Unk107: number, Unk108: number): boolean;

declare function DoesScriptExist(name: string): boolean;

declare function DoesScriptFireExist(fire: number): boolean;

declare function DoesTextLabelExist(gxtentry: string): boolean;

declare function DoesThisMinigameScriptAllowNonMinigameHelpMessages(): boolean;

/**
 * DOES_TIMECYCLE_MODIFIER_HAS_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @return Whether or not variable by name was found on the specified timecycle modifier.
 */
declare function DoesTimecycleModifierHasVar(modifierName: string, varName: string): boolean;

declare function DoesVehicleExist(vehicle: number): boolean;

declare function DoesVehicleExistWithNetworkId(nedid: number): boolean;

declare function DoesViewportExist(viewportid: number): boolean;

declare function DoesWebPageExist(webaddress: string): boolean;

declare function DoesWidgetGroupExist(Unk1114: number): boolean;

declare function DontAbortCarConversations(flag0: boolean, flag1: boolean): void;

declare function DontDispatchCopsForPlayer(player: number, dont: boolean): void;

declare function DontDisplayLoadingOnFadeThisFrame(): void;

declare function DontRemoveChar(ped: number): void;

declare function DontRemoveObject(obj: number): void;

declare function DontSuppressAnyCarModels(): void;

declare function DontSuppressAnyPedModels(): void;

declare function DontSuppressCarModel(model: number): void;

declare function DontSuppressPedModel(model: number): void;

/**
 * Returns a list of door system entries: a door system hash (see [ADD_DOOR_TO_SYSTEM](#\_0x6F8838D03D1DC226)) and its object handle.
 * The data returned adheres to the following layout:
 * ```
 * [{doorHash1, doorHandle1}, ..., {doorHashN, doorHandleN}]
 * ```
 * @return An object containing a list of door system entries.
 */
declare function DoorSystemGetActive(): any;

/**
 * DOOR_SYSTEM_GET_SIZE
 * @return The number of doors registered in the system
 */
declare function DoorSystemGetSize(): number;

declare function DrawCheckpoint(x: number, y: number, z: number, radius: number, r: number, g: number, b: number): void;

declare function DrawCheckpointWithAlpha(x: number, y: number, z: number, radius: number, r: number, g: number, b: number, a: number): void;

declare function DrawColouredCylinder(x: number, y: number, z: number, Unk712: number, Unk713: number, r: number, g: number, b: number, a: number): void;

declare function DrawCorona(x: number, y: number, z: number, radius: number, Unk714: number, Unk715: number, Unk716: number, Unk717: number, Unk718: number): void;

declare function DrawCurvedWindow(Unk719: number, Unk720: number, Unk721: number, Unk722: number, alpha: number): void;

declare function DrawCurvedWindowNotext(Unk723: number, Unk724: number, Unk725: number, Unk726: number, Unk727: number): void;

declare function DrawCurvedWindowText(Unk728: number, Unk729: number, Unk730: number, Unk731: number, Unk732: number, str0: string, str1: string, Unk733: number): void;

declare function DrawDebugSphere(x: number, y: number, z: number, radius: number): void;

declare function DrawFrontendHelperText(str0: string, str1: string, Unk734: boolean): void;

declare function DrawLightWithRange(x: number, y: number, z: number, r: number, g: number, b: number, width: number, height: number): void;

declare function DrawMovie(Unk735: number, Unk736: number, Unk737: number, Unk738: number, Unk739: number, r: number, g: number, b: number, a: number): void;

declare function DrawRect(x1: number, y1: number, x2: number, y2: number, r: number, g: number, b: number, a: number): void;

declare function DrawSphere(x: number, y: number, z: number, radius: number): void;

declare function DrawSprite(texture: number, Unk740: number, Unk741: number, Unk742: number, Unk743: number, angle: number, r: number, g: number, b: number, a: number): void;

declare function DrawSpriteFrontBuff(x0: number, y0: number, x1: number, y1: number, rotation: number, r: number, g: number, b: number, a: number): void;

declare function DrawSpritePhoto(x0: number, y0: number, x1: number, y1: number, rotation: number, r: number, g: number, b: number, a: number): void;

declare function DrawSpriteWithFixedRotation(texture: number, Unk744: number, Unk745: number, Unk746: number, Unk747: number, angle: number, r: number, g: number, b: number, a: number): void;

declare function DrawSpriteWithUv(texture: number, Unk748: number, Unk749: number, Unk750: number, Unk751: number, angle: number, r: number, g: number, b: number, a: number): void;

declare function DrawSpriteWithUvCoords(texture: number, Unk752: number, Unk753: number, Unk754: number, Unk755: number, Unk756: number, Unk757: number, Unk758: number, Unk759: number, angle: number, r: number, g: number, b: number, a: number): void;

declare function DrawToplevelSprite(texture: number, Unk760: number, Unk761: number, Unk762: number, Unk763: number, angle: number, r: number, g: number, b: number, a: number): void;

declare function DrawWindow(Unk764: number, Unk765: number, Unk766: number, Unk767: number, str: string, alpha: number): void;

declare function DrawWindowText(Unk768: number, Unk769: number, Unk770: number, Unk771: number, str0: string, Unk772: number): void;

declare function DropObject(ped: number, unknownTrue: boolean): void;

/**
 * DUPLICATE_FUNCTION_REFERENCE
 */
declare function DuplicateFunctionReference(referenceIdentity: string): string;

declare function EnableAllPedHelmets(enable: boolean): void;

declare function EnableCamCollision(cam: number, enable: boolean): void;

declare function EnableChaseAudio(enable: boolean): void;

declare function EnableDebugCam(enable: boolean): void;

declare function EnableDeferredLighting(enable: boolean): void;

declare function EnableDisabledAttractorsOnObject(obj: number, enable: boolean): void;

declare function EnableEndCreditsFade(): void;

declare function EnableFancyWater(enable: boolean): void;

declare function EnableFovLodMultiplier(enable: boolean): void;

declare function EnableFrontendRadio(): void;

declare function EnableGpsInVehicle(veh: number, enable: boolean): void;

declare function EnableMaxAmmoCap(enable: boolean): void;

declare function EnablePedHelmet(ped: number, enable: boolean): void;

declare function EnablePoliceScanner(): void;

declare function EnableSaveHouse(savehouse: number, enable: boolean): void;

declare function EnableSceneStreaming(enable: boolean): void;

declare function EnableScriptControlledMicrophone(): boolean;

declare function EnableShadows(enable: boolean): void;

declare function EndCamCommands(Unk558: number): void;

declare function EndCharSearchCriteria(): void;

/**
 * END_FIND_KVP
 * @param handle The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
 * @return None.
 */
declare function EndFindKvp(handle: number): void;

/**
 * END_FIND_OBJECT
 */
declare function EndFindObject(findHandle: number): void;

/**
 * END_FIND_PED
 */
declare function EndFindPed(findHandle: number): void;

/**
 * END_FIND_PICKUP
 */
declare function EndFindPickup(findHandle: number): void;

/**
 * END_FIND_VEHICLE
 */
declare function EndFindVehicle(findHandle: number): void;

declare function EndWidgetGroup(): void;

/**
 * Internal function for ensuring an entity has a state bag.
 */
declare function EnsureEntityStateBag(entity: number): void;

declare function EvolvePtfx(ptfx: number, evolvetype: string, val: number): void;

/**
 * Depending on your use case you may need to use `add_acl resource.<your_resource_name> command.<command_name> allow` to use this native in your resource.
 */
declare function ExecuteCommand(commandString: string): void;

declare function Exp(Unk1084: number): number;

/**
 * This native is not implemented.
 */
declare function ExperimentalLoadCloneCreate(data: string, objectId: number, tree: string): number;

/**
 * This native is not implemented.
 */
declare function ExperimentalLoadCloneSync(entity: number, data: string): void;

/**
 * This native is not implemented.
 */
declare function ExperimentalSaveCloneCreate(entity: number): string;

/**
 * This native is not implemented.
 */
declare function ExperimentalSaveCloneSync(entity: number): string;

declare function ExplodeCar(vehicle: number, unknownTrue: boolean, unknownFalse: boolean): void;

declare function ExplodeCarInCutscene(car: number, explode: boolean): void;

declare function ExplodeCarInCutsceneShakeAndBit(car: number, flag0: boolean, flag1: boolean, flag2: boolean): void;

declare function ExplodeCharHead(ped: number): void;

declare function ExtendPatrolRoute(Unk484: number, Unk485: number, Unk486: number, Unk487: number, Unk488: number): void;

declare function ExtinguishCarFire(vehicle: number): void;

declare function ExtinguishCharFire(ped: number): void;

declare function ExtinguishFireAtPoint(x: number, y: number, z: number, radius: number): void;

declare function ExtinguishObjectFire(obj: number): void;

declare function FailKillFrenzy(): void;

declare function FakeDeatharrest(): void;

/**
 * FIND_FIRST_OBJECT
 */
declare function FindFirstObject(outEntity: number): [number, number];

/**
 * FIND_FIRST_PED
 */
declare function FindFirstPed(outEntity: number): [number, number];

/**
 * FIND_FIRST_PICKUP
 */
declare function FindFirstPickup(outEntity: number): [number, number];

/**
 * FIND_FIRST_VEHICLE
 */
declare function FindFirstVehicle(outEntity: number): [number, number];

/**
 * FIND_KVP
 * @param handle The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
 * @return None.
 */
declare function FindKvp(handle: number): string;

declare function FindMaxNumberOfGroupMembers(): number;

declare function FindNearestCollectableBinBags(x: number, y: number, z: number): void;

declare function FindNearestEntitiesWithSpecialAttribute(x: number, y: number, z: number): void;

declare function FindNetworkKillerOfPlayer(playerIndex: number): number;

declare function FindNetworkRestartPoint(Unk911: number, Unk912: number, Unk913: number): void;

/**
 * FIND_NEXT_OBJECT
 */
declare function FindNextObject(findHandle: number, outEntity?: number): [boolean, number];

/**
 * FIND_NEXT_PED
 */
declare function FindNextPed(findHandle: number, outEntity?: number): [boolean, number];

/**
 * FIND_NEXT_PICKUP
 */
declare function FindNextPickup(findHandle: number, outEntity?: number): [boolean, number];

/**
 * FIND_NEXT_VEHICLE
 */
declare function FindNextVehicle(findHandle: number, outEntity?: number): [boolean, number];

declare function FindPositionInRecording(car: number): number;

declare function FindPrimaryPopulationZoneGroup(): [number, number];

declare function FindStaticEmitterIndex(StaticEmitterName: string): number;

declare function FindStreetNameAtPosition(pX: number, pY: number, pZ: number): [number, number];

declare function FindTimePositionInRecording(car: number): number;

declare function FindTrainDirection(train: number): number;

declare function FinishStreamingRequestList(): void;

declare function FinishWidgetCombo(Unk1112: number, Unk1113: number): void;

declare function FirePedWeapon(ped: number, x: number, y: number, z: number): void;

declare function FireSingleBullet(x: number, y: number, z: number, targetX: number, targetY: number, targetZ: number, unknown: number): void;

declare function FixAmbienceOrientation(fix: boolean): void;

declare function FixCar(vehicle: number): void;

declare function FixCarTyre(vehicle: number, tyre: number): void;

declare function FixScriptMicToCurrentPosisition(): void;

declare function FlashBlip(blip: number, on: boolean): void;

declare function FlashBlipAlt(blip: number, on: boolean): void;

declare function FlashRadar(flash: boolean): void;

declare function FlashRoute(flash: boolean): void;

declare function FlashWeaponIcon(on: boolean): void;

declare function Floor(value: number): number;

declare function FlushAllOutOfDateRadarBlipsFromMissionCleanupList(): void;

declare function FlushAllPlayerRespawnCoords(): void;

declare function FlushAllSpawnBlockingAreas(): void;

declare function FlushCoverBlockingAreas(): void;

declare function FlushPatrolRoute(): void;

declare function FlushScenarioBlockingAreas(): void;

declare function ForceAirDragMultForPlayersCar(player: number, multiplier: number): void;

declare function ForceAllVehicleLightsOff(off: boolean): void;

declare function ForceCarLights(car: number, lights: number): void;

declare function ForceCharToDropWeapon(ped: number): void;

declare function ForceFullVoice(ped: number): void;

declare function ForceGameTelescopeCam(force: boolean): void;

declare function ForceGenerateParkedCarsTooCloseToOthers(_set: boolean): void;

declare function ForceHighLod(force: boolean): void;

declare function ForceInitialPlayerStation(stationName: string): void;

declare function ForceInteriorLightingForPlayer(player: number, force: boolean): void;

declare function ForceLoadingScreen(force: boolean): void;

declare function ForceNoCamPause(foce: boolean): void;

declare function ForceNoiseOff(off: boolean): void;

declare function ForcePedPinnedDown(ped: number, force: boolean, timerMaybe: number): void;

declare function ForcePedToFleeWhilstDrivingVehicle(ped: number, vehicle: number): void;

declare function ForcePedToLoadCover(ped: number, force: boolean): void;

declare function ForcePopulationInit(): void;

declare function ForceRadioTrack(radiostation: string, trackname: string, Unk533: number, Unk534: number): void;

declare function ForceRandomCarModel(hash: number): void;

declare function ForceRandomPedType(_type: number): void;

declare function ForceSpawnScenarioPedsInArea(x: number, y: number, z: number, radius: number, Unk41: number): void;

declare function ForceTimeOfDay(hour: number, minute: number): void;

declare function ForceWeather(weather: number): void;

declare function ForceWeatherNow(weather: number): void;

declare function ForceWind(wind: number): void;

/**
 * An internal function for converting a stack trace object to a string.
 */
declare function FormatStackTrace(traceData: any): string;

declare function ForwardToTimeOfDay(hour: number, minute: number): void;

declare function FreezeCarPosition(vehicle: number, frozen: boolean): void;

declare function FreezeCarPositionAndDontLoadCollision(vehicle: number, frozen: boolean): void;

declare function FreezeCharPosition(ped: number, frozen: boolean): void;

declare function FreezeCharPositionAndDontLoadCollision(ped: number, frozen: boolean): void;

declare function FreezeObjectPosition(obj: number, _set: boolean): void;

declare function FreezeObjectPosition(obj: number, frozen: boolean): void;

declare function FreezeObjectPositionAndDontLoadCollision(obj: number, freeze: boolean): void;

declare function FreezeOnscreenTimer(freeze: boolean): void;

declare function FreezePositionOfClosestObjectOfType(x: number, y: number, z: number, radius: number, model: number, frozen: boolean): void;

declare function FreezeRadioStation(stationName: string): void;

declare function GenerateDirections(x: number, y: number, z: number): [number, number[]];

declare function GenerateRandomFloat(Unk1086: number): void;

declare function GenerateRandomFloatInRange(min: number, max: number, pValue?: number): number;

declare function GenerateRandomInt(Unk1087: number): void;

declare function GenerateRandomIntInRange(min: number, max: number, pValue?: number): number;

declare function GetAcceptButton(): number;

/**
 * Returns all player indices for 'active' physical players known to the client.
 * The data returned adheres to the following layout:
 * ```
 * [127, 42, 13, 37]
 * ```
 * @return An object containing a list of player indices.
 */
declare function GetActivePlayers(): any;

declare function GetAmmoInCharWeapon(ped: number, weapon: number, pAmmo?: number): number;

declare function GetAmmoInClip(ped: number, weapon: number, pAmmo?: number): [boolean, number];

declare function GetAngleBetween_2dVectors(x1: number, y1: number, x2: number, y2: number, pResult?: number): number;

declare function GetAnimGroupFromChar(ped: number): string;

declare function GetAsciiJustPressed(key: number, Unk830: number): number;

declare function GetAsciiPressed(key: number, Unk820?: number): [boolean, number];

declare function GetAspectRatio(): number;

declare function GetAudibleMusicTrackTextId(): number;

declare function GetAudioRoomId(): number;

declare function GetBitsInRange(val: number, rangebegin: number, rangeend: number): number;

declare function GetBlipAlpha(blip: number, alpha?: number): number;

declare function GetBlipColour(blip: number, pColour?: number): number;

declare function GetBlipCoords(blip: number): number[];

declare function GetBlipInfoIdCarIndex(blip: number): number;

declare function GetBlipInfoIdDisplay(blip: number): number;

declare function GetBlipInfoIdObjectIndex(blip: number): number;

declare function GetBlipInfoIdPedIndex(blip: number): number;

declare function GetBlipInfoIdPickupIndex(blip: number): number;

declare function GetBlipInfoIdRotation(blip: number): number;

declare function GetBlipInfoIdType(blip: number): number;

declare function GetBlipSprite(blip: number): number;

declare function GetBufferedAscii(key: number, Unk821?: number): [boolean, number];

declare function GetCamFarClip(cam: number, clip?: number): number;

declare function GetCamFarDof(cam: number, fardof?: number): number;

declare function GetCamFov(camera: number, fov?: number): number;

/**
 * Returns the world matrix of the specified camera. To turn this into a view matrix, calculate the inverse.
 */
declare function GetCamMatrix(camera: number): [number[], number[], number[], number[]];

declare function GetCamMotionBlur(cam: number, blur?: number): number;

declare function GetCamNearClip(cam: number, clip?: number): number;

declare function GetCamNearDof(cam: number, dof?: number): number;

declare function GetCamPos(camera: number): [number, number, number];

declare function GetCamRot(camera: number): [number, number, number];

declare function GetCamState(cam: number): number;

declare function GetCameraFromNetworkId(ned_id: number, cam?: number): number;

declare function GetCarAnimCurrentTime(car: number, animname0: string, animname1: string, time?: number): number;

declare function GetCarAnimTotalTime(car: number, animname0: string, animname1: string, time?: number): number;

declare function GetCarBlockingCar(car0: number, car1?: number): number;

declare function GetCarCharIsUsing(ped: number, pVehicle?: number): number;

declare function GetCarColours(vehicle: number): [number, number];

declare function GetCarCoordinates(vehicle: number): [number, number, number];

declare function GetCarDeformationAtPos(vehicle: number, x: number, y: number, z: number): number[];

declare function GetCarDoorLockStatus(vehicle: number, pValue?: number): number;

declare function GetCarForwardVector(car: number): number[];

declare function GetCarForwardX(vehicle: number, pValue?: number): number;

declare function GetCarForwardY(vehicle: number, pValue?: number): number;

declare function GetCarHeading(vehicle: number, pValue?: number): number;

declare function GetCarHealth(vehicle: number, pValue?: number): number;

declare function GetCarLivery(car: number, livery?: number): number;

declare function GetCarMass(car: number, mass?: number): number;

declare function GetCarModel(vehicle: number, pValue?: number): number;

declare function GetCarModelValue(car: number, value?: number): number;

declare function GetCarObjectIsAttachedTo(obj: number): number;

declare function GetCarPitch(vehicle: number, pValue?: number): number;

declare function GetCarRoll(vehicle: number, pValue?: number): number;

declare function GetCarSirenHealth(car: number): number;

declare function GetCarSpeed(vehicle: number, pValue?: number): number;

declare function GetCarSpeedVector(vehicle: number, unknownFalse: boolean): number[];

declare function GetCarUprightValue(vehicle: number, pValue?: number): number;

declare function GetCellphoneRanked(): boolean;

declare function GetCharAllowedToRunOnBoats(ped: number): boolean;

declare function GetCharAnimBlendAmount(ped: number, AnimName0: string, AnimName1: string, amount?: number): number;

declare function GetCharAnimCurrentTime(ped: number, animGroup: string, animName: string, pValue?: number): number;

declare function GetCharAnimIsEvent(ped: number, AnimName0: string, AnimName1: string, flag: boolean): boolean;

declare function GetCharAnimTotalTime(ped: number, animGroup: string, animName: string, pValue?: number): number;

declare function GetCharArmour(ped: number, pArmour?: number): number;

declare function GetCharCoordinates(ped: number): [number, number, number];

declare function GetCharDrawableVariation(ped: number, component: number): number;

declare function GetCharExtractedDisplacement(ped: number, unknown: boolean): [number, number, number];

declare function GetCharExtractedVelocity(ped: number, Unk5: boolean): [number, number, number];

declare function GetCharGravity(ped: number): number;

declare function GetCharHeading(ped: number, pValue?: number): number;

declare function GetCharHealth(ped: number, pHealth?: number): number;

declare function GetCharHeightAboveGround(ped: number, pValue?: number): number;

declare function GetCharHighestPriorityEvent(ped: number, event?: number): number;

declare function GetCharInCarPassengerSeat(vehicle: number, seatIndex: number, pPed?: number): number;

declare function GetCharLastDamageBone(ped: number, pBone?: number): [number, number];

declare function GetCharMaxMoveBlendRatio(ped: number): number;

declare function GetCharMeleeActionFlag0(ped: number): boolean;

declare function GetCharMeleeActionFlag1(ped: number): boolean;

declare function GetCharMeleeActionFlag2(ped: number): boolean;

declare function GetCharModel(ped: number, pModel?: number): number;

declare function GetCharMoney(ped: number): number;

declare function GetCharMoveAnimSpeedMultiplier(ped: number, multiplier?: number): number;

declare function GetCharMovementAnimsBlocked(ped: number): boolean;

declare function GetCharPropIndex(ped: number, unknown: boolean, pIndex?: number): number;

declare function GetCharReadyToBeExecuted(ped: number): boolean;

declare function GetCharReadyToBeStunned(ped: number): boolean;

declare function GetCharSpeed(ped: number, pValue?: number): number;

declare function GetCharSwimState(ped: number, state?: number): [boolean, number];

declare function GetCharTextureVariation(ped: number, component: number): number;

declare function GetCharVelocity(ped: number): [number, number, number];

declare function GetCharWalkAlongsideLeaderWhenAppropriate(ped: number): boolean;

declare function GetCharWeaponInSlot(ped: number, slot: number): [number, number, number];

declare function GetCharWillCowerInsteadOfFleeing(ped: number): boolean;

declare function GetCharWillTryToLeaveBoatAfterLeader(ped: number): boolean;

declare function GetCharWillTryToLeaveWater(ped: number): boolean;

declare function GetCinematicCam(cam: number): void;

declare function GetClosestCar(x: number, y: number, z: number, radius: number, unknownFalse: boolean, unknown70: number): number;

declare function GetClosestCarNode(x: number, y: number, z: number): [boolean, number, number, number];

declare function GetClosestCarNodeFavourDirection(Unk802: number, x: number, y: number, z: number): [boolean, number, number, number, number];

declare function GetClosestCarNodeWithHeading(x: number, y: number, z: number): [boolean, number, number, number, number];

declare function GetClosestChar(x: number, y: number, z: number, radius: number, unknown1: boolean, unknown2: boolean, pPed?: number): [boolean, number];

declare function GetClosestMajorCarNode(x: number, y: number, z: number): [boolean, number, number, number];

declare function GetClosestNetworkRestartNode(Unk1008: number, Unk1009: number, Unk1010: number, Unk1011: number, Unk1012: number): number;

declare function GetClosestRoad(x: number, y: number, z: number, Unk803: number, Unk804: number): [boolean, number[], number[], number, number, number];

declare function GetClosestStealableObject(x: number, y: number, z: number, radius: number, obj?: number): number;

declare function GetConsoleCommandToken(): number;

declare function GetContentsOfTextWidget(Unk1090: number): number;

declare function GetControlValue(Unk831: number, controlid: number): number;

/**
 * Can be used to get a console variable of type `char*`, for example a string.
 * @param varName The console variable to look up.
 * @param default_ The default value to set if none is found.
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
declare function GetConvar(varName: string, default_: string): string;

/**
 * Can be used to get a console variable casted back to `bool`.
 * @param varName The console variable to look up.
 * @param defaultValue The default value to set if none is found.
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
declare function GetConvarBool(varName: string, defaultValue: boolean): boolean;

/**
 * This will have floating point inaccuracy.
 * @param varName The console variable to get
 * @param defaultValue The default value to set, if none are found.
 * @return Returns the value set in varName, or `default` if none are specified
 */
declare function GetConvarFloat(varName: string, defaultValue: number): number;

/**
 * Can be used to get a console variable casted back to `int` (an integer value).
 * @param varName The console variable to look up.
 * @param default_ The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
declare function GetConvarInt(varName: string, default_: number): number;

declare function GetCoordinatesForNetworkRestartNode(Unk914: number, Unk915: number, Unk916: number): void;

declare function GetCorrectedColour(r: number, g: number, b: number): [number, number, number];

declare function GetCreateRandomCops(): boolean;

declare function GetCurrentBasicCopModel(pModel: number): void;

declare function GetCurrentBasicPoliceCarModel(pModel: number): void;

declare function GetCurrentCharWeapon(ped: number, pWeapon?: number): [boolean, number];

declare function GetCurrentCopModel(pModel: number): void;

declare function GetCurrentDate(): [number, number];

declare function GetCurrentDayOfWeek(): number;

declare function GetCurrentEpisode(): number;

/**
 * This native returns the currently used game's name.
 * @return The game name as a string, one of the following values: gta4, gta5, rdr3
 */
declare function GetCurrentGameName(): string;

declare function GetCurrentLanguage(): number;

declare function GetCurrentPlaybackNumberForCar(car: number): number;

declare function GetCurrentPoliceCarModel(pModel: number): void;

declare function GetCurrentPopulationZoneType(): number;

/**
 * Returns the name of the currently executing resource.
 * @return The name of the resource.
 */
declare function GetCurrentResourceName(): string;

declare function GetCurrentScriptedConversationLine(): number;

/**
 * Returns the peer address of the remote game server that the user is currently connected to.
 * @return The peer address of the game server (e.g. `127.0.0.1:30120`), or NULL if not available.
 */
declare function GetCurrentServerEndpoint(): string;

declare function GetCurrentStackSize(): number;

declare function GetCurrentStationForTrain(train: number): number;

declare function GetCurrentTaxiCarModel(pModel: number): void;

declare function GetCurrentWeather(pWeather: number): void;

declare function GetCurrentWeatherFull(): [number, number, number];

declare function GetCurrentZoneScumminess(): number;

declare function GetCutsceneAudioTimeMs(): number;

declare function GetCutscenePedPosition(unkped: number): number[];

declare function GetCutsceneSectionPlaying(): number;

declare function GetCutsceneTime(): number;

declare function GetDamageToPedBodyPart(ped: number, part: number): number;

declare function GetDeadCarCoordinates(vehicle: number): [number, number, number];

declare function GetDeadCharPickupCoords(ped: number): [number, number, number];

declare function GetDebugCam(cam: number): void;

declare function GetDestroyerOfNetworkId(playerIndex: number, id: number): number;

declare function GetDisplayNameFromVehicleModel(model: number): string;

declare function GetDistanceBetweenCoords_2d(x1: number, y1: number, x2: number, y2: number, pDist?: number): number;

declare function GetDistanceBetweenCoords_3d(x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, pDist?: number): number;

declare function GetDoorAngleRatio(vehicle: number, door: number, pAngleRatio?: number): number;

declare function GetDoorState(obj: number): [number, number];

declare function GetDriverOfCar(vehicle: number, pPed?: number): number;

/**
 * Returns the NUI window handle for a specified DUI browser object.
 * @param duiObject The DUI browser handle.
 * @return The NUI window handle, for use in e.g. CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.
 */
declare function GetDuiHandle(duiObject: number): string;

declare function GetEngineHealth(vehicle: number): number;

/**
 * ### Supported types
 * *   \[1] : Peds (including animals) and players.
 * *   \[2] : Vehicles.
 * *   \[3] : Objects (props), doors, and projectiles.
 * ### Coordinates need to be send unpacked (x,y,z)
 * ```lua
 * -- Define the allowed model hashes
 * local allowedModelHashes = { GetHashKey("p_crate03x"), GetHashKey("p_crate22x") }
 * -- Get the player's current coordinates
 * local playerCoords = GetEntityCoords(PlayerPedId())
 * -- Retrieve all entities of type Object (type 3) within a radius of 10.0 units
 * -- that match the allowed model hashes
 * -- and sort output entities by distance
 * local entities = GetEntitiesInRadius(playerCoords.x, playerCoords.y, playerCoords.z, 10.0, 3, true, allowedModelHashes)
 * -- Iterate through the list of entities and print their ids
 * for i = 1, #entities do
 * local entity = entities[i]
 * print(entity)
 * end
 * ```
 * @param x The X coordinate.
 * @param y The Y coordinate.
 * @param z The Z coordinate.
 * @param radius Max distance from coordinate to entity
 * @param entityType Entity types see list below
 * @param sortByDistance Sort output entites by distance from nearest to farthest
 * @param models List of allowed models its also optional
 * @return An array containing entity handles for each entity.
 */
declare function GetEntitiesInRadius(x: number, y: number, z: number, radius: number, entityType: number, sortByDistance: boolean, models: any): any;

/**
 * **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
 * Returns the memory address of an entity.
 * This native is intended for singleplayer debugging, and may not be available during multiplayer.
 * @param entity The handle of the entity to get the address of.
 * @return A pointer containing the memory address of the entity.
 */
declare function GetEntityAddress(entity: number): number;

/**
 * Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](#\_0x5BA35AAF).
 * @param bagName An internal state bag ID from the argument to a state bag change handler.
 * @return The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.
 */
declare function GetEntityFromStateBagName(bagName: string): number;

declare function GetEpisodeIndexFromSummons(): number;

declare function GetEpisodeName(episodeIndex: number): string;

/**
 * A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A float that contains the value stored in the Kvp or nil/null if none.
 */
declare function GetExternalKvpFloat(resource: string, key: string): number;

/**
 * A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A int that contains the value stored in the Kvp or nil/null if none.
 */
declare function GetExternalKvpInt(resource: string, key: string): number;

/**
 * A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A string that contains the value stored in the Kvp or nil/null if none.
 */
declare function GetExternalKvpString(resource: string, key: string): string;

declare function GetExtraCarColours(vehicle: number): [number, number];

declare function GetFilterMenuOn(): boolean;

declare function GetFilterSaveSetting(filterid: number): number;

declare function GetFirstBlipInfoId(_type: number): number;

declare function GetFirstNCharactersOfLiteralString(literalString: string, chars: number): string;

declare function GetFirstNCharactersOfString(gxtName: string, chars: number): string;

declare function GetFloatStat(stat: number): number;

declare function GetFollowVehicleCamSubmode(mode: number): void;

declare function GetFragmentDamageHealthOfClosestObjectOfType(x: number, y: number, z: number, radius: number, Unk77: number, flag: boolean): number;

declare function GetFrameCount(): number;

declare function GetFrameTime(time: number): void;

declare function GetFreeCam(cam: number): void;

declare function GetFrontendDesignValue(frontendid: number): [number, number];

/**
 * Returns the internal build number of the current game being executed.
 * Possible values:
 * *   FiveM
 * *   1604
 * *   2060
 * *   2189
 * *   2372
 * *   2545
 * *   2612
 * *   2699
 * *   2802
 * *   2944
 * *   3095
 * *   3258
 * *   3323
 * *   3407
 * *   3570
 * *   RedM
 * *   1311
 * *   1355
 * *   1436
 * *   1491
 * *   LibertyM
 * *   43
 * *   FXServer
 * *   0
 * @return The build number, or **0** if no build number is known.
 */
declare function GetGameBuildNumber(): number;

declare function GetGameCam(camera: number): void;

declare function GetGameCamChild(camera: number): void;

/**
 * Returns the current game being executed.
 * Possible values:
 * | Return value | Meaning                        |
 * | ------------ | ------------------------------ |
 * | `fxserver`   | Server-side code ('Duplicity') |
 * | `fivem`      | FiveM for GTA V                |
 * | `libertym`   | LibertyM for GTA IV            |
 * | `redm`       | RedM for Red Dead Redemption 2 |
 * @return The game the script environment is running in.
 */
declare function GetGameName(): string;

/**
 * Returns a list of entity handles (script GUID) for all entities in the specified pool - the data returned is an array as
 * follows:
 * ```json
 * [ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
 * ```
 * ### Supported pools
 * *   `CPed`: Peds (including animals) and players.
 * *   `CObject`: Objects (props), doors, and projectiles.
 * *   `CNetObject`: Networked objects
 * *   `CVehicle`: Vehicles.
 * *   `CPickup`: Pickups.
 * @param poolName The pool name to get a list of entities from.
 * @return An array containing entity handles for each entity in the named pool.
 */
declare function GetGamePool(poolName: string): any;

declare function GetGameTimer(pTimer: number): void;

declare function GetGameViewportId(viewportid: number): void;

declare function GetGamerNetworkScore(playerIndex: number, Unk888: number, Unk889: number): number;

declare function GetGfwlHasSafeHouse(): boolean;

declare function GetGfwlIsReturningToSinglePlayer(): boolean;

declare function GetGroundZFor_3dCoord(x: number, y: number, z: number, pGroundZ?: number): [number, number];

declare function GetGroupCharDucksWhenAimedAt(ped: number): boolean;

declare function GetGroupFormation(group: number, formation?: number): number;

declare function GetGroupFormationSpacing(group: number, spacing?: number): number;

declare function GetGroupLeader(group: number, pPed?: number): number;

declare function GetGroupMember(group: number, index: number, pPed?: number): number;

declare function GetGroupSize(group: number): [number, number];

declare function GetHashKey(value: string): number;

declare function GetHeadingFromVector_2d(x: number, y: number, pHeading?: number): number;

declare function GetHeightOfVehicle(vehicle: number, x: number, y: number, z: number, unknownTrue1: boolean, unknownTrue2: boolean): number;

declare function GetHelpMessageBoxSize(): [number, number];

declare function GetHostId(): number;

declare function GetHostMatchOn(): boolean;

declare function GetHoursOfDay(): number;

declare function GetHudColour(_type: number): [number, number, number, number];

declare function GetIdOfThisThread(): number;

/**
 * GET_INSTANCE_ID
 */
declare function GetInstanceId(): number;

declare function GetIntStat(stat: number): number;

declare function GetInteriorAtCoords(x: number, y: number, z: number, pInterior?: number): number;

declare function GetInteriorFromCar(vehicle: number, pInterior?: number): number;

declare function GetInteriorFromChar(ped: number, pInterior?: number): number;

declare function GetInteriorHeading(interior: number, pHeading?: number): number;

/**
 * GET_INVOKING_RESOURCE
 */
declare function GetInvokingResource(): string;

declare function GetIsAutosaveOff(): boolean;

declare function GetIsDepositAnimRunning(): boolean;

declare function GetIsDisplayingsavemessage(): boolean;

declare function GetIsHidef(): boolean;

declare function GetIsProjectileTypeInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, _type: number): boolean;

declare function GetIsStickyBombStuckToObject(obj: number): boolean;

declare function GetIsStickyBombStuckToVehicle(veh: number): boolean;

declare function GetIsWidescreen(): boolean;

declare function GetKeyForCarInRoom(vehicle: number, pKey?: number): number;

declare function GetKeyForCharInRoom(ped: number, pKey?: number): number;

declare function GetKeyForViewportInRoom(viewportid: number, roomkey?: number): number;

declare function GetKeyboardMoveInput(): [number, number];

declare function GetLatestConsoleCommand(): number;

declare function GetLeftPlayerCashToReachLevel(playerRank: number): number;

declare function GetLengthOfLiteralString(literalString: string): number;

declare function GetLengthOfStringWithThisHashKey(gxtkey: number): number;

declare function GetLengthOfStringWithThisTextLabel(gxtName: string): number;

declare function GetLengthOfStringWithThisTextLabelInsNum(Unk608: number, Unk609: number, Unk610: number): number;

declare function GetLevelDesignCoordsForObject(obj: number, Unk78: number): [number, number, number, number];

declare function GetLineHeight(): number;

declare function GetLocalGamerlevelFromProfilesettings(): number;

declare function GetLocalPlayerMpCash(): number;

declare function GetLocalPlayerWeaponStat(wtype: number, wid: number): number;

declare function GetMapAreaFromCoords(x: number, y: number, z: number): number;

declare function GetMaxAmmo(ped: number, weapon: number, pMaxAmmo?: number): [boolean, number];

declare function GetMaxAmmoInClip(ped: number, weapon: number, pMaxAmmo?: number): number;

declare function GetMaxWantedLevel(pMaxWantedLevel: number): void;

declare function GetMaximumNumberOfPassengers(vehicle: number, pMax?: number): number;

declare function GetMenuItemAccepted(menuid: number): number;

declare function GetMenuItemSelected(menuid: number): number;

declare function GetMenuPosition(menuid: number): [number, number];

declare function GetMinutesOfDay(): number;

declare function GetMinutesToTimeOfDay(hour: number, minute: number): number;

declare function GetMissionFlag(): boolean;

declare function GetMobilePhoneRenderId(pRenderId: number): void;

declare function GetMobilePhoneScale(): number;

declare function GetMobilePhoneTaskSubTask(ped: number, Unk798?: number): [boolean, number];

declare function GetModelDimensions(model: number): [number[], number[]];

declare function GetModelNameForDebug(model: number): string;

declare function GetModelPedIsHolding(ped: number): number;

declare function GetMouseInput(): [number, number];

declare function GetMousePosition(): [number, number];

declare function GetMouseSensitivity(): number;

declare function GetMouseWheel(Unk834: number): void;

declare function GetNameOfInfoZone(x: number, y: number, z: number): string;

declare function GetNameOfZone(x: number, y: number, z: number): string;

declare function GetNavmeshRouteResult(navmesh: number): number;

declare function GetNearestCableCar(x: number, y: number, z: number, radius: number, pVehicle?: number): number;

declare function GetNetworkIdFromObject(obj: number, netid?: number): number;

declare function GetNetworkIdFromPed(ped: number, netid?: number): number;

declare function GetNetworkIdFromVehicle(vehicle: number, netid?: number): number;

declare function GetNetworkJoinFail(): boolean;

declare function GetNetworkTimer(Unk917: number): void;

/**
 * GET_NETWORK_WALK_MODE
 */
declare function GetNetworkWalkMode(): boolean;

declare function GetNextBlipInfoId(_type: number): number;

declare function GetNextClosestCarNode(x: number, y: number, z: number): [boolean, number, number, number];

declare function GetNextClosestCarNodeFavourDirection(x: number, y: number, z: number): [boolean, number, number, number, number];

declare function GetNextClosestCarNodeWithHeading(x: number, y: number, z: number): [boolean, number, number, number, number];

declare function GetNextClosestCarNodeWithHeadingOnIsland(x: number, y: number, z: number): [boolean, number, number, number, number];

declare function GetNextStationForTrain(train: number): number;

declare function GetNoLawVehiclesDestroyedByLocalPlayer(): number;

declare function GetNoOfPlayersInTeam(team: number): number;

declare function GetNthClosestCarNode(x: number, y: number, z: number, n: number): [boolean, number, number, number];

declare function GetNthClosestCarNodeFavourDirection(Unk810: number, x: number, y: number, z: number, n: number): [boolean, number, number, number, number];

declare function GetNthClosestCarNodeWithHeading(x: number, y: number, z: number, nodeNum: number): [boolean, number, number, number, number];

declare function GetNthClosestCarNodeWithHeadingOnIsland(x: number, y: number, z: number, nodeNum: number, areaId: number): [boolean, number, number, number, number, number];

declare function GetNthClosestWaterNodeWithHeading(x: number, y: number, z: number, flag0: boolean, flag1: boolean): [boolean, number[], number];

declare function GetNthGroupMember(group: number, n: number, ped?: number): number;

declare function GetNthIntegerInString(gxtName: string, index: number): string;

/**
 * GET_NUI_CURSOR_POSITION
 */
declare function GetNuiCursorPosition(): [number, number];

declare function GetNumCarColours(vehicle: number, pNumColours?: number): number;

declare function GetNumCarLiveries(car: number, num?: number): number;

declare function GetNumOfModelsKilledByPlayer(player: number, model: number, num?: number): number;

/**
 * Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
 * See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
 * @param resourceName The resource name.
 * @param metadataKey The key to look up in the resource manifest.
 */
declare function GetNumResourceMetadata(resourceName: string, metadataKey: string): number;

/**
 * GET_NUM_RESOURCES
 */
declare function GetNumResources(): number;

declare function GetNumStreamingRequests(): number;

declare function GetNumberLines(Unk703: number, Unk704: number, str: string): number;

declare function GetNumberLinesWithLiteralStrings(Unk705: number, Unk706: number, str1: string, str2: string, str3: string): number;

declare function GetNumberLinesWithSubstrings(Unk707: number, Unk708: number, str1: string, str2: string, str3: string): number;

declare function GetNumberOfActiveStickyBombsOwnedByPed(ped: number): number;

declare function GetNumberOfCharDrawableVariations(ped: number, component: number): number;

declare function GetNumberOfCharTextureVariations(ped: number, component: number, unknown1: number): number;

declare function GetNumberOfFiresInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): number;

declare function GetNumberOfFiresInRange(x: number, y: number, z: number, radius: number): number;

declare function GetNumberOfFollowers(ped: number, followers?: number): number;

declare function GetNumberOfInjuredPedsInRange(x: number, y: number, z: number, radius: number): number;

declare function GetNumberOfInstancesOfStreamedScript(scriptName: string): number;

declare function GetNumberOfPassengers(vehicle: number, pNumPassengers?: number): number;

declare function GetNumberOfPlayers(): number;

declare function GetNumberOfStickyBombsStuckToObject(obj: number): number;

declare function GetNumberOfStickyBombsStuckToVehicle(veh: number): number;

declare function GetNumberOfWebPageLinks(htmlviewport: number): number;

declare function GetObjectAnimCurrentTime(obj: number, animname0: string, animname1: string, time?: number): number;

declare function GetObjectAnimTotalTime(obj: number, animname0: string, animname1: string, time?: number): number;

declare function GetObjectCoordinates(obj: number): [number, number, number];

declare function GetObjectFragmentDamageHealth(obj: number, unknown: boolean): number;

declare function GetObjectFromNetworkId(netid: number, obj?: number): number;

declare function GetObjectHeading(obj: number, pHeading?: number): number;

declare function GetObjectHealth(obj: number, pHealth?: number): number;

declare function GetObjectMass(obj: number, mass?: number): number;

declare function GetObjectModel(obj: number, pModel?: number): number;

declare function GetObjectPedIsHolding(ped: number): number;

declare function GetObjectQuaternion(obj: number): [number, number, number, number];

declare function GetObjectRotationVelocity(obj: number): [number, number, number];

declare function GetObjectSpeed(obj: number, pSpeed?: number): number;

declare function GetObjectTurnMass(obj: number, turnmass?: number): number;

declare function GetObjectVelocity(obj: number): [number, number, number];

declare function GetOffsetFromCarGivenWorldCoords(vehicle: number, x: number, y: number, z: number): [number, number, number];

declare function GetOffsetFromCarInWorldCoords(vehicle: number, x: number, y: number, z: number): [number, number, number];

declare function GetOffsetFromCharInWorldCoords(ped: number, x: number, y: number, z: number): [number, number, number];

declare function GetOffsetFromInteriorInWorldCoords(interior: number, x: number, y: number, z: number, pOffset?: number): number;

declare function GetOffsetFromObjectInWorldCoords(obj: number, x: number, y: number, z: number): [number, number, number];

declare function GetOffsetsForAttachCarToCar(car0: number, car1: number): [number[], number[]];

declare function GetOnlineLan(): number;

declare function GetOnlineScore(Unk887: number): number;

declare function GetPadPitchRoll(padIndex: number): [boolean, number, number];

declare function GetPadState(Unk835: number, Unk836: number, Unk837?: number): number;

declare function GetPedBonePosition(ped: number, bone: number, x: number, y: number, z: number): number[];

declare function GetPedClimbState(ped: number): number;

declare function GetPedFromNetworkId(netid: number, ped?: number): number;

declare function GetPedGroupIndex(ped: number, pIndex?: number): number;

declare function GetPedModelFromIndex(index: number): number;

declare function GetPedObjectIsAttachedTo(obj: number): number;

declare function GetPedPathMayDropFromHeight(ped: number): boolean;

declare function GetPedPathMayUseClimbovers(ped: number): boolean;

declare function GetPedPathMayUseLadders(ped: number): boolean;

declare function GetPedPathWillAvoidDynamicObjects(ped: number): boolean;

declare function GetPedSteersAroundObjects(ped: number): boolean;

declare function GetPedSteersAroundPeds(ped: number): boolean;

declare function GetPedType(ped: number, pType?: number): number;

declare function GetPetrolTankHealth(vehicle: number): number;

declare function GetPhysicalScreenResolution(): [number, number];

declare function GetPickupCoordinates(pickup: number): [number, number, number];

declare function GetPlaneUndercarriagePosition(plane: number, pos?: number): number;

declare function GetPlayerChar(playerIndex: number, pPed?: number): number;

declare function GetPlayerColour(Player: number): number;

/**
 * Gets a local client's Player ID from its server ID counterpart, assuming the passed `serverId` exists on the client.
 * If no matching client is found, or an invalid value is passed over as the `serverId` native's parameter, the native result will be `-1`.
 * It's worth noting that this native method can only retrieve information about clients that are culled to the connected client.
 * @param serverId The player's server ID.
 * @return A valid Player ID if one is found, `-1` if not.
 */
declare function GetPlayerFromServerId(serverId: number): number;

/**
 * On the server this will return the players source, on the client it will return the player handle.
 * @param bagName An internal state bag ID from the argument to a state bag change handler.
 * @return The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.
 */
declare function GetPlayerFromStateBagName(bagName: string): number;

declare function GetPlayerGroup(playerIndex: number, pGroup?: number): number;

declare function GetPlayerHasTracks(): boolean;

declare function GetPlayerId(): number;

declare function GetPlayerIdForThisPed(ped: number): number;

declare function GetPlayerMaxArmour(playerIndex: number, pMaxArmour?: number): number;

declare function GetPlayerMaxHealth(player: number, maxhealth?: number): number;

declare function GetPlayerName(playerIndex: number): string;

declare function GetPlayerRadioMode(): number;

declare function GetPlayerRadioStationIndex(): number;

declare function GetPlayerRadioStationName(): string;

declare function GetPlayerRadioStationName(): string;

declare function GetPlayerRadioStationNameRoll(): string;

declare function GetPlayerRankLevelDuringMp(playerIndex: number): number;

declare function GetPlayerRgbColour(Player: number): [number, number, number];

/**
 * GET_PLAYER_SERVER_ID
 */
declare function GetPlayerServerId(player: number): number;

declare function GetPlayerTeam(Player: number): number;

declare function GetPlayerToPlaceBombInCar(vehicle: number): number;

declare function GetPlayerWantedLevelIncrement(player: number, increment?: number): number;

declare function GetPlayersLastCarNoSave(pVehicle: number): void;

declare function GetPlayersettingsModelChoice(): number;

declare function GetPositionOfAnalogueSticks(padIndex: number): [number, number, number, number];

declare function GetPositionOfCarRecordingAtTime(CarRec: number, time: number, pos?: number): number;

declare function GetProfileSetting(settingid: number): number;

declare function GetProgressPercentage(): number;

declare function GetRadarViewportId(viewport: number): void;

declare function GetRadioName(id: number): string;

declare function GetRandomCarBackBumperInSphere(x: number, y: number, z: number, radius: number, Unk812: number, Unk813: number, veh?: number): number;

declare function GetRandomCarFrontBumperInSphereNoSave(x: number, y: number, z: number, radius: number, flag0: boolean, flag1: boolean, flag2: boolean): number;

declare function GetRandomCarInSphere(x: number, y: number, z: number, radius: number, model: number, Unk814: number, car?: number): number;

declare function GetRandomCarInSphereNoSave(x: number, y: number, z: number, radius: number, model: number, flag: boolean, car?: number): number;

declare function GetRandomCarModelInMemory(MustIncludeSpecialModels: boolean): [number, number];

declare function GetRandomCarNode(x: number, y: number, z: number, radius: number, flag0: boolean, flag1: boolean, flag2: boolean): [boolean, number, number, number, number];

declare function GetRandomCarNodeIncludeSwitchedOffNodes(x: number, y: number, z: number, radius: number, flag0: boolean, flag1: boolean, flag2: boolean): [boolean, number, number, number, number];

declare function GetRandomCarOfTypeInAngledAreaNoSave(Unk815: number, Unk816: number, Unk817: number, Unk818: number, Unk819: number, _type: number, car?: number): number;

declare function GetRandomCarOfTypeInAreaNoSave(x0: number, y0: number, x1: number, y1: number, model: number, car?: number): number;

declare function GetRandomCharInAreaOffsetNoSave(x: number, y: number, z: number, sx: number, sy: number, sz: number, pPed?: number): number;

declare function GetRandomNetworkRestartNode(Unk1013: number, Unk1014: number, Unk1015: number, Unk1016: number, Unk1017: number, Unk1018: number): number;

declare function GetRandomNetworkRestartNodeUsingGroupList(Unk1019: number, Unk1020: number, Unk1021: number, Unk1022: number, Unk1023: number, Unk1024: number): number;

declare function GetRandomWaterNode(x: number, y: number, z: number, radius: number, flag0: boolean, flag1: boolean, flag2: boolean, flag3: boolean): [boolean, number, number, number, number];

/**
 * Returns all commands that are registered in the command system.
 * The data returned adheres to the following layout:
 * ```
 * [
 * {
 * "name": "cmdlist",
 * "resource": "resource",
 * "arity" = -1,
 * },
 * {
 * "name": "command1"
 * "resource": "resource_2",
 * "arity" = -1,
 * }
 * ]
 * ```
 * @return An object containing registered commands.
 */
declare function GetRegisteredCommands(): any;

/**
 * GET_RESOURCE_BY_FIND_INDEX
 * @param findIndex The index of the resource (starting at 0)
 * @return The resource name as a `string`
 */
declare function GetResourceByFindIndex(findIndex: number): string;

/**
 * Returns all commands registered by the specified resource.
 * The data returned adheres to the following layout:
 * ```
 * [
 * {
 * "name": "cmdlist",
 * "resource": "example_resource",
 * "arity" = -1,
 * },
 * {
 * "name": "command1"
 * "resource": "example_resource2",
 * "arity" = -1,
 * }
 * ]
 * ```
 * @return An object containing registered commands.
 */
declare function GetResourceCommands(resource: string): any;

/**
 * A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
 * @param key The key to fetch
 * @return The floating-point value stored under the specified key, or 0.0 if not found.
 */
declare function GetResourceKvpFloat(key: string): number;

/**
 * A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
 * @param key The key to fetch
 * @return The integer value stored under the specified key, or 0 if not found.
 */
declare function GetResourceKvpInt(key: string): number;

/**
 * A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
 * @param key The key to fetch
 * @return The string value stored under the specified key, or nil/null if not found.
 */
declare function GetResourceKvpString(key: string): string;

/**
 * Gets the metadata value at a specified key/index from a resource's manifest.
 * See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
 * @param resourceName The resource name.
 * @param metadataKey The key in the resource manifest.
 * @param index The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].
 */
declare function GetResourceMetadata(resourceName: string, metadataKey: string, index: number): string;

/**
 * Returns the current state of the specified resource.
 * @param resourceName The name of the resource.
 * @return The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.
 */
declare function GetResourceState(resourceName: string): string;

declare function GetReturnToFilterMenu(): boolean;

declare function GetRoomKeyFromObject(obj: number, pRoomKey?: number): number;

declare function GetRoomKeyFromPickup(pickup: number, hash?: number): number;

declare function GetRootCam(rootcam: number): void;

declare function GetRouteSize(): number;

declare function GetSafePickupCoords(x: number, y: number, z: number): [number, number, number];

declare function GetSafePositionForChar(x: number, y: number, z: number, unknownTrue: boolean): [boolean, number, number, number];

declare function GetScreenFadeAlpha(): number;

declare function GetScreenResolution(): [number, number];

declare function GetScreenViewportId(viewportid: number): void;

declare function GetScriptCam(cam: number): void;

declare function GetScriptDrawCam(cam: number): void;

declare function GetScriptFireCoords(fire: number): [number, number, number];

declare function GetScriptRendertargetRenderId(pRenderId: number): void;

declare function GetScriptTaskStatus(ped: number, task: number, status?: number): number;

declare function GetSequenceProgress(seq: number, progress?: number): number;

declare function GetServerId(): number;

declare function GetSimpleBlipId(): number;

declare function GetSortedNetworkRestartNode(Unk1025: number, Unk1026: number, Unk1027: number, Unk1028: number, Unk1029: number, Unk1030: number, Unk1031: number, Unk1032: number, Unk1033: number): number;

declare function GetSortedNetworkRestartNodeUsingGroupList(Unk1034: number, Unk1035: number, Unk1036: number, Unk1037: number, Unk1038: number, Unk1039: number, Unk1040: number, Unk1041: number, Unk1042: number): number;

declare function GetSoundId(): number;

declare function GetSoundLevelAtCoords(ped: number, x: number, y: number, z: number, level?: number): number;

declare function GetSpawnCoordinatesForCarNode(Unk918: number, Unk919: number, Unk920: number, Unk921: number, Unk922: number, Unk923: number): void;

declare function GetSpeechForEmergencyServiceCall(): string;

declare function GetStartFromFilterMenu(): number;

declare function GetStatFrontendDisplayType(stat: number): number;

declare function GetStatFrontendVisibility(stat: number): boolean;

/**
 * GET_STATE_BAG_KEYS
 * @param bagName The name of the bag.
 * @return Returns an array containing all keys for which the state bag has associated values.
 */
declare function GetStateBagKeys(bagName: string): any;

/**
 * Returns the value of a state bag key.
 * @return Value.
 */
declare function GetStateBagValue(bagName: string, key: string): any;

declare function GetStateOfClosestDoorOfType(model: number, x: number, y: number, z: number): [number, number];

declare function GetStaticEmitterPlaytime(StaticEmitterIndex: number): number;

declare function GetStationName(train: number, station: number): string;

declare function GetStreamBeatInfo(): [number, number, number];

declare function GetStreamPlaytime(): number;

declare function GetStringFromHashKey(hash: number): string;

declare function GetStringFromString(str: string, startsymb: number, endsymb: number): string;

declare function GetStringFromTextFile(gxtentry: string): string;

declare function GetStringWidth(gxtName: string): number;

declare function GetStringWidthWithNumber(gxtName: string, _number: number): number;

declare function GetStringWidthWithString(gxtName: string, literalString: string): number;

declare function GetStringWidthWithTextAndInt(gxtname: string, gxtnamenext: string, val: number): number;

declare function GetTaskPlaceCarBombUnsuccessful(): boolean;

declare function GetTeamRgbColour(team: number): [number, number, number];

declare function GetTextInputActive(): boolean;

declare function GetTexture(dictionary: number, textureName: string): number;

declare function GetTextureFromStreamedTxd(txdName: string, textureName: string): number;

declare function GetTextureResolution(texture: number): [number, number];

declare function GetTimeOfDay(): [number, number];

declare function GetTimeSinceLastArrest(): number;

declare function GetTimeSinceLastDeath(): number;

declare function GetTimeSincePlayerDroveAgainstTraffic(playerIndex: number): number;

declare function GetTimeSincePlayerDroveOnPavement(playerIndex: number): number;

declare function GetTimeSincePlayerHitBuilding(playerIndex: number): number;

declare function GetTimeSincePlayerHitCar(playerIndex: number): number;

declare function GetTimeSincePlayerHitObject(playerIndex: number): number;

declare function GetTimeSincePlayerHitPed(playerIndex: number): number;

declare function GetTimeSincePlayerRanLight(playerIndex: number): number;

declare function GetTimeTilNextStation(train: number): number;

/**
 * GET_TIMECYCLE_MODIFIER_COUNT
 * @return Returns the amount of timecycle modifiers loaded.
 */
declare function GetTimecycleModifierCount(): number;

/**
 * GET_TIMECYCLE_MODIFIER_INDEX_BY_NAME
 * @param modifierName The timecycle modifier name.
 * @return The timecycle modifier index.
 */
declare function GetTimecycleModifierIndexByName(modifierName: string): number;

/**
 * GET_TIMECYCLE_MODIFIER_NAME_BY_INDEX
 * @param modifierIndex The timecycle modifier index.
 * @return The timecycle modifier name.
 */
declare function GetTimecycleModifierNameByIndex(modifierIndex: number): string;

/**
 * A getter for [SET_TIMECYCLE_MODIFIER_STRENGTH](#\_0x82E7FFCD5B2326B3).
 * @return Returns current timecycle modifier strength.
 */
declare function GetTimecycleModifierStrength(): number;

/**
 * GET_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @return Whether or not variable by name was found on the specified timecycle modifier.
 */
declare function GetTimecycleModifierVar(modifierName: string, varName: string): [boolean, number, number];

/**
 * GET_TIMECYCLE_MODIFIER_VAR_COUNT
 * @param modifierName The timecycle modifier name.
 * @return The amount of variables used on a specified timecycle modifier.
 */
declare function GetTimecycleModifierVarCount(modifierName: string): number;

/**
 * GET_TIMECYCLE_MODIFIER_VAR_NAME_BY_INDEX
 * @param modifierName The name of timecycle modifier.
 * @param modifierVarIndex The index of a variable on the specified timecycle modifier.
 * @return The name of a variable by index.
 */
declare function GetTimecycleModifierVarNameByIndex(modifierName: string, modifierVarIndex: number): string;

/**
 * Returns the amount of variables available to be applied on timecycle modifiers.
 * @return The amount of available variables for timecycle modifiers.
 */
declare function GetTimecycleVarCount(): number;

/**
 * See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
 * @param varIndex The index of variable.
 * @return The default value of a timecycle variable.
 */
declare function GetTimecycleVarDefaultValueByIndex(varIndex: number): number;

/**
 * See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
 * @param varIndex The index of variable.
 * @return The name of a timecycle variable.
 */
declare function GetTimecycleVarNameByIndex(varIndex: number): string;

declare function GetTotalDurationOfCarRecording(CarRec: number): number;

declare function GetTotalNumberOfStats(): number;

declare function GetTrainCaboose(train: number, caboose?: number): number;

declare function GetTrainCarriage(train: number, num: number, carriage?: number): number;

declare function GetTrainPlayerWouldEnter(player: number, train?: number): number;

declare function GetTxd(txdName: string): number;

declare function GetVehicleComponentInfo(veh: number, component_id: number, flag: boolean): [boolean, number[], number[], number];

declare function GetVehicleDirtLevel(vehicle: number, pIntensity?: number): number;

declare function GetVehicleEngineRevs(veh: number): number;

declare function GetVehicleFromNetworkId(netid: number, vehicle?: number): number;

declare function GetVehicleGear(veh: number): number;

/**
 * **Note**: Flags are not the same based on your `gamebuild`. Please see [here](https://docs.fivem.net/docs/game-references/vehicle-references/vehicle-flags) to see a complete list of all vehicle flags.
 * Get vehicle.meta flag by index. Useful examples include `FLAG_LAW_ENFORCEMENT` (31), `FLAG_RICH_CAR` (36), `FLAG_IS_ELECTRIC` (43), `FLAG_IS_OFFROAD_VEHICLE` (48).
 * @param vehicle The vehicle to obtain flags for.
 * @param flagIndex Flag index.
 * @return A boolean for whether the flag is set.### Example```lua
local vehicleFlags = {
    FLAG_SMALL_WORKER = 0,
    FLAG_BIG = 1,
    FLAG_NO_BOOT = 2,
    FLAG_ONLY_DURING_OFFICE_HOURS = 3
    -- This is just a example, see fivem-docs to see all flags.
}

local function getAllVehicleFlags(vehicle)
    local flags = {}
    for i = 0, 256 do
        if GetVehicleHasFlag(vehicle, i) then
            flags[#flags+1] = i
        end
    end
    return flags
end

local flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
print(getAllVehicleFlags)
``````javascript
const VEHICLE_FLAGS = {
    FLAG_SMALL_WORKER: 0,
    FLAG_BIG: 1,
    FLAG_NO_BOOT: 2,
    FLAG_ONLY_DURING_OFFICE_HOURS: 3
    // This is just a example, see fivem-docs to see all flags.
};

function getAllVehicleFlags(mVehicle = GetVehiclePedIsIn(PlayerPedId(), false)) {
    const flags = [];
    for (let i = 0; i < 204; i++) {
        if (GetVehicleHasFlag(mVehicle, i)) {
            flags.push(i);
        }
    }
    return flags;
}

let flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false);
console.log(getAllVehicleFlags);
```
 */
declare function GetVehicleHasFlag(vehicle: number, flagIndex: number): boolean;

declare function GetVehicleModelFromIndex(index: number): number;

declare function GetVehiclePlayerWouldEnter(player: number, veh?: number): number;

declare function GetVehicleQuaternion(veh: number): [number, number, number, number];

/**
 * Returns the type of the passed vehicle.
 * For client scripts, reference the more detailed [GET_VEHICLE_TYPE_RAW](#\_0xDE73BC10) native.
 * ### Vehicle types
 * *   automobile
 * *   bike
 * *   boat
 * *   heli
 * *   plane
 * *   submarine
 * *   trailer
 * *   train
 * @param vehicle The vehicle's entity handle.
 * @return If the entity is a vehicle, the vehicle type. If it is not a vehicle, the return value will be null.
 */
declare function GetVehicleType(vehicle: number): string;

declare function GetVehicleTypeOfModel(model: number): number;

/**
 * Returns the model type of the vehicle as defined by:
 * ```cpp
 * enum VehicleType
 * {
 * VEHICLE_TYPE_NONE = -1,
 * VEHICLE_TYPE_CAR = 0,
 * VEHICLE_TYPE_PLANE = 1,
 * VEHICLE_TYPE_TRAILER = 2,
 * VEHICLE_TYPE_QUADBIKE = 3,
 * VEHICLE_TYPE_DRAFT = 4,
 * VEHICLE_TYPE_SUBMARINECAR = 5,
 * VEHICLE_TYPE_AMPHIBIOUS_AUTOMOBILE = 6,
 * VEHICLE_TYPE_AMPHIBIOUS_QUADBIKE = 7,
 * VEHICLE_TYPE_HELI = 8,
 * VEHICLE_TYPE_BLIMP = 9,
 * VEHICLE_TYPE_AUTOGYRO = 10,
 * VEHICLE_TYPE_BIKE = 11,
 * VEHICLE_TYPE_BICYCLE = 12,
 * VEHICLE_TYPE_BOAT = 13,
 * VEHICLE_TYPE_TRAIN = 14,
 * VEHICLE_TYPE_SUBMARINE = 15,
 * };
 * ```
 * @param vehicle The vehicle's entity handle.
 * @return Returns the vehicles model type
 */
declare function GetVehicleTypeRaw(vehicle: number): number;

declare function GetViewportPosAndSize(viewportid: number): [number, number, number, number];

declare function GetViewportPositionOfCoord(x: number, y: number, z: number): [boolean, number, number, number];

/**
 * A getter for [SET_VISUAL_SETTING_FLOAT](#\_0xD1D31681).
 * @param name The name of the value to get, such as `pedLight.color.red`.
 * @return Returns the floating point value of the specified visual setting on success.
 */
declare function GetVisualSettingFloat(name: string): number;

declare function GetWaterHeight(x: number, y: number, z: number, pheight?: number): [boolean, number];

declare function GetWaterHeightNoWaves(x: number, y: number, z: number, height?: number): [boolean, number];

declare function GetWeapontypeModel(weapontype: number, model?: number): number;

declare function GetWeapontypeSlot(weapon: number, slot?: number): number;

declare function GetWebPageHeight(htmlviewport: number): number;

declare function GetWebPageLinkAtPosn(htmlviewport: number, x: number, y: number): number;

declare function GetWebPageLinkHref(htmlviewport: number, linkid: number): string;

declare function GetWebPageLinkPosn(htmlviewport: number, linkid: number): [number, number];

declare function GetWidthOfLiteralString(str: string): number;

declare function GetWidthOfSubstringGivenTextLabel(gxtname: string, Unk611: boolean, Unk612: number, Unk613: number, Unk614: number): number;

/**
 * Converts a screen coordinate into its relative world coordinate.
 * @param screenX A screen horizontal axis coordinate (0.0 - 1.0).
 * @param screenY A screen vertical axis coordinate (0.0 - 1.0).
 * @param worldVector The world coord vector pointer.
 * @param normalVector The screen normal vector pointer.
 * @return A Vector3 representing the world coordinates relative to the specified screen coordinates and a screen plane normal Vector3 (normalised).
 */
declare function GetWorldCoordFromScreenCoord(screenX: number, screenY: number): [number[], number[]];

declare function GiveDelayedWeaponToChar(ped: number, weapon: number, delaytime: number, flag: boolean): void;

declare function GivePedAmbientObject(ped: number, model: number): void;

declare function GivePedFakeNetworkName(ped: number, name: string, r: number, g: number, b: number, a: number): void;

declare function GivePedHelmet(ped: number): void;

declare function GivePedHelmetWithOpts(ped: number, Unk42: boolean): void;

declare function GivePedPickupObject(ped: number, obj: number, flag: boolean): void;

declare function GivePlayerRagdollControl(player: number, give: boolean): void;

declare function GiveWeaponToChar(ped: number, weapon: number, ammo: number, unknown0: boolean): void;

declare function GrabNearbyObjectWithSpecialAttribute(attribute: number, obj?: number): number;

declare function HandVehicleControlBackToPlayer(veh: number): void;

declare function HandleAudioAnimEvent(ped: number, AudioAnimEventName: string): void;

declare function HasAchievementBeenPassed(achievement: number): boolean;

declare function HasAdditionalTextLoaded(textIndex: number): boolean;

declare function HasCarBeenDamagedByCar(vehicle: number, otherCar: number): boolean;

declare function HasCarBeenDamagedByChar(vehicle: number, ped: number): boolean;

declare function HasCarBeenDamagedByWeapon(vehicle: number, weapon: number): boolean;

declare function HasCarBeenDroppedOff(car: number): boolean;

declare function HasCarBeenResprayed(vehicle: number): boolean;

declare function HasCarRecordingBeenLoaded(CarRec: number): boolean;

declare function HasCarStoppedBecauseOfLight(car: number): boolean;

declare function HasCharAnimFinished(ped: number, AnimName0: string, AnimName1: string): boolean;

declare function HasCharBeenArrested(ped: number): boolean;

declare function HasCharBeenDamagedByCar(ped: number, vehicle: number): boolean;

declare function HasCharBeenDamagedByChar(ped: number, otherChar: number, unknownFalse: boolean): boolean;

declare function HasCharBeenDamagedByWeapon(ped: number, weapon: number): boolean;

declare function HasCharBeenPhotographed(ped: number): boolean;

declare function HasCharGotWeapon(ped: number, weapon: number): boolean;

declare function HasCharSpottedChar(ped: number, otherChar: number): boolean;

declare function HasCharSpottedCharInFront(ped: number, otherChar: number): boolean;

declare function HasClosestObjectOfTypeBeenDamagedByCar(x: number, y: number, z: number, radius: number, type_or_model: number, car: number): boolean;

declare function HasClosestObjectOfTypeBeenDamagedByChar(x: number, y: number, z: number, radius: number, objectModel: number, ped: number): boolean;

declare function HasCollisionForModelLoaded(model: number): boolean;

declare function HasControlOfNetworkId(netid: number): boolean;

declare function HasCutsceneFinished(): boolean;

declare function HasCutsceneLoaded(): boolean;

declare function HasDeatharrestExecuted(): boolean;

declare function HasFragmentRootOfClosestObjectOfTypeBeenDamaged(x: number, y: number, z: number, radius: number, Unk70: number): boolean;

declare function HasGamerChangedNetworkModelSettings(): boolean;

declare function HasModelLoaded(model: number): boolean;

declare function HasNetworkPlayerLeftGame(playerIndex: number): boolean;

declare function HasObjectBeenDamaged(obj: number): boolean;

declare function HasObjectBeenDamagedByCar(obj: number, vehicle: number): boolean;

declare function HasObjectBeenDamagedByChar(obj: number, ped: number): boolean;

declare function HasObjectBeenDamagedByWeapon(obj: number, Unk71: number): boolean;

declare function HasObjectBeenPhotographed(obj: number): boolean;

declare function HasObjectBeenUprooted(obj: number): boolean;

declare function HasObjectCollidedWithAnything(obj: number): boolean;

declare function HasObjectFragmentRootBeenDamaged(obj: number): boolean;

declare function HasOverridenSitIdleAnimFinished(ped: number): boolean;

declare function HasPickupBeenCollected(pickup: number): boolean;

declare function HasPlayerCollectedPickup(playerIndex: number, pikcup: number): boolean;

declare function HasPlayerDamagedAtLeastOnePed(playerIndex: number): boolean;

declare function HasPlayerDamagedAtLeastOneVehicle(playerIndex: number): boolean;

declare function HasPoolObjectCollidedWithCushion(obj: number): boolean;

declare function HasPoolObjectCollidedWithObject(obj: number, otherObj: number): boolean;

declare function HasReloadedWithMotionControl(ukn0: number): [boolean, any /* actually bool */];

declare function HasResprayHappened(): boolean;

declare function HasScriptLoaded(scriptName: string): boolean;

declare function HasSoundFinished(sound: number): boolean;

declare function HasStreamedTxdLoaded(txdName: string): boolean;

declare function HasThisAdditionalTextLoaded(textName: string, textIndex: number): boolean;

declare function HaveAnimsLoaded(animName: string): boolean;

declare function HaveRequestedPathNodesBeenLoaded(requestId: number): boolean;

declare function HeliAudioShouldSkipStartup(heli: number, skip: boolean): void;

declare function HideCharWeaponForScriptedCutscene(ped: number, hide: boolean): void;

declare function HideHelpTextThisFrame(): void;

declare function HideHudAndRadarThisFrame(): void;

declare function HighFallScream(ped: number): void;

declare function HighlightMenuItem(menuid: number, item: number, highlight: boolean): void;

declare function HintCam(x: number, y: number, z: number, Unk559: number, Unk560: number, Unk561: number, Unk562: number): void;

declare function HowLongHasNetworkPlayerBeenDeadFor(playerIndex: number): number;

declare function ImproveLowPerformanceMissionPerFrameFlag(): void;

declare function IncreasePlayerMaxArmour(player: number, armour: number): void;

declare function IncreasePlayerMaxHealth(player: number, maxhealth: number): void;

declare function IncrementFloatStat(stat: number, val: number): void;

declare function IncrementFloatStatNoMessage(stat: number, value: number): void;

declare function IncrementIntStat(stat: number, value: number): void;

declare function IncrementIntStatNoMessage(stat: number, value: number): void;

declare function InitCutscene(name: string): void;

declare function InitDebugWidgets(): void;

declare function InitFrontendHelperText(): void;

/**
 * IS_ACE_ALLOWED
 */
declare function IsAceAllowed(object: string): boolean;

declare function IsAmbientSpeechDisabled(ped: number): boolean;

declare function IsAmbientSpeechPlaying(ped: number): boolean;

declare function IsAnyCharShootingInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, flag: boolean): boolean;

declare function IsAnyPickupAtCoords(x: number, y: number, z: number): boolean;

declare function IsAnySpeechPlaying(ped: number): boolean;

declare function IsAreaOccupied(x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, unknownFalse1: boolean, unknownTrue: boolean, unknownFalse2: boolean, unknownFalse3: boolean, unknownFalse4: boolean): boolean;

declare function IsAutoAimingOn(): boolean;

declare function IsAutoSaveInProgress(): boolean;

declare function IsBigVehicle(vehicle: number): boolean;

declare function IsBitSet(val: number, bitnum: number): boolean;

declare function IsBlipShortRange(blip: number): boolean;

declare function IsBulletInArea(x: number, y: number, z: number, radius: number, unknownTrue: boolean): boolean;

declare function IsBulletInBox(x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, unknown: boolean): boolean;

declare function IsButtonJustPressed(padIndex: number, button: number): boolean;

declare function IsButtonPressed(padIndex: number, button: number): boolean;

declare function IsCamActive(camera: number): boolean;

declare function IsCamHappy(cam: number): boolean;

declare function IsCamInterpolating(): boolean;

declare function IsCamPropagating(camera: number): boolean;

declare function IsCamSequenceComplete(Unk535: number): boolean;

declare function IsCamShaking(): boolean;

declare function IsCarAMissionCar(vehicle: number): boolean;

declare function IsCarAttached(vehicle: number): boolean;

declare function IsCarDead(vehicle: number): boolean;

declare function IsCarDoorDamaged(vehicle: number, door: number): boolean;

declare function IsCarDoorFullyOpen(vehicle: number, door: number): boolean;

declare function IsCarHealthGreater(car: number, health: number): boolean;

declare function IsCarInAirProper(vehicle: number): boolean;

declare function IsCarInArea_2d(vehicle: number, x1: number, y1: number, x2: number, y2: number, unknownFalse: boolean): boolean;

declare function IsCarInArea_3d(vehicle: number, x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, unknownFalse: number): boolean;

declare function IsCarInGarageArea(garageName: string, vehicle: number): boolean;

declare function IsCarInWater(vehicle: number): boolean;

declare function IsCarLowRider(car: number): boolean;

declare function IsCarModel(vehicle: number, model: number): boolean;

declare function IsCarOnFire(vehicle: number): boolean;

declare function IsCarOnScreen(vehicle: number): boolean;

declare function IsCarPassengerSeatFree(vehicle: number, seatIndex: number): boolean;

declare function IsCarPlayingAnim(car: number, animname0: string, animname1: string): boolean;

declare function IsCarSirenOn(vehicle: number): boolean;

declare function IsCarStopped(vehicle: number): boolean;

declare function IsCarStoppedAtTrafficLights(vehicle: number): boolean;

declare function IsCarStreetRacer(car: number): boolean;

declare function IsCarStuck(car: number): boolean;

declare function IsCarStuckOnRoof(vehicle: number): boolean;

declare function IsCarTouchingCar(vehicle: number, otherCar: number): boolean;

declare function IsCarTyreBurst(vehicle: number, tyre: number): boolean;

declare function IsCarUpright(vehicle: number): boolean;

declare function IsCarUpsidedown(vehicle: number): boolean;

declare function IsCarWaitingForWorldCollision(vehicle: number): boolean;

declare function IsCharArmed(ped: number, slot: number): boolean;

declare function IsCharDead(ped: number): boolean;

declare function IsCharDucking(ped: number): boolean;

declare function IsCharFacingChar(ped: number, otherChar: number, angle: number): boolean;

declare function IsCharFatallyInjured(ped: number): boolean;

declare function IsCharGesturing(ped: number): boolean;

declare function IsCharGettingInToACar(ped: number): boolean;

declare function IsCharGettingUp(ped: number): boolean;

declare function IsCharHealthGreater(ped: number, health: number): boolean;

declare function IsCharInAir(ped: number): boolean;

declare function IsCharInAngledArea_2d(ped: number, x1: number, y1: number, x2: number, y2: number, unknown: number, unknownFalse: boolean): boolean;

declare function IsCharInAngledArea_3d(ped: number, x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, unknown: number, unknownFalse: boolean): boolean;

declare function IsCharInAnyBoat(ped: number): boolean;

declare function IsCharInAnyCar(ped: number): boolean;

declare function IsCharInAnyHeli(ped: number): boolean;

declare function IsCharInAnyPlane(ped: number): boolean;

declare function IsCharInAnyPoliceVehicle(ped: number): boolean;

declare function IsCharInAnyTrain(ped: number): boolean;

declare function IsCharInAreaOnFoot_2d(ped: number, x1: number, y1: number, x2: number, y2: number, unknownFalse: boolean): boolean;

declare function IsCharInArea_2d(ped: number, x1: number, y1: number, x2: number, y2: number, unknownFalse: boolean): boolean;

declare function IsCharInArea_3d(ped: number, x1: number, y1: number, z1: number, x2: number, y2: number, z2: number, unknownFalse: boolean): boolean;

declare function IsCharInCar(ped: number, vehicle: number): boolean;

declare function IsCharInFlyingVehicle(ped: number): boolean;

declare function IsCharInMeleeCombat(ped: number): boolean;

declare function IsCharInModel(ped: number, model: number): boolean;

declare function IsCharInTaxi(ped: number): boolean;

declare function IsCharInWater(ped: number): boolean;

declare function IsCharInZone(ped: number, zonename: string): boolean;

declare function IsCharInjured(ped: number): boolean;

declare function IsCharMale(ped: number): boolean;

declare function IsCharModel(ped: number, model: number): boolean;

declare function IsCharOnAnyBike(ped: number): boolean;

declare function IsCharOnFire(ped: number): boolean;

declare function IsCharOnFoot(ped: number): boolean;

declare function IsCharOnScreen(ped: number): boolean;

declare function IsCharPlayingAnim(ped: number, animSet: string, animName: string): boolean;

declare function IsCharRespondingToAnyEvent(ped: number): boolean;

declare function IsCharRespondingToEvent(ped: number, eventid: number): boolean;

declare function IsCharShooting(ped: number): boolean;

declare function IsCharShootingInArea(ped: number, x1: number, y1: number, x2: number, y2: number, unknownFalse: boolean): boolean;

declare function IsCharSittingIdle(ped: number): boolean;

declare function IsCharSittingInAnyCar(ped: number): boolean;

declare function IsCharSittingInCar(ped: number, vehicle: number): boolean;

declare function IsCharStopped(ped: number): boolean;

declare function IsCharStuckUnderCar(ped: number): boolean;

declare function IsCharSwimming(ped: number): boolean;

declare function IsCharTouchingChar(ped: number, otherChar: number): boolean;

declare function IsCharTouchingObject(ped: number, obj: number): boolean;

declare function IsCharTouchingObjectOnFoot(ped: number, obj: number): boolean;

declare function IsCharTouchingVehicle(ped: number, vehicle: number): boolean;

declare function IsCharTryingToEnterALockedCar(ped: number): boolean;

declare function IsCharUsingAnyScenario(ped: number): boolean;

declare function IsCharUsingMapAttractor(ped: number): boolean;

declare function IsCharUsingScenario(ped: number, scenarioName: string): boolean;

declare function IsCharVisible(ped: number): boolean;

declare function IsCharWaitingForWorldCollision(ped: number): boolean;

declare function IsClosestObjectOfTypeSmashedOrDamaged(x: number, y: number, z: number, radius: number, type_or_model: number, flag0: boolean, flag1: boolean): boolean;

declare function IsControlJustPressed(Unk822: number, controlid: number): boolean;

declare function IsControlPressed(Unk823: number, controlid: number): boolean;

declare function IsCopPedInArea_3dNoSave(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): boolean;

declare function IsCopVehicleInArea_3dNoSave(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): boolean;

declare function IsDamageTrackerActiveOnNetworkId(Unk882: number): boolean;

declare function IsDebugCameraOn(): boolean;

/**
 * Gets if the specified `rawKeyIndex` is pressed down, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
declare function IsDisabledRawKeyDown(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` is pressed, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of pressed state.
 */
declare function IsDisabledRawKeyPressed(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` was released, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of released state.
 */
declare function IsDisabledRawKeyReleased(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` is up, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of up state.
 */
declare function IsDisabledRawKeyUp(rawKeyIndex: number): boolean;

/**
 * Returns whether or not a browser is created for a specified DUI browser object.
 * @param duiObject The DUI browser handle.
 * @return A boolean indicating TRUE if the browser is created.
 */
declare function IsDuiAvailable(duiObject: number): boolean;

/**
 * Gets whether or not this is the CitizenFX server.
 * @return A boolean value.
 */
declare function IsDuplicityVersion(): boolean;

declare function IsEmergencyServicesVehicle(veh: number): boolean;

declare function IsEpisodeAvailable(episode: number): boolean;

declare function IsEpisodicDiscBuild(): boolean;

declare function IsExplosionInArea(expnum: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): boolean;

declare function IsExplosionInSphere(expnum: number, x: number, y: number, z: number, radius: number): boolean;

declare function IsFollowVehicleCamOffsetActive(): boolean;

declare function IsFontLoaded(font: number): boolean;

declare function IsFrontendFading(): boolean;

declare function IsGameInControlOfMusic(): boolean;

declare function IsGameKeyboardKeyJustPressed(key: number): boolean;

declare function IsGameKeyboardKeyPressed(key: number): boolean;

declare function IsGameKeyboardNavDownPressed(Unk824: boolean): boolean;

declare function IsGameKeyboardNavLeftPressed(Unk825: boolean): boolean;

declare function IsGameKeyboardNavRightPressed(Unk826: boolean): boolean;

declare function IsGameKeyboardNavUpPressed(Unk827: boolean): boolean;

declare function IsGarageClosed(garageName: string): boolean;

declare function IsGarageOpen(garageName: string): boolean;

declare function IsGroupLeader(ped: number, group: number): boolean;

declare function IsGroupMember(ped: number, group: number): boolean;

declare function IsHeliPartBroken(heli: number, flag0: boolean, flag1: boolean, flag2: boolean): boolean;

declare function IsHelpMessageBeingDisplayed(): boolean;

declare function IsHintRunning(): boolean;

declare function IsHudPreferenceSwitchedOn(): boolean;

declare function IsHudReticuleComplex(): boolean;

declare function IsInCarFireButtonPressed(): boolean;

declare function IsInLanMode(): boolean;

declare function IsInPlayerSettingsMenu(): boolean;

declare function IsInSpectatorMode(): boolean;

declare function IsInteriorScene(): boolean;

declare function IsJapaneseVersion(): boolean;

declare function IsKeyboardKeyJustPressed(key: number): boolean;

declare function IsKeyboardKeyPressed(key: number): boolean;

declare function IsLazlowStationLocked(): boolean;

declare function IsLcpdDataValid(): boolean;

declare function IsLookInverted(): boolean;

declare function IsMemoryCardInUse(): boolean;

declare function IsMessageBeingDisplayed(): boolean;

declare function IsMinigameInProgress(): boolean;

declare function IsMissionCompletePlaying(): boolean;

declare function IsMobilePhoneCallOngoing(): boolean;

declare function IsMobilePhoneRadioActive(): boolean;

declare function IsModelInCdimage(model: number): boolean;

declare function IsMoneyPickupAtCoords(x: number, y: number, z: number): boolean;

declare function IsMouseButtonJustPressed(Unk828: number): boolean;

declare function IsMouseButtonPressed(Unk829: number): boolean;

declare function IsMouseUsingVerticalInversion(): boolean;

declare function IsNetworkGamePending(): boolean;

declare function IsNetworkGameRunning(): boolean;

declare function IsNetworkPlayerActive(playerIndex: number): boolean;

declare function IsNetworkSession(): boolean;

declare function IsNextStationAllowed(veh: number): boolean;

declare function IsNonFragObjectSmashed(x: number, y: number, z: number, radius: number, model: number): boolean;

/**
 * Checks if keyboard input is enabled during NUI focus using `SET_NUI_FOCUS_KEEP_INPUT`.
 * @return True or false.
 */
declare function IsNuiFocusKeepingInput(): boolean;

/**
 * Returns the current NUI focus state previously set with `SET_NUI_FOCUS`.
 * @return True or false.
 */
declare function IsNuiFocused(): boolean;

declare function IsNumlockEnabled(): boolean;

declare function IsObjectAttached(obj: number): boolean;

declare function IsObjectInAngledArea_3d(obj: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, Unk72: number, flag: boolean): boolean;

declare function IsObjectInArea_2d(obj: number, x0: number, y0: number, x1: number, y2: number, flag: boolean): boolean;

declare function IsObjectInArea_3d(obj: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, flag: boolean): boolean;

declare function IsObjectInWater(obj: number): boolean;

declare function IsObjectOnFire(obj: number): boolean;

declare function IsObjectOnScreen(obj: number): boolean;

declare function IsObjectPlayingAnim(obj: number, animname0: string, animname1: string): boolean;

declare function IsObjectReassignmentInProgress(): boolean;

declare function IsObjectStatic(obj: number): boolean;

declare function IsObjectTouchingObject(obj0: number, obj1: number): boolean;

declare function IsObjectUpright(obj: number, angle: number): boolean;

declare function IsObjectWithinBrainActivationRange(obj: number): boolean;

declare function IsOurPlayerHigherPriorityForCarGeneration(playerIndex: number): boolean;

declare function IsPainPlaying(ped: number): boolean;

declare function IsPartyMode(): boolean;

declare function IsPauseMenuActive(): boolean;

declare function IsPayNSprayActive(): boolean;

declare function IsPcUsingJoypad(): boolean;

declare function IsPedAMissionPed(ped: number): boolean;

declare function IsPedAttachedToAnyCar(ped: number): boolean;

declare function IsPedAttachedToObject(ped: number, obj: number): boolean;

declare function IsPedBeingJacked(ped: number): boolean;

declare function IsPedClimbing(ped: number): boolean;

declare function IsPedDoingDriveby(ped: number): boolean;

declare function IsPedFleeing(ped: number): boolean;

declare function IsPedHoldingAnObject(ped: number): boolean;

declare function IsPedInCombat(ped: number): boolean;

declare function IsPedInCover(ped: number): boolean;

declare function IsPedInCutsceneBlockingBounds(ped: number): boolean;

declare function IsPedInGroup(ped: number): boolean;

declare function IsPedJacking(ped: number): boolean;

declare function IsPedLookingAtCar(ped: number, car: number): boolean;

declare function IsPedLookingAtObject(ped: number, obj: number): boolean;

declare function IsPedLookingAtPed(ped: number, otherChar: number): boolean;

declare function IsPedPinnedDown(ped: number): boolean;

declare function IsPedRagdoll(ped: number): boolean;

declare function IsPedRetreating(ped: number): boolean;

declare function IsPedsVehicleHot(ped: number): boolean;

declare function IsPlaceCarBombActive(): boolean;

declare function IsPlaybackGoingOnForCar(car: number): boolean;

declare function IsPlayerBeingArrested(): boolean;

declare function IsPlayerClimbing(playerIndex: number): boolean;

declare function IsPlayerControlOn(playerIndex: number): boolean;

declare function IsPlayerDead(playerIndex: number): boolean;

declare function IsPlayerFreeAimingAtChar(playerIndex: number, ped: number): boolean;

declare function IsPlayerFreeForAmbientTask(playerIndex: number): boolean;

declare function IsPlayerInRemoteMode(player: number): boolean;

declare function IsPlayerOnline(): boolean;

declare function IsPlayerPerformingStoppie(player: number): boolean;

declare function IsPlayerPerformingWheelie(player: number): boolean;

declare function IsPlayerPlaying(playerIndex: number): boolean;

declare function IsPlayerPressingHorn(playerIndex: number): boolean;

declare function IsPlayerReadyForCutscene(player: number): boolean;

declare function IsPlayerScriptControlOn(player: number): boolean;

declare function IsPlayerSignedInLocally(): boolean;

declare function IsPlayerTargettingAnything(playerIndex: number): boolean;

declare function IsPlayerTargettingChar(playerIndex: number, ped: number): boolean;

declare function IsPlayerTargettingObject(playerIndex: number, obj: number): boolean;

declare function IsPlayerVehicleEntryDisabled(player: number): boolean;

declare function IsPointObscuredByAMissionEntity(pX: number, pY: number, pZ: number, sizeX: number, sizeY: number, sizeZ: number): boolean;

declare function IsPosInCutsceneBlockingBounds(x: number, y: number, z: number): boolean;

/**
 * IS_PRINCIPAL_ACE_ALLOWED
 */
declare function IsPrincipalAceAllowed(principal: string, object: string): boolean;

declare function IsProjectileInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): boolean;

declare function IsRadioHudOn(): boolean;

declare function IsRadioRetuning(): boolean;

/**
 * Gets if the specified `rawKeyIndex` is pressed down on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
declare function IsRawKeyDown(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` is pressed on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of pressed state.
 */
declare function IsRawKeyPressed(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` was just released on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of released state.
 */
declare function IsRawKeyReleased(rawKeyIndex: number): boolean;

/**
 * Gets if the specified `rawKeyIndex` is up  on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of up state.
 */
declare function IsRawKeyUp(rawKeyIndex: number): boolean;

declare function IsRelationshipSet(Unk493: number, Unk494: number, Unk495: number): boolean;

declare function IsReplaySaving(): boolean;

declare function IsReplaySystemSaving(): boolean;

declare function IsScoreGreater(playerIndex: number, score: number): boolean;

declare function IsScreenFadedIn(): boolean;

declare function IsScreenFadedOut(): boolean;

declare function IsScreenFading(): boolean;

declare function IsScreenFadingIn(): boolean;

declare function IsScreenFadingOut(): boolean;

declare function IsScriptFireExtinguished(fire: number): boolean;

declare function IsScriptedConversationOngoing(): boolean;

declare function IsScriptedSpeechPlaying(ped: number): boolean;

declare function IsSittingObjectNear(x: number, y: number, z: number, Unk73: number): boolean;

declare function IsSniperBulletInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): number;

declare function IsSniperInverted(): boolean;

declare function IsSpecificCamInterpolating(cam: number): boolean;

declare function IsSphereVisibleToAnotherMachine(Unk1043: number, Unk1044: number, Unk1045: number, Unk1046: number): number;

declare function IsStreamingAdditionalText(textIndex: number): boolean;

declare function IsStreamingPriorityRequests(): boolean;

declare function IsStreamingThisAdditionalText(str0: string, Unk597: number, Unk598: number): boolean;

declare function IsStringNull(str: string): boolean;

declare function IsSystemUiShowing(): boolean;

declare function IsThisAMinigameScript(): boolean;

declare function IsThisHelpMessageBeingDisplayed(gxtentry: string): boolean;

declare function IsThisHelpMessageWithNumberBeingDisplayed(gxtentry: string, _number: number): boolean;

declare function IsThisHelpMessageWithStringBeingDisplayed(gxtentry: string, str: string): boolean;

declare function IsThisMachineTheServer(): boolean;

declare function IsThisModelABike(model: number): boolean;

declare function IsThisModelABoat(model: number): boolean;

declare function IsThisModelACar(model: number): boolean;

declare function IsThisModelAHeli(model: number): boolean;

declare function IsThisModelAPed(model: number): boolean;

declare function IsThisModelAPlane(model: number): boolean;

declare function IsThisModelATrain(model: number): boolean;

declare function IsThisModelAVehicle(model: number): boolean;

declare function IsThisPedAPlayer(ped: number): boolean;

declare function IsThisPrintBeingDisplayed(gxtentry: string, Unk615: number, Unk616: number, Unk617: number, Unk618: number, Unk619: number, Unk620: number, Unk621: number, Unk622: number, Unk623: number, Unk624: number): number;

declare function IsThreadActive(threadId: number): boolean;

declare function IsUsingController(): boolean;

declare function IsVehDriveable(vehicle: number): boolean;

declare function IsVehStuck(veh: number, time: number, flag0: boolean, flag1: boolean, flag2: boolean): boolean;

declare function IsVehWindowIntact(vehicle: number, window: number): boolean;

declare function IsVehicleExtraTurnedOn(vehicle: number, extra: number): boolean;

declare function IsVehicleOnAllWheels(vehicle: number): boolean;

declare function IsVehicleTouchingObject(veh: number, obj: number): boolean;

/**
 * Getter for [BREAK_OFF_VEHICLE_WHEEL](?\_0xA274CADB).
 * @param vehicle The vehicle handle.
 * @param wheelIndex The wheel index.
 */
declare function IsVehicleWheelBrokenOff(vehicle: number, wheelIndex: number): boolean;

declare function IsViewportActive(viewportid: number): boolean;

declare function IsWantedLevelGreater(playerIndex: number, level: number): boolean;

declare function IsWorldPointWithinBrainActivationRange(): boolean;

declare function KnockPedOffBike(vehicle: number): void;

declare function LaunchLocalPlayerInNetworkGame(): void;

declare function LimitAngle(angle: number, anglelimited?: number): number;

declare function LimitTwoPlayerDistance(distance: number): void;

declare function Line(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function ListenToPlayerGroupCommands(ped: number, _set: boolean): void;

declare function LoadAdditionalText(textName: string, textIndex: number): void;

declare function LoadAllObjectsNow(): void;

declare function LoadAllPathNodes(value: boolean): number;

declare function LoadCharDecisionMaker(_type: number, pDM?: number): number;

declare function LoadCombatDecisionMaker(_type: number, pDM?: number): number;

declare function LoadPathNodesInArea(x: number, y: number, z: number, radius: number): void;

/**
 * Reads the contents of a text file in a specified resource.
 * If executed on the client, this file has to be included in `files` in the resource manifest.
 * Example: `local data = LoadResourceFile("devtools", "data.json")`
 * @param resourceName The resource name.
 * @param fileName The file in the resource.
 * @return The file contents
 */
declare function LoadResourceFile(resourceName: string, fileName: string): string;

declare function LoadScene(x: number, y: number, z: number): void;

declare function LoadSceneForRoomByKey(interior: number, roomhash: number): void;

declare function LoadSettings(): void;

declare function LoadTextFont(font: number): void;

declare function LoadTxd(txdName: string): number;

declare function LoadWebPage(htmlviewport: number, webaddress: string): void;

declare function LocalPlayerIsReadyToStartPlaying(): boolean;

declare function LocateCar_2d(car: number, x0: number, y0: number, xUnk48: number, yUnk49: number, flag: boolean): boolean;

declare function LocateCar_3d(car: number, x: number, y: number, z: number, xa: number, ya: number, za: number, flag: boolean): boolean;

declare function LocateCharAnyMeansCar_2d(ped: number, car: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharAnyMeansCar_3d(ped: number, car: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharAnyMeansChar_2d(ped: number, pednext: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharAnyMeansChar_3d(ped: number, pednext: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharAnyMeansObject_2d(ped: number, obj: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharAnyMeansObject_3d(ped: number, obj: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharAnyMeans_2d(ped: number, x0: number, y0: number, x1: number, y1: number, flag: boolean): boolean;

declare function LocateCharAnyMeans_3d(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, flag: boolean): boolean;

declare function LocateCharInCarCar_2d(ped: number, car: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharInCarCar_3d(ped: number, car: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharInCarChar_2d(ped: number, pednext: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharInCarChar_3d(ped: number, pednext: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharInCarObject_2d(ped: number, obj: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharInCarObject_3d(ped: number, obj: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharInCar_2d(ped: number, x0: number, y0: number, x1: number, y1: number, flag: boolean): boolean;

declare function LocateCharInCar_3d(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number, z: number, flag: boolean): boolean;

declare function LocateCharOnFootCar_2d(ped: number, car: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharOnFootCar_3d(ped: number, car: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharOnFootChar_2d(ped: number, pednext: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharOnFootChar_3d(ped: number, pednext: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharOnFootObject_2d(ped: number, obj: number, x: number, y: number, flag: boolean): boolean;

declare function LocateCharOnFootObject_3d(ped: number, obj: number, x: number, y: number, z: number, flag: boolean): boolean;

declare function LocateCharOnFoot_2d(ped: number, x0: number, y0: number, x1: number, y1: number, flag: boolean): boolean;

declare function LocateCharOnFoot_3d(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, flag: boolean): boolean;

declare function LocateDeadCar_3d(car: number, x: number, y: number, z: number, xa: number, ya: number, za: number, flag: boolean): boolean;

declare function LocateObject_2d(obj: number, x0: number, y0: number, x1: number, y1: number, flag: boolean): boolean;

declare function LocateObject_3d(obj: number, x: number, y: number, z: number, xr: number, yr: number, zr: number, flag: boolean): boolean;

declare function LockCarDoors(vehicle: number, value: number): void;

declare function LockLazlowStation(): void;

declare function LockPlayerSettingsGenreChange(lock_bit_mask: number): void;

declare function LookAtNearbyEntityWithSpecialAttribute(Unk98: number): [boolean, number[], number, number, number, number];

declare function LoopRaceTrack(loop: boolean): void;

declare function MaintainFlashingStarAfterOffence(player: number, maintain: boolean): void;

declare function MakeObjectTargettable(obj: number, targettable: boolean): void;

declare function MakePlayerFireProof(player: number, proof: boolean): void;

declare function MakePlayerGangDisappear(): void;

declare function MakePlayerGangReappear(): void;

declare function MakePlayerSafeForCutscene(player: number): void;

declare function MarkCarAsConvoyCar(vehicle: number, convoyCar: boolean): void;

declare function MarkCarAsNoLongerNeeded(pVehicle: number): void;

declare function MarkCharAsNoLongerNeeded(pPed: number): void;

declare function MarkMissionTrainAsNoLongerNeeded(train: number): void;

declare function MarkMissionTrainsAsNoLongerNeeded(): void;

declare function MarkModelAsNoLongerNeeded(model: number): void;

declare function MarkObjectAsNoLongerNeeded(pObj: number): void;

declare function MarkRoadNodeAsDontWander(x: number, y: number, z: number): void;

declare function MarkScriptAsNoLongerNeeded(scriptName: string): void;

declare function MarkStreamedTxdAsNoLongerNeeded(txdName: string): void;

declare function MissionAudioBankNoLongerNeeded(): void;

declare function ModifyCharMoveBlendRatio(ped: number, Unk6: number): void;

declare function ModifyCharMoveState(ped: number, state: number): void;

declare function MpGetAmountOfAnchorPoints(ped: number, id: number): number;

declare function MpGetAmountOfVariationComponent(ped: number, componentid: number): number;

declare function MpGetPreferenceValue(prefid: number): number;

declare function MpGetPropSetup(ped: number, ukn0: number, ukn1: number, ukn2: number, ukn3: number): number;

declare function MpGetVariationSetup(ped: number, Unk890: number, Unk891: number, Unk892: number, Unk893: number): number;

declare function MpSetPreferenceValue(prefid: number, value: number): void;

/**
 * Starts listening to the specified channel, when available.
 * @param channel A game voice channel ID.
 */
declare function MumbleAddVoiceChannelListen(channel: number): void;

/**
 * Adds the specified channel to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param channel A game voice channel ID.
 */
declare function MumbleAddVoiceTargetChannel(targetId: number, channel: number): void;

/**
 * Adds the specified player to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param player A game player index.
 */
declare function MumbleAddVoiceTargetPlayer(targetId: number, player: number): void;

/**
 * Adds the specified player to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param serverId The player's server id.
 */
declare function MumbleAddVoiceTargetPlayerByServerId(targetId: number, serverId: number): void;

/**
 * MUMBLE_CLEAR_VOICE_CHANNEL
 */
declare function MumbleClearVoiceChannel(): void;

/**
 * Clears the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
declare function MumbleClearVoiceTarget(targetId: number): void;

/**
 * Clears channels from the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
declare function MumbleClearVoiceTargetChannels(targetId: number): void;

/**
 * Clears players from the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
declare function MumbleClearVoiceTargetPlayers(targetId: number): void;

/**
 * Check whether specified channel exists on the Mumble server.
 * @param channel A game voice channel ID.
 * @return True if the specific channel exists. False otherwise.
 */
declare function MumbleDoesChannelExist(channel: number): boolean;

/**
 * MUMBLE_GET_TALKER_PROXIMITY
 * @return Talker proximity value.
 */
declare function MumbleGetTalkerProximity(): number;

/**
 * Returns the mumble voice channel from a player's server id.
 * @param serverId The player's server id.
 * @return Int representing the identifier of the voice channel.
 */
declare function MumbleGetVoiceChannelFromServerId(serverId: number): number;

/**
 * MUMBLE_IS_ACTIVE
 * @return True if the player has enabled voice chat.
 */
declare function MumbleIsActive(): boolean;

/**
 * This native will return true if the user succesfully connected to the voice server.
 * If the user disabled the voice-chat setting it will return false.
 * @return True if the player is connected to a mumble server.
 */
declare function MumbleIsConnected(): boolean;

/**
 * MUMBLE_IS_PLAYER_TALKING
 * @param player The target player.
 * @return Whether or not the player is talking.
 */
declare function MumbleIsPlayerTalking(player: number): boolean;

/**
 * Stops listening to the specified channel.
 * @param channel A game voice channel ID.
 */
declare function MumbleRemoveVoiceChannelListen(channel: number): void;

/**
 * Removes the specified voice channel from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_CHANNEL](#\_0x4D386C9E)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param channel The game voice channel ID to remove from the target.
 */
declare function MumbleRemoveVoiceTargetChannel(targetId: number, channel: number): void;

/**
 * Removes the specified player from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER](#\_0x32C5355A)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param player The player index to remove from the target.
 */
declare function MumbleRemoveVoiceTargetPlayer(targetId: number, player: number): void;

/**
 * Removes the specified player from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER_BY_SERVER_ID](#\_0x25F2B65F)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param serverId The player's server id to remove from the target.
 */
declare function MumbleRemoveVoiceTargetPlayerByServerId(targetId: number, serverId: number): void;

/**
 * MUMBLE_SET_ACTIVE
 * @param state Voice chat state.
 */
declare function MumbleSetActive(state: boolean): void;

/**
 * Sets the current input distance. The player will be able to talk to other players within this distance.
 * @param distance The input distance.
 */
declare function MumbleSetAudioInputDistance(distance: number): void;

/**
 * Use this native to disable noise suppression and high pass filters.
 * The possible intents for this are as follows (backticks are used to represent hashes):
 * | Index | Description |
 * |-|-|
 * | \`speech\` | Default intent |
 * | \`music\` | Disable noise suppression and high pass filter |
 * @param intentHash The intent hash.
 */
declare function MumbleSetAudioInputIntent(intentHash: string | number): void;

/**
 * Sets the current output distance. The player will be able to hear other players talking within this distance.
 * @param distance The output distance.
 */
declare function MumbleSetAudioOutputDistance(distance: number): void;

/**
 * Changes the Mumble server address to connect to, and reconnects to the new address.
 * Setting the address to an empty string and the port to -1 will reset to the built in FXServer Mumble Implementation.
 * @param address The address of the mumble server.
 * @param port The port of the mumble server.
 */
declare function MumbleSetServerAddress(address: string, port: number): void;

/**
 * Sets the audio submix ID for a specified player using Mumble 'Native Audio' functionality.
 * @param serverId The player's server ID.
 * @param submixId The submix ID.
 */
declare function MumbleSetSubmixForServerId(serverId: number, submixId: number): void;

/**
 * MUMBLE_SET_TALKER_PROXIMITY
 * @param value Proximity value.
 */
declare function MumbleSetTalkerProximity(value: number): void;

/**
 * MUMBLE_SET_VOICE_CHANNEL
 * @param channel A game voice channel ID.
 */
declare function MumbleSetVoiceChannel(channel: number): void;

/**
 * Sets the current Mumble voice target ID to broadcast voice to.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive). 0 disables voice targets, and 31 is server loopback.
 */
declare function MumbleSetVoiceTarget(targetId: number): void;

/**
 * Overrides the output volume for a particular player on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.
 * Set to -1.0 to reset the Volume override.
 * @param player A game player index.
 * @param volume The volume, ranging from 0.0 to 1.0 (or above).
 */
declare function MumbleSetVolumeOverride(player: number, volume: number): void;

/**
 * Overrides the output volume for a particular player with the specified server id and player name on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.
 * @param serverId The player's server id.
 * @param volume The volume, ranging from 0.0 to 1.0 (or above).
 */
declare function MumbleSetVolumeOverrideByServerId(serverId: number, volume: number): void;

declare function MuteGameworldAndPositionedRadioForTv(mute: boolean): void;

declare function MuteGameworldAudio(mute: boolean): void;

declare function MutePositionedRadio(mute: boolean): void;

declare function MuteStaticEmitter(StaticEmitterIndex: number, mute: boolean): void;

declare function NetworkAcceptInvite(playerIndex: number): number;

declare function NetworkAdvertiseSession(advertise: boolean): void;

declare function NetworkAllPartyMembersPresent(): boolean;

declare function NetworkAmIBlockedByPlayer(playerIndex: number): boolean;

declare function NetworkAmIMutedByPlayer(playerIndex: number): boolean;

declare function NetworkChangeExtendedGameConfig(Unk924: number): void;

declare function NetworkChangeGameMode(Unk1047: number, Unk1048: number, Unk1049: number, Unk1050: number): number;

declare function NetworkChangeGameModePending(): boolean;

declare function NetworkChangeGameModeSucceeded(): boolean;

declare function NetworkCheckInviteArrival(): boolean;

declare function NetworkClearInviteArrival(): void;

declare function NetworkClearSummons(): void;

declare function NetworkDidInviteFriend(FRIENDNAME: string): boolean;

declare function NetworkEndSession(): void;

declare function NetworkEndSessionPending(): boolean;

declare function NetworkExpandTo_32Players(): void;

declare function NetworkFindGame(GameMode: number, ukn0: number, ukn1: number, ukn2: number): void;

declare function NetworkFindGamePending(): boolean;

declare function NetworkFinishExtendedSearch(): void;

/**
 * Returns the owner ID of the specified entity.
 * @param entity The entity to get the owner for.
 * @return On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.
 */
declare function NetworkGetEntityOwner(entity: number): number;

declare function NetworkGetFindResult(Unk925: number, Unk926: number): void;

declare function NetworkGetFriendCount(): number;

declare function NetworkGetFriendName(id: number): string;

declare function NetworkGetGameMode(): number;

declare function NetworkGetHostAverageRank(host: number): number;

declare function NetworkGetHostLatency(host: number): number;

declare function NetworkGetHostMatchProgress(host: number): number;

declare function NetworkGetHostServerName(host: number): string;

declare function NetworkGetLanSession(): boolean;

declare function NetworkGetMaxPrivateSlots(): number;

declare function NetworkGetMaxSlots(): number;

declare function NetworkGetMetPlayerName(Unk1051: number): number;

declare function NetworkGetNextTextChat(): string;

declare function NetworkGetNumOpenPublicSlots(): number;

declare function NetworkGetNumPartyMembers(): number;

declare function NetworkGetNumPlayersMet(): number;

declare function NetworkGetNumUnacceptedInvites(): number;

declare function NetworkGetNumUnfilledReservations(): number;

declare function NetworkGetNumberOfGames(): number;

declare function NetworkGetPlayerIdOfNextTextChat(): number;

declare function NetworkGetRendezvousHostPlayerId(): number;

declare function NetworkGetServerName(): number;

declare function NetworkGetUnacceptedInviteEpisode(Unk894: number): number;

declare function NetworkGetUnacceptedInviteGameMode(Unk1052: number): number;

declare function NetworkGetUnacceptedInviterName(Unk886: number): string;

declare function NetworkHasStrictNat(): boolean;

declare function NetworkHaveAcceptedInvite(): boolean;

declare function NetworkHaveOnlinePrivileges(): boolean;

declare function NetworkHaveSummons(): boolean;

declare function NetworkHostGameE1(Gamemode: number, Ranked: number, Slots: number, Private: number, Episode: number, MaxTeams: number): boolean;

declare function NetworkHostGamePending(): boolean;

declare function NetworkHostGameSucceeded(): boolean;

declare function NetworkHostRendezvousE1(Gamemode: number, Slots: number, Episode: number): boolean;

declare function NetworkInviteFriend(friendname: string, ukn: string): void;

declare function NetworkIsBeingKicked(): boolean;

declare function NetworkIsCommonEpisode(id: number): boolean;

declare function NetworkIsFindResultUpdated(ukn0: number): boolean;

declare function NetworkIsFindResultValid(Unk883: number): boolean;

declare function NetworkIsFriendInSameTitle(friendid: number): boolean;

declare function NetworkIsFriendOnline(Unk896: number): number;

declare function NetworkIsGameRanked(): boolean;

declare function NetworkIsInviteeOnline(): boolean;

declare function NetworkIsNetworkAvailable(): boolean;

declare function NetworkIsOperationPending(): boolean;

declare function NetworkIsPlayerBlockedByMe(playerIndex: number): boolean;

declare function NetworkIsPlayerMutedByMe(playerIndex: number): boolean;

declare function NetworkIsPlayerTalking(playerIndex: number): boolean;

declare function NetworkIsPlayerTyping(playerIndex: number): boolean;

declare function NetworkIsRendezvous(): boolean;

declare function NetworkIsRendezvousHost(): boolean;

declare function NetworkIsRockstartSessionIdValid(): boolean;

declare function NetworkIsSessionAdvertise(): boolean;

declare function NetworkIsSessionInvitable(): boolean;

declare function NetworkIsSessionStarted(): boolean;

declare function NetworkIsTvt(): boolean;

declare function NetworkJoinGame(Unk1053: number): number;

declare function NetworkJoinGamePending(): boolean;

declare function NetworkJoinGameSucceeded(): boolean;

declare function NetworkJoinSummons(): boolean;

declare function NetworkKickPlayer(playerIndex: number, value: boolean): void;

declare function NetworkLeaveGame(): void;

declare function NetworkLeaveGamePending(): boolean;

declare function NetworkLimitTo_16Players(): void;

declare function NetworkPlayerHasCommPrivs(): boolean;

declare function NetworkPlayerHasDiedRecently(playerIndex: number): boolean;

declare function NetworkPlayerHasHeadset(Unk884: number): boolean;

declare function NetworkPlayerHasKeyboard(playerIndex: number): boolean;

declare function NetworkRestoreGameConfig(Unk1054: number): number;

declare function NetworkResultMatchesSearchCriteria(result: number): boolean;

declare function NetworkReturnToRendezvous(): boolean;

declare function NetworkReturnToRendezvousPending(): boolean;

declare function NetworkReturnToRendezvousSucceeded(): boolean;

declare function NetworkSendTextChat(playerIndex: number, Unk1055: number): number;

declare function NetworkSetFriendlyFireOption(Unk927: number): void;

declare function NetworkSetHealthReticuleOption(Unk928: boolean): void;

declare function NetworkSetLanSession(Unk929: number): void;

declare function NetworkSetLocalPlayerIsTyping(playerIndex: number): void;

declare function NetworkSetMatchProgress(Unk930: number): void;

declare function NetworkSetPlayerMuted(playerIndex: number, value: boolean): boolean;

declare function NetworkSetScriptLobbyState(Unk931: number): void;

declare function NetworkSetServerName(name: string): number;

declare function NetworkSetSessionInvitable(invitable: boolean): void;

declare function NetworkSetTalkerFocus(Unk932: number): void;

declare function NetworkSetTalkerProximity(Unk933: number): void;

declare function NetworkSetTeamOnlyChat(Unk934: boolean): void;

declare function NetworkSetTextChatRecipients(Unk935: number): void;

declare function NetworkShowFriendProfileUi(Unk936: number): void;

declare function NetworkShowMetPlayerFeedbackUi(metPlayerIndex: number): void;

declare function NetworkShowMetPlayerProfileUi(Unk937: number): void;

declare function NetworkShowPlayerFeedbackUi(payerIndex: number): void;

declare function NetworkShowPlayerProfileUi(playerIndex: number): void;

declare function NetworkStartExtendedSearch(Unk938: number): void;

declare function NetworkStartSession(): void;

declare function NetworkStartSessionPending(): boolean;

declare function NetworkStartSessionSucceeded(): boolean;

declare function NetworkStoreGameConfig(Unk939: number): void;

declare function NetworkStoreSinglePlayerGame(): number;

declare function NetworkStringVerifyPending(): boolean;

declare function NetworkStringVerifySucceeded(): boolean;

declare function NetworkVerifyUserString(Unk940: number): void;

declare function NewMobilePhoneCall(): void;

declare function NewScriptedConversation(): void;

declare function ObfuscateInt(Unk941: number, Unk942: number): void;

declare function ObfuscateIntArray(Unk943: number, Unk944: number): void;

declare function ObfuscateString(str: string): string;

declare function OnFireScream(ped: number): void;

declare function OpenCarDoor(vehicle: number, door: number): void;

declare function OpenDebugFile(): void;

declare function OpenGarage(name: string): void;

declare function OpenSequenceTask(pTaskSequence: number): void;

declare function OverrideFreezeFlags(Unk504: boolean): void;

declare function OverrideNextRestart(x: number, y: number, z: number, heading: number): void;

declare function OverrideNumberOfParkedCars(num: number): void;

declare function PanicScream(ped: number): void;

declare function PauseGame(): void;

declare function PausePlaybackRecordedCar(car: number): void;

declare function PauseScriptedConversation(pause: boolean): void;

declare function PedQueueConsiderPedsWithFlagFalse(flagid: number): void;

declare function PedQueueConsiderPedsWithFlagTrue(flagid: number): void;

declare function PedQueueRejectPedsWithFlagFalse(flagid: number): void;

declare function PedQueueRejectPedsWithFlagTrue(flagid: number): void;

declare function PickupsPassTime(time: number): void;

declare function PlaceObjectRelativeToCar(obj: number, car: number, x: number, y: number, z: number): void;

declare function PlaneStartsInAir(plane: number): void;

declare function PlayAudioEvent(name: string): void;

declare function PlayAudioEventFromObject(EventName: string, obj: number): void;

declare function PlayAudioEventFromPed(name: string, ped: number): void;

declare function PlayAudioEventFromVehicle(name: string, veh: number): void;

declare function PlayCarAnim(car: number, animname0: string, animname1: string, Unk50: number, flag0: boolean, flag1: boolean): boolean;

declare function PlayFireSoundFromPosition(sound_id: number, x: number, y: number, z: number): void;

declare function PlayMovie(): void;

declare function PlayObjectAnim(obj: number, animname0: string, animname1: string, Unk74: number, flag0: boolean, flag1: boolean): boolean;

declare function PlayScriptedConversationFrontend(play: boolean): void;

declare function PlaySound(SoundId: number, SoundName: string): void;

declare function PlaySoundFromObject(sound_id: number, name: string, obj: number): void;

declare function PlaySoundFromPed(SoundId: number, SoundName: string, ped: number): void;

declare function PlaySoundFromPosition(sound_id: number, name: string, x: number, y: number, z: number): void;

declare function PlaySoundFromVehicle(SoundId: number, SoundName: string, veh: number): void;

declare function PlaySoundFrontend(sound: number, soundName: string): void;

declare function PlayStreamFromObject(obj: number): void;

declare function PlayStreamFromPed(ped: number): void;

declare function PlayStreamFrontend(): void;

declare function PlayerHasChar(playerIndex: number): boolean;

declare function PlayerHasFlashingStarsAboutToDrop(playerIndex: number): boolean;

declare function PlayerHasGreyedOutStars(playerIndex: number): boolean;

declare function PlayerIsInteractingWithGarage(): boolean;

declare function PlayerIsNearFirstPigeon(x: number, y: number, z: number): boolean;

declare function PlayerIsPissedOff(player: number): boolean;

declare function PlayerWantsToJoinNetworkGame(Unk885: number): boolean;

declare function PlaystatsCheat(stat: number): void;

declare function PlaystatsFloat(Unk785: number, Unk786: number): void;

declare function PlaystatsInt(Unk787: number, Unk788: number): void;

declare function PlaystatsIntFloat(Unk789: number, Unk790: number, Unk791: number): void;

declare function PlaystatsIntInt(Unk792: number, Unk793: number, Unk794: number): void;

declare function PlaystatsMissionCancelled(Unk795: number): void;

declare function PlaystatsMissionFailed(Unk796: number): void;

declare function PlaystatsMissionPassed(str0: string): void;

declare function PlaystatsMissionStarted(Unk797: number): void;

declare function PointCamAtCam(cam: number, camnext: number): void;

declare function PointCamAtCoord(cam: number, x: number, y: number, z: number): void;

declare function PointCamAtObject(cam: number, obj: number): void;

declare function PointCamAtPed(cam: number, ped: number): void;

declare function PointCamAtVehicle(cam: number, veh: number): void;

declare function PointFixedCam(x: number, y: number, z: number, Unk563: number): void;

declare function PointFixedCamAtObj(obj: number, cam: number): void;

declare function PointFixedCamAtPed(ped: number, cam: number): void;

declare function PointFixedCamAtPos(x: number, y: number, z: number, cam: number): void;

declare function PointFixedCamAtVehicle(veh: number, cam: number): void;

declare function PopCarBoot(vehicle: number): void;

declare function PopulateNow(): void;

declare function Pow(base: number, power: number): number;

declare function PreloadStream(name: string): boolean;

declare function PreloadStreamWithStartOffset(StreamName: string, StartOffset: number): boolean;

declare function PreviewRingtone(RingtoneId: number): void;

declare function Print(gxtName: string, timeMS: number, enable: boolean): void;

declare function PrintBig(gxtName: string, timeMS: number, enable: boolean): void;

declare function PrintBigQ(gxtentry: string, time: number, flag: number): void;

declare function PrintHelp(gxtName: string): void;

declare function PrintHelpForever(gxtName: string): void;

declare function PrintHelpForeverWithNumber(gxtName: string, value: number): void;

declare function PrintHelpForeverWithString(gxtName: string, gxtText: string): void;

declare function PrintHelpForeverWithStringNoSound(gxtName: string, gxtText: string): void;

declare function PrintHelpForeverWithTwoNumbers(gxtentry: string, Unk658: number, Unk659: number): void;

declare function PrintHelpOverFrontend(gxtentry: string): void;

declare function PrintHelpWithNumber(gxtName: string, value: number): void;

declare function PrintHelpWithString(gxtName: string, gxtText: string): void;

declare function PrintHelpWithStringNoSound(gxtName: string, gxtText: string): void;

declare function PrintHelpWithTwoNumbers(gxtentry: string, Unk660: number, Unk661: number): void;

declare function PrintNow(gxtName: string, timeMS: number, enable: boolean): void;

declare function PrintStringInString(gxtName: string, gxtText: string, timeMS: number, enable: boolean): void;

declare function PrintStringInStringNow(gxtName: string, gxtText: string, timeMS: number, enable: boolean): void;

declare function PrintStringWithLiteralString(gxtentry: string, _string: string, time: number, flag: number): void;

declare function PrintStringWithLiteralStringNow(gxtName: string, text: string, timeMS: number, enable: boolean): void;

declare function PrintStringWithSubstringGivenHashKeyNow(gxtkey0: string, gxtkey1: number, time: number, style: number): void;

declare function PrintStringWithTwoLiteralStrings(gxtentry: string, string1: string, string2: string, time: number, flag: number): void;

declare function PrintStringWithTwoLiteralStringsNow(gxtentry: string, string1: string, string2: string, time: number, flag: number): void;

declare function PrintWithNumber(gxtName: string, value: number, timeMS: number, enable: boolean): void;

declare function PrintWithNumberBig(gxtName: string, value: number, timeMS: number, enable: boolean): void;

declare function PrintWithNumberNow(gxtName: string, value: number, timeMS: number, enable: boolean): void;

declare function PrintWith_2Numbers(gxtName: string, value1: number, value2: number, timeMS: number, enable: boolean): void;

declare function PrintWith_2NumbersBig(gxtentry: string, Unk662: number, Unk663: number, time: number, flag: number): void;

declare function PrintWith_2NumbersNow(gxtName: string, value1: number, value2: number, timeMS: number, enable: boolean): void;

declare function PrintWith_3Numbers(gxtentry: string, Unk664: number, Unk665: number, Unk666: number, time: number, flag: number): void;

declare function PrintWith_3NumbersNow(gxtentry: string, Unk667: number, Unk668: number, Unk669: number, time: number, flag: number): void;

declare function PrintWith_4Numbers(gxtentry: string, Unk670: number, Unk671: number, Unk672: number, Unk673: number, time: number, flag: number): void;

declare function PrintWith_4NumbersNow(gxtentry: string, Unk674: number, Unk675: number, Unk676: number, Unk677: number, time: number, flag: number): void;

declare function PrintWith_5Numbers(gxtentry: string, Unk678: number, Unk679: number, Unk680: number, Unk681: number, Unk682: number, time: number, flag: number): void;

declare function PrintWith_5NumbersNow(gxtentry: string, Unk683: number, Unk684: number, Unk685: number, Unk686: number, Unk687: number, time: number, flag: number): void;

declare function PrintWith_6Numbers(gxtentry: string, Unk688: number, Unk689: number, Unk690: number, Unk691: number, Unk692: number, Unk693: number, time: number, flag: number): void;

declare function PrintWith_6NumbersNow(gxtentry: string, Unk694: number, Unk695: number, Unk696: number, Unk697: number, Unk698: number, Unk699: number, time: number, flag: number): void;

declare function Printfloat(value: number): void;

declare function Printint(value: number): void;

declare function Printnl(): void;

declare function Printstring(value: string): void;

declare function Printvector(x: number, y: number, z: number): void;

declare function PrioritizeStreamingRequest(): void;

declare function ProcessMissionDeletionList(): void;

/**
 * Scope entry for profiler.
 * @param scopeName Scope name.
 */
declare function ProfilerEnterScope(scopeName: string): void;

/**
 * Scope exit for profiler.
 */
declare function ProfilerExitScope(): void;

/**
 * Returns true if the profiler is active.
 * @return True or false.
 */
declare function ProfilerIsRecording(): boolean;

declare function ProstituteCamActivate(activate: boolean): void;

declare function ReadKillFrenzyStatus(): number;

declare function RegisterBestPosition(Unk505: number, position: number): void;

declare function RegisterClientBroadcastVariables(Unk945: number, Unk946: number, Unk947: number): void;

/**
 * Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
 * Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
 * The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
 * **Example result**:
 * ![](https://i.imgur.com/TaCnG09.png)
 * @param commandName The command you want to register.
 * @param handler A handler function that gets called whenever the command is executed.
 * @param restricted If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.
 */
declare function RegisterCommand(commandName: string, handler: Function, restricted: boolean): void;

declare function RegisterFloatStat(stat: number, val: number): void;

declare function RegisterHatedTargetsAroundPed(ped: number, radius: number): void;

declare function RegisterHatedTargetsInArea(ped: number, x: number, y: number, z: number, radius: number): void;

declare function RegisterHostBroadcastVariables(Unk948: number, ukn0: number, ukn1: number): void;

declare function RegisterIntStat(stat: number, val: number): void;

declare function RegisterKillInMultiplayerGame(playerIndex: number, id: number, ukn: number): void;

declare function RegisterMissionPassed(str: string): void;

declare function RegisterMultiplayerGameWin(playerIndex: number, Unk949: boolean): void;

declare function RegisterNetworkBestGameScores(playerIndex: number, Unk950: number, Unk951: number): void;

/**
 * REGISTER_NUI_CALLBACK
 */
declare function RegisterNuiCallback(callbackType: string, callback: Function): void;

/**
 * REGISTER_NUI_CALLBACK_TYPE
 */
declare function RegisterNuiCallbackType(callbackType: string): void;

declare function RegisterOddjobMissionPassed(): void;

declare function RegisterPlayerRespawnCoords(playerIndex: number, x: number, y: number, z: number): void;

/**
 * REGISTER_RAW_NUI_CALLBACK
 */
declare function RegisterRawNuiCallback(callbackType: string, callback: Function): void;

/**
 * An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
 * @param eventName An event name, or "\*" to disable HLL event filtering for this resource.
 */
declare function RegisterResourceAsEventHandler(eventName: string): void;

declare function RegisterSaveHouse(x: number, y: number, z: number, unkf: number, name: string, unk0: number): number;

declare function RegisterScriptWithAudio(reg: boolean): void;

declare function RegisterStringForFrontendStat(stat: number, str: string): void;

declare function RegisterTarget(ped: number, target: number): void;

declare function RegisterTrackNumber(_number: number): void;

declare function RegisterWorldPointScriptBrain(ScriptName: string, radius: number): void;

declare function ReleaseMovie(): void;

declare function ReleasePathNodes(): void;

declare function ReleaseScriptControlledMicrophone(): void;

declare function ReleaseSoundId(sound: number): void;

declare function ReleaseTexture(texture: number): void;

declare function ReleaseTimeOfDay(): void;

declare function ReleaseWeather(): void;

declare function ReloadWebPage(htmlviewport: number): void;

declare function RemoveAdditionalPopulationModel(model: number): void;

declare function RemoveAllCharWeapons(ped: number): void;

declare function RemoveAllInactiveGroupsFromCleanupList(): void;

declare function RemoveAllPickupsOfType(_type: number): void;

declare function RemoveAnims(animName: string): void;

declare function RemoveBlip(blip: number): void;

declare function RemoveBlipAndClearIndex(blip: number): void;

declare function RemoveCarRecording(CarRec: number): void;

declare function RemoveCarWindow(car: number, windnum: number): void;

declare function RemoveCarsFromGeneratorsInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function RemoveCharDefensiveArea(ped: number): void;

declare function RemoveCharElegantly(ped: number): void;

declare function RemoveCharFromCarMaintainPosition(ped: number, car: number): void;

declare function RemoveCharFromGroup(ped: number): void;

declare function RemoveCloseMicPed(ped: number): void;

/**
 * REMOVE_CONVAR_CHANGE_LISTENER
 * @param cookie The cookie returned from [ADD_CONVAR_CHANGE_LISTENER](#\_0xAB7F7241)
 */
declare function RemoveConvarChangeListener(cookie: number): void;

declare function RemoveCoverPoint(coverPoint: number): void;

declare function RemoveDecisionMaker(dm: number): void;

declare function RemoveFakeNetworkNameFromPed(ped: number): void;

declare function RemoveGroup(group: number): void;

declare function RemoveIpl(iplName: string): void;

declare function RemoveIplDiscreetly(iplname: string): void;

declare function RemoveNavmeshRequiredRegion(Unk599: number, Unk600: number): boolean;

declare function RemovePedHelmet(ped: number, removed: boolean): void;

declare function RemovePickup(pickup: number): void;

declare function RemovePlayerHelmet(playerIndex: number, remove: boolean): void;

declare function RemoveProjtexFromObject(obj: number): void;

declare function RemoveProjtexInRange(x: number, y: number, z: number, radius: number): void;

declare function RemovePtfx(ptfx: number): void;

declare function RemovePtfxFromObject(obj: number): void;

declare function RemovePtfxFromPed(ped: number): void;

declare function RemovePtfxFromVehicle(veh: number): void;

declare function RemoveScriptFire(fire: number): void;

declare function RemoveScriptMic(): void;

/**
 * **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
 * Removes a handler for changes to a state bag.
 * @param cookie The cookie.
 */
declare function RemoveStateBagChangeHandler(cookie: number): void;

declare function RemoveStuckCarCheck(vehicle: number): void;

declare function RemoveTemporaryRadarBlipsForPickups(): void;

/**
 * REMOVE_TIMECYCLE_MODIFIER
 * @param modifierName The timecycle modifier name.
 */
declare function RemoveTimecycleModifier(modifierName: string): void;

/**
 * REMOVE_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 */
declare function RemoveTimecycleModifierVar(modifierName: string, varName: string): void;

declare function RemoveTxd(txd: number): void;

declare function RemoveUpsidedownCarCheck(vehicle: number): void;

declare function RemoveWeaponFromChar(ped: number, weapon: number): void;

declare function RenderRaceTrack(render: boolean): void;

declare function RenderWeaponPickupsBigger(value: boolean): void;

declare function ReportCrime(x: number, y: number, z: number, name: string): void;

declare function ReportDispatch(id: number, x: number, y: number, z: number): void;

declare function ReportPoliceSpottingSuspect(veh: number): void;

declare function ReportSuspectArrested(): void;

declare function ReportSuspectDown(): void;

declare function ReportTaggedRadioTrack(TrackTextId: number): void;

declare function RequestAdditionalText(textName: string, textIndex: number): void;

declare function RequestAllSlodsInWorld(): void;

declare function RequestAmbientAudioBank(name: string): boolean;

declare function RequestAnims(animName: string): void;

declare function RequestCarRecording(CarRecId: number): void;

declare function RequestCollisionAtPosn(x: number, y: number, z: number): void;

declare function RequestCollisionForModel(model: number): void;

declare function RequestControlOfNetworkId(netid: number): boolean;

declare function RequestInteriorModels(model: number, interiorName: string): void;

declare function RequestIpl(iplName: string): void;

declare function RequestMissionAudioBank(name: string): boolean;

declare function RequestModel(model: number): void;

/**
 * Requests a resource file set with the specified name to be downloaded and mounted on top of the current resource.
 * Resource file sets are specified in `fxmanifest.lua` with the following syntax:
 * ```lua
 * file_set 'addon_ui' {
 * 'ui/addon/index.html',
 * 'ui/addon -- [[*.js',
 * }
 * ```
 * This command will trigger a script error if the request failed.
 * @param setName The name of the file set as specified in `fxmanifest.lua`.
 * @return `TRUE` if the set is mounted, `FALSE` if the request is still pending.
 */
declare function RequestResourceFileSet(setName: string): boolean;

declare function RequestScript(scriptName: string): void;

declare function RequestStreamedTxd(txdName: string, unknown: boolean): void;

declare function ReserveNetworkMissionObjectsForHost(count: number): void;

declare function ReserveNetworkMissionPedsForHost(Unk952: number): void;

declare function ReserveNetworkMissionVehicles(Unk953: number): void;

declare function ReserveNetworkMissionVehiclesForHost(Unk954: number): void;

declare function ResetAchievementsAward(): void;

declare function ResetCamInterpCustomSpeedGraph(): void;

declare function ResetCamSplineCustomSpeedGraph(): void;

declare function ResetCarWheels(car: number, reset: boolean): void;

declare function ResetLocalPlayerWeaponStat(wtype: number, wid: number): void;

declare function ResetNoLawVehiclesDestroyedByLocalPlayer(): void;

declare function ResetNumOfModelsKilledByPlayer(model: number): void;

declare function ResetStuckTimer(car: number, timer_num: number): void;

declare function ResetVisiblePedDamage(ped: number): void;

declare function RestartScriptedConversation(): void;

declare function RestoreScriptArrayFromScratchpad(Unk955: number, Unk956: number, Unk957: number, Unk958: number): void;

declare function RestoreScriptValuesForNetworkGame(Unk1056: number): number;

declare function ResurrectNetworkPlayer(playerIndex: number, x: number, y: number, z: number, ukn0: number): void;

declare function RetuneRadioDown(): void;

declare function RetuneRadioToStationIndex(radioStation: number): void;

declare function RetuneRadioToStationName(name: string): void;

declare function RetuneRadioUp(): void;

declare function ReviveInjuredPed(ped: number): void;

declare function RotateObject(obj: number, x: number, y: number, flag: boolean): boolean;

declare function Round(Unk1085: number): number;

declare function SaveFloatToDebugFile(Unk1117: number): void;

declare function SaveIntToDebugFile(Unk1118: number): void;

declare function SaveNewlineToDebugFile(): void;

declare function SaveScriptArrayInScratchpad(Unk959: number, Unk960: number, Unk961: number, Unk962: number): void;

declare function SaveSettings(): void;

declare function SaveStringToDebugFile(Unk1119: number): void;

declare function SayAmbientSpeech(ped: number, phraseName: string, flag0: boolean, flag1: boolean, style: number): void;

declare function SayAmbientSpeechWithVoice(ped: number, SpeechName: string, VoiceName: string, flag0: boolean, flag1: boolean, style: number): void;

declare function ScriptAssert(text: string): void;

declare function ScriptIsMovingMobilePhoneOffscreen(_set: boolean): void;

declare function ScriptIsUsingMobilePhone(_set: boolean): void;

declare function SearchCriteriaConsiderPedsWithFlagFalse(flagid: number): void;

declare function SearchCriteriaConsiderPedsWithFlagTrue(flagId: number): void;

declare function SearchCriteriaRejectPedsWithFlagFalse(flagid: number): void;

declare function SearchCriteriaRejectPedsWithFlagTrue(flagId: number): void;

declare function SecuromSpotCheck1(): boolean;

declare function SecuromSpotCheck2(): boolean;

declare function SecuromSpotCheck3(): boolean;

declare function SecuromSpotCheck4(): boolean;

declare function SelectWeaponsForVehicle(veh: number, weapon: number): void;

declare function SendClientBroadcastVariablesNow(): void;

/**
 * Sends a message to the specific DUI root page. This is similar to SEND_NUI_MESSAGE.
 * @param duiObject The DUI browser handle.
 * @param jsonString The message, encoded as JSON.
 */
declare function SendDuiMessage(duiObject: number, jsonString: string): void;

/**
 * Injects a 'mouse down' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
 * @param duiObject The DUI browser handle.
 * @param button Either `'left'`, `'middle'` or `'right'`.
 */
declare function SendDuiMouseDown(duiObject: number, button: string): void;

/**
 * Injects a 'mouse move' event for a DUI object. Coordinates are in browser space.
 * @param duiObject The DUI browser handle.
 * @param x The mouse X position.
 * @param y The mouse Y position.
 */
declare function SendDuiMouseMove(duiObject: number, x: number, y: number): void;

/**
 * Injects a 'mouse up' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
 * @param duiObject The DUI browser handle.
 * @param button Either `'left'`, `'middle'` or `'right'`.
 */
declare function SendDuiMouseUp(duiObject: number, button: string): void;

/**
 * Injects a 'mouse wheel' event for a DUI object.
 * @param duiObject The DUI browser handle.
 * @param deltaY The wheel Y delta.
 * @param deltaX The wheel X delta.
 */
declare function SendDuiMouseWheel(duiObject: number, deltaY: number, deltaX: number): void;

/**
 * Sends a message to the `loadingScreen` NUI frame, which contains the HTML page referenced in `loadscreen` resources.
 * @param jsonString The JSON-encoded message.
 * @return A success value.
 */
declare function SendLoadingScreenMessage(jsonString: string): boolean;

declare function SendNmMessage(ped: number): void;

/**
 * SEND_NUI_MESSAGE
 */
declare function SendNuiMessage(jsonString: string): boolean;

declare function SetActivateObjectPhysicsAsSoonAsItIsUnfrozen(obj: number, _set: boolean): void;

declare function SetAdvancedBoolInDecisionMaker(dm: number, Unk844: number, Unk845: number, Unk846: number, Unk847: boolean): void;

declare function SetAllCarGeneratorsBackToActive(): void;

declare function SetAllCarsCanBeDamaged(_set: boolean): void;

declare function SetAllPickupsOfTypeCollectableByCar(pickuptype: number, _set: boolean): void;

declare function SetAllRandomPedsFlee(player: number, _set: boolean): void;

declare function SetAllowDummyConversions(_set: boolean): void;

declare function SetAlwaysDisplayWeaponPickupMessage(_set: boolean): void;

declare function SetAmbientPlanesSpeedMultiplier(multiplier: number): void;

declare function SetAmbientVoiceName(ped: number, name: string): void;

declare function SetAmmoInClip(ped: number, weapon: number, ammo: number): number;

declare function SetAnimGroupForChar(ped: number, grname: string): void;

declare function SetArmourPickupNetworkRegenTime(timeMS: number): void;

/**
 * Sets a floating-point parameter for a submix effect.
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix. It is expected that the effect is set in this slot beforehand.
 * @param paramIndex The parameter index for the effect.
 * @param paramValue The parameter value to set.
 */
declare function SetAudioSubmixEffectParamFloat(submixId: number, effectSlot: number, paramIndex: number, paramValue: number): void;

/**
 * Sets an integer parameter for a submix effect.
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix. It is expected that the effect is set in this slot beforehand.
 * @param paramIndex The parameter index for the effect.
 * @param paramValue The parameter value to set.
 */
declare function SetAudioSubmixEffectParamInt(submixId: number, effectSlot: number, paramIndex: number, paramValue: number): void;

/**
 * Assigns a RadioFX effect to a submix effect slot.
 * The parameter values for this effect are as follows (backticks are used to represent hashes):
 * | Index | Type | Description |
 * |-|-|-|
 * | \`enabled\` | int | Enables or disables RadioFX on this DSP. |
 * | \`default\` | int | Sets default parameters for the RadioFX DSP and enables it. |
 * | \`freq_low\` | float |  |
 * | \`freq_hi\` | float |  |
 * | \`fudge\` | float |  |
 * | \`rm_mod_freq\` | float |  |
 * | \`rm_mix\` | float |  |
 * | \`o_freq_lo\` | float |  |
 * | \`o_freq_hi\` | float |  |
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix.
 */
declare function SetAudioSubmixEffectRadioFx(submixId: number, effectSlot: number): void;

/**
 * Sets the volumes for the sound channels in a submix effect.
 * Values can be between 0.0 and 1.0.
 * Channel 5 and channel 6 are not used in voice chat but are believed to be center and LFE channels.
 * Output slot starts at 0 for the first ADD_AUDIO_SUBMIX_OUTPUT call then incremented by 1 on each subsequent call.
 * @param submixId The submix.
 * @param outputSlot The output slot index.
 * @param frontLeftVolume The volume for the front left channel.
 * @param frontRightVolume The volume for the front right channel.
 * @param rearLeftVolume The volume for the rear left channel.
 * @param rearRightVolume The volume for the rear right channel.
 * @param channel5Volume The volume for channel 5.
 * @param channel6Volume The volume for channel 6.
 */
declare function SetAudioSubmixOutputVolumes(submixId: number, outputSlot: number, frontLeftVolume: number, frontRightVolume: number, rearLeftVolume: number, rearRightVolume: number, channel5Volume: number, channel6Volume: number): void;

declare function SetBikeRiderWillPutFootDownWhenStopped(bike: number, _set: boolean): void;

declare function SetBit(bit: number): number;

declare function SetBitsInRange(rangebegin: number, rangeend: number, val: number): number;

declare function SetBlipAsFriendly(blip: number, value: boolean): void;

declare function SetBlipAsShortRange(blip: number, value: boolean): void;

declare function SetBlipCoordinates(blip: number, x: number, y: number, z: number): void;

declare function SetBlipMarkerLongDistance(blip: number, _set: boolean): void;

declare function SetBlipThrottleRandomly(veh: number, _set: boolean): void;

declare function SetBlockCameraToggle(_set: boolean): void;

declare function SetBlockingOfNonTemporaryEvents(ped: number, value: boolean): void;

declare function SetBriansMood(mood: number): void;

declare function SetCamActive(camera: number, value: boolean): void;

declare function SetCamAttachOffset(cam: number, x: number, y: number, z: number): void;

declare function SetCamAttachOffsetIsRelative(cam: number, _set: boolean): void;

declare function SetCamBehindPed(ped: number): void;

declare function SetCamComponentShake(cam: number, componentid: number, Unk564: number, time: number, x: number, y: number, z: number): void;

declare function SetCamDofFocuspoint(cam: number, x: number, y: number, z: number, Unk565: number): void;

declare function SetCamFarClip(cam: number, clip: number): void;

declare function SetCamFarDof(cam: number, fardof: number): void;

declare function SetCamFov(camera: number, fov: number): void;

declare function SetCamInFrontOfPed(ped: number): void;

declare function SetCamInheritRollObject(cam: number, obj: number): void;

declare function SetCamInheritRollPed(cam: number, ped: number): void;

declare function SetCamInheritRollVehicle(cam: number, veh: number): void;

declare function SetCamInterpCustomSpeedGraph(speed: number): void;

declare function SetCamInterpDetailRotStyleAngles(Unk566: number): void;

declare function SetCamInterpDetailRotStyleQuats(Unk567: number): void;

declare function SetCamInterpStyleCore(cam0: number, cam1: number, cam2: number, time: number, flag: boolean): void;

declare function SetCamInterpStyleDetailed(cam: number, Unk568: boolean, Unk569: boolean, Unk570: boolean, Unk571: boolean): void;

declare function SetCamMotionBlur(cam: number, blur: number): void;

declare function SetCamName(cam: number, camname: string): void;

declare function SetCamNearClip(cam: number, clip: number): void;

declare function SetCamNearDof(cam: number, dof: number): void;

declare function SetCamPointDampingParams(cam: number, x: number, y: number, z: number): void;

declare function SetCamPointOffset(cam: number, x: number, y: number, z: number): void;

declare function SetCamPointOffsetIsRelative(cam: number, _set: boolean): void;

declare function SetCamPos(camera: number, pX: number, pY: number, pZ: number): void;

declare function SetCamPropagate(camera: number, value: boolean): void;

declare function SetCamRoll(cam: number, roll: number): void;

declare function SetCamRot(camera: number, angleX: number, angleY: number, angleZ: number): void;

declare function SetCamShake(cam: number, Unk572: boolean, shakeval: number): void;

declare function SetCamSplineCustomSpeedGraph(speed: number): void;

declare function SetCamSplineDuration(cam: number, duration: number): void;

declare function SetCamSplineProgress(cam: number, progress: number): void;

declare function SetCamSplineSpeedConstant(cam: number, _set: boolean): void;

declare function SetCamSplineSpeedGraph(cam: number, Unk573: number): void;

declare function SetCamTargetPed(camera: number, ped: number): void;

declare function SetCameraAutoScriptActivation(_set: boolean): void;

declare function SetCameraBeginCamCommandsRequired(_set: boolean): void;

declare function SetCameraControlsDisabledWithPlayerControls(value: boolean): void;

declare function SetCameraState(cam: number, state: number): void;

declare function SetCanBurstCarTyres(car: number, _set: boolean): void;

declare function SetCanResprayCar(car: number, can: boolean): void;

declare function SetCanTargetCharWithoutLos(ped: number, _set: boolean): void;

declare function SetCarAllowedToDrown(car: number, allowed: boolean): void;

declare function SetCarAlwaysCreateSkids(car: number, _set: boolean): void;

declare function SetCarAnimCurrentTime(car: number, animname0: string, animname1: string, time: number): void;

declare function SetCarAnimSpeed(car: number, animname0: string, animname1: string, speed: number): void;

declare function SetCarAsMissionCar(car: number): void;

declare function SetCarCanBeDamaged(vehicle: number, value: boolean): void;

declare function SetCarCanBeVisiblyDamaged(vehicle: number, value: boolean): void;

declare function SetCarCollision(car: number, _set: boolean): void;

declare function SetCarColourCombination(car: number, combination: number): void;

declare function SetCarCoordinates(vehicle: number, pX: number, pY: number, pZ: number): void;

declare function SetCarCoordinatesNoOffset(car: number, x: number, y: number, z: number): void;

declare function SetCarDensityMultiplier(density: number): void;

declare function SetCarDistanceAheadMultiplier(car: number, multiplier: number): void;

declare function SetCarDoorLatched(car: number, door: number, flag0: boolean, flag1: boolean): void;

declare function SetCarEngineOn(car: number, flag0: boolean, flag1: boolean): void;

declare function SetCarExistsOnAllMachines(vehicle: number, exists: boolean): void;

declare function SetCarForwardSpeed(vehicle: number, speed: number): void;

declare function SetCarFovFadeMult(multiplier: number): void;

declare function SetCarFovMax(maxfov: number): void;

declare function SetCarFovMin(minfov: number): void;

declare function SetCarFovRate(rate: number): void;

declare function SetCarFovStartSpeed(speed: number): void;

declare function SetCarFovStartSpeedBoat(speed: number): void;

declare function SetCarGeneratorsActiveInArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, _set: boolean): void;

declare function SetCarHeading(vehicle: number, dir: number): void;

declare function SetCarHealth(vehicle: number, Value: number): void;

declare function SetCarInCutscene(car: number, _set: boolean): void;

declare function SetCarLaneShift(car: number, shift: number): void;

declare function SetCarLightMultiplier(car: number, multiplier: number): void;

declare function SetCarLivery(car: number, livery: number): void;

declare function SetCarMotionBlurEffectBoat(blur: number): void;

declare function SetCarNotDamagedByRelationshipGroup(car: number, _set: boolean, group: number): void;

declare function SetCarOnGroundProperly(vehicle: number): number;

declare function SetCarOnlyDamagedByPlayer(car: number, _set: boolean): void;

declare function SetCarOnlyDamagedByRelationshipGroup(car: number, _set: boolean, group: number): void;

declare function SetCarProofs(vehicle: number, bulletProof: boolean, fireProof: boolean, explosionProof: boolean, collisionProof: boolean, meleeProof: boolean): void;

declare function SetCarRandomRouteSeed(car: number, seed: number): void;

declare function SetCarStayInFastLane(car: number, _set: boolean): void;

declare function SetCarStayInSlowLane(car: number, _set: boolean): void;

declare function SetCarStrong(vehicle: number, strong: boolean): void;

declare function SetCarTraction(car: number, traction: number): void;

declare function SetCarVisible(vehicle: number, value: boolean): void;

declare function SetCarWatertight(car: number, _set: boolean): void;

declare function SetCellphoneRanked(toggle: boolean): void;

declare function SetCharAccuracy(ped: number, value: number): void;

declare function SetCharAllAnimsSpeed(ped: number, speed: number): void;

declare function SetCharAllowedToDuck(ped: number, _set: boolean): void;

declare function SetCharAllowedToRunOnBoats(ped: number, _set: boolean): void;

declare function SetCharAmmo(ped: number, weapon: number, ammo: number): void;

declare function SetCharAngledDefensiveArea(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, angle: number): void;

declare function SetCharAnimBlendOutDelta(ped: number, AnimName0: string, AnimName1: string, delta: number): void;

declare function SetCharAnimCurrentTime(ped: number, AnimName0: string, AnimName1: string, time: number): void;

declare function SetCharAnimPlayingFlag(ped: number, AnimName0: string, AnimName1: string, flag: boolean): boolean;

declare function SetCharAnimSpeed(ped: number, AnimName0: string, AnimName1: string, speed: number): void;

declare function SetCharAsEnemy(ped: number, value: boolean): void;

declare function SetCharAsMissionChar(ped: number): void;

declare function SetCharBleeding(ped: number, _set: boolean): void;

declare function SetCharBulletproofVest(ped: number, _set: boolean): void;

declare function SetCharCanBeKnockedOffBike(ped: number, value: boolean): void;

declare function SetCharCanBeShotInVehicle(ped: number, enabled: boolean): void;

declare function SetCharCanSmashGlass(ped: number, _set: boolean): void;

declare function SetCharCantBeDraggedOut(ped: number, enabled: boolean): void;

declare function SetCharClimbAnimRate(ped: number, rate: number): void;

declare function SetCharCollision(ped: number, _set: boolean): void;

declare function SetCharComponentVariation(ped: number, component: number, modelVariation: number, textureVariation: number): void;

declare function SetCharCoordinates(ped: number, x: number, y: number, z: number): void;

declare function SetCharCoordinatesDontClearPlayerTasks(ped: number, x: number, y: number, z: number): void;

declare function SetCharCoordinatesDontWarpGang(ped: number, x: number, y: number, z: number): void;

declare function SetCharCoordinatesDontWarpGangNoOffset(ped: number, x: number, y: number, z: number): void;

declare function SetCharCoordinatesNoOffset(ped: number, x: number, y: number, z: number): void;

declare function SetCharCurrentWeaponVisible(ped: number, visble: boolean): void;

declare function SetCharDecisionMaker(ped: number, dm: number): void;

declare function SetCharDecisionMakerToDefault(ped: number): void;

declare function SetCharDefaultComponentVariation(ped: number): void;

declare function SetCharDefensiveAreaAttachedToPed(ped: number, pednext: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number, Unk7: number, Unk8: number): void;

declare function SetCharDesiredHeading(ped: number, heading: number): void;

declare function SetCharDiesInstantlyInWater(ped: number, allow: boolean): void;

declare function SetCharDropsWeaponsWhenDead(ped: number, value: boolean): void;

declare function SetCharDrownsInSinkingVehicle(ped: number, _set: boolean): void;

declare function SetCharDrownsInWater(ped: number, _set: boolean): void;

declare function SetCharDruggedUp(ped: number, drugged: boolean): void;

declare function SetCharDucking(ped: number, _set: boolean): number;

declare function SetCharDuckingTimed(ped: number, timed: number): void;

declare function SetCharFireDamageMultiplier(ped: number, multiplier: number): void;

declare function SetCharForceDieInCar(ped: number, _set: boolean): void;

declare function SetCharGestureGroup(ped: number, AnimGroup: string): void;

declare function SetCharGetOutUpsideDownCar(ped: number, _set: boolean): void;

declare function SetCharGravity(ped: number, value: number): void;

declare function SetCharHeading(ped: number, heading: number): void;

declare function SetCharHealth(ped: number, health: number): void;

declare function SetCharInCutscene(ped: number, _set: boolean): void;

declare function SetCharInvincible(ped: number, enable: boolean): void;

declare function SetCharIsTargetPriority(ped: number, enable: boolean): void;

declare function SetCharKeepTask(ped: number, value: boolean): void;

declare function SetCharMaxHealth(ped: number, value: number): void;

declare function SetCharMaxMoveBlendRatio(ped: number, ratio: number): void;

declare function SetCharMaxTimeInWater(ped: number, time: number): void;

declare function SetCharMaxTimeUnderwater(ped: number, time: number): void;

declare function SetCharMeleeActionFlag0(ped: number, _set: boolean): void;

declare function SetCharMeleeActionFlag1(ped: number, _set: boolean): void;

declare function SetCharMeleeActionFlag2(ped: number, _set: boolean): void;

declare function SetCharMeleeMovementConstaintBox(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SetCharMoney(ped: number, amount: number): void;

declare function SetCharMoveAnimSpeedMultiplier(ped: number, multiplier: number): void;

declare function SetCharMovementAnimsBlocked(ped: number, _set: boolean): void;

declare function SetCharNameDebug(ped: number, debugName: string): void;

declare function SetCharNeverLeavesGroup(ped: number, value: boolean): void;

declare function SetCharNeverTargetted(ped: number, _set: boolean): void;

declare function SetCharNotDamagedByRelationshipGroup(ped: number, relationshipGroup: number, enable: boolean): void;

declare function SetCharOnlyDamagedByPlayer(ped: number, _set: boolean): void;

declare function SetCharOnlyDamagedByRelationshipGroup(ped: number, _set: boolean, relgroup: number): void;

declare function SetCharProofs(ped: number, unknown0: boolean, fallingDamage: boolean, unknown1: boolean, unknown2: boolean, unknown3: boolean): void;

declare function SetCharPropIndex(ped: number, propType: number, index: number): void;

declare function SetCharPropIndexTexture(ped: number, Unk9: number, Unk10: number, Unk11: number): void;

declare function SetCharProvideCoveringFire(ped: number, _set: boolean): void;

declare function SetCharRandomComponentVariation(ped: number): void;

declare function SetCharReadyToBeExecuted(ped: number, _set: boolean): void;

declare function SetCharReadyToBeStunned(ped: number, _set: boolean): void;

declare function SetCharRelationship(ped: number, relationshipLevel: number, relationshipGroup: number): void;

declare function SetCharRelationshipGroup(ped: number, relationshipGroup: number): void;

declare function SetCharRotation(ped: number, xr: number, yr: number, zr: number): void;

declare function SetCharShootRate(ped: number, rate: number): void;

declare function SetCharSignalAfterKill(ped: number, _set: boolean): void;

declare function SetCharSphereDefensiveArea(ped: number, x: number, y: number, z: number, radius: number): void;

declare function SetCharStayInCarWhenJacked(ped: number, _set: boolean): void;

declare function SetCharSuffersCriticalHits(ped: number, value: boolean): void;

declare function SetCharUsesDeafultAnimGroupWhenFleeing(ped: number, _set: boolean): void;

declare function SetCharUsesUpperbodyDamageAnimsOnly(ped: number, _set: boolean): void;

declare function SetCharVelocity(ped: number, x: number, y: number, z: number): void;

declare function SetCharVisible(ped: number, value: boolean): void;

declare function SetCharWalkAlongsideLeaderWhenAppropriate(ped: number, _set: boolean): void;

declare function SetCharWantedByPolice(ped: number, wanted: boolean): void;

declare function SetCharWatchMelee(ped: number, _set: boolean): void;

declare function SetCharWeaponSkill(ped: number, skill: number): void;

declare function SetCharWillCowerInsteadOfFleeing(ped: number, _set: boolean): void;

declare function SetCharWillDoDrivebys(ped: number, value: boolean): void;

declare function SetCharWillFlyThroughWindscreen(ped: number, value: boolean): void;

declare function SetCharWillLeaveCarInCombat(ped: number, _set: boolean): void;

declare function SetCharWillMoveWhenInjured(ped: number, value: boolean): void;

declare function SetCharWillOnlyFireWithClearLos(ped: number, _set: boolean): void;

declare function SetCharWillRemainOnBoatAfterMissionEnds(ped: number, _set: boolean): void;

declare function SetCharWillTryToLeaveBoatAfterLeader(ped: number, _set: boolean): void;

declare function SetCharWillTryToLeaveWater(ped: number, _set: boolean): void;

declare function SetCharWillUseCarsInCombat(ped: number, value: boolean): void;

declare function SetCharWillUseCover(ped: number, value: boolean): void;

declare function SetCinematicButtonEnabled(_set: boolean): void;

declare function SetClearHelpInMissionCleanup(_set: boolean): void;

declare function SetClearManifolds(_set: boolean): void;

declare function SetCollectable1Total(total: number): void;

declare function SetCollideWithPeds(_set: boolean): void;

declare function SetCombatDecisionMaker(ped: number, dm: number): void;

declare function SetContentsOfTextWidget(Unk1115: number, Unk1116: number): void;

declare function SetConvertibleRoof(car: number, _set: boolean): void;

declare function SetCreateRandomCops(_set: boolean): void;

declare function SetCreditsToRenderBeforeFade(_set: boolean): void;

declare function SetCurrentCharWeapon(ped: number, w: number, unknownTrue: boolean): void;

declare function SetCurrentMovie(filename: string): void;

declare function SetCutsceneExtraRoomPos(x: number, y: number, z: number): void;

declare function SetDanceShakeActiveThisUpdate(shake: number): void;

declare function SetDanceShakeInactiveImmediately(): void;

declare function SetDeadCharCoordinates(ped: number, x: number, y: number, z: number): void;

declare function SetDeadPedsDropWeapons(_set: boolean): void;

declare function SetDeathWeaponsPersist(ped: number, _set: boolean): void;

declare function SetDebugTextVisible(Unk1120: boolean): void;

declare function SetDecisionMakerAttributeCanChangeTarget(dm: number, value: boolean): void;

declare function SetDecisionMakerAttributeCaution(dm: number, value: number): void;

declare function SetDecisionMakerAttributeFireRate(dm: number, value: number): void;

declare function SetDecisionMakerAttributeLowHealth(dm: number, value: number): void;

declare function SetDecisionMakerAttributeMovementStyle(dm: number, value: number): void;

declare function SetDecisionMakerAttributeNavigationStyle(dm: number, value: number): void;

declare function SetDecisionMakerAttributeRetreatingBehaviour(dm: number, value: number): void;

declare function SetDecisionMakerAttributeSightRange(dm: number, value: number): void;

declare function SetDecisionMakerAttributeStandingStyle(dm: number, value: number): void;

declare function SetDecisionMakerAttributeTargetInjuredReaction(dm: number, value: number): void;

declare function SetDecisionMakerAttributeTargetLossResponse(dm: number, value: number): void;

declare function SetDecisionMakerAttributeTeamwork(dm: number, value: number): void;

declare function SetDecisionMakerAttributeWeaponAccuracy(dm: number, value: number): void;

declare function SetDefaultGlobalInstancePriority(): void;

declare function SetDefaultTargetScoringFunction(ped: number, Unk132: number): void;

declare function SetDisablePlayerShoveAnimation(ped: number, disable: boolean): void;

/**
 * This native sets the app id for the discord rich presence implementation.
 * @param appId A valid Discord API App Id, can be generated at https://discordapp.com/developers/applications/
 */
declare function SetDiscordAppId(appId: string): void;

/**
 * Sets a clickable button to be displayed in a player's Discord rich presence.
 * @param index The button index, either 0 or 1.
 * @param label The text to display on the button.
 * @param url The URL to open when clicking the button. This has to start with `fivem://connect/` or `https://`.
 */
declare function SetDiscordRichPresenceAction(index: number, label: string, url: string): void;

/**
 * This native sets the image asset for the discord rich presence implementation.
 * @param assetName The name of a valid asset registered on Discordapp's developer dashboard. note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.
 */
declare function SetDiscordRichPresenceAsset(assetName: string): void;

/**
 * This native sets the small image asset for the discord rich presence implementation.
 * @param assetName The name of a valid asset registered on Discordapp's developer dashboard. Note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.
 */
declare function SetDiscordRichPresenceAssetSmall(assetName: string): void;

/**
 * This native sets the hover text of the small image asset for the discord rich presence implementation.
 * @param text Text to be displayed when hovering over small image asset. Note that you must also set a valid small image asset using the SET_DISCORD_RICH_PRESENCE_ASSET_SMALL native.
 */
declare function SetDiscordRichPresenceAssetSmallText(text: string): void;

/**
 * This native sets the hover text of the image asset for the discord rich presence implementation.
 * @param text Text to be displayed when hovering over image asset. Note that you must also set a valid image asset using the SET_DISCORD_RICH_PRESENCE_ASSET native.
 */
declare function SetDiscordRichPresenceAssetText(text: string): void;

declare function SetDisplayPlayerNameAndIcon(playerIndex: number, _set: boolean): void;

declare function SetDitchPoliceModels(_set: boolean): void;

declare function SetDoNotSpawnParkedCarsOnTop(pickup: number, _set: boolean): void;

declare function SetDontActivateRagdollFromPlayerImpact(ped: number, _set: boolean): void;

declare function SetDoorState(door: number, flag: boolean, Unk95: number): void;

declare function SetDrawPlayerComponent(component: number, _set: boolean): void;

declare function SetDriveTaskCruiseSpeed(ped: number, speed: number): void;

declare function SetDrunkCam(cam: number, val: number, time: number): void;

/**
 * Navigates the specified DUI browser to a different URL.
 * @param duiObject The DUI browser handle.
 * @param url The new URL.
 */
declare function SetDuiUrl(duiObject: number, url: string): void;

declare function SetEnableNearClipScan(_set: boolean): void;

declare function SetEnableRcDetonate(_set: boolean): void;

declare function SetEnableRcDetonateOnContact(_set: boolean): void;

declare function SetEngineHealth(vehicle: number, health: number): void;

declare function SetEveryoneIgnorePlayer(playerIndex: number, value: boolean): void;

declare function SetExtraCarColours(vehicle: number, colour1: number, colour2: number): void;

declare function SetExtraHospitalRestartPoint(x: number, y: number, z: number, Unk489: number, Unk490: number): void;

declare function SetExtraPoliceStationRestartPoint(x: number, y: number, z: number, Unk491: number, Unk492: number): void;

declare function SetFadeInAfterLoad(_set: boolean): void;

declare function SetFakeWantedCircle(x: number, y: number, radius: number): void;

declare function SetFakeWantedLevel(lvl: number): void;

declare function SetFilterMenuOn(toggle: boolean): void;

declare function SetFilterSaveSetting(filterid: number, setting: number): void;

declare function SetFixedCamPos(x: number, y: number, z: number): void;

declare function SetFloatStat(stat: number, value: number): void;

declare function SetFollowPedPitchLimitDown(pitchdownlim: number): void;

declare function SetFollowPedPitchLimitUp(pitchuplim: number): void;

declare function SetFollowVehicleCamOffset(Unk574: boolean, x: number, y: number, z: number): void;

declare function SetFollowVehicleCamSubmode(mode: number): void;

declare function SetFollowVehiclePitchLimitDown(pitchdownlim: number): void;

declare function SetFollowVehiclePitchLimitUp(pitchuplim: number): void;

declare function SetForceLookBehind(_set: boolean): void;

declare function SetForcePlayerToEnterThroughDirectDoor(ped: number, _set: boolean): void;

declare function SetFovChannelScript(_set: boolean): void;

declare function SetFreeHealthCare(player: number, _set: boolean): void;

declare function SetFreeResprays(_set: boolean): void;

declare function SetFreebiesInVehicle(veh: number, _set: boolean): void;

declare function SetGameCamHeading(heading: number): void;

declare function SetGameCamPitch(pitch: number): void;

declare function SetGameCameraControlsActive(active: boolean): void;

declare function SetGangCar(car: number, _set: boolean): void;

declare function SetGarageLeaveCameraAlone(garageName: string, _set: boolean): void;

declare function SetGfwlHasSafeHouse(ukn: number): void;

declare function SetGfwlIsReturningToSinglePlayer(Unk963: number): void;

declare function SetGlobalInstancePriority(priority: number): void;

declare function SetGlobalRenderFlags(Unk507: boolean, Unk508: boolean, Unk509: boolean, Unk510: boolean): void;

declare function SetGpsRemainsWhenTargetReachedFlag(_set: boolean): void;

declare function SetGpsTestIn_3dFlag(_set: boolean): void;

declare function SetGpsVoiceForVehicle(veh: number, VoiceId: number): void;

declare function SetGravityOff(_set: boolean): void;

declare function SetGroupCharDecisionMaker(group: number, dm: number): void;

declare function SetGroupCharDucksWhenAimedAt(ped: number, value: boolean): void;

declare function SetGroupCombatDecisionMaker(group: number, dm: number): void;

declare function SetGroupFollowStatus(group: number, status: number): void;

declare function SetGroupFormation(group: number, formation: number): void;

declare function SetGroupFormationSpacing(group: number, space: number): void;

declare function SetGroupLeader(group: number, leader: number): void;

declare function SetGroupMember(group: number, member: number): void;

declare function SetGroupSeparationRange(group: number, seperation: number): void;

declare function SetGunshotSenseRangeForRiot2(range: number): void;

declare function SetHasBeenOwnedByPlayer(car: number, _set: boolean): void;

declare function SetHasBeenOwnedForCarGenerator(CarGen: number, _set: boolean): void;

declare function SetHeadingLimitForAttachedPed(ped: number, heading0: number, heading1: number): void;

declare function SetHeadingOfClosestObjectOfType(x: number, y: number, z: number, radius: number, type_or_model: number, heading: number): void;

declare function SetHealthPickupNetworkRegenTime(timeMS: number): void;

declare function SetHeliBladesFullSpeed(heli: number): void;

declare function SetHeliForceEngineOn(heli: number, _set: boolean): number;

declare function SetHeliStabiliser(heli: number, _set: boolean): void;

declare function SetHelpMessageBoxSize(Unk773: number): void;

declare function SetHelpMessageBoxSizeF(size: number): void;

declare function SetHideWeaponIcon(_set: boolean): void;

declare function SetHintAdvancedParams(Unk575: number, Unk576: number, Unk577: number, Unk578: number, Unk579: boolean): void;

declare function SetHintFov(fov: number): void;

declare function SetHintMoveInDist(dist: number): void;

declare function SetHintMoveInDistDefault(): void;

declare function SetHintTimes(Unk580: number, Unk581: number, Unk582: number): void;

declare function SetHintTimesDefault(): void;

declare function SetHostMatchOn(Unk964: boolean): void;

declare function SetHotWeaponSwap(_set: boolean): void;

declare function SetIgnoreLowPriorityShockingEvents(ped: number, value: boolean): void;

declare function SetIgnoreNoGpsFlag(_set: boolean): void;

declare function SetIkDisabledForNetworkPlayer(playerIndex: number, Unk965: boolean): void;

declare function SetInMpTutorial(_set: boolean): void;

declare function SetInSpectatorMode(spectate: boolean): void;

declare function SetInformRespectedFriends(ped: number, Unk43: number, Unk44: number): void;

declare function SetInstantWidescreenBorders(_set: boolean): void;

declare function SetIntStat(stat: number, value: number): void;

declare function SetInterpFromGameToScript(Unk604: boolean, Unk605: number): void;

declare function SetInterpFromScriptToGame(Unk606: boolean, Unk607: number): void;

declare function SetInterpInOutVehicleEnabledThisFrame(_set: boolean): void;

/**
 * Toggles the visibility of resource names in the FiveM key mapping page.
 * @param hide `true` will disable the display of resource names, and `false` will enable it.
 */
declare function SetKeyMappingHideResources(hide: boolean): void;

declare function SetKillstreak(): void;

declare function SetLoadCollisionForCarFlag(car: number, _set: boolean): void;

declare function SetLoadCollisionForCharFlag(ped: number, _set: boolean): void;

declare function SetLoadCollisionForObjectFlag(obj: number, _set: boolean): void;

declare function SetLobbyMuteOverride(_set: boolean): void;

declare function SetLocalPlayerPainVoice(name: string): void;

declare function SetLocalPlayerVoice(name: string): void;

declare function SetLoudVehicleRadio(veh: number, _set: boolean): void;

/**
 * Sets whether or not `SHUTDOWN_LOADING_SCREEN` automatically shuts down the NUI frame for the loading screen. If this is enabled,
 * you will have to manually invoke `SHUTDOWN_LOADING_SCREEN_NUI` whenever you want to hide the NUI loading screen.
 * @param manualShutdown TRUE to manually shut down the loading screen NUI.
 */
declare function SetManualShutdownLoadingScreenNui(manualShutdown: boolean): void;

declare function SetMask(Unk774: number, Unk775: number, Unk776: number, Unk777: number): void;

declare function SetMaxFireGenerations(max: number): void;

declare function SetMaxWantedLevel(lvl: number): void;

declare function SetMenuColumn(menuid: number, Unk866: number, Unk867: number, Unk868: number, Unk869: number, Unk870: number, Unk871: number, Unk872: number, Unk873: number, Unk874: number, Unk875: number, Unk876: number, Unk877: number, Unk878: number, Unk879: number): void;

declare function SetMenuColumnOrientation(menuid: number, column: number, orientation: number): void;

declare function SetMenuColumnWidth(menuid: number, column: number, width: number): void;

declare function SetMenuItemWithNumber(menuid: number, item: number, Unk881: number, gxtkey: string, _number: number): void;

declare function SetMenuItemWith_2Numbers(menuid: number, item: number, Unk880: number, gxtkey: string, number0: number, number1: number): void;

declare function SetMessageFormatting(Unk700: boolean, Unk701: number, Unk702: number): void;

declare function SetMessagesWaiting(_set: boolean): void;

declare function SetMinMaxPedAccuracy(ped: number, min: number, max: number): void;

declare function SetMinigameInProgress(_set: boolean): void;

declare function SetMissionFlag(isMission: boolean): void;

declare function SetMissionPassedCash(add: boolean, cash: number, Unk511: number): void;

declare function SetMissionPickupSound(model: number, SoundName: string): void;

declare function SetMissionRespectTotal(respect: number): void;

declare function SetMissionTrainCoordinates(train: number, x: number, y: number, z: number): void;

declare function SetMobilePhonePosition(x: number, y: number, z: number): void;

declare function SetMobilePhoneRadioState(state: boolean): void;

declare function SetMobilePhoneRotation(x: number, y: number, z: number): void;

declare function SetMobilePhoneScale(scale: number): void;

declare function SetMobileRadioEnabledDuringGameplay(_set: boolean): void;

declare function SetMobileRingType(_type: number): void;

declare function SetMoneyCarriedByAllNewPeds(money: number): void;

declare function SetMoneyCarriedByPedWithModel(model: number, m0: number, m1: number): void;

declare function SetMovieTime(time: number): void;

declare function SetMovieVolume(volume: number): void;

declare function SetMsgForLoadingScreen(label: string): void;

declare function SetMultiplayerHudCash(cash: number): void;

declare function SetMultiplayerHudTime(str: string): void;

declare function SetNeedsToBeHotwired(veh: number, _set: boolean): void;

declare function SetNetworkIdCanMigrate(netid: number, value: boolean): void;

declare function SetNetworkIdExistsOnAllMachines(netID: number, _set: boolean): void;

declare function SetNetworkIdStopCloning(id: number, Unk966: boolean): void;

declare function SetNetworkJoinFail(ukn0: boolean): void;

declare function SetNetworkPedUsingParachute(ped: number): void;

declare function SetNetworkPlayerAsVip(playerIndex: number, Unk967: boolean): void;

declare function SetNetworkVehicleRespotTimer(id: number, ukn4000: number): void;

/**
 * SET_NETWORK_WALK_MODE
 */
declare function SetNetworkWalkMode(enabled: boolean): void;

declare function SetNextDesiredMoveState(state: number): void;

declare function SetNmAnimPose(ped: number, AnimName0: string, AnimName1: string, pose: number): void;

declare function SetNmMessageBool(id: number, value: boolean): void;

declare function SetNmMessageFloat(id: number, value: number): void;

declare function SetNmMessageInstanceIndex(id: number, ped: number, car: number, obj: number): void;

declare function SetNmMessageInt(id: number, value: number): void;

declare function SetNmMessageString(id: number, _string: string): void;

declare function SetNmMessageVec3(id: number, x: number, y: number, z: number): void;

declare function SetNoResprays(_set: boolean): void;

/**
 * SET_NUI_FOCUS
 */
declare function SetNuiFocus(hasFocus: boolean, hasCursor: boolean): void;

/**
 * SET_NUI_FOCUS_KEEP_INPUT
 */
declare function SetNuiFocusKeepInput(keepInput: boolean): void;

/**
 * Set the z-index of the NUI resource.
 * @param zIndex New z-index value.
 */
declare function SetNuiZindex(zIndex: number): void;

declare function SetObjectAlpha(obj: number, alpha: number): void;

declare function SetObjectAnimCurrentTime(obj: number, animname0: string, animname1: string, time: number): void;

declare function SetObjectAnimPlayingFlag(obj: number, animname0: string, animname1: string, flag: boolean): void;

declare function SetObjectAnimSpeed(obj: number, animname0: string, animname1: string, speed: number): void;

declare function SetObjectAsStealable(obj: number, _set: boolean): void;

declare function SetObjectCcd(obj: number, _set: boolean): void;

declare function SetObjectCollision(obj: number, value: boolean): void;

declare function SetObjectCoordinates(obj: number, pX: number, pY: number, pZ: number): void;

declare function SetObjectDrawLast(obj: number, _set: boolean): void;

declare function SetObjectDynamic(obj: number, _set: boolean): void;

declare function SetObjectExistsOnAllMachines(obj: number, exists: boolean): void;

declare function SetObjectHeading(obj: number, value: number): void;

declare function SetObjectHealth(obj: number, health: number): void;

declare function SetObjectInitialRotationVelocity(obj: number, x: number, y: number, z: number): void;

declare function SetObjectInitialVelocity(obj: number, x: number, y: number, z: number): void;

declare function SetObjectInvincible(obj: number, _set: boolean): void;

declare function SetObjectLights(obj: number, lights: boolean): void;

declare function SetObjectOnlyDamagedByPlayer(obj: number, _set: boolean): void;

declare function SetObjectPhysicsParams(obj: number, Unk96: number, Unk97: number, v0x: number, v0y: number, v0z: number, v1x: number, v1y: number, v1z: number, flag0: number, flag1: number): void;

declare function SetObjectProofs(obj: number, unknown0: boolean, fallingDamage: boolean, unknown1: boolean, unknown2: boolean, unknown3: boolean): void;

declare function SetObjectQuaternion(obj: number, qx: number, qy: number, qz: number, qw: number): void;

declare function SetObjectRecordsCollisions(obj: number, _set: boolean): void;

declare function SetObjectRenderScorched(obj: number, _set: boolean): void;

declare function SetObjectRotation(obj: number, Pitch: number, Roll: number, Yaw: number): void;

declare function SetObjectScale(obj: number, scale: number): void;

declare function SetObjectUsedInPoolGame(obj: number, _set: boolean): void;

declare function SetObjectVisible(obj: number, value: boolean): void;

declare function SetOnlineLan(Unk968: boolean): void;

declare function SetOnlineScore(Unk1059: number, Unk1060: number): void;

declare function SetOnscreenCounterFlashWhenFirstDisplayed(counterid: number, flash: boolean): void;

declare function SetOverrideNoSprintingOnPhoneInMultiplayer(Unk969: boolean): void;

declare function SetParkedCarDensityMultiplier(multiplier: number): void;

declare function SetPedAllowMissionOnlyDrivebyUse(ped: number, _set: boolean): void;

declare function SetPedAlpha(ped: number, alpha: number): void;

declare function SetPedComponentsToNetworkPlayersettingsModel(ped: number): void;

declare function SetPedDensityMultiplier(density: number): void;

declare function SetPedDiesWhenInjured(ped: number, value: boolean): void;

declare function SetPedDontDoEvasiveDives(ped: number, value: boolean): void;

declare function SetPedDontUseVehicleSpecificAnims(ped: number, _set: boolean): void;

declare function SetPedEnableLegIk(ped: number, _set: boolean): void;

declare function SetPedExistsOnAllMachines(ped: number, exists: boolean): void;

declare function SetPedFallOffBikesWhenShot(ped: number, _set: boolean): void;

declare function SetPedFireFxLodScaler(scale: number): void;

declare function SetPedForceFlyThroughWindscreen(ped: number, _set: boolean): void;

declare function SetPedForceVisualiseHeadDamageFromBullets(ped: number, _set: boolean): void;

declare function SetPedGeneratesDeadBodyEvents(ped: number, _set: boolean): void;

declare function SetPedHeedsTheEveryoneIgnorePlayerFlag(ped: number, _set: boolean): void;

declare function SetPedHeliPilotRespectsMinimummHeight(ped: number, _set: boolean): void;

declare function SetPedHelmetTextureIndex(ped: number, index: number): void;

declare function SetPedInstantBlendsWeaponAnims(ped: number, _set: boolean): void;

declare function SetPedIsBlindRaging(ped: number, value: boolean): void;

declare function SetPedIsDrunk(ped: number, value: boolean): void;

declare function SetPedMobileRingType(ped: number, RingtoneId: number): void;

declare function SetPedMotionBlur(ped: number, _set: boolean): void;

declare function SetPedNonCreationArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SetPedNonRemovalArea(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SetPedPathMayDropFromHeight(ped: number, value: boolean): void;

declare function SetPedPathMayUseClimbovers(ped: number, value: boolean): void;

declare function SetPedPathMayUseLadders(ped: number, value: boolean): void;

declare function SetPedPathWillAvoidDynamicObjects(ped: number, _set: boolean): void;

declare function SetPedSkipsComplexCoverCollisionChecks(ped: number, _set: boolean): void;

declare function SetPedSteersAroundObjects(ped: number, _set: boolean): void;

declare function SetPedSteersAroundPeds(ped: number, _set: boolean): void;

declare function SetPedWindyClothingScale(ped: number, scale: number): void;

declare function SetPedWithBrainCanBeConvertedToDummyPed(ped: number, _set: boolean): void;

declare function SetPedWontAttackPlayerWithoutWantedLevel(ped: number, _set: boolean): void;

declare function SetPetrolTankHealth(vehicle: number, value: number): void;

declare function SetPetrolTankWeakpoint(car: number, _set: boolean): void;

declare function SetPhoneHudItem(id: number, gxttext: string, Unk800: number): void;

declare function SetPhysCcdHandlesRotation(_set: boolean): void;

declare function SetPickupCollectableByCar(pickup: number, _set: boolean): void;

declare function SetPickupsFixCars(_set: boolean): void;

declare function SetPlaneThrottle(plane: number, throttle: number): void;

declare function SetPlaneUndercarriageUp(plain: number, _set: boolean): void;

declare function SetPlaybackSpeed(car: number, speed: number): void;

declare function SetPlayerAsCop(player: number, _set: boolean): void;

declare function SetPlayerAsDamagedPlayer(playerIndex: number, Unk1057: number, Unk1058: boolean): void;

declare function SetPlayerCanBeHassledByGangs(playerIndex: number, value: boolean): void;

declare function SetPlayerCanDoDriveBy(playerIndex: number, value: boolean): void;

declare function SetPlayerCanDropWeaponsInCar(_set: boolean): void;

declare function SetPlayerCanUseCover(playerIndex: number, value: boolean): void;

declare function SetPlayerControl(playerIndex: number, value: boolean): void;

declare function SetPlayerControlAdvanced(playerIndex: number, unknown1: boolean, unknown2: boolean, unknown3: boolean): void;

declare function SetPlayerControlForAmbientScript(player: number, flag0: boolean, flag1: boolean): void;

declare function SetPlayerControlForNetwork(playerIndex: number, unknownTrue: boolean, unknownFalse: boolean): void;

declare function SetPlayerControlForTextChat(player: number, _set: boolean): void;

declare function SetPlayerControlOnInMissionCleanup(_set: boolean): void;

declare function SetPlayerDisableCrouch(player: number, _set: boolean): void;

declare function SetPlayerDisableJump(player: number, _set: boolean): void;

declare function SetPlayerFastReload(playerIndex: number, value: boolean): void;

declare function SetPlayerForcedAim(player: number, _set: boolean): void;

declare function SetPlayerGroupRecruitment(player: number, _set: boolean): void;

declare function SetPlayerGroupToFollowAlways(playerIndex: number, value: boolean): void;

declare function SetPlayerGroupToFollowNever(player: number, _set: boolean): void;

declare function SetPlayerIconColour(colour: number): void;

declare function SetPlayerInvincible(playerIndex: number, value: boolean): void;

declare function SetPlayerInvisibleToAi(_set: boolean): void;

declare function SetPlayerIsInStadium(_set: boolean): void;

declare function SetPlayerKeepsWeaponsWhenRespawned(_set: boolean): void;

declare function SetPlayerMayOnlyEnterThisVehicle(player: number, veh: number): void;

declare function SetPlayerMoodNormal(playerIndex: number): void;

declare function SetPlayerMoodPissedOff(playerIndex: number, unknown150: number): void;

declare function SetPlayerMpModifier(player: number, Unk12: number, modifier: number): void;

declare function SetPlayerNeverGetsTired(playerIndex: number, value: boolean): void;

declare function SetPlayerPainRootBankName(name: string): void;

declare function SetPlayerPlayerTargetting(_set: boolean): void;

declare function SetPlayerSettingsGenre(ped: number): void;

/**
 * the status of default voip system. It affects on `NETWORK_IS_PLAYER_TALKING` and `mp_facial` animation.
 * This function doesn't need to be called every frame, it works like a switcher.
 * @param player The target player.
 * @param state Overriding state.
 */
declare function SetPlayerTalkingOverride(player: number, state: boolean): void;

declare function SetPlayerTeam(Player: number, team: number): void;

declare function SetPlayersDropMoneyInNetworkGame(toggle: boolean): void;

declare function SetPlayersettingsModelVariationsChoice(playerIndex: number): void;

declare function SetPoliceFocusWillTrackCar(car: number, _set: boolean): void;

declare function SetPoliceIgnorePlayer(playerIndex: number, value: boolean): void;

declare function SetPoliceRadarBlips(_set: boolean): void;

declare function SetPtfxCamInsideVehicle(_set: boolean): void;

declare function SetRadarAsInteriorThisFrame(): void;

declare function SetRadarScale(scale: number): void;

declare function SetRadarZoom(zoom: number): void;

declare function SetRailtrackResistanceMult(resistance: number): void;

declare function SetRandomCarDensityMultiplier(density: number): void;

declare function SetRandomSeed(seed: number): void;

declare function SetRecordingToPointNearestToCoors(cat: number, x: number, y: number, z: number): void;

declare function SetReducePedModelBudget(_set: boolean): void;

declare function SetReduceVehicleModelBudget(_set: boolean): void;

declare function SetRelationship(relationshipLevel: number, relationshipGroup1: number, relationshipGroup2: number): void;

declare function SetRenderTrainAsDerailed(train: number, _set: boolean): void;

/**
 * A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvp(key: string, value: string): void;

/**
 * A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvpFloat(key: string, value: number): void;

/**
 * Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvpFloatNoSync(key: string, value: number): void;

/**
 * A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvpInt(key: string, value: number): void;

/**
 * Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvpIntNoSync(key: string, value: number): void;

/**
 * Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
declare function SetResourceKvpNoSync(key: string, value: string): void;

declare function SetReturnToFilterMenu(Unk970: boolean): void;

declare function SetRichPresence(Unk971: number, Unk972: number, Unk973: number, Unk974: number, Unk975: number): void;

/**
 * Sets the player's rich presence detail state for social platform providers to a specified string.
 * @param presenceState The rich presence string to set.
 */
declare function SetRichPresence(presenceState: string): void;

declare function SetRichPresenceTemplatefilter(): void;

declare function SetRichPresenceTemplatelobby(Unk976: number): void;

declare function SetRichPresenceTemplatemp1(Unk977: number, Unk978: number, Unk979: number, Unk980: number): void;

declare function SetRichPresenceTemplatemp2(Unk981: number): void;

declare function SetRichPresenceTemplatemp3(Unk982: number, Unk983: number): void;

declare function SetRichPresenceTemplatemp4(Unk984: number, Unk985: number): void;

declare function SetRichPresenceTemplatemp5(Unk986: number, Unk987: number, Unk988: number): void;

declare function SetRichPresenceTemplatemp6(Unk989: number, Unk990: number, Unk991: number): void;

declare function SetRichPresenceTemplateparty(): void;

declare function SetRichPresenceTemplatesp1(Unk992: number, Unk993: number, Unk994: number): void;

declare function SetRichPresenceTemplatesp2(Unk995: number): void;

declare function SetRocketLauncherFreebieInHeli(_set: boolean): void;

declare function SetRomansMood(moood: number): void;

declare function SetRoomForCarByKey(car: number, roomkey: number): void;

declare function SetRoomForCarByName(car: number, roomname: string): void;

declare function SetRoomForCharByKey(ped: number, key: number): void;

declare function SetRoomForCharByName(ped: number, roomname: string): void;

declare function SetRoomForViewportByKey(viewportid: number, roomkey: number): void;

declare function SetRoomForViewportByName(viewportid: number, roomname: string): void;

declare function SetRotOrder(order: number): void;

declare function SetRotationForAttachedPed(ped: number, xr: number, yr: number, zr: number): void;

declare function SetRoute(blip: number, value: boolean): void;

declare function SetScenarioPedDensityMultiplier(density: number, densitynext: number): void;

declare function SetScreenFade(viewportid: number, Unk778: number, Unk779: number, Unk780: boolean, r: number, g: number, b: number, a: number, Unk781: number, Unk782: number, Unk783: number): void;

declare function SetScriptLimitToGangSize(size: number): void;

declare function SetScriptMicLookAt(x: number, y: number, z: number): void;

declare function SetScriptMicPosition(x: number, y: number, z: number): void;

declare function SetScriptedAnimSeatOffset(ped: number, offset: number): void;

declare function SetScriptedConversionCentre(x: number, y: number, z: number): void;

declare function SetSelectedMenuItem(menuid: number, item: number): void;

declare function SetSenseRange(ped: number, value: number): void;

declare function SetSequenceToRepeat(seq: number, repeat: number): void;

declare function SetServerId(id: number): void;

declare function SetSirenWithNoDriver(car: number, _set: boolean): void;

declare function SetSleepModeActive(_set: boolean): void;

/**
 * SET_SNAKEOIL_FOR_ENTRY
 */
declare function SetSnakeoilForEntry(name: string, path: string, data: string): void;

declare function SetSniperZoomFactor(factor: number): void;

declare function SetSpecificPassengerIndexToUseInGroups(ped: number, index: number): void;

declare function SetSpritesDrawBeforeFade(_set: boolean): void;

declare function SetStartFromFilterMenu(Unk996: number): void;

declare function SetStatFrontendAlwaysVisible(_set: boolean): void;

declare function SetStatFrontendDisplayType(stat: number, _type: number): void;

declare function SetStatFrontendNeverVisible(stat: number): void;

declare function SetStatFrontendVisibility(stat: number, _set: boolean): void;

declare function SetStatFrontendVisibleAfterIncremented(stat: number): void;

/**
 * Internal function for setting a state bag value.
 */
declare function SetStateBagValue(bagName: string, keyName: string, valueData: string, valueLength: number, replicated: boolean): void;

declare function SetStateOfClosestDoorOfType(model: number, x: number, y: number, z: number, state: number, Unk601: number): void;

declare function SetStreamParams(rolloff: number, UnkTime: number): void;

declare function SetStreamingRequestListTime(time: number): void;

declare function SetSuppressHeadlightSwitch(_set: boolean): void;

declare function SetSwimSpeed(ped: number, speed: number): void;

declare function SetSyncWeatherAndGameTime(Unk997: boolean): void;

declare function SetTargetCarForMissionGarage(garage: number, car: number): void;

declare function SetTaxiGarageRadioState(radiostate: boolean): void;

declare function SetTaxiLights(car: number, _set: boolean): void;

declare function SetTelescopeCamAngleLimits(Unk583: number, Unk584: number, Unk585: number, Unk586: number, Unk587: number, Unk588: number): void;

declare function SetTextBackground(value: boolean): void;

declare function SetTextCentre(value: boolean): void;

declare function SetTextCentreWrapx(wrapx: number): void;

/**
 * SET_TEXT_CHAT_ENABLED
 */
declare function SetTextChatEnabled(enabled: boolean): boolean;

declare function SetTextColour(r: number, g: number, b: number, a: number): void;

declare function SetTextDrawBeforeFade(value: boolean): void;

declare function SetTextDropshadow(displayShadow: boolean, r: number, g: number, b: number, a: number): void;

declare function SetTextEdge(displayEdge: boolean, r: number, g: number, b: number, a: number): void;

declare function SetTextFont(font: number): void;

declare function SetTextInputActive(_set: boolean): void;

declare function SetTextJustify(value: boolean): void;

declare function SetTextLineDisplay(unk1: number, unk2: number): void;

declare function SetTextLineHeightMult(lineHeight: number): void;

declare function SetTextProportional(value: boolean): void;

declare function SetTextRenderId(renderId: number): void;

declare function SetTextRightJustify(value: boolean): void;

declare function SetTextScale(w: number, h: number): void;

declare function SetTextToUseTextFileColours(value: boolean): void;

declare function SetTextUseUnderscore(value: boolean): void;

declare function SetTextViewportId(id: number): void;

declare function SetTextWrap(unk1: number, unk2: number): void;

declare function SetThisMachineRunningServerScript(host: boolean): void;

declare function SetThisScriptCanRemoveBlipsCreatedByAnyScript(allow: boolean): void;

declare function SetTimeCycleFarClipDisabled(_set: boolean): void;

declare function SetTimeOfDay(hour: number, minute: number): void;

declare function SetTimeOfNextAppointment(time: number): void;

declare function SetTimeOneDayBack(): void;

declare function SetTimeOneDayForward(): void;

declare function SetTimeScale(scale: number): void;

declare function SetTimecycleModifier(name: string): void;

/**
 * SET_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @param value1 The first value of variable.
 * @param value2 The second value of variable.
 */
declare function SetTimecycleModifierVar(modifierName: string, varName: string, value1: number, value2: number): void;

declare function SetTimerBeepCountdownTime(timerid: number, beeptime: number): void;

declare function SetTotalNumberOfMissions(floatstatval: number): void;

declare function SetTrainAudioRolloff(train: number, rolloff: number): void;

declare function SetTrainCruiseSpeed(train: number, speed: number): void;

declare function SetTrainForcedToSlowDown(train: number, _set: boolean): void;

declare function SetTrainIsStoppedAtStation(train: number): void;

declare function SetTrainSpeed(train: number, speed: number): void;

declare function SetTrainStopsForStations(train: number, _set: boolean): void;

declare function SetUpsidedownCarNotDamaged(car: number, _set: boolean): void;

declare function SetUseHighdof(_set: boolean): void;

declare function SetUseLegIk(player: number, _set: boolean): void;

declare function SetUsePoolGamePhysicsSettings(_set: boolean): void;

declare function SetUsesCollisionOfClosestObjectOfType(x: number, y: number, z: number, radius: number, type_or_model: number, flag: boolean): void;

declare function SetVariableOnSound(sound: number, varname: string, value: number): void;

declare function SetVehAlarm(veh: number, _set: boolean): void;

declare function SetVehAlarmDuration(veh: number, duration: number): void;

declare function SetVehHasStrongAxles(veh: number, _set: boolean): void;

declare function SetVehHazardlights(vehicle: number, on: boolean): void;

declare function SetVehIndicatorlights(veh: number, _set: boolean): void;

declare function SetVehInteriorlight(veh: number, _set: boolean): void;

declare function SetVehicleAlpha(veh: number, alpha: number): void;

declare function SetVehicleAlwaysRender(veh: number): void;

declare function SetVehicleCanBeTargetted(veh: number, _set: boolean): void;

/**
 * SET_VEHICLE_CURRENT_GEAR
 * @param vehicle The vehicle handle.
 * @param gear The gear you want the vehicle to use.
 */
declare function SetVehicleCurrentGear(vehicle: number, gear: number): void;

declare function SetVehicleDeformationMult(veh: number, multiplier: number): void;

declare function SetVehicleDirtLevel(vehicle: number, intensity: number): void;

declare function SetVehicleExplodesOnHighExplosionDamage(veh: number, _set: boolean): void;

/**
 * This native is a setter for [`GET_VEHICLE_HAS_FLAG`](#\_0xD85C9F57).
 * @param vehicle The vehicle to set flag for.
 * @param flagIndex Flag index.
 * @param value `true` to enable the flag, `false` to disable it.
 */
declare function SetVehicleFlag(vehicle: number, flagIndex: number, value: boolean): boolean;

declare function SetVehicleIsConsideredByPlayer(veh: number, _set: boolean): void;

/**
 * SET_VEHICLE_NEXT_GEAR
 * @param vehicle The vehicle handle.
 * @param nextGear The vehicles next gear.
 */
declare function SetVehicleNextGear(vehicle: number, nextGear: number): void;

declare function SetVehicleQuaternion(veh: number, qx: number, qy: number, qz: number, qw: number): void;

declare function SetVehicleRenderScorched(veh: number, _set: boolean): void;

declare function SetVehicleSteerBias(veh: number, val: number): void;

declare function SetViewport(viewportid: number, Unk589: number, Unk590: number, Unk591: number, Unk592: number): void;

declare function SetViewportDestination(viewportid: number, x: number, y: number, z: number, Unk593: number, Unk594: number, Unk595: number): void;

declare function SetViewportMirrored(viewportid: number, _set: boolean): void;

declare function SetViewportPriority(viewportid: number, priority: number): void;

declare function SetViewportShape(cam: number, shape: number): void;

declare function SetVisibilityOfClosestObjectOfType(x: number, y: number, z: number, radius: number, type_or_model: number, _set: boolean): void;

declare function SetVisibilityOfNearbyEntityWithSpecialAttribute(attribute: number, _set: boolean): void;

/**
 * Overrides a floating point value from `visualsettings.dat` temporarily.
 * @param name The name of the value to set, such as `pedLight.color.red`.
 * @param value The value to write.
 */
declare function SetVisualSettingFloat(name: string, value: number): void;

declare function SetVoiceIdFromHeadComponent(ped: number, VoiceId: number, IsMale: boolean): void;

declare function SetWantedMultiplier(multiplier: number): void;

declare function SetWeaponPickupNetworkRegenTime(weaponType: number, timeMS: number): void;

/**
 * SET_WEATHER_CYCLE_ENTRY
 * @param index The index of the entry to set. Must be between 0 and 255
 * @param typeName The name of the weather type for this cycle
 * @param timeMult The relative duration of this cycle, which is multiplied by `msPerCycle` during ['APPLY_WEATHER_CYCLES'](#\_0x3422291C). Must be between 1 and 255
 * @return Returns true if all parameters were valid, otherwise false.
 */
declare function SetWeatherCycleEntry(index: number, typeName: string, timeMult: number): boolean;

/**
 * Sets whether or not the weather should be owned by the network subsystem.
 * To be able to use [\_SET_WEATHER_TYPE_TRANSITION](#\_0x578C752848ECFA0C), this has to be set to false.
 * @param network true to let the network control weather, false to not use network weather behavior.
 */
declare function SetWeatherOwnedByNetwork(network: boolean): void;

declare function SetWebPageLinkActive(htmlviewport: number, linkid: number, active: boolean): void;

declare function SetWebPageScroll(htmlviewport: number, scroll: number): void;

declare function SetWidescreenBorders(_set: boolean): void;

declare function SetWidescreenFormat(wideformatid: number): void;

declare function SetZoneNoCops(name: string, _set: boolean): void;

declare function SetZonePopulationType(zone: string, poptype: number): void;

declare function SetZoneScumminess(zone: string, scumminess: number): void;

declare function Settimera(value: number): void;

declare function Settimerb(value: number): void;

declare function Settimerc(Unk1088: number): void;

declare function ShakePad(Unk838: number, Unk839: number, Unk840: number): void;

declare function ShakePadInCutscene(Unk841: number, Unk842: number, Unk843: number): void;

declare function ShakePlayerpadWhenControllerDisabled(): void;

declare function ShiftLeft(val: number, shifts: number): number;

declare function ShiftRight(val: number, shifts: number): number;

declare function ShowBlipOnAltimeter(blip: number, show: boolean): void;

declare function ShowSigninUi(): void;

declare function ShowUpdateStats(show: boolean): void;

declare function ShutCarDoor(vehicle: number, door: number): void;

declare function ShutdownAndLaunchNetworkGame(episode: number): void;

declare function ShutdownAndLaunchSinglePlayerGame(): void;

/**
 * Shuts down the `loadingScreen` NUI frame, similarly to `SHUTDOWN_LOADING_SCREEN`.
 */
declare function ShutdownLoadingScreenNui(): void;

declare function SimulateUpdateLoadScene(): void;

declare function Sin(value: number): number;

declare function SkipInPlaybackRecordedCar(car: number, time: number): void;

declare function SkipRadioForward(): void;

declare function SkipTimeInPlaybackRecordedCar(CarRec: number, time: number): void;

declare function SkipToEndAndStopPlaybackRecordedCar(car: number): void;

declare function SkipToNextAllowedStation(train: number): void;

declare function SkipToNextScriptedConversationLine(): void;

declare function SlideObject(obj: number, x: number, y: number, z: number, xs: number, ys: number, zs: number, flag: boolean): boolean;

declare function SmashCarWindow(car: number, windownum: number): void;

declare function SmashGlassOnObject(x: number, y: number, z: number, Unk75: number, model: number, Unk76: number): boolean;

declare function SnapshotCam(cam: number, Unk596: number): void;

declare function SoundCarHorn(vehicle: number, duration: number): void;

declare function SpecifyScriptPopulationZoneArea(Unk848: number, Unk849: number, Unk850: number, Unk851: number, Unk852: number, Unk853: number): void;

declare function SpecifyScriptPopulationZoneGroups(Unk854: number, Unk855: number, Unk856: number, Unk857: number, Unk858: number): void;

declare function SpecifyScriptPopulationZoneNumCars(num: number): void;

declare function SpecifyScriptPopulationZoneNumParkedCars(num: number): void;

declare function SpecifyScriptPopulationZoneNumPeds(num: number): void;

declare function SpecifyScriptPopulationZoneNumScenarioPeds(num: number): void;

declare function SpecifyScriptPopulationZonePercentageCops(percentage: number): void;

declare function SpotCheck5(): boolean;

declare function SpotCheck6(): boolean;

declare function SpotCheck7(): boolean;

declare function SpotCheck8(): boolean;

declare function Sqrt(value: number): number;

declare function StartCarFire(vehicle: number): number;

declare function StartCharFire(ped: number): number;

declare function StartCredits(): void;

declare function StartCustomMobilePhoneRinging(RingtoneId: number): void;

declare function StartCutscene(): void;

declare function StartCutsceneNow(name: string): void;

declare function StartEndCreditsMusic(): void;

/**
 * Equivalent of [START_FIND_KVP](#\_0xDD379006), but for another resource than the current one.
 * @param resourceName The resource to try finding the key/values for
 * @param prefix A prefix match
 * @return A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
 */
declare function StartFindExternalKvp(resourceName: string, prefix: string): number;

/**
 * START_FIND_KVP
 * @param prefix A prefix match
 * @return A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
 */
declare function StartFindKvp(prefix: string): number;

declare function StartFiringAmnesty(): void;

declare function StartGpsRaceTrack(trackid: number): void;

declare function StartKillFrenzy(gxtname: string, Unk512: number, Unk513: number, Unk514: number, Unk515: number, Unk516: number, Unk517: number, Unk518: number, Unk519: boolean): void;

declare function StartLoadScene(x: number, y: number, z: number): void;

declare function StartMobilePhoneCall(callfrom: number, callfromvoice: string, callto: number, calltovoice: string, flag0: boolean, flag1: boolean): void;

declare function StartMobilePhoneCalling(): void;

declare function StartMobilePhoneRinging(): void;

declare function StartNewScript(scriptName: string, stacksize: number): number;

declare function StartNewScriptWithArgs(scriptname: string, paramcount: number, stacksize: number): [number, number];

declare function StartNewWidgetCombo(): void;

declare function StartObjectFire(obj: number): number;

declare function StartPedMobileRinging(ped: number, Unk801: number): void;

declare function StartPlaybackRecordedCar(car: number, CarRec: number): void;

declare function StartPlaybackRecordedCarLooped(car: number, Unk69: number): void;

declare function StartPlaybackRecordedCarUsingAi(car: number, CarRec: number): void;

declare function StartPlaybackRecordedCarWithOffset(car: number, CarRec: number, x: number, y: number, z: number): void;

declare function StartPtfx(name: string, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, scale: number): number;

declare function StartPtfxOnObj(name: string, obj: number, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, scale: number): number;

declare function StartPtfxOnObjBone(name: string, obj: number, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, objbone: number, scale: number): number;

declare function StartPtfxOnPed(name: string, ped: number, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, scale: number): number;

declare function StartPtfxOnPedBone(name: string, ped: number, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, pedbone: number, scale: number): number;

declare function StartPtfxOnVeh(name: string, veh: number, x: number, y: number, z: number, yaw: number, pitch: number, roll: number, scale: number): number;

declare function StartScriptConversation(flag0: boolean, flag1: boolean): void;

declare function StartScriptFire(x: number, y: number, z: number, numGenerationsAllowed: number, strength: number): number;

declare function StartStreamingRequestList(name: string): void;

/**
 * STATE_BAG_HAS_KEY
 * @param bagName The name of the bag.
 * @param key The key used to check data existence.
 * @return Returns true if the data associated with the specified key exists; otherwise, returns false.
 */
declare function StateBagHasKey(bagName: string, key: string): boolean;

declare function StopCarBreaking(car: number, stop: boolean): void;

declare function StopCredits(): void;

declare function StopCutscene(): void;

declare function StopEndCreditsMusic(): void;

declare function StopMobilePhoneRinging(): void;

declare function StopMovie(): void;

declare function StopPedDoingFallOffTestsWhenShot(ped: number): void;

declare function StopPedMobileRinging(ped: number): void;

declare function StopPedSpeaking(ped: number, stopspeaking: boolean): void;

declare function StopPedWeaponFiringWhenDropped(ped: number): void;

declare function StopPlaybackRecordedCar(car: number): void;

declare function StopPreviewRingtone(): void;

declare function StopPtfx(ptfx: number): void;

declare function StopSound(sound: number): void;

declare function StopStream(): void;

declare function StopSyncingScriptAnimations(Unk1061: boolean): void;

declare function StopVehicleAlwaysRender(veh: number): void;

declare function StoreCarCharIsInNoSave(ped: number, car?: number): number;

declare function StoreDamageTrackerForNetworkPlayer(playerIndex: number, ukn57: number, Unk895: number): number;

declare function StoreScore(playerIndex: number, value?: number): number;

declare function StoreScriptValuesForNetworkGame(Unk998: number): void;

declare function StoreWantedLevel(playerIndex: number, value?: number): number;

declare function StreamCutscene(): void;

declare function StringDifference(str0: string, str1: string): number;

declare function StringString(str0: string, str1: string): number;

declare function StringToInt(str: string, intval?: number): [boolean, number];

declare function SuppressCarModel(model: number): void;

declare function SuppressFadeInAfterDeathArrest(_set: boolean): void;

declare function SuppressPedModel(model: number): void;

declare function SwapNearestBuildingModel(x: number, y: number, z: number, radius: number, modelfrom: number, modelto: number): void;

declare function SwitchAmbientPlanes(on: boolean): void;

declare function SwitchArrowAboveBlippedPickups(on: boolean): void;

declare function SwitchCarGenerator(handle: number, _type: number): void;

declare function SwitchCarSiren(car: number, siren: boolean): void;

declare function SwitchGarbageTrucks(on: boolean): void;

declare function SwitchMadDrivers(on: boolean): void;

declare function SwitchObjectBrains(brain: number, switchstate: boolean): void;

declare function SwitchOffWaypoint(): void;

declare function SwitchPedPathsOff(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchPedPathsOn(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchPedRoadsBackToOriginal(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchPedToAnimated(ped: number, unknownTrue: boolean): void;

declare function SwitchPedToRagdoll(ped: number, Unk14: number, time: number, flag0: boolean, flag1: boolean, flag2: boolean, flag3: boolean): boolean;

declare function SwitchPedToRagdollWithFall(ped: number, Unk15: number, Unk16: number, Unk17: number, Unk18: number, Unk19: number, Unk20: number, Unk21: number, Unk22: number, Unk23: number, Unk24: number, Unk25: number, Unk26: number, Unk27: number): boolean;

declare function SwitchPoliceHelis(_set: boolean): void;

declare function SwitchRandomBoats(on: boolean): void;

declare function SwitchRandomTrains(on: boolean): void;

declare function SwitchRoadsBackToOriginal(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchRoadsOff(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchRoadsOn(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): void;

declare function SwitchStreaming(on: boolean): void;

declare function SynchAmbientPlanes(Unk520: number, Unk521: number): void;

declare function SynchRecordingWithWater(): boolean;

declare function TakeCarOutOfParkedCarsBudget(car: number, out: boolean): void;

declare function Tan(value: number): number;

declare function TaskAchieveHeading(ped: number, heading: number): void;

declare function TaskAimGunAtChar(ped: number, targetPed: number, duration: number): void;

declare function TaskAimGunAtCoord(ped: number, tX: number, tY: number, tZ: number, duration: number): void;

declare function TaskCarDriveToCoord(ped: number, veh: number, Unk133: number, Unk134: number, Unk135: number, Unk136: number, Unk137: number, Unk138: number, Unk139: number, Unk140: number, Unk141: number): void;

declare function TaskCarDriveToCoordNotAgainstTraffic(ped: number, Unk142: number, Unk143: number, Unk144: number, Unk145: number, Unk146: number, Unk147: number, Unk148: number, Unk149: number, Unk150: number, Unk151: number): void;

declare function TaskCarDriveWander(ped: number, vehicle: number, speed: number, drivingStyle: number): void;

declare function TaskCarMission(ped: number, vehicle: number, targetEntity: number, missionType: number, speed: number, drivingStyle: number, unknown6_10: number, unknown7_5: number): void;

declare function TaskCarMissionCoorsTarget(ped: number, vehicle: number, x: number, y: number, z: number, unknown0_4: number, speed: number, unknown2_1: number, unknown3_5: number, unknown4_10: number): void;

declare function TaskCarMissionCoorsTargetNotAgainstTraffic(ped: number, vehicle: number, x: number, y: number, z: number, unknown0_4: number, speed: number, unknown2_1: number, unknown3_5: number, unknown4_10: number): void;

declare function TaskCarMissionNotAgainstTraffic(ped: number, vehicle: number, targetEntity: number, missionType: number, speed: number, drivingStyle: number, unknown6_10: number, unknown7_5: number): void;

declare function TaskCarMissionPedTarget(ped: number, vehicle: number, target: number, unknown0_4: number, speed: number, unknown2_1: number, unknown3_5: number, unknown4_10: number): void;

declare function TaskCarMissionPedTargetNotAgainstTraffic(ped: number, Unk152: number, Unk153: number, Unk154: number, Unk155: number, Unk156: number, Unk157: number, Unk158: number): void;

declare function TaskCarTempAction(ped: number, vehicle: number, action: number, duration: number): void;

declare function TaskCharArrestChar(ped0: number, ped1: number): void;

declare function TaskCharSlideToCoord(ped: number, Unk159: number, Unk160: number, Unk161: number, Unk162: number, Unk163: number): void;

declare function TaskCharSlideToCoordAndPlayAnim(ped: number, Unk164: number, Unk165: number, Unk166: number, Unk167: number, Unk168: number, Unk169: number, Unk170: number, Unk171: number, Unk172: number, Unk173: number, Unk174: number, Unk175: number, Unk176: number): void;

declare function TaskCharSlideToCoordHdgRate(ped: number, Unk177: number, Unk178: number, Unk179: number, Unk180: number, Unk181: number, Unk182: number): void;

declare function TaskChatWithChar(ped: number, pednext: number, Unk183: boolean, Unk184: boolean): void;

declare function TaskClearLookAt(ped: number): void;

declare function TaskClimb(ped: number, Unk185: boolean): void;

declare function TaskClimbLadder(ped: number, Unk186: number): void;

declare function TaskCombat(ped: number, target: number): void;

declare function TaskCombatHatedTargetsAroundChar(ped: number, radius: number): void;

declare function TaskCombatHatedTargetsAroundCharTimed(ped: number, radius: number, duration: number): void;

declare function TaskCombatHatedTargetsInArea(ped: number, Unk187: number, Unk188: number, Unk189: number, Unk190: number): void;

declare function TaskCombatRoll(ped: number, Unk191: number): void;

declare function TaskCombatTimed(ped: number, target: number, duration: number): void;

declare function TaskCower(ped: number): void;

declare function TaskDead(ped: number): void;

declare function TaskDestroyCar(ped: number, car: number): void;

declare function TaskDie(ped: number): void;

declare function TaskDriveBy(ped: number, pednext: number, Unk192: number, x: number, y: number, z: number, angle: number, Unk193: number, Unk194: boolean, Unk195: number): void;

declare function TaskDrivePointRoute(ped: number, point: number, radius: number): void;

declare function TaskDrivePointRouteAdvanced(ped: number, Unk197: number, Unk198: number, Unk199: number, Unk200: number, Unk201: number): void;

declare function TaskDuck(ped: number, Unk202: number): void;

declare function TaskEnterCarAsDriver(ped: number, vehicle: number, duration: number): void;

declare function TaskEnterCarAsPassenger(ped: number, vehicle: number, duration: number, seatIndex: number): void;

declare function TaskEveryoneLeaveCar(vehicle: number): void;

declare function TaskExtendRoute(ped: number, Unk203: number, Unk204: number): void;

declare function TaskFallAndGetUp(ped: number, Unk205: number, Unk206: number): void;

declare function TaskFleeCharAnyMeans(ped: number, Unk207: number, Unk208: number, Unk209: number, Unk210: number, Unk211: number, Unk212: number, Unk213: number): void;

declare function TaskFlushRoute(): void;

declare function TaskFollowFootsteps(ped: number, Unk214: number): void;

declare function TaskFollowNavMeshAndSlideToCoord(ped: number, x: number, y: number, z: number, Unk215: number, Unk216: number, Unk217: number, angle: number): void;

declare function TaskFollowNavMeshAndSlideToCoordHdgRate(ped: number, x: number, y: number, z: number, Unk218: number, Unk219: number, Unk220: number, angle: number, rate: number): void;

declare function TaskFollowNavMeshToCoord(ped: number, x: number, y: number, z: number, unknown0_2: number, unknown1_minus1: number, unknown2_1: number): void;

declare function TaskFollowNavMeshToCoordNoStop(ped: number, x: number, y: number, z: number, unknown0_2: number, unknown1_minus1: number, unknown2_1: number): void;

declare function TaskGetOffBoat(ped: number, timeout: number): void;

declare function TaskGoStraightToCoord(ped: number, x: number, y: number, z: number, unknown2: number, unknown45000: number): void;

declare function TaskGoStraightToCoordRelativeToCar(ped: number, Unk227: number, Unk228: number, Unk229: number, Unk230: number, Unk231: number, Unk232: number): void;

declare function TaskGoToChar(ped: number, Unk233: number, Unk234: number, Unk235: number): void;

declare function TaskGoToCoordAnyMeans(ped: number, Unk236: number, Unk237: number, Unk238: number, Unk239: number, Unk240: number): void;

declare function TaskGoToCoordWhileAiming(ped: number, Unk241: number, Unk242: number, Unk243: number, Unk244: number, Unk245: number, Unk246: number, Unk247: number, Unk248: number, Unk249: number, Unk250: number, Unk251: number): void;

declare function TaskGoToCoordWhileShooting(ped: number, Unk252: number, Unk253: number, Unk254: number, Unk255: number, Unk256: number, Unk257: number, Unk258: number, Unk259: number): void;

declare function TaskGoToObject(ped: number, Unk260: number, Unk261: number, Unk262: number): void;

declare function TaskGotoCar(ped: number, Unk221: number, Unk222: number, Unk223: number): void;

declare function TaskGotoCharAiming(ped: number, Unk224: number, Unk225: number, Unk226: number): void;

declare function TaskGotoCharOffset(ped: number, target: number, duration: number, offsetRight: number, offsetFront: number): void;

declare function TaskGuardAngledDefensiveArea(ped: number, Unk263: number, Unk264: number, Unk265: number, Unk266: number, Unk267: number, Unk268: number, Unk269: number, Unk270: number, Unk271: number, Unk272: number, Unk273: number, Unk274: number, Unk275: number): void;

declare function TaskGuardAssignedDefensiveArea(ped: number, Unk276: number, Unk277: number, Unk278: number, Unk279: number, Unk280: number, Unk281: number): void;

declare function TaskGuardCurrentPosition(ped: number, unknown0_15: number, unknown1_10: number, unknown2_1: number): void;

declare function TaskGuardSphereDefensiveArea(ped: number, Unk282: number, Unk283: number, Unk284: number, Unk285: number, Unk286: number, Unk287: number, Unk288: number, Unk289: number, Unk290: number, Unk291: number): void;

declare function TaskHandsUp(ped: number, duration: number): void;

declare function TaskHeliMission(ped: number, heli: number, uk0_0: number, uk1_0: number, pX: number, pY: number, pZ: number, uk2_4: number, speed: number, uk3_5: number, uk4_minus1: number, uk5_round_z_plus_1: number, uk6_40: number): void;

declare function TaskJump(ped: number, flag: boolean): void;

declare function TaskLeaveAnyCar(ped: number): void;

declare function TaskLeaveCar(ped: number, vehicle: number): void;

declare function TaskLeaveCarAndFlee(ped: number, Unk292: number, Unk293: number, Unk294: number, Unk295: number): void;

declare function TaskLeaveCarDontCloseDoor(ped: number, vehicle: number): void;

declare function TaskLeaveCarImmediately(ped: number, vehicle: number): void;

declare function TaskLeaveCarInDirection(ped: number, car: number, direction: boolean): void;

declare function TaskLeaveGroup(ped: number): void;

declare function TaskLookAtChar(ped: number, targetPed: number, duration: number, unknown_0: number): void;

declare function TaskLookAtCoord(ped: number, x: number, y: number, z: number, duration: number, unknown_0: number): void;

declare function TaskLookAtObject(ped: number, targetObject: number, duration: number, unknown_0: number): void;

declare function TaskLookAtVehicle(ped: number, targetVehicle: number, duration: number, unknown_0: number): void;

declare function TaskMobileConversation(ped: number, Unk296: number): void;

declare function TaskOpenDriverDoor(ped: number, vehicle: number, unknown0: number): void;

declare function TaskOpenPassengerDoor(ped: number, vehicle: number, seatIndex: number, unknown0: number): void;

declare function TaskPause(ped: number, duration: number): void;

declare function TaskPerformSequence(ped: number, taskSequence: number): void;

declare function TaskPerformSequenceFromProgress(ped: number, Unk297: number, Unk298: number, Unk299: number): void;

declare function TaskPerformSequenceLocally(ped: number, Unk300: number): void;

declare function TaskPickupAndCarryObject(ped: number, Unk301: number, Unk302: number, Unk303: number, Unk304: number, Unk305: number): void;

declare function TaskPlayAnim(ped: number, Unk306: number, Unk307: number, Unk308: number, Unk309: number, Unk310: number, Unk311: number, Unk312: number, Unk313: number): void;

declare function TaskPlayAnimFacial(ped: number, Unk314: number, Unk315: number, Unk316: number, Unk317: number, Unk318: number, Unk319: number): void;

declare function TaskPlayAnimNonInterruptable(ped: number, animname0: string, animname1: string, Unk320: number, Unk321: number, Unk322: number, Unk323: number, Unk324: number, Unk325: number): void;

declare function TaskPlayAnimOnClone(ped: number, Unk326: number, Unk327: number, Unk328: number, Unk329: number, Unk330: number, Unk331: number, Unk332: number, Unk333: number): void;

declare function TaskPlayAnimReadyToBeExecuted(ped: number, Unk334: number, Unk335: number, Unk336: number): void;

declare function TaskPlayAnimSecondary(ped: number, Unk337: number, Unk338: number, Unk339: number, Unk340: number, Unk341: number, Unk342: number, Unk343: number, Unk344: number): void;

declare function TaskPlayAnimSecondaryInCar(ped: number, Unk345: number, Unk346: number, Unk347: number, Unk348: number, Unk349: number, Unk350: number, Unk351: number, Unk352: number): void;

declare function TaskPlayAnimSecondaryNoInterrupt(ped: number, Unk353: number, Unk354: number, Unk355: number, Unk356: number, Unk357: number, Unk358: number, Unk359: number, Unk360: number): void;

declare function TaskPlayAnimSecondaryUpperBody(ped: number, Unk361: number, Unk362: number, Unk363: number, Unk364: number, Unk365: number, Unk366: number, Unk367: number, Unk368: number): void;

declare function TaskPlayAnimUpperBody(ped: number, Unk369: number, Unk370: number, Unk371: number, Unk372: number, Unk373: number, Unk374: number, Unk375: number, Unk376: number): void;

declare function TaskPlayAnimWithAdvancedFlags(ped: number, Unk377: number, Unk378: number, Unk379: number, Unk380: number, Unk381: number, Unk382: number, Unk383: number, Unk384: number, Unk385: number, Unk386: number, Unk387: number): void;

declare function TaskPlayAnimWithFlags(ped: number, animName: string, animSet: string, unknown0_8: number, unknown1_0: number, flags: number): void;

declare function TaskPlayAnimWithFlagsAndStartPhase(ped: number, Unk388: number, Unk389: number, Unk390: number, Unk391: number, Unk392: number, Unk393: number): void;

declare function TaskPutCharDirectlyIntoCover(Unk394: number, Unk395: number, Unk396: number, Unk397: number, Unk398: number): void;

declare function TaskSeekCoverFromPed(ped: number, Unk399: number, Unk400: number): void;

declare function TaskSeekCoverFromPos(ped: number, Unk401: number, Unk402: number, Unk403: number, Unk404: number): void;

declare function TaskSeekCoverToCoords(ped: number, Unk405: number, Unk406: number, Unk407: number, Unk408: number, Unk409: number, Unk410: number, Unk411: number): void;

declare function TaskSeekCoverToCoverPoint(ped: number, Unk412: number, Unk413: number, Unk414: number, Unk415: number, Unk416: number): void;

declare function TaskSeekCoverToObject(ped: number, Unk417: number, Unk418: number, Unk419: number, Unk420: number, Unk421: number): void;

declare function TaskSetCharDecisionMaker(ped: number, dm: number): void;

declare function TaskSetCombatDecisionMaker(ped: number, dm: number): void;

declare function TaskSetIgnoreWeaponRangeFlag(ped: number, ignore: boolean): void;

declare function TaskShakeFist(ped: number): void;

declare function TaskShimmy(ped: number, Unk422: number): void;

declare function TaskShimmyClimbUp(ped: number): boolean;

declare function TaskShimmyInDirection(ped: number, Unk109: number): boolean;

declare function TaskShimmyLetGo(ped: number): boolean;

declare function TaskShootAtChar(shooter: number, victim: number, time: number, shootmode: number): void;

declare function TaskShootAtCoord(ped: number, Unk423: number, Unk424: number, Unk425: number, Unk426: number, Unk427: number): void;

declare function TaskShuffleToNextCarSeat(ped: number, Unk428: number): void;

declare function TaskSitDown(ped: number, Unk429: number, Unk430: number, Unk431: number): void;

declare function TaskSitDownInstantly(ped: number, Unk432: number, Unk433: number, Unk434: number): void;

declare function TaskSitDownOnNearestObject(ped: number, Unk435: number, Unk436: number, Unk437: number, Unk438: number, Unk439: number, Unk440: number, Unk441: number, Unk442: number, Unk443: number): void;

declare function TaskSitDownOnObject(ped: number, Unk444: number, Unk445: number, Unk446: number, Unk447: number, Unk448: number, Unk449: number, Unk450: number, Unk451: number, Unk452: number): void;

declare function TaskSitDownOnSeat(ped: number, Unk453: number, Unk454: number, Unk455: number, Unk456: number, Unk457: number, Unk458: number, Unk459: number): void;

declare function TaskSmartFleeChar(ped: number, fleeFromPed: number, unknown0_100: number, duration: number): void;

declare function TaskSmartFleeCharPreferringPavements(ped: number, fleeFromPed: number, unknown0_100: number, duration: number): void;

declare function TaskSmartFleePoint(ped: number, x: number, y: number, z: number, unknown0_100: number, duration: number): void;

declare function TaskSmartFleePointPreferringPavements(ped: number, x: number, y: number, z: number, radius: number, time_prob: number): void;

declare function TaskStandGuard(ped: number, x: number, y: number, z: number, Unk460: number, Unk461: number, Unk462: boolean, Unk463: number): void;

declare function TaskStandStill(ped: number, duration: number): void;

declare function TaskStartScenarioAtPosition(ped: number, Unk464: number, Unk465: number, Unk466: number, Unk467: number, Unk468: number): void;

declare function TaskStartScenarioInPlace(ped: number, Unk469: number, Unk470: number): void;

declare function TaskSwapWeapon(ped: number, weapon: number): void;

declare function TaskSwimToCoord(ped: number, x: number, y: number, z: number): void;

declare function TaskTired(ped: number, Unk471: number): void;

declare function TaskToggleDuck(ped: number, Unk472: number): void;

declare function TaskTogglePedThreatScanner(ped: number, Unk473: boolean, Unk474: boolean, Unk475: boolean): void;

declare function TaskTurnCharToFaceChar(ped: number, targetPed: number): void;

declare function TaskTurnCharToFaceCoord(ped: number, x: number, y: number, z: number): void;

declare function TaskUseMobilePhone(ped: number, use: boolean): void;

declare function TaskUseMobilePhoneTimed(ped: number, duration: number): void;

declare function TaskUseNearestScenarioToPos(ped: number, Unk476: number, Unk477: number, Unk478: number, Unk479: number): void;

declare function TaskUseNearestScenarioToPosWarp(ped: number, Unk480: number, Unk481: number, Unk482: number, Unk483: number): void;

declare function TaskWanderStandard(ped: number): void;

declare function TaskWarpCharIntoCarAsDriver(ped: number, vehicle: number): void;

declare function TaskWarpCharIntoCarAsPassenger(ped: number, vehicle: number, seatIndex: number): void;

declare function TellNetPlayerToStartPlaying(playerIndex: number, Unk999: boolean): void;

declare function TerminateAllScriptsForNetworkGame(): void;

declare function TerminateAllScriptsWithThisName(name: string): void;

declare function TerminateThisScript(): void;

declare function ThisScriptIsSafeForNetworkGame(): void;

declare function ThisScriptShouldBeSaved(): void;

declare function Timera(): number;

declare function Timerb(): number;

declare function Timerc(): number;

declare function Timestep(): number;

declare function Timestepunwarped(): number;

declare function ToFloat(value: number): number;

declare function ToggleCharDucking(ped: number): number;

declare function ToggleToplevelSprite(toggle: boolean): void;

declare function TrainLeaveStation(train: number): void;

/**
 * The backing function for TriggerEvent.
 */
declare function TriggerEventInternal(eventName: string, eventPayload: string, payloadLength: number): void;

/**
 * The backing function for TriggerLatentServerEvent.
 */
declare function TriggerLatentServerEventInternal(eventName: string, eventPayload: string, payloadLength: number, bps: number): void;

declare function TriggerLoadingMusicOnNextFade(): void;

declare function TriggerMissionCompleteAudio(id: number): void;

declare function TriggerPoliceReport(name: string): void;

declare function TriggerPtfx(name: string, x: number, y: number, z: number, Unk1062: number, Unk1063: number, Unk1064: number, flags: number): boolean;

declare function TriggerPtfxOnObj(name: string, obj: number, x: number, y: number, z: number, Unk1065: number, Unk1066: number, Unk1067: number, flags: number): boolean;

declare function TriggerPtfxOnObjBone(name: string, obj: number, x: number, y: number, z: number, Unk1068: number, Unk1069: number, Unk1070: number, objbone: number, flags: number): boolean;

declare function TriggerPtfxOnPed(name: string, ped: number, x: number, y: number, z: number, Unk1071: number, Unk1072: number, Unk1073: number, flags: number): boolean;

declare function TriggerPtfxOnPedBone(name: string, ped: number, x: number, y: number, z: number, Unk1074: number, Unk1075: number, Unk1076: number, pedbone: number, flags: number): boolean;

declare function TriggerPtfxOnVeh(name: string, veh: number, x: number, y: number, z: number, Unk1077: number, Unk1078: number, Unk1079: number, Unk1080: number): boolean;

/**
 * The backing function for TriggerServerEvent.
 */
declare function TriggerServerEventInternal(eventName: string, eventPayload: string, payloadLength: number): void;

declare function TriggerVehAlarm(car: number): void;

declare function TriggerVigilanteCrime(id: number, x: number, y: number, z: number): void;

declare function TurnCarToFaceCoord(car: number, x: number, y: number): void;

declare function TurnOffRadiohudInLobby(): void;

declare function TurnOffVehicleExtra(veh: number, extra: number, turnoff: boolean): void;

declare function UnattachCam(cam: number): void;

declare function UnfreezeRadioStation(radiostation: string): void;

declare function UninheritCamRoll(cam: number): void;

declare function UnloadTextFont(): void;

declare function UnlockGenericNewsStory(StoryId: number): void;

declare function UnlockLazlowStation(): void;

declare function UnlockMissionNewsStory(id: number): void;

declare function UnlockRagdoll(ped: number, value: boolean): void;

declare function UnmarkAllRoadNodesAsDontWander(): void;

declare function UnobfuscateInt(count: number, val?: number): number;

declare function UnobfuscateIntArray(Unk1000: number, Unk1001: number): void;

declare function UnobfuscateString(str: string): string;

declare function UnpauseGame(): void;

declare function UnpausePlaybackRecordedCar(car: number): void;

declare function UnpauseRadio(): void;

declare function UnpointCam(cam: number): void;

/**
 * Will unregister and cleanup a registered NUI callback handler.
 * Use along side the REGISTER_RAW_NUI_CALLBACK native.
 * @param callbackType The callback type to target
 */
declare function UnregisterRawNuiCallback(callbackType: string): void;

declare function UnregisterScriptWithAudio(): void;

declare function UnsetCharMeleeMovementConstaintBox(ped: number): void;

declare function UpdateLoadScene(): boolean;

declare function UpdateNetworkRelativeScore(Unk1002: number, Unk1003: number, Unk1004: number): void;

declare function UpdateNetworkStatistics(playerIndex: number, ukn0: number, ukn1: number, ukn2: number): void;

declare function UpdatePedPhysicalAttachmentPosition(ped: number, x0: number, y0: number, z0: number, x1: number, y1: number): void;

declare function UpdatePtfxOffsets(ptfx: number, x: number, y: number, z: number, Unk1081: number, Unk1082: number, Unk1083: number): void;

declare function UpdatePtfxTint(ptfx: number, r: number, g: number, b: number, a: number): void;

declare function UseMask(use: boolean): void;

declare function UsePlayerColourInsteadOfTeamColour(Unk1005: boolean): void;

declare function UsePreviousFontSettings(): void;

declare function UsingStandardControls(): boolean;

declare function Vdist(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): number;

declare function Vdist2(x0: number, y0: number, z0: number, x1: number, y1: number, z1: number): number;

declare function VehicleCanBeTargettedByHsMissile(car: number, _set: boolean): void;

declare function VehicleDoesProvideCover(veh: number, cover: boolean): void;

declare function Vmag(x: number, y: number, z: number): number;

declare function Vmag2(x: number, y: number, z: number): number;

declare function Wait(timeMS: number): void;

declare function WantedStarsAreFlashing(): boolean;

declare function WarpCharFromCarToCar(ped: number, vehicle: number, seatIndex: number): void;

declare function WarpCharFromCarToCoord(ped: number, x: number, y: number, z: number): void;

declare function WarpCharIntoCar(ped: number, vehicle: number): void;

declare function WarpCharIntoCarAsPassenger(ped: number, vehicle: number, seatIndex: number): void;

declare function WasCutsceneSkipped(): boolean;

/**
 * Returns whether or not the currently executing event was canceled.
 * @return A boolean.
 */
declare function WasEventCanceled(): boolean;

declare function WasPedKilledByHeadshot(ped: number): boolean;

declare function WasPedSkeletonUpdated(ped: number): boolean;

declare function WashVehicleTextures(vehicle: number, intensity: number): void;

declare function WhatWillPlayerPickup(player: number): number;

declare function WinchCanPickObjectUp(obj: number, can: boolean): void;

declare function m(cam: number, heading: number): void;

