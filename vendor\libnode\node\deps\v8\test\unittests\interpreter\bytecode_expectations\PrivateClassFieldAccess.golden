#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  class A {
    #a;
    #b;
    constructor() {
      this.#a = this.#b;
    }
  }
  
  var test = A;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 28
bytecodes: [
  /*   35 E> */ B(GetNamedProperty), R(closure), U8(0), U8(0),
                B(JumpIfUndefined), U8(10),
                B(Star1),
                B(CallProperty0), R(1), R(this), U8(2),
                B(Mov), R(this), R(0),
  /*   44 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star3),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   59 E> */ B(GetKeyedProperty), R(this), U8(4),
  /*   52 E> */ B(SetKeyedProperty), R(this), R(3), U8(6),
                B(LdaUndefined),
  /*   65 S> */ B(Return),
]
constant pool: [
  SYMBOL_TYPE,
]
handlers: [
]

---
snippet: "
  class B {
    #a;
    #b;
    constructor() {
      this.#a = this.#b;
    }
    force(str) {
      eval(str);
    }
  }
  
  var test = B;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 28
bytecodes: [
  /*   35 E> */ B(GetNamedProperty), R(closure), U8(0), U8(0),
                B(JumpIfUndefined), U8(10),
                B(Star1),
                B(CallProperty0), R(1), R(this), U8(2),
                B(Mov), R(this), R(0),
  /*   44 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star3),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   59 E> */ B(GetKeyedProperty), R(this), U8(4),
  /*   52 E> */ B(SetKeyedProperty), R(this), R(3), U8(6),
                B(LdaUndefined),
  /*   65 S> */ B(Return),
]
constant pool: [
  SYMBOL_TYPE,
]
handlers: [
]

