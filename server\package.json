{"name": "ganghaicity-content-server", "version": "1.0.0", "description": "Local content server for GangHaiCity to replace CFX API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "fs-extra": "^11.1.1", "path": "^0.12.7", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["fivem", "ganghaicity", "content-server", "api"], "author": "GangHaiCity Team", "license": "MIT"}