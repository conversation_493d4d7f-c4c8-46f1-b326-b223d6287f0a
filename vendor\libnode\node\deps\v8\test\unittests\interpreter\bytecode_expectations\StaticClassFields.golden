#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      a;
      ['b'];
      static c;
      static ['d'];
    }
  
    class B {
      a = 1;
      ['b'] = this.a;
      static c = 3;
      static ['d'] = this.c;
    }
    new A;
    new B;
  }
"
frame size: 9
parameter count: 1
bytecode array length: 164
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
  /*   60 S> */ B(LdaConstant), U8(3),
                B(StaCurrentContextSlot), U8(2),
                B(Star7),
  /*   92 S> */ B(LdaConstant), U8(4),
                B(Star8),
                B(LdaConstant), U8(5),
                B(TestEqualStrict), R(8), U8(0),
                B(Mov), R(3), R(5),
                B(JumpIfFalse), U8(7),
                B(CallRuntime), U16(Runtime::kThrowStaticPrototypeError), R(0), U8(0),
                B(Ldar), R(8),
                B(StaCurrentContextSlot), U8(3),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(5),
                B(CreateClosure), U8(6), U8(1), U8(2),
                B(SetNamedProperty), R(3), U8(7), U8(1),
                B(CreateClosure), U8(8), U8(2), U8(2),
                B(Star5),
                B(CallProperty0), R(5), R(3), U8(3),
                B(PopContext), R(2),
                B(Mov), R(3), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(9),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(11), U8(3), U8(2),
                B(Star3),
                B(LdaConstant), U8(10),
                B(Star4),
  /*  131 S> */ B(LdaConstant), U8(3),
                B(StaCurrentContextSlot), U8(2),
                B(Star7),
  /*  176 S> */ B(LdaConstant), U8(4),
                B(Star8),
                B(LdaConstant), U8(5),
                B(TestEqualStrict), R(8), U8(0),
                B(Mov), R(3), R(5),
                B(JumpIfFalse), U8(7),
                B(CallRuntime), U16(Runtime::kThrowStaticPrototypeError), R(0), U8(0),
                B(Ldar), R(8),
                B(StaCurrentContextSlot), U8(3),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(5),
                B(CreateClosure), U8(12), U8(4), U8(2),
                B(SetNamedProperty), R(3), U8(7), U8(5),
                B(CreateClosure), U8(13), U8(5), U8(2),
                B(Star5),
                B(CallProperty0), R(5), R(3), U8(7),
                B(PopContext), R(2),
                B(Mov), R(3), R(1),
  /*  197 S> */ B(Ldar), R(0),
  /*  197 E> */ B(Construct), R(0), R(0), U8(0), U8(9),
  /*  206 S> */ B(Ldar), R(1),
  /*  206 E> */ B(Construct), R(1), R(0), U8(0), U8(11),
                B(LdaUndefined),
  /*  215 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["d"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["prototype"],
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A extends class {} {
      a;
      ['b'];
      static c;
      static ['d'];
    }
  
    class B extends class {} {
      a = 1;
      ['b'] = this.a;
      static c = 3;
      static ['d'] = this.c;
      foo() { return 1; }
      constructor() {
        super();
      }
    }
  
    class C extends B {
      a = 1;
      ['b'] = this.a;
      static c = 3;
      static ['d'] = super.foo();
      constructor() {
        (() => super())();
      }
    }
  
    new A;
    new B;
    new C;
  }
"
frame size: 12
parameter count: 1
bytecode array length: 295
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star8),
                B(LdaConstant), U8(2),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star4),
                B(LdaConstant), U8(1),
                B(Star5),
  /*   77 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
  /*  109 S> */ B(LdaConstant), U8(6),
                B(Star9),
                B(LdaConstant), U8(7),
                B(TestEqualStrict), R(9), U8(0),
                B(Mov), R(10), R(7),
                B(Mov), R(4), R(6),
                B(JumpIfFalse), U8(7),
                B(CallRuntime), U16(Runtime::kThrowStaticPrototypeError), R(0), U8(0),
                B(Ldar), R(9),
                B(StaCurrentContextSlot), U8(3),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(5),
                B(CreateClosure), U8(8), U8(2), U8(2),
                B(SetNamedProperty), R(4), U8(9), U8(1),
                B(CreateClosure), U8(10), U8(3), U8(2),
                B(Star6),
                B(CallProperty0), R(6), R(4), U8(3),
                B(PopContext), R(3),
                B(Mov), R(4), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(11),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(14), U8(4), U8(2),
                B(Star8),
                B(LdaConstant), U8(13),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(15), U8(5), U8(2),
                B(Star4),
                B(LdaConstant), U8(12),
                B(Star5),
  /*  165 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
  /*  210 S> */ B(LdaConstant), U8(6),
                B(Star9),
                B(LdaConstant), U8(7),
                B(TestEqualStrict), R(9), U8(0),
                B(Mov), R(4), R(6),
                B(Mov), R(10), R(7),
                B(JumpIfFalse), U8(7),
                B(CallRuntime), U16(Runtime::kThrowStaticPrototypeError), R(0), U8(0),
                B(Ldar), R(9),
                B(StaCurrentContextSlot), U8(3),
                B(CreateClosure), U8(16), U8(6), U8(2),
                B(Star10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(6),
                B(CreateClosure), U8(17), U8(7), U8(2),
                B(SetNamedProperty), R(4), U8(9), U8(5),
                B(CreateClosure), U8(18), U8(8), U8(2),
                B(Star6),
                B(CallProperty0), R(6), R(4), U8(7),
                B(PopContext), R(3),
                B(Mov), R(4), R(1),
  /*  122 E> */ B(CreateBlockContext), U8(19),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(3),
  /*  313 E> */ B(CreateClosure), U8(21), U8(9), U8(2),
                B(Star4),
                B(LdaConstant), U8(20),
                B(Star5),
  /*  333 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
  /*  378 S> */ B(LdaConstant), U8(6),
                B(Star9),
                B(LdaConstant), U8(7),
                B(TestEqualStrict), R(9), U8(0),
                B(Mov), R(4), R(6),
                B(Mov), R(1), R(7),
                B(JumpIfFalse), U8(7),
                B(CallRuntime), U16(Runtime::kThrowStaticPrototypeError), R(0), U8(0),
                B(Ldar), R(9),
                B(StaCurrentContextSlot), U8(3),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(5),
                B(StaCurrentContextSlot), U8(4),
                B(Ldar), R(4),
                B(StaCurrentContextSlot), U8(5),
                B(CreateClosure), U8(22), U8(10), U8(2),
                B(SetNamedProperty), R(4), U8(9), U8(9),
                B(CreateClosure), U8(23), U8(11), U8(2),
                B(Star6),
                B(CallProperty0), R(6), R(4), U8(11),
                B(PopContext), R(3),
                B(Mov), R(4), R(2),
  /*  456 S> */ B(Ldar), R(0),
  /*  456 E> */ B(Construct), R(0), R(0), U8(0), U8(13),
  /*  465 S> */ B(Ldar), R(1),
  /*  465 E> */ B(Construct), R(1), R(0), U8(0), U8(15),
  /*  474 S> */ B(Ldar), R(2),
  /*  474 E> */ B(Construct), R(2), R(0), U8(0), U8(17),
                B(LdaUndefined),
  /*  483 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["d"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["prototype"],
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

