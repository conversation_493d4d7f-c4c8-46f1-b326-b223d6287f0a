  0x00, 0x61, 0x73, 0x6d,  // wasm magic
  0x01, 0x00, 0x00, 0x00,  // wasm version

  0x01,                    // section kind: Type
  0x08,                    // section length 8
  0x02,                    // types count 2
  0x60,                    //  kind: func
  0x00,                    // param count 0
  0x01, 0x7b,              // return count 1:  v128
  0x60,                    //  kind: func
  0x00,                    // param count 0
  0x00,                    // return count 0

  0x03,                    // section kind: Function
  0x03,                    // section length 3
  0x02,                    // functions count 2
  0x00,                    // 0 $func0 (result v128)
  0x01,                    // 1 $func1

  0x05,                    // section kind: Memory
  0x03,                    // section length 3
  0x01, 0x00,              // memory count 1:  no maximum
  0x00,                    // initial size 0

  0x0a,                    // section kind: Code
  0xed, 0x08,              // section length 1133
  0x02,                    // functions count 2
                           // function #0 $func0
  0x14,                    // body size 20
  0x00,                    // 0 entries in locals list
  0xfd, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // v128.const i32x4 0x00000000 0x00000000 0x00000000 0x00000000
  0x0b,                    // end
                           // function #1 $func1
  0xd5, 0x08,              // body size 1109
  0x00,                    // 0 entries in locals list
  0x41, 0x00,              // i32.const 0
  0xfd, 0x00, 0x04, 0x00,  // v128.load
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x01, 0x03, 0x03,  // v128.load8x8_s offset=3
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x02, 0x01, 0x00,  // v128.load8x8_u align=2
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x03, 0x02, 0x03,  // v128.load16x4_s offset=3 align=4
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x04, 0x03, 0x00,  // v128.load16x4_u
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x05, 0x03, 0x00,  // v128.load32x2_s
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x06, 0x03, 0x00,  // v128.load32x2_u
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x07, 0x00, 0x00,  // v128.load8_splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x08, 0x01, 0x00,  // v128.load16_splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x09, 0x02, 0x00,  // v128.load32_splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x0a, 0x03, 0x00,  // v128.load64_splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x5c, 0x02, 0x00,  // v128.load32_zero
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x5d, 0x03, 0x00,  // v128.load64_zero
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x0b, 0x04, 0x00,  // v128.store
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x54, 0x00, 0x00, 0x00,  // v128.load8_lane 0
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x55, 0x01, 0x00, 0x01,  // v128.load16_lane 1
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x56, 0x02, 0x00, 0x03,  // v128.load32_lane 3
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x57, 0x03, 0x00, 0x00,  // v128.load64_lane 0
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x59, 0x01, 0x00, 0x00,  // v128.store16_lane 0
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x5a, 0x02, 0x00, 0x01,  // v128.store32_lane 1
  0x41, 0x00,              // i32.const 0
  0x10, 0x00,              // call $func0
  0xfd, 0x5b, 0x03, 0x00, 0x00,  // v128.store64_lane 0
  0x10, 0x00,              // call $func0
  0x10, 0x00,              // call $func0
  0xfd, 0x0d, 0x00, 0x01, 0x02, 0x03, 0x00, 0x01, 0x02, 0x03, 0x00, 0x01, 0x02, 0x03, 0x00, 0x01, 0x02, 0x03,  // i8x16.shuffle 0 1 2 3 0 1 2 3 0 1 2 3 0 1 2 3
  0x10, 0x00,              // call $func0
  0xfd, 0x0e,              // i8x16.swizzle
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x0f,              // i8x16.splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x10,              // i16x8.splat
  0x1a,                    // drop
  0x41, 0x00,              // i32.const 0
  0xfd, 0x11,              // i32x4.splat
  0x1a,                    // drop
  0x42, 0x00,              // i64.const 0
  0xfd, 0x12,              // i64x2.splat
  0x1a,                    // drop
  0x43, 0x00, 0x00, 0x00, 0x00,  // f32.const 0.0
  0xfd, 0x13,              // f32x4.splat
  0x1a,                    // drop
  0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // f64.const 0.0
  0xfd, 0x14,              // f64x2.splat
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x15, 0x00,        // i8x16.extract_lane_s 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x16, 0x00,        // i8x16.extract_lane_u 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x18, 0x00,        // i16x8.extract_lane_s 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x19, 0x00,        // i16x8.extract_lane_u 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x1b, 0x00,        // i32x4.extract_lane 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x1d, 0x00,        // i64x2.extract_lane 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x1f, 0x00,        // f32x4.extract_lane 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x21, 0x00,        // f64x2.extract_lane 0
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0x41, 0x00,              // i32.const 0
  0xfd, 0x17, 0x00,        // i8x16.replace_lane 0
  0x41, 0x00,              // i32.const 0
  0xfd, 0x1a, 0x00,        // i16x8.replace_lane 0
  0x41, 0x00,              // i32.const 0
  0xfd, 0x1c, 0x00,        // i32x4.replace_lane 0
  0x42, 0x00,              // i64.const 0
  0xfd, 0x1e, 0x00,        // i64x2.replace_lane 0
  0x43, 0x00, 0x00, 0x00, 0x00,  // f32.const 0.0
  0xfd, 0x20, 0x00,        // f32x4.replace_lane 0
  0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // f64.const 0.0
  0xfd, 0x22, 0x00,        // f64x2.replace_lane 0
  0x10, 0x00,              // call $func0
  0xfd, 0x23,              // i8x16.eq
  0x10, 0x00,              // call $func0
  0xfd, 0x24,              // i8x16.ne
  0x10, 0x00,              // call $func0
  0xfd, 0x25,              // i8x16.lt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x26,              // i8x16.lt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x27,              // i8x16.gt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x28,              // i8x16.gt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x29,              // i8x16.le_s
  0x10, 0x00,              // call $func0
  0xfd, 0x2a,              // i8x16.le_u
  0x10, 0x00,              // call $func0
  0xfd, 0x2b,              // i8x16.ge_s
  0x10, 0x00,              // call $func0
  0xfd, 0x2c,              // i8x16.ge_u
  0x10, 0x00,              // call $func0
  0xfd, 0x2d,              // i16x8.eq
  0x10, 0x00,              // call $func0
  0xfd, 0x2e,              // i16x8.ne
  0x10, 0x00,              // call $func0
  0xfd, 0x2f,              // i16x8.lt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x30,              // i16x8.lt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x31,              // i16x8.gt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x32,              // i16x8.gt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x33,              // i16x8.le_s
  0x10, 0x00,              // call $func0
  0xfd, 0x34,              // i16x8.le_u
  0x10, 0x00,              // call $func0
  0xfd, 0x35,              // i16x8.ge_s
  0x10, 0x00,              // call $func0
  0xfd, 0x36,              // i16x8.ge_u
  0x10, 0x00,              // call $func0
  0xfd, 0x37,              // i32x4.eq
  0x10, 0x00,              // call $func0
  0xfd, 0x38,              // i32x4.ne
  0x10, 0x00,              // call $func0
  0xfd, 0x39,              // i32x4.lt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x3a,              // i32x4.lt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x3b,              // i32x4.gt_s
  0x10, 0x00,              // call $func0
  0xfd, 0x3c,              // i32x4.gt_u
  0x10, 0x00,              // call $func0
  0xfd, 0x3d,              // i32x4.le_s
  0x10, 0x00,              // call $func0
  0xfd, 0x3e,              // i32x4.le_u
  0x10, 0x00,              // call $func0
  0xfd, 0x3f,              // i32x4.ge_s
  0x10, 0x00,              // call $func0
  0xfd, 0x40,              // i32x4.ge_u
  0x10, 0x00,              // call $func0
  0xfd, 0xd6, 0x01,        // i64x2.eq
  0x10, 0x00,              // call $func0
  0xfd, 0xd7, 0x01,        // i64x2.ne
  0x10, 0x00,              // call $func0
  0xfd, 0xd8, 0x01,        // i64x2.lt_s
  0x10, 0x00,              // call $func0
  0xfd, 0xd9, 0x01,        // i64x2.gt_s
  0x10, 0x00,              // call $func0
  0xfd, 0xda, 0x01,        // i64x2.le_s
  0x10, 0x00,              // call $func0
  0xfd, 0xdb, 0x01,        // i64x2.ge_s
  0x10, 0x00,              // call $func0
  0xfd, 0x41,              // f32x4.eq
  0x10, 0x00,              // call $func0
  0xfd, 0x42,              // f32x4.ne
  0x10, 0x00,              // call $func0
  0xfd, 0x43,              // f32x4.lt
  0x10, 0x00,              // call $func0
  0xfd, 0x44,              // f32x4.gt
  0x10, 0x00,              // call $func0
  0xfd, 0x45,              // f32x4.le
  0x10, 0x00,              // call $func0
  0xfd, 0x46,              // f32x4.ge
  0x10, 0x00,              // call $func0
  0xfd, 0x47,              // f64x2.eq
  0x10, 0x00,              // call $func0
  0xfd, 0x48,              // f64x2.ne
  0x10, 0x00,              // call $func0
  0xfd, 0x49,              // f64x2.lt
  0x10, 0x00,              // call $func0
  0xfd, 0x4a,              // f64x2.gt
  0x10, 0x00,              // call $func0
  0xfd, 0x4b,              // f64x2.le
  0x10, 0x00,              // call $func0
  0xfd, 0x4c,              // f64x2.ge
  0xfd, 0x4d,              // v128.not
  0x10, 0x00,              // call $func0
  0xfd, 0x4e,              // v128.and
  0x10, 0x00,              // call $func0
  0xfd, 0x4f,              // v128.andnot
  0x10, 0x00,              // call $func0
  0xfd, 0x50,              // v128.or
  0x10, 0x00,              // call $func0
  0xfd, 0x51,              // v128.xor
  0x10, 0x00,              // call $func0
  0x10, 0x00,              // call $func0
  0xfd, 0x52,              // v128.bitselect
  0xfd, 0x53,              // v128.any_true
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x5e,              // f32x4.demote_f64x2_zero
  0xfd, 0x5f,              // f64x2.promote_low_f32x4
  0xfd, 0x60,              // i8x16.abs
  0xfd, 0x61,              // i8x16.neg
  0xfd, 0x62,              // i8x16.popcnt
  0xfd, 0x63,              // i8x16.all_true
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x64,              // i8x16.bitmask
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0x10, 0x00,              // call $func0
  0xfd, 0x65,              // i8x16.narrow_i16x8_s
  0x10, 0x00,              // call $func0
  0xfd, 0x66,              // i8x16.narrow_i16x8_u
  0x41, 0x00,              // i32.const 0
  0xfd, 0x6b,              // i8x16.shl
  0x41, 0x00,              // i32.const 0
  0xfd, 0x6c,              // i8x16.shr_s
  0x41, 0x00,              // i32.const 0
  0xfd, 0x6d,              // i8x16.shr_u
  0x10, 0x00,              // call $func0
  0xfd, 0x6e,              // i8x16.add
  0x10, 0x00,              // call $func0
  0xfd, 0x6f,              // i8x16.add_sat_s
  0x10, 0x00,              // call $func0
  0xfd, 0x70,              // i8x16.add_sat_u
  0x10, 0x00,              // call $func0
  0xfd, 0x71,              // i8x16.sub
  0x10, 0x00,              // call $func0
  0xfd, 0x72,              // i8x16.sub_sat_s
  0x10, 0x00,              // call $func0
  0xfd, 0x73,              // i8x16.sub_sat_u
  0x10, 0x00,              // call $func0
  0xfd, 0x76,              // i8x16.min_s
  0x10, 0x00,              // call $func0
  0xfd, 0x77,              // i8x16.min_u
  0x10, 0x00,              // call $func0
  0xfd, 0x78,              // i8x16.max_s
  0x10, 0x00,              // call $func0
  0xfd, 0x79,              // i8x16.max_u
  0x10, 0x00,              // call $func0
  0xfd, 0x7b,              // i8x16.avgr_u
  0xfd, 0x80, 0x01,        // i16x8.abs
  0xfd, 0x81, 0x01,        // i16x8.neg
  0x10, 0x00,              // call $func0
  0xfd, 0x82, 0x01,        // i16x8.q15mulr_sat_s
  0xfd, 0x83, 0x01,        // i16x8.all_true
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0x84, 0x01,        // i16x8.bitmask
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0x10, 0x00,              // call $func0
  0xfd, 0x85, 0x01,        // i16x8.narrow_i32x4_s
  0x10, 0x00,              // call $func0
  0xfd, 0x86, 0x01,        // i16x8.narrow_i32x4_u
  0xfd, 0x87, 0x01,        // i16x8.extend_low_i8x16_s
  0xfd, 0x88, 0x01,        // i16x8.extend_high_i8x16_s
  0xfd, 0x89, 0x01,        // i16x8.extend_low_i8x16_u
  0xfd, 0x8a, 0x01,        // i16x8.extend_high_i8x16_u
  0x41, 0x00,              // i32.const 0
  0xfd, 0x8b, 0x01,        // i16x8.shl
  0x41, 0x00,              // i32.const 0
  0xfd, 0x8c, 0x01,        // i16x8.shr_s
  0x41, 0x00,              // i32.const 0
  0xfd, 0x8d, 0x01,        // i16x8.shr_u
  0x10, 0x00,              // call $func0
  0xfd, 0x8e, 0x01,        // i16x8.add
  0x10, 0x00,              // call $func0
  0xfd, 0x8f, 0x01,        // i16x8.add_sat_s
  0x10, 0x00,              // call $func0
  0xfd, 0x90, 0x01,        // i16x8.add_sat_u
  0x10, 0x00,              // call $func0
  0xfd, 0x91, 0x01,        // i16x8.sub
  0x10, 0x00,              // call $func0
  0xfd, 0x92, 0x01,        // i16x8.sub_sat_s
  0x10, 0x00,              // call $func0
  0xfd, 0x93, 0x01,        // i16x8.sub_sat_u
  0x10, 0x00,              // call $func0
  0xfd, 0x95, 0x01,        // i16x8.mul
  0x10, 0x00,              // call $func0
  0xfd, 0x96, 0x01,        // i16x8.min_s
  0x10, 0x00,              // call $func0
  0xfd, 0x97, 0x01,        // i16x8.min_u
  0x10, 0x00,              // call $func0
  0xfd, 0x98, 0x01,        // i16x8.max_s
  0x10, 0x00,              // call $func0
  0xfd, 0x99, 0x01,        // i16x8.max_u
  0x10, 0x00,              // call $func0
  0xfd, 0x9b, 0x01,        // i16x8.avgr_u
  0xfd, 0xa0, 0x01,        // i32x4.abs
  0xfd, 0xa1, 0x01,        // i32x4.neg
  0xfd, 0xa3, 0x01,        // i32x4.all_true
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0xa4, 0x01,        // i32x4.bitmask
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0xa7, 0x01,        // i32x4.extend_low_i16x8_s
  0xfd, 0xa8, 0x01,        // i32x4.extend_high_i16x8_s
  0xfd, 0xa9, 0x01,        // i32x4.extend_low_i16x8_u
  0xfd, 0xaa, 0x01,        // i32x4.extend_high_i16x8_u
  0x41, 0x00,              // i32.const 0
  0xfd, 0xab, 0x01,        // i32x4.shl
  0x41, 0x00,              // i32.const 0
  0xfd, 0xac, 0x01,        // i32x4.shr_s
  0x41, 0x00,              // i32.const 0
  0xfd, 0xad, 0x01,        // i32x4.shr_u
  0x10, 0x00,              // call $func0
  0xfd, 0xae, 0x01,        // i32x4.add
  0x10, 0x00,              // call $func0
  0xfd, 0xb1, 0x01,        // i32x4.sub
  0x10, 0x00,              // call $func0
  0xfd, 0xb5, 0x01,        // i32x4.mul
  0x10, 0x00,              // call $func0
  0xfd, 0xb6, 0x01,        // i32x4.min_s
  0x10, 0x00,              // call $func0
  0xfd, 0xb7, 0x01,        // i32x4.min_u
  0x10, 0x00,              // call $func0
  0xfd, 0xb8, 0x01,        // i32x4.max_s
  0x10, 0x00,              // call $func0
  0xfd, 0xb9, 0x01,        // i32x4.max_u
  0x10, 0x00,              // call $func0
  0xfd, 0xba, 0x01,        // i32x4.dot_i16x8_s
  0xfd, 0xc0, 0x01,        // i64x2.abs
  0xfd, 0xc1, 0x01,        // i64x2.neg
  0xfd, 0xc3, 0x01,        // i64x2.all_true
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0xc4, 0x01,        // i64x2.bitmask
  0x1a,                    // drop
  0x10, 0x00,              // call $func0
  0xfd, 0xc7, 0x01,        // i64x2.extend_low_i32x4_s
  0xfd, 0xc8, 0x01,        // i64x2.extend_high_i32x4_s
  0xfd, 0xc9, 0x01,        // i64x2.extend_low_i32x4_u
  0xfd, 0xca, 0x01,        // i64x2.extend_high_i32x4_u
  0x41, 0x00,              // i32.const 0
  0xfd, 0xcb, 0x01,        // i64x2.shl
  0x41, 0x00,              // i32.const 0
  0xfd, 0xcc, 0x01,        // i64x2.shr_s
  0x41, 0x00,              // i32.const 0
  0xfd, 0xcd, 0x01,        // i64x2.shr_u
  0x10, 0x00,              // call $func0
  0xfd, 0xce, 0x01,        // i64x2.add
  0x10, 0x00,              // call $func0
  0xfd, 0xd1, 0x01,        // i64x2.sub
  0x10, 0x00,              // call $func0
  0xfd, 0xd5, 0x01,        // i64x2.mul
  0xfd, 0x67,              // f32x4.ceil
  0xfd, 0x68,              // f32x4.floor
  0xfd, 0x69,              // f32x4.trunc
  0xfd, 0x6a,              // f32x4.nearest
  0xfd, 0xe0, 0x01,        // f32x4.abs
  0xfd, 0xe1, 0x01,        // f32x4.neg
  0xfd, 0xe3, 0x01,        // f32x4.sqrt
  0x10, 0x00,              // call $func0
  0xfd, 0xe4, 0x01,        // f32x4.add
  0x10, 0x00,              // call $func0
  0xfd, 0xe5, 0x01,        // f32x4.sub
  0x10, 0x00,              // call $func0
  0xfd, 0xe6, 0x01,        // f32x4.mul
  0x10, 0x00,              // call $func0
  0xfd, 0xe7, 0x01,        // f32x4.div
  0x10, 0x00,              // call $func0
  0xfd, 0xe8, 0x01,        // f32x4.min
  0x10, 0x00,              // call $func0
  0xfd, 0xe9, 0x01,        // f32x4.max
  0x10, 0x00,              // call $func0
  0xfd, 0xea, 0x01,        // f32x4.pmin
  0x10, 0x00,              // call $func0
  0xfd, 0xeb, 0x01,        // f32x4.pmax
  0xfd, 0x74,              // f64x2.ceil
  0xfd, 0x75,              // f64x2.floor
  0xfd, 0x7a,              // f64x2.trunc
  0xfd, 0x94, 0x01,        // f64x2.nearest
  0xfd, 0xec, 0x01,        // f64x2.abs
  0xfd, 0xed, 0x01,        // f64x2.neg
  0xfd, 0xef, 0x01,        // f64x2.sqrt
  0x10, 0x00,              // call $func0
  0xfd, 0xf0, 0x01,        // f64x2.add
  0x10, 0x00,              // call $func0
  0xfd, 0xf1, 0x01,        // f64x2.sub
  0x10, 0x00,              // call $func0
  0xfd, 0xf2, 0x01,        // f64x2.mul
  0x10, 0x00,              // call $func0
  0xfd, 0xf3, 0x01,        // f64x2.div
  0x10, 0x00,              // call $func0
  0xfd, 0xf4, 0x01,        // f64x2.min
  0x10, 0x00,              // call $func0
  0xfd, 0xf5, 0x01,        // f64x2.max
  0x10, 0x00,              // call $func0
  0xfd, 0xf6, 0x01,        // f64x2.pmin
  0x10, 0x00,              // call $func0
  0xfd, 0xf7, 0x01,        // f64x2.pmax
  0xfd, 0x7c,              // i16x8.extadd_pairwise_i8x16_s
  0xfd, 0x7d,              // i16x8.extadd_pairwise_i8x16_u
  0xfd, 0x7e,              // i32x4.extadd_pairwise_i16x8_s
  0xfd, 0x7f,              // i32x4.extadd_pairwise_i16x8_u
  0x10, 0x00,              // call $func0
  0xfd, 0x9c, 0x01,        // i16x8.extmul_low_i8x16_s
  0x10, 0x00,              // call $func0
  0xfd, 0x9d, 0x01,        // i16x8.extmul_high_i8x16_s
  0x10, 0x00,              // call $func0
  0xfd, 0x9e, 0x01,        // i16x8.extmul_low_i8x16_u
  0x10, 0x00,              // call $func0
  0xfd, 0x9f, 0x01,        // i16x8.extmul_high_i8x16_u
  0x10, 0x00,              // call $func0
  0xfd, 0xbc, 0x01,        // i32x4.extmul_low_i16x8_s
  0x10, 0x00,              // call $func0
  0xfd, 0xbd, 0x01,        // i32x4.extmul_high_i16x8_s
  0x10, 0x00,              // call $func0
  0xfd, 0xbe, 0x01,        // i32x4.extmul_low_i16x8_u
  0x10, 0x00,              // call $func0
  0xfd, 0xbf, 0x01,        // i32x4.extmul_high_i16x8_u
  0x10, 0x00,              // call $func0
  0xfd, 0xdc, 0x01,        // i64x2.extmul_low_i32x4_s
  0x10, 0x00,              // call $func0
  0xfd, 0xdd, 0x01,        // i64x2.extmul_high_i32x4_s
  0x10, 0x00,              // call $func0
  0xfd, 0xde, 0x01,        // i64x2.extmul_low_i32x4_u
  0x10, 0x00,              // call $func0
  0xfd, 0xdf, 0x01,        // i64x2.extmul_high_i32x4_u
  0xfd, 0xf8, 0x01,        // i32x4.trunc_sat_f32x4_s
  0xfd, 0xf9, 0x01,        // i32x4.trunc_sat_f32x4_u
  0xfd, 0xfa, 0x01,        // f32x4.convert_i32x4_s
  0xfd, 0xfb, 0x01,        // f32x4.convert_i32x4_u
  0xfd, 0xfc, 0x01,        // i32x4.trunc_sat_f64x2_s_zero
  0xfd, 0xfd, 0x01,        // i32x4.trunc_sat_f64x2_u_zero
  0xfd, 0xfe, 0x01,        // f64x2.convert_low_i32x4_s
  0xfd, 0xff, 0x01,        // f64x2.convert_low_i32x4_u
  0x1a,                    // drop
  0x0b,                    // end
