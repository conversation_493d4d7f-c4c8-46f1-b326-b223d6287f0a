---
ns: CFX
apiset: server
---
## SET_PED_INTO_VEHICLE

```c
void SET_PED_INTO_VEHICLE(Ped ped, Vehicle vehicle, int seatIndex);
```

SET_PED_INTO_VEHICLE

**This is the server-side RPC native equivalent of the client native [SET\_PED\_INTO\_VEHICLE](?_0xF75B0D629E1C063D).**

## Parameters
* **ped**: 
* **vehicle**: 
* **seatIndex**: See eSeatPosition declared in [`IS_VEHICLE_SEAT_FREE`](#\_0x22AC59A870E6A669). -2 for the first available seat.

