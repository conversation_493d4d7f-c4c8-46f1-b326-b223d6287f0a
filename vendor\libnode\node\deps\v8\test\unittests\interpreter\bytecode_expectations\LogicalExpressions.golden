#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var x = 0; return x || 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(JumpIfToBooleanTrue), U8(4),
                B(LdaSmi), I8(3),
  /*   59 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return (x == 1) || 3;
"
frame size: 1
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(1),
  /*   55 E> */ B(TestEqual), R(0), U8(0),
                B(JumpIfTrue), U8(4),
                B(LdaSmi), I8(3),
  /*   66 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return x && 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(JumpIfToBooleanFalse), U8(4),
                B(LdaSmi), I8(3),
  /*   59 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return (x == 0) && 3;
"
frame size: 1
parameter count: 1
bytecode array length: 11
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(LdaZero),
  /*   55 E> */ B(TestEqual), R(0), U8(0),
                B(JumpIfFalse), U8(4),
                B(LdaSmi), I8(3),
  /*   66 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return x || (1, 2, 3);
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(JumpIfToBooleanTrue), U8(4),
  /*   64 S> */ B(LdaSmi), I8(3),
  /*   67 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 2, b = 3, c = 4; return a || (a, b, a, b, c = 5, 3);
"
frame size: 3
parameter count: 1
bytecode array length: 19
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(2),
                B(Star0),
  /*   49 S> */ B(LdaSmi), I8(3),
                B(Star1),
  /*   56 S> */ B(LdaSmi), I8(4),
                B(Star2),
  /*   59 S> */ B(Ldar), R(0),
                B(JumpIfToBooleanTrue), U8(7),
  /*   86 S> */ B(LdaSmi), I8(5),
                B(Star2),
  /*   91 S> */ B(LdaSmi), I8(3),
  /*   94 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; var a = 2, b = 3; return x || (
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 3);
"
frame size: 3
parameter count: 1
bytecode array length: 208
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   60 S> */ B(LdaSmi), I8(3),
                B(Star2),
  /*   63 S> */ B(Ldar), R(0),
                B(JumpIfToBooleanTrue), U8(196),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   88 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*   98 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  105 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  115 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  122 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  132 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  139 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  149 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  156 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  166 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  173 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  183 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  190 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  200 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  207 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  217 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  224 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  234 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  241 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  251 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  258 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  268 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  275 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  285 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  292 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  302 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  309 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  319 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  326 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  336 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  343 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  353 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  360 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  370 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  377 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  387 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  394 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  404 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  411 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  421 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  428 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  438 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  445 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  455 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  462 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  472 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  479 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  489 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  496 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  506 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  513 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  523 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  530 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  540 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  547 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  557 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  564 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  574 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  581 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  591 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  598 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  608 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  615 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  620 S> */ B(LdaSmi), I8(3),
  /*  623 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; var a = 2, b = 3; return x && (
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 3);
"
frame size: 3
parameter count: 1
bytecode array length: 207
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   60 S> */ B(LdaSmi), I8(3),
                B(Star2),
  /*   63 S> */ B(Ldar), R(0),
                B(JumpIfToBooleanFalse), U8(196),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   88 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*   98 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  105 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  115 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  122 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  132 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  139 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  149 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  156 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  166 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  173 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  183 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  190 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  200 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  207 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  217 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  224 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  234 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  241 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  251 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  258 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  268 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  275 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  285 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  292 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  302 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  309 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  319 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  326 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  336 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  343 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  353 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  360 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  370 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  377 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  387 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  394 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  404 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  411 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  421 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  428 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  438 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  445 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  455 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  462 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  472 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  479 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  489 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  496 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  506 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  513 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  523 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  530 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  540 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  547 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  557 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  564 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  574 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  581 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  591 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  598 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  608 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  615 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  620 S> */ B(LdaSmi), I8(3),
  /*  623 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; var a = 2, b = 3; return (x > 3) || (
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 3);
"
frame size: 3
parameter count: 1
bytecode array length: 211
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   60 S> */ B(LdaSmi), I8(3),
                B(Star2),
  /*   63 S> */ B(LdaSmi), I8(3),
  /*   73 E> */ B(TestGreaterThan), R(0), U8(0),
                B(JumpIfTrue), U8(196),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   94 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  104 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  111 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  121 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  128 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  138 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  145 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  155 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  162 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  172 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  179 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  189 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  196 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  206 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  213 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  223 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  230 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  240 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  247 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  257 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  264 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  274 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  281 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  291 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  298 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  308 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  315 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  325 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  332 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  342 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  349 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  359 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  366 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  376 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  383 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  393 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  400 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  410 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  417 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  427 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  434 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  444 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  451 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  461 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  468 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  478 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  485 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  495 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  502 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  512 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  519 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  529 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  536 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  546 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  553 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  563 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  570 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  580 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  587 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  597 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  604 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  614 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  621 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  626 S> */ B(LdaSmi), I8(3),
  /*  629 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; var a = 2, b = 3; return (x < 5) && (
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 
    a = 1, b = 2, 3);
"
frame size: 3
parameter count: 1
bytecode array length: 210
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   60 S> */ B(LdaSmi), I8(3),
                B(Star2),
  /*   63 S> */ B(LdaSmi), I8(5),
  /*   73 E> */ B(TestLessThan), R(0), U8(0),
                B(JumpIfFalse), U8(196),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   94 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  104 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  111 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  121 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  128 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  138 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  145 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  155 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  162 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  172 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  179 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  189 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  196 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  206 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  213 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  223 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  230 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  240 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  247 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  257 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  264 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  274 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  281 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  291 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  298 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  308 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  315 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  325 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  332 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  342 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  349 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  359 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  366 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  376 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  383 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  393 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  400 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  410 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  417 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  427 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  434 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  444 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  451 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  461 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  468 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  478 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  485 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  495 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  502 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  512 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  519 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  529 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  536 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  546 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  553 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  563 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  570 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  580 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  587 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  597 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  604 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  614 S> */ B(LdaSmi), I8(1),
                B(Star1),
  /*  621 S> */ B(LdaSmi), I8(2),
                B(Star2),
  /*  626 S> */ B(LdaSmi), I8(3),
  /*  629 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return 0 && 3;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaZero),
  /*   48 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return 1 || 3;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(1),
  /*   48 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return x && 3 || 0, 1;
"
frame size: 1
parameter count: 1
bytecode array length: 13
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   45 S> */ B(JumpIfToBooleanFalse), U8(4),
                B(LdaSmi), I8(3),
                B(JumpIfToBooleanTrue), U8(3),
                B(LdaZero),
  /*   65 S> */ B(LdaSmi), I8(1),
  /*   67 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

