Tests for ES6 class constructor return values

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


Base class
PASS (new BaseNoReturn) instanceof BaseNoReturn is true
PASS (new BaseReturnImplicit) instanceof BaseReturnImplicit is true
PASS (new BaseReturnImplicit) !== undefined is true
PASS (new BaseReturnUndefined) instanceof BaseReturnUndefined is true
PASS (new BaseReturnUndefined) !== undefined is true
PASS (new BaseReturnThis) instanceof BaseReturnThis is true
PASS (new BaseReturnObject) instanceof BaseReturnObject is false
PASS typeof (new BaseReturnObject) === "object" is true
PASS (new BaseReturnObject2) instanceof BaseReturnObject is false
PASS (new BaseReturnObject2) === globalVariable is true
PASS (new BaseReturnString) instanceof BaseReturnString is true
PASS typeof (new BaseReturnString) !== "string" is true
PASS (new BaseReturnNumber) instanceof BaseReturnNumber is true
PASS typeof (new BaseReturnNumber) !== "number" is true
PASS (new BaseR<PERSON>urnNull) instanceof BaseReturnNull is true
PASS (new BaseReturnNull) !== null is true
PASS (new BaseReturnSymbol) instanceof BaseReturnSymbol is true
PASS (new BaseReturnSymbol) !== globalSymbol is true
PASS (new BaseThrow) threw exception Thrown Exception String.

Function constructor (non-class)
PASS (new FunctionNoReturn) instanceof FunctionNoReturn is true
PASS (new FunctionReturnImplicit) instanceof FunctionReturnImplicit is true
PASS (new FunctionReturnImplicit) !== undefined is true
PASS (new FunctionReturnUndefined) instanceof FunctionReturnUndefined is true
PASS (new FunctionReturnUndefined) !== undefined is true
PASS (new FunctionReturnThis) instanceof FunctionReturnThis is true
PASS (new FunctionReturnObject) instanceof FunctionReturnObject is false
PASS typeof (new FunctionReturnObject) === "object" is true
PASS (new FunctionReturnObject2) instanceof FunctionReturnObject is false
PASS (new FunctionReturnObject2) === globalVariable is true
PASS (new FunctionReturnString) instanceof FunctionReturnString is true
PASS typeof (new FunctionReturnString) !== "string" is true
PASS (new FunctionReturnNumber) instanceof FunctionReturnNumber is true
PASS typeof (new FunctionReturnNumber) !== "number" is true
PASS (new FunctionReturnNull) instanceof FunctionReturnNull is true
PASS (new FunctionReturnNull) !== null is true
PASS (new FunctionReturnSymbol) instanceof FunctionReturnSymbol is true
PASS (new FunctionReturnSymbol) !== globalSymbol is true
PASS (new FunctionThrow) threw exception Thrown Exception String.

Derived class calling super()
PASS (new DerivedNoReturn) instanceof DerivedNoReturn is true
PASS (new DerivedReturnImplicit) instanceof DerivedReturnImplicit is true
PASS (new DerivedReturnImplicit) !== undefined is true
PASS (new DerivedReturnUndefined) instanceof DerivedReturnUndefined is true
PASS (new DerivedReturnUndefined) !== undefined is true
PASS (new DerivedReturnThis) instanceof DerivedReturnThis is true
PASS (new DerivedReturnObject) instanceof DerivedReturnObject is false
PASS typeof (new DerivedReturnObject) === "object" is true
PASS (new DerivedReturnObject2) instanceof DerivedReturnObject2 is false
PASS (new DerivedReturnObject2) === globalVariable is true
PASS (new DerivedReturnString) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedReturnNumber) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedReturnNull) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedReturnSymbol) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedThrow) threw exception Thrown Exception String.

Derived class not calling super()
PASS (new DerivedNoSuperNoReturn) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS (new DerivedNoSuperReturnImplicit) threw exception ReferenceError: DerivedNoSuperReturnImplicit is not defined.
PASS (new DerivedNoSuperReturnUndefined) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS (new DerivedNoSuperReturnThis) threw exception ReferenceError: Must call super constructor in derived class before accessing 'this' or returning from derived constructor.
PASS (new DerivedNoSuperReturnObject) did not throw exception.
PASS (new DerivedNoSuperReturnObject2) did not throw exception.
PASS (new DerivedNoSuperReturnString) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedNoSuperReturnNumber) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedNoSuperReturnNull) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedNoSuperReturnSymbol) threw exception TypeError: Derived constructors may only return object or undefined.
PASS (new DerivedNoSuperThrow) threw exception Thrown Exception String.

Derived class with default constructor and base class returning different values
PASS (new DerivedDefaultConstructorWithBaseNoReturn) instanceof DerivedDefaultConstructorWithBaseNoReturn is true
PASS (new DerivedDefaultConstructorWithBaseReturnImplicit) instanceof DerivedDefaultConstructorWithBaseReturnImplicit is true
PASS (new DerivedDefaultConstructorWithBaseReturnUndefined) instanceof DerivedDefaultConstructorWithBaseReturnUndefined is true
PASS (new DerivedDefaultConstructorWithBaseReturnObject) instanceof DerivedDefaultConstructorWithBaseReturnObject is false
PASS typeof (new DerivedDefaultConstructorWithBaseReturnObject) === "object" is true
PASS (new DerivedDefaultConstructorWithBaseReturnObject2) instanceof DerivedDefaultConstructorWithBaseReturnObject2 is false
PASS (new DerivedDefaultConstructorWithBaseReturnObject2) === globalVariable is true
PASS (new DerivedDefaultConstructorWithBaseReturnThis) instanceof DerivedDefaultConstructorWithBaseReturnThis is true
PASS (new DerivedDefaultConstructorWithBaseReturnString) instanceof DerivedDefaultConstructorWithBaseReturnString is true
PASS (new DerivedDefaultConstructorWithBaseReturnNumber) instanceof DerivedDefaultConstructorWithBaseReturnNumber is true
PASS (new DerivedDefaultConstructorWithBaseReturnNull) instanceof DerivedDefaultConstructorWithBaseReturnNull is true
PASS (new DerivedDefaultConstructorWithBaseReturnSymbol) instanceof DerivedDefaultConstructorWithBaseReturnSymbol is true
PASS (new DerivedDefaultConstructorWithBaseThrow) threw exception Thrown Exception String.
PASS successfullyParsed is true

TEST COMPLETE
