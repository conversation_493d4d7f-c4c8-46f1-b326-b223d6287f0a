// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=a824395854fca10143c0329a0f95dcfc837c6d86$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_DOMNODE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_DOMNODE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_dom_capi.h"
#include "include/cef_dom.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefDOMNodeCToCpp
    : public CefCToCppRefCounted<CefDOMNodeCToCpp, CefDOMNode, cef_domnode_t> {
 public:
  CefDOMNodeCToCpp();
  virtual ~CefDOMNodeCToCpp();

  // CefDOMNode methods.
  Type GetType() override;
  bool IsText() override;
  bool IsElement() override;
  bool IsEditable() override;
  bool IsFormControlElement() override;
  CefString GetFormControlElementType() override;
  bool IsSame(CefRefPtr<CefDOMNode> that) override;
  CefString GetName() override;
  CefString GetValue() override;
  bool SetValue(const CefString& value) override;
  CefString GetAsMarkup() override;
  CefRefPtr<CefDOMDocument> GetDocument() override;
  CefRefPtr<CefDOMNode> GetParent() override;
  CefRefPtr<CefDOMNode> GetPreviousSibling() override;
  CefRefPtr<CefDOMNode> GetNextSibling() override;
  bool HasChildren() override;
  CefRefPtr<CefDOMNode> GetFirstChild() override;
  CefRefPtr<CefDOMNode> GetLastChild() override;
  CefString GetElementTagName() override;
  bool HasElementAttributes() override;
  bool HasElementAttribute(const CefString& attrName) override;
  CefString GetElementAttribute(const CefString& attrName) override;
  void GetElementAttributes(AttributeMap& attrMap) override;
  bool SetElementAttribute(const CefString& attrName,
                           const CefString& value) override;
  CefString GetElementInnerText() override;
  CefRect GetElementBounds() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_DOMNODE_CTOCPP_H_
