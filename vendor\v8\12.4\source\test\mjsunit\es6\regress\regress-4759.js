// Copyright 2016 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

function iterable(done) {
  return {
    [Symbol.iterator]: function() {
      return {
        next: function() {
          if (done) return { done: true };
          done = true;
          return { value: 42, done: false };
        }
      }
    }
  }
}

var [...result] = iterable(true);
assertEquals([], result);

var [...result] = iterable(false);
assertEquals([42], result);
