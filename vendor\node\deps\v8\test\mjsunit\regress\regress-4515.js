// Copyright 2015 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Flags: --allow-natives-syntax --turbo-filter=f

function f(array) {
  return array.length >>> 0;
};
%PrepareFunctionForOptimization(f);
var a = new Array();
a[**********] = "A";

assertEquals(**********, f(a));
assertEquals(**********, f(a));
%OptimizeFunctionOnNextCall(f);
assertEquals(**********, f(a));
