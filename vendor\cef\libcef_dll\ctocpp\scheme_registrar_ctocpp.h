// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=6c522fb5e064daeea21350a548af4bee6c0a2acf$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_SCHEME_REGISTRAR_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_SCHEME_REGISTRAR_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_scheme_capi.h"
#include "include/cef_scheme.h"
#include "libcef_dll/ctocpp/ctocpp_scoped.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefSchemeRegistrarCToCpp
    : public CefCToCppScoped<CefSchemeRegistrarCToCpp,
                             CefSchemeRegistrar,
                             cef_scheme_registrar_t> {
 public:
  CefSchemeRegistrarCToCpp();
  virtual ~CefSchemeRegistrarCToCpp();

  // CefSchemeRegistrar methods.
  bool AddCustomScheme(const CefString& scheme_name, int options) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_SCHEME_REGISTRAR_CTOCPP_H_
