// Copyright 2010 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Whenever we enter a with-scope, we copy the context. This in itself is fine
// (contexts may escape), but when leaving a with-scope, we currently also copy
// the context instead of reverting to the original. This does not work because
// inner functions may already have been created using the original context. In
// the failing test case below, the inner function is run in the original context
// (where x is undefined), but the assignment to x after the with-statement is
// run in the copied context:

// RegExp caching doesn't set lastIndex correctly.
// See http://code.google.com/p/chromium/issues/detail?id=52801

var re = /a/g;

var str = "bbbbabbbbabbbb";

// Test

re.test(str);
assertEquals(5, re.lastIndex);

re.lastIndex = 0;
re.test(str);
assertEquals(5, re.lastIndex);  // Fails if caching.

re.lastIndex = 0;
re.test(str);
assertEquals(5, re.lastIndex);  // Fails if caching.

// Exec

re = /a/g;

re.exec(str);
assertEquals(5, re.lastIndex);

re.lastIndex = 0;
re.exec(str);
assertEquals(5, re.lastIndex);  // Fails if caching.

re.lastIndex = 0;
re.exec(str);
assertEquals(5, re.lastIndex);  // Fails if caching.
