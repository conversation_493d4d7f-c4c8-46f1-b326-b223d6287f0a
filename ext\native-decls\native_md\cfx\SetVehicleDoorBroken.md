---
ns: CFX
apiset: server
---
## SET_VEHICLE_DOOR_BROKEN

```c
void SET_VEHICLE_DOOR_BROKEN(Vehicle vehicle, int doorIndex, BOOL deleteDoor);
```

See eDoorId declared in [`SET_VEHICLE_DOOR_SHUT`](#\_0x93D9BD300D7789E5)

**This is the server-side RPC native equivalent of the client native [SET\_VEHICLE\_DOOR\_BROKEN](?_0xD4D4F6A4AB575A33).**

## Parameters
* **vehicle**: 
* **doorIndex**: 
* **deleteDoor**: 

