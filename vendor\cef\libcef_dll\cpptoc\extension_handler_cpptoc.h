// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=b695266a9c10c4fc0b68f96b64a77cc5c0235827$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_EXTENSION_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_EXTENSION_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_client_capi.h"
#include "include/capi/cef_extension_handler_capi.h"
#include "include/cef_client.h"
#include "include/cef_extension_handler.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefExtensionHandlerCppToC
    : public CefCppToCRefCounted<CefExtensionHandlerCppToC,
                                 CefExtensionHandler,
                                 cef_extension_handler_t> {
 public:
  CefExtensionHandlerCppToC();
  virtual ~CefExtensionHandlerCppToC();
};

#endif  // CEF_LIBCEF_DLL_CPPTOC_EXTENSION_HANDLER_CPPTOC_H_
