#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return 1.2;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaConstant), U8(0),
  /*   45 S> */ B(Return),
]
constant pool: [
  HEAP_NUMBER_TYPE [1.2],
]
handlers: [
]

---
snippet: "
  var a = 1.2; return 2.6;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   47 S> */ B(LdaConstant), U8(1),
  /*   58 S> */ B(Return),
]
constant pool: [
  HEAP_NUMBER_TYPE [1.2],
  HEAP_NUMBER_TYPE [2.6],
]
handlers: [
]

---
snippet: "
  var a = 3.14; return 3.14;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaConstant), U8(0),
                B(Star0),
  /*   48 S> */ B(LdaConstant), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
  HEAP_NUMBER_TYPE [3.14],
]
handlers: [
]

