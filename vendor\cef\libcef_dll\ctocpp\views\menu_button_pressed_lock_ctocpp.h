// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=ea81c8b651b803c0d78b06a850c409da3e632b44$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_VIEWS_MENU_BUTTON_PRESSED_LOCK_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_VIEWS_MENU_BUTTON_PRESSED_LOCK_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/views/cef_menu_button_capi.h"
#include "include/capi/views/cef_menu_button_delegate_capi.h"
#include "include/views/cef_menu_button.h"
#include "include/views/cef_menu_button_delegate.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefMenuButtonPressedLockCToCpp
    : public CefCToCppRefCounted<CefMenuButtonPressedLockCToCpp,
                                 CefMenuButtonPressedLock,
                                 cef_menu_button_pressed_lock_t> {
 public:
  CefMenuButtonPressedLockCToCpp();
  virtual ~CefMenuButtonPressedLockCToCpp();

  // CefMenuButtonPressedLock methods.
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_VIEWS_MENU_BUTTON_PRESSED_LOCK_CTOCPP_H_
