#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f(a) { return a.name; }
  f({name : \"test\"});
"
frame size: 0
parameter count: 2
bytecode array length: 5
bytecodes: [
  /*   25 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
  /*   30 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a) { return a[\"key\"]; }
  f({key : \"test\"});
"
frame size: 0
parameter count: 2
bytecode array length: 5
bytecodes: [
  /*   24 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
  /*   32 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["key"],
]
handlers: [
]

---
snippet: "
  function f(a) { return a[100]; }
  f({100 : \"test\"});
"
frame size: 0
parameter count: 2
bytecode array length: 6
bytecodes: [
  /*   16 S> */ B(LdaSmi), I8(100),
  /*   24 E> */ B(GetKeyedProperty), R(arg0), U8(0),
  /*   30 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a, b) { return a[b]; }
  f({arg : \"test\"}, \"arg\");
"
frame size: 0
parameter count: 3
bytecode array length: 6
bytecodes: [
  /*   19 S> */ B(Ldar), R(arg1),
  /*   27 E> */ B(GetKeyedProperty), R(arg0), U8(0),
  /*   31 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a) { var b = a.name; return a[-124]; }
  f({\"-124\" : \"test\", name : 123 })
"
frame size: 1
parameter count: 2
bytecode array length: 11
bytecodes: [
  /*   26 S> */ B(GetNamedProperty), R(arg0), U8(0), U8(0),
                B(Star0),
  /*   32 S> */ B(LdaSmi), I8(-124),
  /*   40 E> */ B(GetKeyedProperty), R(arg0), U8(2),
  /*   47 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a) {
    var b = {};
    b.name0;
    b.name1;
    b.name2;
    b.name3;
    b.name4;
    b.name5;
    b.name6;
    b.name7;
    b.name8;
    b.name9;
    b.name10;
    b.name11;
    b.name12;
    b.name13;
    b.name14;
    b.name15;
    b.name16;
    b.name17;
    b.name18;
    b.name19;
    b.name20;
    b.name21;
    b.name22;
    b.name23;
    b.name24;
    b.name25;
    b.name26;
    b.name27;
    b.name28;
    b.name29;
    b.name30;
    b.name31;
    b.name32;
    b.name33;
    b.name34;
    b.name35;
    b.name36;
    b.name37;
    b.name38;
    b.name39;
    b.name40;
    b.name41;
    b.name42;
    b.name43;
    b.name44;
    b.name45;
    b.name46;
    b.name47;
    b.name48;
    b.name49;
    b.name50;
    b.name51;
    b.name52;
    b.name53;
    b.name54;
    b.name55;
    b.name56;
    b.name57;
    b.name58;
    b.name59;
    b.name60;
    b.name61;
    b.name62;
    b.name63;
    b.name64;
    b.name65;
    b.name66;
    b.name67;
    b.name68;
    b.name69;
    b.name70;
    b.name71;
    b.name72;
    b.name73;
    b.name74;
    b.name75;
    b.name76;
    b.name77;
    b.name78;
    b.name79;
    b.name80;
    b.name81;
    b.name82;
    b.name83;
    b.name84;
    b.name85;
    b.name86;
    b.name87;
    b.name88;
    b.name89;
    b.name90;
    b.name91;
    b.name92;
    b.name93;
    b.name94;
    b.name95;
    b.name96;
    b.name97;
    b.name98;
    b.name99;
    b.name100;
    b.name101;
    b.name102;
    b.name103;
    b.name104;
    b.name105;
    b.name106;
    b.name107;
    b.name108;
    b.name109;
    b.name110;
    b.name111;
    b.name112;
    b.name113;
    b.name114;
    b.name115;
    b.name116;
    b.name117;
    b.name118;
    b.name119;
    b.name120;
    b.name121;
    b.name122;
    b.name123;
    b.name124;
    b.name125;
    b.name126;
    b.name127;
    return a.name;
  }
  f({name : \"test\"})
"
frame size: 1
parameter count: 2
bytecode array length: 523
bytecodes: [
  /*   26 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   34 S> */ B(GetNamedProperty), R(0), U8(0), U8(0),
  /*   45 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   56 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   67 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*   78 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*   89 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  100 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  111 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  122 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  133 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  144 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  156 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  168 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  180 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  192 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  204 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  216 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  228 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  240 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  252 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  264 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  276 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  288 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  300 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  312 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  324 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  336 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  348 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  360 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  372 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  384 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  396 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  408 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  420 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  432 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  444 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  456 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  468 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  480 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  492 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  504 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  516 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  528 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  540 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  552 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  564 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  576 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  588 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  600 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  612 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  624 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  636 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  648 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  660 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  672 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  684 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  696 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  708 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  720 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  732 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  744 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  756 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  768 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  780 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  792 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  804 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  816 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  828 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  840 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  852 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  864 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  876 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  888 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /*  900 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /*  912 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /*  924 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /*  936 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /*  948 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /*  960 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /*  972 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /*  984 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /*  996 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1008 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1020 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1032 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1044 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1056 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1068 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1080 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1092 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1104 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1116 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1128 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1140 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1152 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1164 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1176 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1188 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1200 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1212 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1224 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1237 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1250 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1263 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1276 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1289 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1302 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1315 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1328 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1341 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1354 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1367 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1380 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1393 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1406 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1419 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1432 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1445 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1458 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1471 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1484 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1497 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1510 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1523 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1536 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1549 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1562 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1575 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1595 S> */ B(Wide), B(GetNamedProperty), R16(arg0), U16(128), U16(256),
  /* 1600 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name0"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name1"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name2"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name3"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name4"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name5"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name6"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name7"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name8"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name9"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name10"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name11"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name12"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name13"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name14"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name15"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name16"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name17"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name18"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name19"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name20"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name21"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name22"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name23"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name24"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name25"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name26"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name27"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name28"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name29"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name30"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name31"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name32"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name33"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name34"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name35"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name36"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name37"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name38"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name39"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name40"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name41"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name42"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name43"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name44"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name45"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name46"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name47"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name48"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name49"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name50"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name51"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name52"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name53"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name54"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name55"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name56"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name57"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name58"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name59"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name60"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name61"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name62"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name63"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name64"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name65"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name66"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name67"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name68"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name69"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name70"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name71"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name72"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name73"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name74"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name75"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name76"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name77"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name78"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name79"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name80"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name81"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name82"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name83"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name84"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name85"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name86"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name87"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name88"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name89"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name90"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name91"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name92"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name93"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name94"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name95"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name96"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name97"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name98"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name99"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name100"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name101"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name102"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name103"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name104"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name105"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name106"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name107"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name108"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name109"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name110"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name111"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name112"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name113"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name114"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name115"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name116"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name117"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name118"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name119"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name120"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name121"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name122"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name123"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name124"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name125"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name126"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name127"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a, b) {
    var c;
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    c = a[b];
    return a[b];
  }
  f({name : \"test\"}, \"name\")
"
frame size: 1
parameter count: 3
bytecode array length: 777
bytecodes: [
  /*   30 S> */ B(Ldar), R(arg1),
  /*   35 E> */ B(GetKeyedProperty), R(arg0), U8(0),
                B(Star0),
  /*   42 S> */ B(Ldar), R(arg1),
  /*   47 E> */ B(GetKeyedProperty), R(arg0), U8(2),
                B(Star0),
  /*   54 S> */ B(Ldar), R(arg1),
  /*   59 E> */ B(GetKeyedProperty), R(arg0), U8(4),
                B(Star0),
  /*   66 S> */ B(Ldar), R(arg1),
  /*   71 E> */ B(GetKeyedProperty), R(arg0), U8(6),
                B(Star0),
  /*   78 S> */ B(Ldar), R(arg1),
  /*   83 E> */ B(GetKeyedProperty), R(arg0), U8(8),
                B(Star0),
  /*   90 S> */ B(Ldar), R(arg1),
  /*   95 E> */ B(GetKeyedProperty), R(arg0), U8(10),
                B(Star0),
  /*  102 S> */ B(Ldar), R(arg1),
  /*  107 E> */ B(GetKeyedProperty), R(arg0), U8(12),
                B(Star0),
  /*  114 S> */ B(Ldar), R(arg1),
  /*  119 E> */ B(GetKeyedProperty), R(arg0), U8(14),
                B(Star0),
  /*  126 S> */ B(Ldar), R(arg1),
  /*  131 E> */ B(GetKeyedProperty), R(arg0), U8(16),
                B(Star0),
  /*  138 S> */ B(Ldar), R(arg1),
  /*  143 E> */ B(GetKeyedProperty), R(arg0), U8(18),
                B(Star0),
  /*  150 S> */ B(Ldar), R(arg1),
  /*  155 E> */ B(GetKeyedProperty), R(arg0), U8(20),
                B(Star0),
  /*  162 S> */ B(Ldar), R(arg1),
  /*  167 E> */ B(GetKeyedProperty), R(arg0), U8(22),
                B(Star0),
  /*  174 S> */ B(Ldar), R(arg1),
  /*  179 E> */ B(GetKeyedProperty), R(arg0), U8(24),
                B(Star0),
  /*  186 S> */ B(Ldar), R(arg1),
  /*  191 E> */ B(GetKeyedProperty), R(arg0), U8(26),
                B(Star0),
  /*  198 S> */ B(Ldar), R(arg1),
  /*  203 E> */ B(GetKeyedProperty), R(arg0), U8(28),
                B(Star0),
  /*  210 S> */ B(Ldar), R(arg1),
  /*  215 E> */ B(GetKeyedProperty), R(arg0), U8(30),
                B(Star0),
  /*  222 S> */ B(Ldar), R(arg1),
  /*  227 E> */ B(GetKeyedProperty), R(arg0), U8(32),
                B(Star0),
  /*  234 S> */ B(Ldar), R(arg1),
  /*  239 E> */ B(GetKeyedProperty), R(arg0), U8(34),
                B(Star0),
  /*  246 S> */ B(Ldar), R(arg1),
  /*  251 E> */ B(GetKeyedProperty), R(arg0), U8(36),
                B(Star0),
  /*  258 S> */ B(Ldar), R(arg1),
  /*  263 E> */ B(GetKeyedProperty), R(arg0), U8(38),
                B(Star0),
  /*  270 S> */ B(Ldar), R(arg1),
  /*  275 E> */ B(GetKeyedProperty), R(arg0), U8(40),
                B(Star0),
  /*  282 S> */ B(Ldar), R(arg1),
  /*  287 E> */ B(GetKeyedProperty), R(arg0), U8(42),
                B(Star0),
  /*  294 S> */ B(Ldar), R(arg1),
  /*  299 E> */ B(GetKeyedProperty), R(arg0), U8(44),
                B(Star0),
  /*  306 S> */ B(Ldar), R(arg1),
  /*  311 E> */ B(GetKeyedProperty), R(arg0), U8(46),
                B(Star0),
  /*  318 S> */ B(Ldar), R(arg1),
  /*  323 E> */ B(GetKeyedProperty), R(arg0), U8(48),
                B(Star0),
  /*  330 S> */ B(Ldar), R(arg1),
  /*  335 E> */ B(GetKeyedProperty), R(arg0), U8(50),
                B(Star0),
  /*  342 S> */ B(Ldar), R(arg1),
  /*  347 E> */ B(GetKeyedProperty), R(arg0), U8(52),
                B(Star0),
  /*  354 S> */ B(Ldar), R(arg1),
  /*  359 E> */ B(GetKeyedProperty), R(arg0), U8(54),
                B(Star0),
  /*  366 S> */ B(Ldar), R(arg1),
  /*  371 E> */ B(GetKeyedProperty), R(arg0), U8(56),
                B(Star0),
  /*  378 S> */ B(Ldar), R(arg1),
  /*  383 E> */ B(GetKeyedProperty), R(arg0), U8(58),
                B(Star0),
  /*  390 S> */ B(Ldar), R(arg1),
  /*  395 E> */ B(GetKeyedProperty), R(arg0), U8(60),
                B(Star0),
  /*  402 S> */ B(Ldar), R(arg1),
  /*  407 E> */ B(GetKeyedProperty), R(arg0), U8(62),
                B(Star0),
  /*  414 S> */ B(Ldar), R(arg1),
  /*  419 E> */ B(GetKeyedProperty), R(arg0), U8(64),
                B(Star0),
  /*  426 S> */ B(Ldar), R(arg1),
  /*  431 E> */ B(GetKeyedProperty), R(arg0), U8(66),
                B(Star0),
  /*  438 S> */ B(Ldar), R(arg1),
  /*  443 E> */ B(GetKeyedProperty), R(arg0), U8(68),
                B(Star0),
  /*  450 S> */ B(Ldar), R(arg1),
  /*  455 E> */ B(GetKeyedProperty), R(arg0), U8(70),
                B(Star0),
  /*  462 S> */ B(Ldar), R(arg1),
  /*  467 E> */ B(GetKeyedProperty), R(arg0), U8(72),
                B(Star0),
  /*  474 S> */ B(Ldar), R(arg1),
  /*  479 E> */ B(GetKeyedProperty), R(arg0), U8(74),
                B(Star0),
  /*  486 S> */ B(Ldar), R(arg1),
  /*  491 E> */ B(GetKeyedProperty), R(arg0), U8(76),
                B(Star0),
  /*  498 S> */ B(Ldar), R(arg1),
  /*  503 E> */ B(GetKeyedProperty), R(arg0), U8(78),
                B(Star0),
  /*  510 S> */ B(Ldar), R(arg1),
  /*  515 E> */ B(GetKeyedProperty), R(arg0), U8(80),
                B(Star0),
  /*  522 S> */ B(Ldar), R(arg1),
  /*  527 E> */ B(GetKeyedProperty), R(arg0), U8(82),
                B(Star0),
  /*  534 S> */ B(Ldar), R(arg1),
  /*  539 E> */ B(GetKeyedProperty), R(arg0), U8(84),
                B(Star0),
  /*  546 S> */ B(Ldar), R(arg1),
  /*  551 E> */ B(GetKeyedProperty), R(arg0), U8(86),
                B(Star0),
  /*  558 S> */ B(Ldar), R(arg1),
  /*  563 E> */ B(GetKeyedProperty), R(arg0), U8(88),
                B(Star0),
  /*  570 S> */ B(Ldar), R(arg1),
  /*  575 E> */ B(GetKeyedProperty), R(arg0), U8(90),
                B(Star0),
  /*  582 S> */ B(Ldar), R(arg1),
  /*  587 E> */ B(GetKeyedProperty), R(arg0), U8(92),
                B(Star0),
  /*  594 S> */ B(Ldar), R(arg1),
  /*  599 E> */ B(GetKeyedProperty), R(arg0), U8(94),
                B(Star0),
  /*  606 S> */ B(Ldar), R(arg1),
  /*  611 E> */ B(GetKeyedProperty), R(arg0), U8(96),
                B(Star0),
  /*  618 S> */ B(Ldar), R(arg1),
  /*  623 E> */ B(GetKeyedProperty), R(arg0), U8(98),
                B(Star0),
  /*  630 S> */ B(Ldar), R(arg1),
  /*  635 E> */ B(GetKeyedProperty), R(arg0), U8(100),
                B(Star0),
  /*  642 S> */ B(Ldar), R(arg1),
  /*  647 E> */ B(GetKeyedProperty), R(arg0), U8(102),
                B(Star0),
  /*  654 S> */ B(Ldar), R(arg1),
  /*  659 E> */ B(GetKeyedProperty), R(arg0), U8(104),
                B(Star0),
  /*  666 S> */ B(Ldar), R(arg1),
  /*  671 E> */ B(GetKeyedProperty), R(arg0), U8(106),
                B(Star0),
  /*  678 S> */ B(Ldar), R(arg1),
  /*  683 E> */ B(GetKeyedProperty), R(arg0), U8(108),
                B(Star0),
  /*  690 S> */ B(Ldar), R(arg1),
  /*  695 E> */ B(GetKeyedProperty), R(arg0), U8(110),
                B(Star0),
  /*  702 S> */ B(Ldar), R(arg1),
  /*  707 E> */ B(GetKeyedProperty), R(arg0), U8(112),
                B(Star0),
  /*  714 S> */ B(Ldar), R(arg1),
  /*  719 E> */ B(GetKeyedProperty), R(arg0), U8(114),
                B(Star0),
  /*  726 S> */ B(Ldar), R(arg1),
  /*  731 E> */ B(GetKeyedProperty), R(arg0), U8(116),
                B(Star0),
  /*  738 S> */ B(Ldar), R(arg1),
  /*  743 E> */ B(GetKeyedProperty), R(arg0), U8(118),
                B(Star0),
  /*  750 S> */ B(Ldar), R(arg1),
  /*  755 E> */ B(GetKeyedProperty), R(arg0), U8(120),
                B(Star0),
  /*  762 S> */ B(Ldar), R(arg1),
  /*  767 E> */ B(GetKeyedProperty), R(arg0), U8(122),
                B(Star0),
  /*  774 S> */ B(Ldar), R(arg1),
  /*  779 E> */ B(GetKeyedProperty), R(arg0), U8(124),
                B(Star0),
  /*  786 S> */ B(Ldar), R(arg1),
  /*  791 E> */ B(GetKeyedProperty), R(arg0), U8(126),
                B(Star0),
  /*  798 S> */ B(Ldar), R(arg1),
  /*  803 E> */ B(GetKeyedProperty), R(arg0), U8(128),
                B(Star0),
  /*  810 S> */ B(Ldar), R(arg1),
  /*  815 E> */ B(GetKeyedProperty), R(arg0), U8(130),
                B(Star0),
  /*  822 S> */ B(Ldar), R(arg1),
  /*  827 E> */ B(GetKeyedProperty), R(arg0), U8(132),
                B(Star0),
  /*  834 S> */ B(Ldar), R(arg1),
  /*  839 E> */ B(GetKeyedProperty), R(arg0), U8(134),
                B(Star0),
  /*  846 S> */ B(Ldar), R(arg1),
  /*  851 E> */ B(GetKeyedProperty), R(arg0), U8(136),
                B(Star0),
  /*  858 S> */ B(Ldar), R(arg1),
  /*  863 E> */ B(GetKeyedProperty), R(arg0), U8(138),
                B(Star0),
  /*  870 S> */ B(Ldar), R(arg1),
  /*  875 E> */ B(GetKeyedProperty), R(arg0), U8(140),
                B(Star0),
  /*  882 S> */ B(Ldar), R(arg1),
  /*  887 E> */ B(GetKeyedProperty), R(arg0), U8(142),
                B(Star0),
  /*  894 S> */ B(Ldar), R(arg1),
  /*  899 E> */ B(GetKeyedProperty), R(arg0), U8(144),
                B(Star0),
  /*  906 S> */ B(Ldar), R(arg1),
  /*  911 E> */ B(GetKeyedProperty), R(arg0), U8(146),
                B(Star0),
  /*  918 S> */ B(Ldar), R(arg1),
  /*  923 E> */ B(GetKeyedProperty), R(arg0), U8(148),
                B(Star0),
  /*  930 S> */ B(Ldar), R(arg1),
  /*  935 E> */ B(GetKeyedProperty), R(arg0), U8(150),
                B(Star0),
  /*  942 S> */ B(Ldar), R(arg1),
  /*  947 E> */ B(GetKeyedProperty), R(arg0), U8(152),
                B(Star0),
  /*  954 S> */ B(Ldar), R(arg1),
  /*  959 E> */ B(GetKeyedProperty), R(arg0), U8(154),
                B(Star0),
  /*  966 S> */ B(Ldar), R(arg1),
  /*  971 E> */ B(GetKeyedProperty), R(arg0), U8(156),
                B(Star0),
  /*  978 S> */ B(Ldar), R(arg1),
  /*  983 E> */ B(GetKeyedProperty), R(arg0), U8(158),
                B(Star0),
  /*  990 S> */ B(Ldar), R(arg1),
  /*  995 E> */ B(GetKeyedProperty), R(arg0), U8(160),
                B(Star0),
  /* 1002 S> */ B(Ldar), R(arg1),
  /* 1007 E> */ B(GetKeyedProperty), R(arg0), U8(162),
                B(Star0),
  /* 1014 S> */ B(Ldar), R(arg1),
  /* 1019 E> */ B(GetKeyedProperty), R(arg0), U8(164),
                B(Star0),
  /* 1026 S> */ B(Ldar), R(arg1),
  /* 1031 E> */ B(GetKeyedProperty), R(arg0), U8(166),
                B(Star0),
  /* 1038 S> */ B(Ldar), R(arg1),
  /* 1043 E> */ B(GetKeyedProperty), R(arg0), U8(168),
                B(Star0),
  /* 1050 S> */ B(Ldar), R(arg1),
  /* 1055 E> */ B(GetKeyedProperty), R(arg0), U8(170),
                B(Star0),
  /* 1062 S> */ B(Ldar), R(arg1),
  /* 1067 E> */ B(GetKeyedProperty), R(arg0), U8(172),
                B(Star0),
  /* 1074 S> */ B(Ldar), R(arg1),
  /* 1079 E> */ B(GetKeyedProperty), R(arg0), U8(174),
                B(Star0),
  /* 1086 S> */ B(Ldar), R(arg1),
  /* 1091 E> */ B(GetKeyedProperty), R(arg0), U8(176),
                B(Star0),
  /* 1098 S> */ B(Ldar), R(arg1),
  /* 1103 E> */ B(GetKeyedProperty), R(arg0), U8(178),
                B(Star0),
  /* 1110 S> */ B(Ldar), R(arg1),
  /* 1115 E> */ B(GetKeyedProperty), R(arg0), U8(180),
                B(Star0),
  /* 1122 S> */ B(Ldar), R(arg1),
  /* 1127 E> */ B(GetKeyedProperty), R(arg0), U8(182),
                B(Star0),
  /* 1134 S> */ B(Ldar), R(arg1),
  /* 1139 E> */ B(GetKeyedProperty), R(arg0), U8(184),
                B(Star0),
  /* 1146 S> */ B(Ldar), R(arg1),
  /* 1151 E> */ B(GetKeyedProperty), R(arg0), U8(186),
                B(Star0),
  /* 1158 S> */ B(Ldar), R(arg1),
  /* 1163 E> */ B(GetKeyedProperty), R(arg0), U8(188),
                B(Star0),
  /* 1170 S> */ B(Ldar), R(arg1),
  /* 1175 E> */ B(GetKeyedProperty), R(arg0), U8(190),
                B(Star0),
  /* 1182 S> */ B(Ldar), R(arg1),
  /* 1187 E> */ B(GetKeyedProperty), R(arg0), U8(192),
                B(Star0),
  /* 1194 S> */ B(Ldar), R(arg1),
  /* 1199 E> */ B(GetKeyedProperty), R(arg0), U8(194),
                B(Star0),
  /* 1206 S> */ B(Ldar), R(arg1),
  /* 1211 E> */ B(GetKeyedProperty), R(arg0), U8(196),
                B(Star0),
  /* 1218 S> */ B(Ldar), R(arg1),
  /* 1223 E> */ B(GetKeyedProperty), R(arg0), U8(198),
                B(Star0),
  /* 1230 S> */ B(Ldar), R(arg1),
  /* 1235 E> */ B(GetKeyedProperty), R(arg0), U8(200),
                B(Star0),
  /* 1242 S> */ B(Ldar), R(arg1),
  /* 1247 E> */ B(GetKeyedProperty), R(arg0), U8(202),
                B(Star0),
  /* 1254 S> */ B(Ldar), R(arg1),
  /* 1259 E> */ B(GetKeyedProperty), R(arg0), U8(204),
                B(Star0),
  /* 1266 S> */ B(Ldar), R(arg1),
  /* 1271 E> */ B(GetKeyedProperty), R(arg0), U8(206),
                B(Star0),
  /* 1278 S> */ B(Ldar), R(arg1),
  /* 1283 E> */ B(GetKeyedProperty), R(arg0), U8(208),
                B(Star0),
  /* 1290 S> */ B(Ldar), R(arg1),
  /* 1295 E> */ B(GetKeyedProperty), R(arg0), U8(210),
                B(Star0),
  /* 1302 S> */ B(Ldar), R(arg1),
  /* 1307 E> */ B(GetKeyedProperty), R(arg0), U8(212),
                B(Star0),
  /* 1314 S> */ B(Ldar), R(arg1),
  /* 1319 E> */ B(GetKeyedProperty), R(arg0), U8(214),
                B(Star0),
  /* 1326 S> */ B(Ldar), R(arg1),
  /* 1331 E> */ B(GetKeyedProperty), R(arg0), U8(216),
                B(Star0),
  /* 1338 S> */ B(Ldar), R(arg1),
  /* 1343 E> */ B(GetKeyedProperty), R(arg0), U8(218),
                B(Star0),
  /* 1350 S> */ B(Ldar), R(arg1),
  /* 1355 E> */ B(GetKeyedProperty), R(arg0), U8(220),
                B(Star0),
  /* 1362 S> */ B(Ldar), R(arg1),
  /* 1367 E> */ B(GetKeyedProperty), R(arg0), U8(222),
                B(Star0),
  /* 1374 S> */ B(Ldar), R(arg1),
  /* 1379 E> */ B(GetKeyedProperty), R(arg0), U8(224),
                B(Star0),
  /* 1386 S> */ B(Ldar), R(arg1),
  /* 1391 E> */ B(GetKeyedProperty), R(arg0), U8(226),
                B(Star0),
  /* 1398 S> */ B(Ldar), R(arg1),
  /* 1403 E> */ B(GetKeyedProperty), R(arg0), U8(228),
                B(Star0),
  /* 1410 S> */ B(Ldar), R(arg1),
  /* 1415 E> */ B(GetKeyedProperty), R(arg0), U8(230),
                B(Star0),
  /* 1422 S> */ B(Ldar), R(arg1),
  /* 1427 E> */ B(GetKeyedProperty), R(arg0), U8(232),
                B(Star0),
  /* 1434 S> */ B(Ldar), R(arg1),
  /* 1439 E> */ B(GetKeyedProperty), R(arg0), U8(234),
                B(Star0),
  /* 1446 S> */ B(Ldar), R(arg1),
  /* 1451 E> */ B(GetKeyedProperty), R(arg0), U8(236),
                B(Star0),
  /* 1458 S> */ B(Ldar), R(arg1),
  /* 1463 E> */ B(GetKeyedProperty), R(arg0), U8(238),
                B(Star0),
  /* 1470 S> */ B(Ldar), R(arg1),
  /* 1475 E> */ B(GetKeyedProperty), R(arg0), U8(240),
                B(Star0),
  /* 1482 S> */ B(Ldar), R(arg1),
  /* 1487 E> */ B(GetKeyedProperty), R(arg0), U8(242),
                B(Star0),
  /* 1494 S> */ B(Ldar), R(arg1),
  /* 1499 E> */ B(GetKeyedProperty), R(arg0), U8(244),
                B(Star0),
  /* 1506 S> */ B(Ldar), R(arg1),
  /* 1511 E> */ B(GetKeyedProperty), R(arg0), U8(246),
                B(Star0),
  /* 1518 S> */ B(Ldar), R(arg1),
  /* 1523 E> */ B(GetKeyedProperty), R(arg0), U8(248),
                B(Star0),
  /* 1530 S> */ B(Ldar), R(arg1),
  /* 1535 E> */ B(GetKeyedProperty), R(arg0), U8(250),
                B(Star0),
  /* 1542 S> */ B(Ldar), R(arg1),
  /* 1547 E> */ B(GetKeyedProperty), R(arg0), U8(252),
                B(Star0),
  /* 1554 S> */ B(Ldar), R(arg1),
  /* 1559 E> */ B(GetKeyedProperty), R(arg0), U8(254),
                B(Star0),
  /* 1566 S> */ B(Ldar), R(arg1),
  /* 1574 E> */ B(Wide), B(GetKeyedProperty), R16(arg0), U16(256),
  /* 1578 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

