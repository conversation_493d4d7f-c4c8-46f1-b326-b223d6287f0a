// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=795e78c45943dd3c23d95ed1b85e889288f2dcf1$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_TASK_RUNNER_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_TASK_RUNNER_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_task_capi.h"
#include "include/cef_task.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefTaskRunnerCToCpp : public CefCToCppRefCounted<CefTaskRunnerCToCpp,
                                                       CefTaskRunner,
                                                       cef_task_runner_t> {
 public:
  CefTaskRunnerCToCpp();
  virtual ~CefTaskRunnerCToCpp();

  // CefTaskRunner methods.
  bool IsSame(CefRefPtr<CefTaskRunner> that) override;
  bool BelongsToCurrentThread() override;
  bool BelongsToThread(CefThreadId threadId) override;
  bool PostTask(CefRefPtr<CefTask> task) override;
  bool PostDelayedTask(CefRefPtr<CefTask> task, int64 delay_ms) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_TASK_RUNNER_CTOCPP_H_
