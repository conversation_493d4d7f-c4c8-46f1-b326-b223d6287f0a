LIBS=../../libcrypto

$RC4ASM=rc4_enc.c rc4_skey.c
IF[{- !$disabled{asm} -}]
  $RC4ASM_x86=rc4-586.S
  $RC4ASM_x86_64=rc4-x86_64.s rc4-md5-x86_64.s
  $RC4ASM_s390x=rc4-s390x.s
  $RC4ASM_parisc11=rc4-parisc.s
  $RC4ASM_parisc20_64=$RC4ASM_parisc11
  $RC4ASM_c64xplus=rc4-c64xplus.s

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$RC4ASM_{- $target{asm_arch} -}]
    $RC4ASM=$RC4ASM_{- $target{asm_arch} -}
    $RC4DEF=RC4_ASM
  ENDIF
ENDIF

SOURCE[../../libcrypto]=$RC4ASM

# When all deprecated symbols are removed, libcrypto doesn't export the
# rc4 functions, so we must include them directly in liblegacy.a
IF[{- !$disabled{module} && !$disabled{shared} -}]
  SOURCE[../../providers/liblegacy.a]=$RC4ASM
ENDIF

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules.
DEFINE[../../libcrypto]=$RC4DEF
DEFINE[../../providers/liblegacy.a]=$RC4DEF

GENERATE[rc4-586.S]=asm/rc4-586.pl
DEPEND[rc4-586.S]=../perlasm/x86asm.pl

GENERATE[rc4-x86_64.s]=asm/rc4-x86_64.pl
GENERATE[rc4-md5-x86_64.s]=asm/rc4-md5-x86_64.pl

GENERATE[rc4-parisc.s]=asm/rc4-parisc.pl
GENERATE[rc4-c64xplus.s]=asm/rc4-c64xplus.pl
GENERATE[rc4-s390x.s]=asm/rc4-s390x.pl
