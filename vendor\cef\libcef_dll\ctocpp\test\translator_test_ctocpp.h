// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=774d22a8a54e71a2511ce6a66491d9563302f0bf$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_TEST_TRANSLATOR_TEST_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_TEST_TRANSLATOR_TEST_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include <map>
#include <vector>
#include "include/capi/test/cef_translator_test_capi.h"
#include "include/test/cef_translator_test.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefTranslatorTestCToCpp
    : public CefCToCppRefCounted<CefTranslatorTestCToCpp,
                                 CefTranslatorTest,
                                 cef_translator_test_t> {
 public:
  CefTranslatorTestCToCpp();
  virtual ~CefTranslatorTestCToCpp();

  // CefTranslatorTest methods.
  void GetVoid() override;
  bool GetBool() override;
  int GetInt() override;
  double GetDouble() override;
  long GetLong() override;
  size_t GetSizet() override;
  bool SetVoid() override;
  bool SetBool(bool val) override;
  bool SetInt(int val) override;
  bool SetDouble(double val) override;
  bool SetLong(long val) override;
  bool SetSizet(size_t val) override;
  bool SetIntList(const std::vector<int>& val) override;
  bool GetIntListByRef(IntList& val) override;
  size_t GetIntListSize() override;
  CefString GetString() override;
  bool SetString(const CefString& val) override;
  void GetStringByRef(CefString& val) override;
  bool SetStringList(const std::vector<CefString>& val) override;
  bool GetStringListByRef(StringList& val) override;
  bool SetStringMap(const StringMap& val) override;
  bool GetStringMapByRef(std::map<CefString, CefString>& val) override;
  bool SetStringMultimap(
      const std::multimap<CefString, CefString>& val) override;
  bool GetStringMultimapByRef(StringMultimap& val) override;
  CefPoint GetPoint() override;
  bool SetPoint(const CefPoint& val) override;
  void GetPointByRef(CefPoint& val) override;
  bool SetPointList(const std::vector<CefPoint>& val) override;
  bool GetPointListByRef(PointList& val) override;
  size_t GetPointListSize() override;
  CefRefPtr<CefTranslatorTestRefPtrLibrary> GetRefPtrLibrary(int val) override;
  int SetRefPtrLibrary(CefRefPtr<CefTranslatorTestRefPtrLibrary> val) override;
  CefRefPtr<CefTranslatorTestRefPtrLibrary> SetRefPtrLibraryAndReturn(
      CefRefPtr<CefTranslatorTestRefPtrLibrary> val) override;
  int SetChildRefPtrLibrary(
      CefRefPtr<CefTranslatorTestRefPtrLibraryChild> val) override;
  CefRefPtr<CefTranslatorTestRefPtrLibrary>
  SetChildRefPtrLibraryAndReturnParent(
      CefRefPtr<CefTranslatorTestRefPtrLibraryChild> val) override;
  bool SetRefPtrLibraryList(
      const std::vector<CefRefPtr<CefTranslatorTestRefPtrLibrary>>& val,
      int val1,
      int val2) override;
  bool GetRefPtrLibraryListByRef(RefPtrLibraryList& val,
                                 int val1,
                                 int val2) override;
  size_t GetRefPtrLibraryListSize() override;
  int SetRefPtrClient(CefRefPtr<CefTranslatorTestRefPtrClient> val) override;
  CefRefPtr<CefTranslatorTestRefPtrClient> SetRefPtrClientAndReturn(
      CefRefPtr<CefTranslatorTestRefPtrClient> val) override;
  int SetChildRefPtrClient(
      CefRefPtr<CefTranslatorTestRefPtrClientChild> val) override;
  CefRefPtr<CefTranslatorTestRefPtrClient> SetChildRefPtrClientAndReturnParent(
      CefRefPtr<CefTranslatorTestRefPtrClientChild> val) override;
  bool SetRefPtrClientList(
      const std::vector<CefRefPtr<CefTranslatorTestRefPtrClient>>& val,
      int val1,
      int val2) override;
  bool GetRefPtrClientListByRef(
      RefPtrClientList& val,
      CefRefPtr<CefTranslatorTestRefPtrClient> val1,
      CefRefPtr<CefTranslatorTestRefPtrClient> val2) override;
  size_t GetRefPtrClientListSize() override;
  CefOwnPtr<CefTranslatorTestScopedLibrary> GetOwnPtrLibrary(int val) override;
  int SetOwnPtrLibrary(CefOwnPtr<CefTranslatorTestScopedLibrary> val) override;
  CefOwnPtr<CefTranslatorTestScopedLibrary> SetOwnPtrLibraryAndReturn(
      CefOwnPtr<CefTranslatorTestScopedLibrary> val) override;
  int SetChildOwnPtrLibrary(
      CefOwnPtr<CefTranslatorTestScopedLibraryChild> val) override;
  CefOwnPtr<CefTranslatorTestScopedLibrary>
  SetChildOwnPtrLibraryAndReturnParent(
      CefOwnPtr<CefTranslatorTestScopedLibraryChild> val) override;
  int SetOwnPtrClient(CefOwnPtr<CefTranslatorTestScopedClient> val) override;
  CefOwnPtr<CefTranslatorTestScopedClient> SetOwnPtrClientAndReturn(
      CefOwnPtr<CefTranslatorTestScopedClient> val) override;
  int SetChildOwnPtrClient(
      CefOwnPtr<CefTranslatorTestScopedClientChild> val) override;
  CefOwnPtr<CefTranslatorTestScopedClient> SetChildOwnPtrClientAndReturnParent(
      CefOwnPtr<CefTranslatorTestScopedClientChild> val) override;
  int SetRawPtrLibrary(CefRawPtr<CefTranslatorTestScopedLibrary> val) override;
  int SetChildRawPtrLibrary(
      CefRawPtr<CefTranslatorTestScopedLibraryChild> val) override;
  bool SetRawPtrLibraryList(
      const std::vector<CefRawPtr<CefTranslatorTestScopedLibrary>>& val,
      int val1,
      int val2) override;
  int SetRawPtrClient(CefRawPtr<CefTranslatorTestScopedClient> val) override;
  int SetChildRawPtrClient(
      CefRawPtr<CefTranslatorTestScopedClientChild> val) override;
  bool SetRawPtrClientList(
      const std::vector<CefRawPtr<CefTranslatorTestScopedClient>>& val,
      int val1,
      int val2) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_TEST_TRANSLATOR_TEST_CTOCPP_H_
