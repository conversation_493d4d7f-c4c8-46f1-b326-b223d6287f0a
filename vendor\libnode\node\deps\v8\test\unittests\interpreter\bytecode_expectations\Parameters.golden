#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f() { return this; }
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   15 S> */ B(Ldar), R(this),
  /*   27 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1) { return arg1; }
  f();
"
frame size: 0
parameter count: 2
bytecode array length: 3
bytecodes: [
  /*   19 S> */ B(Ldar), R(arg0),
  /*   31 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1) { return this; }
  f();
"
frame size: 0
parameter count: 2
bytecode array length: 3
bytecodes: [
  /*   19 S> */ B(Ldar), R(this),
  /*   31 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1, arg2, arg3, arg4, arg5, arg6, arg7) { return arg4; }
  f();
"
frame size: 0
parameter count: 8
bytecode array length: 3
bytecodes: [
  /*   55 S> */ B(Ldar), R(arg3),
  /*   67 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1, arg2, arg3, arg4, arg5, arg6, arg7) { return this; }
  f();
"
frame size: 0
parameter count: 8
bytecode array length: 3
bytecodes: [
  /*   55 S> */ B(Ldar), R(this),
  /*   67 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1) { arg1 = 1; }
  f();
"
frame size: 0
parameter count: 2
bytecode array length: 6
bytecodes: [
  /*   19 S> */ B(LdaSmi), I8(1),
                B(Star), R(arg0),
                B(LdaUndefined),
  /*   29 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(arg1, arg2, arg3, arg4) { arg2 = 1; }
  f();
"
frame size: 0
parameter count: 5
bytecode array length: 6
bytecodes: [
  /*   37 S> */ B(LdaSmi), I8(1),
                B(Star), R(arg1),
                B(LdaUndefined),
  /*   47 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

