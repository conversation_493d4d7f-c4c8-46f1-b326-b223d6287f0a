// © 2016 and later: Unicode, Inc. and others.
// License & terms of use: http://www.unicode.org/copyright.html
/*
*******************************************************************************
*   Copyright (C) 2002-2016, International Business Machines
*   Corporation and others.  All Rights Reserved.
*******************************************************************************
*
*   file name:  urename.h
*   encoding:   UTF-8
*   tab size:   8 (not used)
*   indentation:4
*
*   Created by: Perl script tools/genren.pl written by <PERSON>
*
*  Contains data for renaming ICU exports.
*  Gets included by umachine.h
*
*  THIS FILE IS MACHINE-GENERATED, DON'T PLAY WITH IT IF YOU DON'T KNOW WHAT
*  YOU ARE DOING, OTHERWISE VERY BAD THINGS WILL HAPPEN!
*/

#ifndef URENAME_H
#define URENAME_H

/* U_DISABLE_RENAMING can be defined in the following ways:
 *   - when running configure, e.g.
 *        runConfigureICU Linux --disable-renaming
 *   - by changing the default setting of U_DISABLE_RENAMING in uconfig.h
 */

#include "unicode/uconfig.h"

#if !U_DISABLE_RENAMING

// Disable Renaming for Visual Studio's IntelliSense feature, so that 'Go-to-Definition' (F12) will work.
#if !(defined(_MSC_VER) && defined(__INTELLISENSE__))

/* We need the U_ICU_ENTRY_POINT_RENAME definition. There's a default one in unicode/uvernum.h we can use, but we will give
   the platform a chance to define it first.
   Normally (if utypes.h or umachine.h was included first) this will not be necessary as it will already be defined.
 */

#ifndef U_ICU_ENTRY_POINT_RENAME
#include "unicode/umachine.h"
#endif

/* If we still don't have U_ICU_ENTRY_POINT_RENAME use the default. */
#ifndef U_ICU_ENTRY_POINT_RENAME
#include "unicode/uvernum.h"
#endif

/* Error out before the following defines cause very strange and unexpected code breakage */
#ifndef U_ICU_ENTRY_POINT_RENAME
#error U_ICU_ENTRY_POINT_RENAME is not defined - cannot continue. Consider defining U_DISABLE_RENAMING if renaming should not be used.
#endif


/* C exports renaming data */

#define T_CString_int64ToString U_ICU_ENTRY_POINT_RENAME(T_CString_int64ToString)
#define T_CString_integerToString U_ICU_ENTRY_POINT_RENAME(T_CString_integerToString)
#define T_CString_stringToInteger U_ICU_ENTRY_POINT_RENAME(T_CString_stringToInteger)
#define T_CString_toLowerCase U_ICU_ENTRY_POINT_RENAME(T_CString_toLowerCase)
#define T_CString_toUpperCase U_ICU_ENTRY_POINT_RENAME(T_CString_toUpperCase)
#define UCNV_FROM_U_CALLBACK_ESCAPE U_ICU_ENTRY_POINT_RENAME(UCNV_FROM_U_CALLBACK_ESCAPE)
#define UCNV_FROM_U_CALLBACK_SKIP U_ICU_ENTRY_POINT_RENAME(UCNV_FROM_U_CALLBACK_SKIP)
#define UCNV_FROM_U_CALLBACK_STOP U_ICU_ENTRY_POINT_RENAME(UCNV_FROM_U_CALLBACK_STOP)
#define UCNV_FROM_U_CALLBACK_SUBSTITUTE U_ICU_ENTRY_POINT_RENAME(UCNV_FROM_U_CALLBACK_SUBSTITUTE)
#define UCNV_TO_U_CALLBACK_ESCAPE U_ICU_ENTRY_POINT_RENAME(UCNV_TO_U_CALLBACK_ESCAPE)
#define UCNV_TO_U_CALLBACK_SKIP U_ICU_ENTRY_POINT_RENAME(UCNV_TO_U_CALLBACK_SKIP)
#define UCNV_TO_U_CALLBACK_STOP U_ICU_ENTRY_POINT_RENAME(UCNV_TO_U_CALLBACK_STOP)
#define UCNV_TO_U_CALLBACK_SUBSTITUTE U_ICU_ENTRY_POINT_RENAME(UCNV_TO_U_CALLBACK_SUBSTITUTE)
#define UDataMemory_createNewInstance U_ICU_ENTRY_POINT_RENAME(UDataMemory_createNewInstance)
#define UDataMemory_init U_ICU_ENTRY_POINT_RENAME(UDataMemory_init)
#define UDataMemory_isLoaded U_ICU_ENTRY_POINT_RENAME(UDataMemory_isLoaded)
#define UDataMemory_normalizeDataPointer U_ICU_ENTRY_POINT_RENAME(UDataMemory_normalizeDataPointer)
#define UDataMemory_setData U_ICU_ENTRY_POINT_RENAME(UDataMemory_setData)
#define UDatamemory_assign U_ICU_ENTRY_POINT_RENAME(UDatamemory_assign)
#define _ASCIIData U_ICU_ENTRY_POINT_RENAME(_ASCIIData)
#define _Bocu1Data U_ICU_ENTRY_POINT_RENAME(_Bocu1Data)
#define _CESU8Data U_ICU_ENTRY_POINT_RENAME(_CESU8Data)
#define _CompoundTextData U_ICU_ENTRY_POINT_RENAME(_CompoundTextData)
#define _HZData U_ICU_ENTRY_POINT_RENAME(_HZData)
#define _IMAPData U_ICU_ENTRY_POINT_RENAME(_IMAPData)
#define _ISCIIData U_ICU_ENTRY_POINT_RENAME(_ISCIIData)
#define _ISO2022Data U_ICU_ENTRY_POINT_RENAME(_ISO2022Data)
#define _LMBCSData1 U_ICU_ENTRY_POINT_RENAME(_LMBCSData1)
#define _LMBCSData11 U_ICU_ENTRY_POINT_RENAME(_LMBCSData11)
#define _LMBCSData16 U_ICU_ENTRY_POINT_RENAME(_LMBCSData16)
#define _LMBCSData17 U_ICU_ENTRY_POINT_RENAME(_LMBCSData17)
#define _LMBCSData18 U_ICU_ENTRY_POINT_RENAME(_LMBCSData18)
#define _LMBCSData19 U_ICU_ENTRY_POINT_RENAME(_LMBCSData19)
#define _LMBCSData2 U_ICU_ENTRY_POINT_RENAME(_LMBCSData2)
#define _LMBCSData3 U_ICU_ENTRY_POINT_RENAME(_LMBCSData3)
#define _LMBCSData4 U_ICU_ENTRY_POINT_RENAME(_LMBCSData4)
#define _LMBCSData5 U_ICU_ENTRY_POINT_RENAME(_LMBCSData5)
#define _LMBCSData6 U_ICU_ENTRY_POINT_RENAME(_LMBCSData6)
#define _LMBCSData8 U_ICU_ENTRY_POINT_RENAME(_LMBCSData8)
#define _Latin1Data U_ICU_ENTRY_POINT_RENAME(_Latin1Data)
#define _MBCSData U_ICU_ENTRY_POINT_RENAME(_MBCSData)
#define _SCSUData U_ICU_ENTRY_POINT_RENAME(_SCSUData)
#define _UTF16BEData U_ICU_ENTRY_POINT_RENAME(_UTF16BEData)
#define _UTF16Data U_ICU_ENTRY_POINT_RENAME(_UTF16Data)
#define _UTF16LEData U_ICU_ENTRY_POINT_RENAME(_UTF16LEData)
#define _UTF16v2Data U_ICU_ENTRY_POINT_RENAME(_UTF16v2Data)
#define _UTF32BEData U_ICU_ENTRY_POINT_RENAME(_UTF32BEData)
#define _UTF32Data U_ICU_ENTRY_POINT_RENAME(_UTF32Data)
#define _UTF32LEData U_ICU_ENTRY_POINT_RENAME(_UTF32LEData)
#define _UTF7Data U_ICU_ENTRY_POINT_RENAME(_UTF7Data)
#define _UTF8Data U_ICU_ENTRY_POINT_RENAME(_UTF8Data)
#define _isUnicodeLocaleTypeSubtag U_ICU_ENTRY_POINT_RENAME(_isUnicodeLocaleTypeSubtag)
#define allowedHourFormatsCleanup U_ICU_ENTRY_POINT_RENAME(allowedHourFormatsCleanup)
#define cmemory_cleanup U_ICU_ENTRY_POINT_RENAME(cmemory_cleanup)
#define dayPeriodRulesCleanup U_ICU_ENTRY_POINT_RENAME(dayPeriodRulesCleanup)
#define deleteAllowedHourFormats U_ICU_ENTRY_POINT_RENAME(deleteAllowedHourFormats)
#define gTimeZoneFilesInitOnce U_ICU_ENTRY_POINT_RENAME(gTimeZoneFilesInitOnce)
#define initNumsysNames U_ICU_ENTRY_POINT_RENAME(initNumsysNames)
#define izrule_clone U_ICU_ENTRY_POINT_RENAME(izrule_clone)
#define izrule_close U_ICU_ENTRY_POINT_RENAME(izrule_close)
#define izrule_equals U_ICU_ENTRY_POINT_RENAME(izrule_equals)
#define izrule_getDSTSavings U_ICU_ENTRY_POINT_RENAME(izrule_getDSTSavings)
#define izrule_getDynamicClassID U_ICU_ENTRY_POINT_RENAME(izrule_getDynamicClassID)
#define izrule_getFinalStart U_ICU_ENTRY_POINT_RENAME(izrule_getFinalStart)
#define izrule_getFirstStart U_ICU_ENTRY_POINT_RENAME(izrule_getFirstStart)
#define izrule_getName U_ICU_ENTRY_POINT_RENAME(izrule_getName)
#define izrule_getNextStart U_ICU_ENTRY_POINT_RENAME(izrule_getNextStart)
#define izrule_getPreviousStart U_ICU_ENTRY_POINT_RENAME(izrule_getPreviousStart)
#define izrule_getRawOffset U_ICU_ENTRY_POINT_RENAME(izrule_getRawOffset)
#define izrule_getStaticClassID U_ICU_ENTRY_POINT_RENAME(izrule_getStaticClassID)
#define izrule_isEquivalentTo U_ICU_ENTRY_POINT_RENAME(izrule_isEquivalentTo)
#define izrule_open U_ICU_ENTRY_POINT_RENAME(izrule_open)
#define locale_getKeywordsStart U_ICU_ENTRY_POINT_RENAME(locale_getKeywordsStart)
#define locale_get_default U_ICU_ENTRY_POINT_RENAME(locale_get_default)
#define locale_set_default U_ICU_ENTRY_POINT_RENAME(locale_set_default)
#define numSysCleanup U_ICU_ENTRY_POINT_RENAME(numSysCleanup)
#define rbbi_cleanup U_ICU_ENTRY_POINT_RENAME(rbbi_cleanup)
#define pl_addFontRun U_ICU_ENTRY_POINT_RENAME(pl_addFontRun)
#define pl_addLocaleRun U_ICU_ENTRY_POINT_RENAME(pl_addLocaleRun)
#define pl_addValueRun U_ICU_ENTRY_POINT_RENAME(pl_addValueRun)
#define pl_close U_ICU_ENTRY_POINT_RENAME(pl_close)
#define pl_closeFontRuns U_ICU_ENTRY_POINT_RENAME(pl_closeFontRuns)
#define pl_closeLine U_ICU_ENTRY_POINT_RENAME(pl_closeLine)
#define pl_closeLocaleRuns U_ICU_ENTRY_POINT_RENAME(pl_closeLocaleRuns)
#define pl_closeValueRuns U_ICU_ENTRY_POINT_RENAME(pl_closeValueRuns)
#define pl_countLineRuns U_ICU_ENTRY_POINT_RENAME(pl_countLineRuns)
#define pl_create U_ICU_ENTRY_POINT_RENAME(pl_create)
#define pl_getAscent U_ICU_ENTRY_POINT_RENAME(pl_getAscent)
#define pl_getDescent U_ICU_ENTRY_POINT_RENAME(pl_getDescent)
#define pl_getFontRunCount U_ICU_ENTRY_POINT_RENAME(pl_getFontRunCount)
#define pl_getFontRunFont U_ICU_ENTRY_POINT_RENAME(pl_getFontRunFont)
#define pl_getFontRunLastLimit U_ICU_ENTRY_POINT_RENAME(pl_getFontRunLastLimit)
#define pl_getFontRunLimit U_ICU_ENTRY_POINT_RENAME(pl_getFontRunLimit)
#define pl_getLeading U_ICU_ENTRY_POINT_RENAME(pl_getLeading)
#define pl_getLineAscent U_ICU_ENTRY_POINT_RENAME(pl_getLineAscent)
#define pl_getLineDescent U_ICU_ENTRY_POINT_RENAME(pl_getLineDescent)
#define pl_getLineLeading U_ICU_ENTRY_POINT_RENAME(pl_getLineLeading)
#define pl_getLineVisualRun U_ICU_ENTRY_POINT_RENAME(pl_getLineVisualRun)
#define pl_getLineWidth U_ICU_ENTRY_POINT_RENAME(pl_getLineWidth)
#define pl_getLocaleRunCount U_ICU_ENTRY_POINT_RENAME(pl_getLocaleRunCount)
#define pl_getLocaleRunLastLimit U_ICU_ENTRY_POINT_RENAME(pl_getLocaleRunLastLimit)
#define pl_getLocaleRunLimit U_ICU_ENTRY_POINT_RENAME(pl_getLocaleRunLimit)
#define pl_getLocaleRunLocale U_ICU_ENTRY_POINT_RENAME(pl_getLocaleRunLocale)
#define pl_getParagraphLevel U_ICU_ENTRY_POINT_RENAME(pl_getParagraphLevel)
#define pl_getTextDirection U_ICU_ENTRY_POINT_RENAME(pl_getTextDirection)
#define pl_getValueRunCount U_ICU_ENTRY_POINT_RENAME(pl_getValueRunCount)
#define pl_getValueRunLastLimit U_ICU_ENTRY_POINT_RENAME(pl_getValueRunLastLimit)
#define pl_getValueRunLimit U_ICU_ENTRY_POINT_RENAME(pl_getValueRunLimit)
#define pl_getValueRunValue U_ICU_ENTRY_POINT_RENAME(pl_getValueRunValue)
#define pl_getVisualRunAscent U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunAscent)
#define pl_getVisualRunDescent U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunDescent)
#define pl_getVisualRunDirection U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunDirection)
#define pl_getVisualRunFont U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunFont)
#define pl_getVisualRunGlyphCount U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunGlyphCount)
#define pl_getVisualRunGlyphToCharMap U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunGlyphToCharMap)
#define pl_getVisualRunGlyphs U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunGlyphs)
#define pl_getVisualRunLeading U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunLeading)
#define pl_getVisualRunPositions U_ICU_ENTRY_POINT_RENAME(pl_getVisualRunPositions)
#define pl_isComplex U_ICU_ENTRY_POINT_RENAME(pl_isComplex)
#define pl_nextLine U_ICU_ENTRY_POINT_RENAME(pl_nextLine)
#define pl_openEmptyFontRuns U_ICU_ENTRY_POINT_RENAME(pl_openEmptyFontRuns)
#define pl_openEmptyLocaleRuns U_ICU_ENTRY_POINT_RENAME(pl_openEmptyLocaleRuns)
#define pl_openEmptyValueRuns U_ICU_ENTRY_POINT_RENAME(pl_openEmptyValueRuns)
#define pl_openFontRuns U_ICU_ENTRY_POINT_RENAME(pl_openFontRuns)
#define pl_openLocaleRuns U_ICU_ENTRY_POINT_RENAME(pl_openLocaleRuns)
#define pl_openValueRuns U_ICU_ENTRY_POINT_RENAME(pl_openValueRuns)
#define pl_reflow U_ICU_ENTRY_POINT_RENAME(pl_reflow)
#define pl_resetFontRuns U_ICU_ENTRY_POINT_RENAME(pl_resetFontRuns)
#define pl_resetLocaleRuns U_ICU_ENTRY_POINT_RENAME(pl_resetLocaleRuns)
#define pl_resetValueRuns U_ICU_ENTRY_POINT_RENAME(pl_resetValueRuns)
#define res_countArrayItems U_ICU_ENTRY_POINT_RENAME(res_countArrayItems)
#define res_findResource U_ICU_ENTRY_POINT_RENAME(res_findResource)
#define res_getAlias U_ICU_ENTRY_POINT_RENAME(res_getAlias)
#define res_getArrayItem U_ICU_ENTRY_POINT_RENAME(res_getArrayItem)
#define res_getBinaryNoTrace U_ICU_ENTRY_POINT_RENAME(res_getBinaryNoTrace)
#define res_getIntVectorNoTrace U_ICU_ENTRY_POINT_RENAME(res_getIntVectorNoTrace)
#define res_getPublicType U_ICU_ENTRY_POINT_RENAME(res_getPublicType)
#define res_getResource U_ICU_ENTRY_POINT_RENAME(res_getResource)
#define res_getStringNoTrace U_ICU_ENTRY_POINT_RENAME(res_getStringNoTrace)
#define res_getTableItemByIndex U_ICU_ENTRY_POINT_RENAME(res_getTableItemByIndex)
#define res_getTableItemByKey U_ICU_ENTRY_POINT_RENAME(res_getTableItemByKey)
#define res_load U_ICU_ENTRY_POINT_RENAME(res_load)
#define res_read U_ICU_ENTRY_POINT_RENAME(res_read)
#define res_unload U_ICU_ENTRY_POINT_RENAME(res_unload)
#define u_UCharsToChars U_ICU_ENTRY_POINT_RENAME(u_UCharsToChars)
#define u_asciiToUpper U_ICU_ENTRY_POINT_RENAME(u_asciiToUpper)
#define u_austrcpy U_ICU_ENTRY_POINT_RENAME(u_austrcpy)
#define u_austrncpy U_ICU_ENTRY_POINT_RENAME(u_austrncpy)
#define u_caseInsensitivePrefixMatch U_ICU_ENTRY_POINT_RENAME(u_caseInsensitivePrefixMatch)
#define u_catclose U_ICU_ENTRY_POINT_RENAME(u_catclose)
#define u_catgets U_ICU_ENTRY_POINT_RENAME(u_catgets)
#define u_catopen U_ICU_ENTRY_POINT_RENAME(u_catopen)
#define u_charAge U_ICU_ENTRY_POINT_RENAME(u_charAge)
#define u_charDigitValue U_ICU_ENTRY_POINT_RENAME(u_charDigitValue)
#define u_charDirection U_ICU_ENTRY_POINT_RENAME(u_charDirection)
#define u_charFromName U_ICU_ENTRY_POINT_RENAME(u_charFromName)
#define u_charMirror U_ICU_ENTRY_POINT_RENAME(u_charMirror)
#define u_charName U_ICU_ENTRY_POINT_RENAME(u_charName)
#define u_charType U_ICU_ENTRY_POINT_RENAME(u_charType)
#define u_charsToUChars U_ICU_ENTRY_POINT_RENAME(u_charsToUChars)
#define u_cleanup U_ICU_ENTRY_POINT_RENAME(u_cleanup)
#define u_countChar32 U_ICU_ENTRY_POINT_RENAME(u_countChar32)
#define u_digit U_ICU_ENTRY_POINT_RENAME(u_digit)
#define u_enumCharNames U_ICU_ENTRY_POINT_RENAME(u_enumCharNames)
#define u_enumCharTypes U_ICU_ENTRY_POINT_RENAME(u_enumCharTypes)
#define u_errorName U_ICU_ENTRY_POINT_RENAME(u_errorName)
#define u_fadopt U_ICU_ENTRY_POINT_RENAME(u_fadopt)
#define u_fclose U_ICU_ENTRY_POINT_RENAME(u_fclose)
#define u_feof U_ICU_ENTRY_POINT_RENAME(u_feof)
#define u_fflush U_ICU_ENTRY_POINT_RENAME(u_fflush)
#define u_fgetConverter U_ICU_ENTRY_POINT_RENAME(u_fgetConverter)
#define u_fgetNumberFormat U_ICU_ENTRY_POINT_RENAME(u_fgetNumberFormat)
#define u_fgetc U_ICU_ENTRY_POINT_RENAME(u_fgetc)
#define u_fgetcodepage U_ICU_ENTRY_POINT_RENAME(u_fgetcodepage)
#define u_fgetcx U_ICU_ENTRY_POINT_RENAME(u_fgetcx)
#define u_fgetfile U_ICU_ENTRY_POINT_RENAME(u_fgetfile)
#define u_fgetlocale U_ICU_ENTRY_POINT_RENAME(u_fgetlocale)
#define u_fgets U_ICU_ENTRY_POINT_RENAME(u_fgets)
#define u_file_read U_ICU_ENTRY_POINT_RENAME(u_file_read)
#define u_file_write U_ICU_ENTRY_POINT_RENAME(u_file_write)
#define u_file_write_flush U_ICU_ENTRY_POINT_RENAME(u_file_write_flush)
#define u_finit U_ICU_ENTRY_POINT_RENAME(u_finit)
#define u_flushDefaultConverter U_ICU_ENTRY_POINT_RENAME(u_flushDefaultConverter)
#define u_foldCase U_ICU_ENTRY_POINT_RENAME(u_foldCase)
#define u_fopen U_ICU_ENTRY_POINT_RENAME(u_fopen)
#define u_fopen_u U_ICU_ENTRY_POINT_RENAME(u_fopen_u)
#define u_forDigit U_ICU_ENTRY_POINT_RENAME(u_forDigit)
#define u_formatMessage U_ICU_ENTRY_POINT_RENAME(u_formatMessage)
#define u_formatMessageWithError U_ICU_ENTRY_POINT_RENAME(u_formatMessageWithError)
#define u_fprintf U_ICU_ENTRY_POINT_RENAME(u_fprintf)
#define u_fprintf_u U_ICU_ENTRY_POINT_RENAME(u_fprintf_u)
#define u_fputc U_ICU_ENTRY_POINT_RENAME(u_fputc)
#define u_fputs U_ICU_ENTRY_POINT_RENAME(u_fputs)
#define u_frewind U_ICU_ENTRY_POINT_RENAME(u_frewind)
#define u_fscanf U_ICU_ENTRY_POINT_RENAME(u_fscanf)
#define u_fscanf_u U_ICU_ENTRY_POINT_RENAME(u_fscanf_u)
#define u_fsetcodepage U_ICU_ENTRY_POINT_RENAME(u_fsetcodepage)
#define u_fsetlocale U_ICU_ENTRY_POINT_RENAME(u_fsetlocale)
#define u_fsettransliterator U_ICU_ENTRY_POINT_RENAME(u_fsettransliterator)
#define u_fstropen U_ICU_ENTRY_POINT_RENAME(u_fstropen)
#define u_fungetc U_ICU_ENTRY_POINT_RENAME(u_fungetc)
#define u_getBidiPairedBracket U_ICU_ENTRY_POINT_RENAME(u_getBidiPairedBracket)
#define u_getBinaryPropertySet U_ICU_ENTRY_POINT_RENAME(u_getBinaryPropertySet)
#define u_getCombiningClass U_ICU_ENTRY_POINT_RENAME(u_getCombiningClass)
#define u_getDataDirectory U_ICU_ENTRY_POINT_RENAME(u_getDataDirectory)
#define u_getDataVersion U_ICU_ENTRY_POINT_RENAME(u_getDataVersion)
#define u_getDefaultConverter U_ICU_ENTRY_POINT_RENAME(u_getDefaultConverter)
#define u_getFC_NFKC_Closure U_ICU_ENTRY_POINT_RENAME(u_getFC_NFKC_Closure)
#define u_getISOComment U_ICU_ENTRY_POINT_RENAME(u_getISOComment)
#define u_getIntPropertyMap U_ICU_ENTRY_POINT_RENAME(u_getIntPropertyMap)
#define u_getIntPropertyMaxValue U_ICU_ENTRY_POINT_RENAME(u_getIntPropertyMaxValue)
#define u_getIntPropertyMinValue U_ICU_ENTRY_POINT_RENAME(u_getIntPropertyMinValue)
#define u_getIntPropertyValue U_ICU_ENTRY_POINT_RENAME(u_getIntPropertyValue)
#define u_getMainProperties U_ICU_ENTRY_POINT_RENAME(u_getMainProperties)
#define u_getNumericValue U_ICU_ENTRY_POINT_RENAME(u_getNumericValue)
#define u_getPropertyEnum U_ICU_ENTRY_POINT_RENAME(u_getPropertyEnum)
#define u_getPropertyName U_ICU_ENTRY_POINT_RENAME(u_getPropertyName)
#define u_getPropertyValueEnum U_ICU_ENTRY_POINT_RENAME(u_getPropertyValueEnum)
#define u_getPropertyValueName U_ICU_ENTRY_POINT_RENAME(u_getPropertyValueName)
#define u_getTimeZoneFilesDirectory U_ICU_ENTRY_POINT_RENAME(u_getTimeZoneFilesDirectory)
#define u_getUnicodeProperties U_ICU_ENTRY_POINT_RENAME(u_getUnicodeProperties)
#define u_getUnicodeVersion U_ICU_ENTRY_POINT_RENAME(u_getUnicodeVersion)
#define u_getVersion U_ICU_ENTRY_POINT_RENAME(u_getVersion)
#define u_get_stdout U_ICU_ENTRY_POINT_RENAME(u_get_stdout)
#define u_hasBinaryProperty U_ICU_ENTRY_POINT_RENAME(u_hasBinaryProperty)
#define u_init U_ICU_ENTRY_POINT_RENAME(u_init)
#define u_isIDIgnorable U_ICU_ENTRY_POINT_RENAME(u_isIDIgnorable)
#define u_isIDPart U_ICU_ENTRY_POINT_RENAME(u_isIDPart)
#define u_isIDStart U_ICU_ENTRY_POINT_RENAME(u_isIDStart)
#define u_isISOControl U_ICU_ENTRY_POINT_RENAME(u_isISOControl)
#define u_isJavaIDPart U_ICU_ENTRY_POINT_RENAME(u_isJavaIDPart)
#define u_isJavaIDStart U_ICU_ENTRY_POINT_RENAME(u_isJavaIDStart)
#define u_isJavaSpaceChar U_ICU_ENTRY_POINT_RENAME(u_isJavaSpaceChar)
#define u_isMirrored U_ICU_ENTRY_POINT_RENAME(u_isMirrored)
#define u_isUAlphabetic U_ICU_ENTRY_POINT_RENAME(u_isUAlphabetic)
#define u_isULowercase U_ICU_ENTRY_POINT_RENAME(u_isULowercase)
#define u_isUUppercase U_ICU_ENTRY_POINT_RENAME(u_isUUppercase)
#define u_isUWhiteSpace U_ICU_ENTRY_POINT_RENAME(u_isUWhiteSpace)
#define u_isWhitespace U_ICU_ENTRY_POINT_RENAME(u_isWhitespace)
#define u_isalnum U_ICU_ENTRY_POINT_RENAME(u_isalnum)
#define u_isalnumPOSIX U_ICU_ENTRY_POINT_RENAME(u_isalnumPOSIX)
#define u_isalpha U_ICU_ENTRY_POINT_RENAME(u_isalpha)
#define u_isbase U_ICU_ENTRY_POINT_RENAME(u_isbase)
#define u_isblank U_ICU_ENTRY_POINT_RENAME(u_isblank)
#define u_iscntrl U_ICU_ENTRY_POINT_RENAME(u_iscntrl)
#define u_isdefined U_ICU_ENTRY_POINT_RENAME(u_isdefined)
#define u_isdigit U_ICU_ENTRY_POINT_RENAME(u_isdigit)
#define u_isgraph U_ICU_ENTRY_POINT_RENAME(u_isgraph)
#define u_isgraphPOSIX U_ICU_ENTRY_POINT_RENAME(u_isgraphPOSIX)
#define u_islower U_ICU_ENTRY_POINT_RENAME(u_islower)
#define u_isprint U_ICU_ENTRY_POINT_RENAME(u_isprint)
#define u_isprintPOSIX U_ICU_ENTRY_POINT_RENAME(u_isprintPOSIX)
#define u_ispunct U_ICU_ENTRY_POINT_RENAME(u_ispunct)
#define u_isspace U_ICU_ENTRY_POINT_RENAME(u_isspace)
#define u_istitle U_ICU_ENTRY_POINT_RENAME(u_istitle)
#define u_isupper U_ICU_ENTRY_POINT_RENAME(u_isupper)
#define u_isxdigit U_ICU_ENTRY_POINT_RENAME(u_isxdigit)
#define u_locbund_close U_ICU_ENTRY_POINT_RENAME(u_locbund_close)
#define u_locbund_getNumberFormat U_ICU_ENTRY_POINT_RENAME(u_locbund_getNumberFormat)
#define u_locbund_init U_ICU_ENTRY_POINT_RENAME(u_locbund_init)
#define u_memcasecmp U_ICU_ENTRY_POINT_RENAME(u_memcasecmp)
#define u_memchr U_ICU_ENTRY_POINT_RENAME(u_memchr)
#define u_memchr32 U_ICU_ENTRY_POINT_RENAME(u_memchr32)
#define u_memcmp U_ICU_ENTRY_POINT_RENAME(u_memcmp)
#define u_memcmpCodePointOrder U_ICU_ENTRY_POINT_RENAME(u_memcmpCodePointOrder)
#define u_memcpy U_ICU_ENTRY_POINT_RENAME(u_memcpy)
#define u_memmove U_ICU_ENTRY_POINT_RENAME(u_memmove)
#define u_memrchr U_ICU_ENTRY_POINT_RENAME(u_memrchr)
#define u_memrchr32 U_ICU_ENTRY_POINT_RENAME(u_memrchr32)
#define u_memset U_ICU_ENTRY_POINT_RENAME(u_memset)
#define u_parseMessage U_ICU_ENTRY_POINT_RENAME(u_parseMessage)
#define u_parseMessageWithError U_ICU_ENTRY_POINT_RENAME(u_parseMessageWithError)
#define u_printf U_ICU_ENTRY_POINT_RENAME(u_printf)
#define u_printf_parse U_ICU_ENTRY_POINT_RENAME(u_printf_parse)
#define u_printf_u U_ICU_ENTRY_POINT_RENAME(u_printf_u)
#define u_releaseDefaultConverter U_ICU_ENTRY_POINT_RENAME(u_releaseDefaultConverter)
#define u_scanf_parse U_ICU_ENTRY_POINT_RENAME(u_scanf_parse)
#define u_setAtomicIncDecFunctions U_ICU_ENTRY_POINT_RENAME(u_setAtomicIncDecFunctions)
#define u_setDataDirectory U_ICU_ENTRY_POINT_RENAME(u_setDataDirectory)
#define u_setMemoryFunctions U_ICU_ENTRY_POINT_RENAME(u_setMemoryFunctions)
#define u_setMutexFunctions U_ICU_ENTRY_POINT_RENAME(u_setMutexFunctions)
#define u_setTimeZoneFilesDirectory U_ICU_ENTRY_POINT_RENAME(u_setTimeZoneFilesDirectory)
#define u_shapeArabic U_ICU_ENTRY_POINT_RENAME(u_shapeArabic)
#define u_snprintf U_ICU_ENTRY_POINT_RENAME(u_snprintf)
#define u_snprintf_u U_ICU_ENTRY_POINT_RENAME(u_snprintf_u)
#define u_sprintf U_ICU_ENTRY_POINT_RENAME(u_sprintf)
#define u_sprintf_u U_ICU_ENTRY_POINT_RENAME(u_sprintf_u)
#define u_sscanf U_ICU_ENTRY_POINT_RENAME(u_sscanf)
#define u_sscanf_u U_ICU_ENTRY_POINT_RENAME(u_sscanf_u)
#define u_strCaseCompare U_ICU_ENTRY_POINT_RENAME(u_strCaseCompare)
#define u_strCompare U_ICU_ENTRY_POINT_RENAME(u_strCompare)
#define u_strCompareIter U_ICU_ENTRY_POINT_RENAME(u_strCompareIter)
#define u_strFindFirst U_ICU_ENTRY_POINT_RENAME(u_strFindFirst)
#define u_strFindLast U_ICU_ENTRY_POINT_RENAME(u_strFindLast)
#define u_strFoldCase U_ICU_ENTRY_POINT_RENAME(u_strFoldCase)
#define u_strFromJavaModifiedUTF8WithSub U_ICU_ENTRY_POINT_RENAME(u_strFromJavaModifiedUTF8WithSub)
#define u_strFromPunycode U_ICU_ENTRY_POINT_RENAME(u_strFromPunycode)
#define u_strFromUTF32 U_ICU_ENTRY_POINT_RENAME(u_strFromUTF32)
#define u_strFromUTF32WithSub U_ICU_ENTRY_POINT_RENAME(u_strFromUTF32WithSub)
#define u_strFromUTF8 U_ICU_ENTRY_POINT_RENAME(u_strFromUTF8)
#define u_strFromUTF8Lenient U_ICU_ENTRY_POINT_RENAME(u_strFromUTF8Lenient)
#define u_strFromUTF8WithSub U_ICU_ENTRY_POINT_RENAME(u_strFromUTF8WithSub)
#define u_strFromWCS U_ICU_ENTRY_POINT_RENAME(u_strFromWCS)
#define u_strHasMoreChar32Than U_ICU_ENTRY_POINT_RENAME(u_strHasMoreChar32Than)
#define u_strToJavaModifiedUTF8 U_ICU_ENTRY_POINT_RENAME(u_strToJavaModifiedUTF8)
#define u_strToLower U_ICU_ENTRY_POINT_RENAME(u_strToLower)
#define u_strToPunycode U_ICU_ENTRY_POINT_RENAME(u_strToPunycode)
#define u_strToTitle U_ICU_ENTRY_POINT_RENAME(u_strToTitle)
#define u_strToUTF32 U_ICU_ENTRY_POINT_RENAME(u_strToUTF32)
#define u_strToUTF32WithSub U_ICU_ENTRY_POINT_RENAME(u_strToUTF32WithSub)
#define u_strToUTF8 U_ICU_ENTRY_POINT_RENAME(u_strToUTF8)
#define u_strToUTF8WithSub U_ICU_ENTRY_POINT_RENAME(u_strToUTF8WithSub)
#define u_strToUpper U_ICU_ENTRY_POINT_RENAME(u_strToUpper)
#define u_strToWCS U_ICU_ENTRY_POINT_RENAME(u_strToWCS)
#define u_strcasecmp U_ICU_ENTRY_POINT_RENAME(u_strcasecmp)
#define u_strcat U_ICU_ENTRY_POINT_RENAME(u_strcat)
#define u_strchr U_ICU_ENTRY_POINT_RENAME(u_strchr)
#define u_strchr32 U_ICU_ENTRY_POINT_RENAME(u_strchr32)
#define u_strcmp U_ICU_ENTRY_POINT_RENAME(u_strcmp)
#define u_strcmpCodePointOrder U_ICU_ENTRY_POINT_RENAME(u_strcmpCodePointOrder)
#define u_strcmpFold U_ICU_ENTRY_POINT_RENAME(u_strcmpFold)
#define u_strcpy U_ICU_ENTRY_POINT_RENAME(u_strcpy)
#define u_strcspn U_ICU_ENTRY_POINT_RENAME(u_strcspn)
#define u_strlen U_ICU_ENTRY_POINT_RENAME(u_strlen)
#define u_strncasecmp U_ICU_ENTRY_POINT_RENAME(u_strncasecmp)
#define u_strncat U_ICU_ENTRY_POINT_RENAME(u_strncat)
#define u_strncmp U_ICU_ENTRY_POINT_RENAME(u_strncmp)
#define u_strncmpCodePointOrder U_ICU_ENTRY_POINT_RENAME(u_strncmpCodePointOrder)
#define u_strncpy U_ICU_ENTRY_POINT_RENAME(u_strncpy)
#define u_strpbrk U_ICU_ENTRY_POINT_RENAME(u_strpbrk)
#define u_strrchr U_ICU_ENTRY_POINT_RENAME(u_strrchr)
#define u_strrchr32 U_ICU_ENTRY_POINT_RENAME(u_strrchr32)
#define u_strrstr U_ICU_ENTRY_POINT_RENAME(u_strrstr)
#define u_strspn U_ICU_ENTRY_POINT_RENAME(u_strspn)
#define u_strstr U_ICU_ENTRY_POINT_RENAME(u_strstr)
#define u_strtok_r U_ICU_ENTRY_POINT_RENAME(u_strtok_r)
#define u_terminateChars U_ICU_ENTRY_POINT_RENAME(u_terminateChars)
#define u_terminateUChar32s U_ICU_ENTRY_POINT_RENAME(u_terminateUChar32s)
#define u_terminateUChars U_ICU_ENTRY_POINT_RENAME(u_terminateUChars)
#define u_terminateWChars U_ICU_ENTRY_POINT_RENAME(u_terminateWChars)
#define u_tolower U_ICU_ENTRY_POINT_RENAME(u_tolower)
#define u_totitle U_ICU_ENTRY_POINT_RENAME(u_totitle)
#define u_toupper U_ICU_ENTRY_POINT_RENAME(u_toupper)
#define u_uastrcpy U_ICU_ENTRY_POINT_RENAME(u_uastrcpy)
#define u_uastrncpy U_ICU_ENTRY_POINT_RENAME(u_uastrncpy)
#define u_unescape U_ICU_ENTRY_POINT_RENAME(u_unescape)
#define u_unescapeAt U_ICU_ENTRY_POINT_RENAME(u_unescapeAt)
#define u_versionFromString U_ICU_ENTRY_POINT_RENAME(u_versionFromString)
#define u_versionFromUString U_ICU_ENTRY_POINT_RENAME(u_versionFromUString)
#define u_versionToString U_ICU_ENTRY_POINT_RENAME(u_versionToString)
#define u_vformatMessage U_ICU_ENTRY_POINT_RENAME(u_vformatMessage)
#define u_vformatMessageWithError U_ICU_ENTRY_POINT_RENAME(u_vformatMessageWithError)
#define u_vfprintf U_ICU_ENTRY_POINT_RENAME(u_vfprintf)
#define u_vfprintf_u U_ICU_ENTRY_POINT_RENAME(u_vfprintf_u)
#define u_vfscanf U_ICU_ENTRY_POINT_RENAME(u_vfscanf)
#define u_vfscanf_u U_ICU_ENTRY_POINT_RENAME(u_vfscanf_u)
#define u_vparseMessage U_ICU_ENTRY_POINT_RENAME(u_vparseMessage)
#define u_vparseMessageWithError U_ICU_ENTRY_POINT_RENAME(u_vparseMessageWithError)
#define u_vsnprintf U_ICU_ENTRY_POINT_RENAME(u_vsnprintf)
#define u_vsnprintf_u U_ICU_ENTRY_POINT_RENAME(u_vsnprintf_u)
#define u_vsprintf U_ICU_ENTRY_POINT_RENAME(u_vsprintf)
#define u_vsprintf_u U_ICU_ENTRY_POINT_RENAME(u_vsprintf_u)
#define u_vsscanf U_ICU_ENTRY_POINT_RENAME(u_vsscanf)
#define u_vsscanf_u U_ICU_ENTRY_POINT_RENAME(u_vsscanf_u)
#define u_writeIdenticalLevelRun U_ICU_ENTRY_POINT_RENAME(u_writeIdenticalLevelRun)
#define ubidi_addPropertyStarts U_ICU_ENTRY_POINT_RENAME(ubidi_addPropertyStarts)
#define ubidi_close U_ICU_ENTRY_POINT_RENAME(ubidi_close)
#define ubidi_countParagraphs U_ICU_ENTRY_POINT_RENAME(ubidi_countParagraphs)
#define ubidi_countRuns U_ICU_ENTRY_POINT_RENAME(ubidi_countRuns)
#define ubidi_getBaseDirection U_ICU_ENTRY_POINT_RENAME(ubidi_getBaseDirection)
#define ubidi_getClass U_ICU_ENTRY_POINT_RENAME(ubidi_getClass)
#define ubidi_getClassCallback U_ICU_ENTRY_POINT_RENAME(ubidi_getClassCallback)
#define ubidi_getCustomizedClass U_ICU_ENTRY_POINT_RENAME(ubidi_getCustomizedClass)
#define ubidi_getDirection U_ICU_ENTRY_POINT_RENAME(ubidi_getDirection)
#define ubidi_getJoiningGroup U_ICU_ENTRY_POINT_RENAME(ubidi_getJoiningGroup)
#define ubidi_getJoiningType U_ICU_ENTRY_POINT_RENAME(ubidi_getJoiningType)
#define ubidi_getLength U_ICU_ENTRY_POINT_RENAME(ubidi_getLength)
#define ubidi_getLevelAt U_ICU_ENTRY_POINT_RENAME(ubidi_getLevelAt)
#define ubidi_getLevels U_ICU_ENTRY_POINT_RENAME(ubidi_getLevels)
#define ubidi_getLogicalIndex U_ICU_ENTRY_POINT_RENAME(ubidi_getLogicalIndex)
#define ubidi_getLogicalMap U_ICU_ENTRY_POINT_RENAME(ubidi_getLogicalMap)
#define ubidi_getLogicalRun U_ICU_ENTRY_POINT_RENAME(ubidi_getLogicalRun)
#define ubidi_getMaxValue U_ICU_ENTRY_POINT_RENAME(ubidi_getMaxValue)
#define ubidi_getMemory U_ICU_ENTRY_POINT_RENAME(ubidi_getMemory)
#define ubidi_getMirror U_ICU_ENTRY_POINT_RENAME(ubidi_getMirror)
#define ubidi_getPairedBracket U_ICU_ENTRY_POINT_RENAME(ubidi_getPairedBracket)
#define ubidi_getPairedBracketType U_ICU_ENTRY_POINT_RENAME(ubidi_getPairedBracketType)
#define ubidi_getParaLevel U_ICU_ENTRY_POINT_RENAME(ubidi_getParaLevel)
#define ubidi_getParaLevelAtIndex U_ICU_ENTRY_POINT_RENAME(ubidi_getParaLevelAtIndex)
#define ubidi_getParagraph U_ICU_ENTRY_POINT_RENAME(ubidi_getParagraph)
#define ubidi_getParagraphByIndex U_ICU_ENTRY_POINT_RENAME(ubidi_getParagraphByIndex)
#define ubidi_getProcessedLength U_ICU_ENTRY_POINT_RENAME(ubidi_getProcessedLength)
#define ubidi_getReorderingMode U_ICU_ENTRY_POINT_RENAME(ubidi_getReorderingMode)
#define ubidi_getReorderingOptions U_ICU_ENTRY_POINT_RENAME(ubidi_getReorderingOptions)
#define ubidi_getResultLength U_ICU_ENTRY_POINT_RENAME(ubidi_getResultLength)
#define ubidi_getRuns U_ICU_ENTRY_POINT_RENAME(ubidi_getRuns)
#define ubidi_getText U_ICU_ENTRY_POINT_RENAME(ubidi_getText)
#define ubidi_getVisualIndex U_ICU_ENTRY_POINT_RENAME(ubidi_getVisualIndex)
#define ubidi_getVisualMap U_ICU_ENTRY_POINT_RENAME(ubidi_getVisualMap)
#define ubidi_getVisualRun U_ICU_ENTRY_POINT_RENAME(ubidi_getVisualRun)
#define ubidi_invertMap U_ICU_ENTRY_POINT_RENAME(ubidi_invertMap)
#define ubidi_isBidiControl U_ICU_ENTRY_POINT_RENAME(ubidi_isBidiControl)
#define ubidi_isInverse U_ICU_ENTRY_POINT_RENAME(ubidi_isInverse)
#define ubidi_isJoinControl U_ICU_ENTRY_POINT_RENAME(ubidi_isJoinControl)
#define ubidi_isMirrored U_ICU_ENTRY_POINT_RENAME(ubidi_isMirrored)
#define ubidi_isOrderParagraphsLTR U_ICU_ENTRY_POINT_RENAME(ubidi_isOrderParagraphsLTR)
#define ubidi_open U_ICU_ENTRY_POINT_RENAME(ubidi_open)
#define ubidi_openSized U_ICU_ENTRY_POINT_RENAME(ubidi_openSized)
#define ubidi_orderParagraphsLTR U_ICU_ENTRY_POINT_RENAME(ubidi_orderParagraphsLTR)
#define ubidi_reorderLogical U_ICU_ENTRY_POINT_RENAME(ubidi_reorderLogical)
#define ubidi_reorderVisual U_ICU_ENTRY_POINT_RENAME(ubidi_reorderVisual)
#define ubidi_setClassCallback U_ICU_ENTRY_POINT_RENAME(ubidi_setClassCallback)
#define ubidi_setContext U_ICU_ENTRY_POINT_RENAME(ubidi_setContext)
#define ubidi_setInverse U_ICU_ENTRY_POINT_RENAME(ubidi_setInverse)
#define ubidi_setLine U_ICU_ENTRY_POINT_RENAME(ubidi_setLine)
#define ubidi_setPara U_ICU_ENTRY_POINT_RENAME(ubidi_setPara)
#define ubidi_setReorderingMode U_ICU_ENTRY_POINT_RENAME(ubidi_setReorderingMode)
#define ubidi_setReorderingOptions U_ICU_ENTRY_POINT_RENAME(ubidi_setReorderingOptions)
#define ubidi_writeReordered U_ICU_ENTRY_POINT_RENAME(ubidi_writeReordered)
#define ubidi_writeReverse U_ICU_ENTRY_POINT_RENAME(ubidi_writeReverse)
#define ubiditransform_close U_ICU_ENTRY_POINT_RENAME(ubiditransform_close)
#define ubiditransform_open U_ICU_ENTRY_POINT_RENAME(ubiditransform_open)
#define ubiditransform_transform U_ICU_ENTRY_POINT_RENAME(ubiditransform_transform)
#define ublock_getCode U_ICU_ENTRY_POINT_RENAME(ublock_getCode)
#define ubrk_clone U_ICU_ENTRY_POINT_RENAME(ubrk_clone)
#define ubrk_close U_ICU_ENTRY_POINT_RENAME(ubrk_close)
#define ubrk_countAvailable U_ICU_ENTRY_POINT_RENAME(ubrk_countAvailable)
#define ubrk_current U_ICU_ENTRY_POINT_RENAME(ubrk_current)
#define ubrk_first U_ICU_ENTRY_POINT_RENAME(ubrk_first)
#define ubrk_following U_ICU_ENTRY_POINT_RENAME(ubrk_following)
#define ubrk_getAvailable U_ICU_ENTRY_POINT_RENAME(ubrk_getAvailable)
#define ubrk_getBinaryRules U_ICU_ENTRY_POINT_RENAME(ubrk_getBinaryRules)
#define ubrk_getLocaleByType U_ICU_ENTRY_POINT_RENAME(ubrk_getLocaleByType)
#define ubrk_getRuleStatus U_ICU_ENTRY_POINT_RENAME(ubrk_getRuleStatus)
#define ubrk_getRuleStatusVec U_ICU_ENTRY_POINT_RENAME(ubrk_getRuleStatusVec)
#define ubrk_isBoundary U_ICU_ENTRY_POINT_RENAME(ubrk_isBoundary)
#define ubrk_last U_ICU_ENTRY_POINT_RENAME(ubrk_last)
#define ubrk_next U_ICU_ENTRY_POINT_RENAME(ubrk_next)
#define ubrk_open U_ICU_ENTRY_POINT_RENAME(ubrk_open)
#define ubrk_openBinaryRules U_ICU_ENTRY_POINT_RENAME(ubrk_openBinaryRules)
#define ubrk_openRules U_ICU_ENTRY_POINT_RENAME(ubrk_openRules)
#define ubrk_preceding U_ICU_ENTRY_POINT_RENAME(ubrk_preceding)
#define ubrk_previous U_ICU_ENTRY_POINT_RENAME(ubrk_previous)
#define ubrk_refreshUText U_ICU_ENTRY_POINT_RENAME(ubrk_refreshUText)
#define ubrk_safeClone U_ICU_ENTRY_POINT_RENAME(ubrk_safeClone)
#define ubrk_setText U_ICU_ENTRY_POINT_RENAME(ubrk_setText)
#define ubrk_setUText U_ICU_ENTRY_POINT_RENAME(ubrk_setUText)
#define ubrk_swap U_ICU_ENTRY_POINT_RENAME(ubrk_swap)
#define ucache_compareKeys U_ICU_ENTRY_POINT_RENAME(ucache_compareKeys)
#define ucache_deleteKey U_ICU_ENTRY_POINT_RENAME(ucache_deleteKey)
#define ucache_hashKeys U_ICU_ENTRY_POINT_RENAME(ucache_hashKeys)
#define ucal_add U_ICU_ENTRY_POINT_RENAME(ucal_add)
#define ucal_clear U_ICU_ENTRY_POINT_RENAME(ucal_clear)
#define ucal_clearField U_ICU_ENTRY_POINT_RENAME(ucal_clearField)
#define ucal_clone U_ICU_ENTRY_POINT_RENAME(ucal_clone)
#define ucal_close U_ICU_ENTRY_POINT_RENAME(ucal_close)
#define ucal_countAvailable U_ICU_ENTRY_POINT_RENAME(ucal_countAvailable)
#define ucal_equivalentTo U_ICU_ENTRY_POINT_RENAME(ucal_equivalentTo)
#define ucal_get U_ICU_ENTRY_POINT_RENAME(ucal_get)
#define ucal_getAttribute U_ICU_ENTRY_POINT_RENAME(ucal_getAttribute)
#define ucal_getAvailable U_ICU_ENTRY_POINT_RENAME(ucal_getAvailable)
#define ucal_getCanonicalTimeZoneID U_ICU_ENTRY_POINT_RENAME(ucal_getCanonicalTimeZoneID)
#define ucal_getDSTSavings U_ICU_ENTRY_POINT_RENAME(ucal_getDSTSavings)
#define ucal_getDayOfWeekType U_ICU_ENTRY_POINT_RENAME(ucal_getDayOfWeekType)
#define ucal_getDefaultTimeZone U_ICU_ENTRY_POINT_RENAME(ucal_getDefaultTimeZone)
#define ucal_getFieldDifference U_ICU_ENTRY_POINT_RENAME(ucal_getFieldDifference)
#define ucal_getGregorianChange U_ICU_ENTRY_POINT_RENAME(ucal_getGregorianChange)
#define ucal_getHostTimeZone U_ICU_ENTRY_POINT_RENAME(ucal_getHostTimeZone)
#define ucal_getKeywordValuesForLocale U_ICU_ENTRY_POINT_RENAME(ucal_getKeywordValuesForLocale)
#define ucal_getLimit U_ICU_ENTRY_POINT_RENAME(ucal_getLimit)
#define ucal_getLocaleByType U_ICU_ENTRY_POINT_RENAME(ucal_getLocaleByType)
#define ucal_getMillis U_ICU_ENTRY_POINT_RENAME(ucal_getMillis)
#define ucal_getNow U_ICU_ENTRY_POINT_RENAME(ucal_getNow)
#define ucal_getTZDataVersion U_ICU_ENTRY_POINT_RENAME(ucal_getTZDataVersion)
#define ucal_getTimeZoneDisplayName U_ICU_ENTRY_POINT_RENAME(ucal_getTimeZoneDisplayName)
#define ucal_getTimeZoneID U_ICU_ENTRY_POINT_RENAME(ucal_getTimeZoneID)
#define ucal_getTimeZoneIDForWindowsID U_ICU_ENTRY_POINT_RENAME(ucal_getTimeZoneIDForWindowsID)
#define ucal_getTimeZoneOffsetFromLocal U_ICU_ENTRY_POINT_RENAME(ucal_getTimeZoneOffsetFromLocal)
#define ucal_getTimeZoneTransitionDate U_ICU_ENTRY_POINT_RENAME(ucal_getTimeZoneTransitionDate)
#define ucal_getType U_ICU_ENTRY_POINT_RENAME(ucal_getType)
#define ucal_getWeekendTransition U_ICU_ENTRY_POINT_RENAME(ucal_getWeekendTransition)
#define ucal_getWindowsTimeZoneID U_ICU_ENTRY_POINT_RENAME(ucal_getWindowsTimeZoneID)
#define ucal_inDaylightTime U_ICU_ENTRY_POINT_RENAME(ucal_inDaylightTime)
#define ucal_isSet U_ICU_ENTRY_POINT_RENAME(ucal_isSet)
#define ucal_isWeekend U_ICU_ENTRY_POINT_RENAME(ucal_isWeekend)
#define ucal_open U_ICU_ENTRY_POINT_RENAME(ucal_open)
#define ucal_openCountryTimeZones U_ICU_ENTRY_POINT_RENAME(ucal_openCountryTimeZones)
#define ucal_openTimeZoneIDEnumeration U_ICU_ENTRY_POINT_RENAME(ucal_openTimeZoneIDEnumeration)
#define ucal_openTimeZones U_ICU_ENTRY_POINT_RENAME(ucal_openTimeZones)
#define ucal_roll U_ICU_ENTRY_POINT_RENAME(ucal_roll)
#define ucal_set U_ICU_ENTRY_POINT_RENAME(ucal_set)
#define ucal_setAttribute U_ICU_ENTRY_POINT_RENAME(ucal_setAttribute)
#define ucal_setDate U_ICU_ENTRY_POINT_RENAME(ucal_setDate)
#define ucal_setDateTime U_ICU_ENTRY_POINT_RENAME(ucal_setDateTime)
#define ucal_setDefaultTimeZone U_ICU_ENTRY_POINT_RENAME(ucal_setDefaultTimeZone)
#define ucal_setGregorianChange U_ICU_ENTRY_POINT_RENAME(ucal_setGregorianChange)
#define ucal_setMillis U_ICU_ENTRY_POINT_RENAME(ucal_setMillis)
#define ucal_setTimeZone U_ICU_ENTRY_POINT_RENAME(ucal_setTimeZone)
#define ucase_addCaseClosure U_ICU_ENTRY_POINT_RENAME(ucase_addCaseClosure)
#define ucase_addPropertyStarts U_ICU_ENTRY_POINT_RENAME(ucase_addPropertyStarts)
#define ucase_addStringCaseClosure U_ICU_ENTRY_POINT_RENAME(ucase_addStringCaseClosure)
#define ucase_fold U_ICU_ENTRY_POINT_RENAME(ucase_fold)
#define ucase_getCaseLocale U_ICU_ENTRY_POINT_RENAME(ucase_getCaseLocale)
#define ucase_getTrie U_ICU_ENTRY_POINT_RENAME(ucase_getTrie)
#define ucase_getType U_ICU_ENTRY_POINT_RENAME(ucase_getType)
#define ucase_getTypeOrIgnorable U_ICU_ENTRY_POINT_RENAME(ucase_getTypeOrIgnorable)
#define ucase_hasBinaryProperty U_ICU_ENTRY_POINT_RENAME(ucase_hasBinaryProperty)
#define ucase_isCaseSensitive U_ICU_ENTRY_POINT_RENAME(ucase_isCaseSensitive)
#define ucase_isSoftDotted U_ICU_ENTRY_POINT_RENAME(ucase_isSoftDotted)
#define ucase_toFullFolding U_ICU_ENTRY_POINT_RENAME(ucase_toFullFolding)
#define ucase_toFullLower U_ICU_ENTRY_POINT_RENAME(ucase_toFullLower)
#define ucase_toFullTitle U_ICU_ENTRY_POINT_RENAME(ucase_toFullTitle)
#define ucase_toFullUpper U_ICU_ENTRY_POINT_RENAME(ucase_toFullUpper)
#define ucase_tolower U_ICU_ENTRY_POINT_RENAME(ucase_tolower)
#define ucase_totitle U_ICU_ENTRY_POINT_RENAME(ucase_totitle)
#define ucase_toupper U_ICU_ENTRY_POINT_RENAME(ucase_toupper)
#define ucasemap_close U_ICU_ENTRY_POINT_RENAME(ucasemap_close)
#define ucasemap_getBreakIterator U_ICU_ENTRY_POINT_RENAME(ucasemap_getBreakIterator)
#define ucasemap_getLocale U_ICU_ENTRY_POINT_RENAME(ucasemap_getLocale)
#define ucasemap_getOptions U_ICU_ENTRY_POINT_RENAME(ucasemap_getOptions)
#define ucasemap_internalUTF8ToTitle U_ICU_ENTRY_POINT_RENAME(ucasemap_internalUTF8ToTitle)
#define ucasemap_open U_ICU_ENTRY_POINT_RENAME(ucasemap_open)
#define ucasemap_setBreakIterator U_ICU_ENTRY_POINT_RENAME(ucasemap_setBreakIterator)
#define ucasemap_setLocale U_ICU_ENTRY_POINT_RENAME(ucasemap_setLocale)
#define ucasemap_setOptions U_ICU_ENTRY_POINT_RENAME(ucasemap_setOptions)
#define ucasemap_toTitle U_ICU_ENTRY_POINT_RENAME(ucasemap_toTitle)
#define ucasemap_utf8FoldCase U_ICU_ENTRY_POINT_RENAME(ucasemap_utf8FoldCase)
#define ucasemap_utf8ToLower U_ICU_ENTRY_POINT_RENAME(ucasemap_utf8ToLower)
#define ucasemap_utf8ToTitle U_ICU_ENTRY_POINT_RENAME(ucasemap_utf8ToTitle)
#define ucasemap_utf8ToUpper U_ICU_ENTRY_POINT_RENAME(ucasemap_utf8ToUpper)
#define ucfpos_close U_ICU_ENTRY_POINT_RENAME(ucfpos_close)
#define ucfpos_constrainCategory U_ICU_ENTRY_POINT_RENAME(ucfpos_constrainCategory)
#define ucfpos_constrainField U_ICU_ENTRY_POINT_RENAME(ucfpos_constrainField)
#define ucfpos_getCategory U_ICU_ENTRY_POINT_RENAME(ucfpos_getCategory)
#define ucfpos_getField U_ICU_ENTRY_POINT_RENAME(ucfpos_getField)
#define ucfpos_getIndexes U_ICU_ENTRY_POINT_RENAME(ucfpos_getIndexes)
#define ucfpos_getInt64IterationContext U_ICU_ENTRY_POINT_RENAME(ucfpos_getInt64IterationContext)
#define ucfpos_matchesField U_ICU_ENTRY_POINT_RENAME(ucfpos_matchesField)
#define ucfpos_open U_ICU_ENTRY_POINT_RENAME(ucfpos_open)
#define ucfpos_reset U_ICU_ENTRY_POINT_RENAME(ucfpos_reset)
#define ucfpos_setInt64IterationContext U_ICU_ENTRY_POINT_RENAME(ucfpos_setInt64IterationContext)
#define ucfpos_setState U_ICU_ENTRY_POINT_RENAME(ucfpos_setState)
#define uchar_addPropertyStarts U_ICU_ENTRY_POINT_RENAME(uchar_addPropertyStarts)
#define uchar_swapNames U_ICU_ENTRY_POINT_RENAME(uchar_swapNames)
#define ucln_cleanupOne U_ICU_ENTRY_POINT_RENAME(ucln_cleanupOne)
#define ucln_common_registerCleanup U_ICU_ENTRY_POINT_RENAME(ucln_common_registerCleanup)
#define ucln_i18n_registerCleanup U_ICU_ENTRY_POINT_RENAME(ucln_i18n_registerCleanup)
#define ucln_io_registerCleanup U_ICU_ENTRY_POINT_RENAME(ucln_io_registerCleanup)
#define ucln_lib_cleanup U_ICU_ENTRY_POINT_RENAME(ucln_lib_cleanup)
#define ucln_registerCleanup U_ICU_ENTRY_POINT_RENAME(ucln_registerCleanup)
#define ucnv_MBCSFromUChar32 U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSFromUChar32)
#define ucnv_MBCSFromUnicodeWithOffsets U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSFromUnicodeWithOffsets)
#define ucnv_MBCSGetFilteredUnicodeSetForUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSGetFilteredUnicodeSetForUnicode)
#define ucnv_MBCSGetType U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSGetType)
#define ucnv_MBCSGetUnicodeSetForUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSGetUnicodeSetForUnicode)
#define ucnv_MBCSIsLeadByte U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSIsLeadByte)
#define ucnv_MBCSSimpleGetNextUChar U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSSimpleGetNextUChar)
#define ucnv_MBCSToUnicodeWithOffsets U_ICU_ENTRY_POINT_RENAME(ucnv_MBCSToUnicodeWithOffsets)
#define ucnv_bld_countAvailableConverters U_ICU_ENTRY_POINT_RENAME(ucnv_bld_countAvailableConverters)
#define ucnv_bld_getAvailableConverter U_ICU_ENTRY_POINT_RENAME(ucnv_bld_getAvailableConverter)
#define ucnv_canCreateConverter U_ICU_ENTRY_POINT_RENAME(ucnv_canCreateConverter)
#define ucnv_cbFromUWriteBytes U_ICU_ENTRY_POINT_RENAME(ucnv_cbFromUWriteBytes)
#define ucnv_cbFromUWriteSub U_ICU_ENTRY_POINT_RENAME(ucnv_cbFromUWriteSub)
#define ucnv_cbFromUWriteUChars U_ICU_ENTRY_POINT_RENAME(ucnv_cbFromUWriteUChars)
#define ucnv_cbToUWriteSub U_ICU_ENTRY_POINT_RENAME(ucnv_cbToUWriteSub)
#define ucnv_cbToUWriteUChars U_ICU_ENTRY_POINT_RENAME(ucnv_cbToUWriteUChars)
#define ucnv_close U_ICU_ENTRY_POINT_RENAME(ucnv_close)
#define ucnv_compareNames U_ICU_ENTRY_POINT_RENAME(ucnv_compareNames)
#define ucnv_convert U_ICU_ENTRY_POINT_RENAME(ucnv_convert)
#define ucnv_convertEx U_ICU_ENTRY_POINT_RENAME(ucnv_convertEx)
#define ucnv_countAliases U_ICU_ENTRY_POINT_RENAME(ucnv_countAliases)
#define ucnv_countAvailable U_ICU_ENTRY_POINT_RENAME(ucnv_countAvailable)
#define ucnv_countStandards U_ICU_ENTRY_POINT_RENAME(ucnv_countStandards)
#define ucnv_createAlgorithmicConverter U_ICU_ENTRY_POINT_RENAME(ucnv_createAlgorithmicConverter)
#define ucnv_createConverter U_ICU_ENTRY_POINT_RENAME(ucnv_createConverter)
#define ucnv_createConverterFromPackage U_ICU_ENTRY_POINT_RENAME(ucnv_createConverterFromPackage)
#define ucnv_createConverterFromSharedData U_ICU_ENTRY_POINT_RENAME(ucnv_createConverterFromSharedData)
#define ucnv_detectUnicodeSignature U_ICU_ENTRY_POINT_RENAME(ucnv_detectUnicodeSignature)
#define ucnv_enableCleanup U_ICU_ENTRY_POINT_RENAME(ucnv_enableCleanup)
#define ucnv_extContinueMatchFromU U_ICU_ENTRY_POINT_RENAME(ucnv_extContinueMatchFromU)
#define ucnv_extContinueMatchToU U_ICU_ENTRY_POINT_RENAME(ucnv_extContinueMatchToU)
#define ucnv_extGetUnicodeSet U_ICU_ENTRY_POINT_RENAME(ucnv_extGetUnicodeSet)
#define ucnv_extInitialMatchFromU U_ICU_ENTRY_POINT_RENAME(ucnv_extInitialMatchFromU)
#define ucnv_extInitialMatchToU U_ICU_ENTRY_POINT_RENAME(ucnv_extInitialMatchToU)
#define ucnv_extSimpleMatchFromU U_ICU_ENTRY_POINT_RENAME(ucnv_extSimpleMatchFromU)
#define ucnv_extSimpleMatchToU U_ICU_ENTRY_POINT_RENAME(ucnv_extSimpleMatchToU)
#define ucnv_fixFileSeparator U_ICU_ENTRY_POINT_RENAME(ucnv_fixFileSeparator)
#define ucnv_flushCache U_ICU_ENTRY_POINT_RENAME(ucnv_flushCache)
#define ucnv_fromAlgorithmic U_ICU_ENTRY_POINT_RENAME(ucnv_fromAlgorithmic)
#define ucnv_fromUChars U_ICU_ENTRY_POINT_RENAME(ucnv_fromUChars)
#define ucnv_fromUCountPending U_ICU_ENTRY_POINT_RENAME(ucnv_fromUCountPending)
#define ucnv_fromUWriteBytes U_ICU_ENTRY_POINT_RENAME(ucnv_fromUWriteBytes)
#define ucnv_fromUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_fromUnicode)
#define ucnv_fromUnicode_UTF8 U_ICU_ENTRY_POINT_RENAME(ucnv_fromUnicode_UTF8)
#define ucnv_fromUnicode_UTF8_OFFSETS_LOGIC U_ICU_ENTRY_POINT_RENAME(ucnv_fromUnicode_UTF8_OFFSETS_LOGIC)
#define ucnv_getAlias U_ICU_ENTRY_POINT_RENAME(ucnv_getAlias)
#define ucnv_getAliases U_ICU_ENTRY_POINT_RENAME(ucnv_getAliases)
#define ucnv_getAvailableName U_ICU_ENTRY_POINT_RENAME(ucnv_getAvailableName)
#define ucnv_getCCSID U_ICU_ENTRY_POINT_RENAME(ucnv_getCCSID)
#define ucnv_getCanonicalName U_ICU_ENTRY_POINT_RENAME(ucnv_getCanonicalName)
#define ucnv_getCompleteUnicodeSet U_ICU_ENTRY_POINT_RENAME(ucnv_getCompleteUnicodeSet)
#define ucnv_getDefaultName U_ICU_ENTRY_POINT_RENAME(ucnv_getDefaultName)
#define ucnv_getDisplayName U_ICU_ENTRY_POINT_RENAME(ucnv_getDisplayName)
#define ucnv_getFromUCallBack U_ICU_ENTRY_POINT_RENAME(ucnv_getFromUCallBack)
#define ucnv_getInvalidChars U_ICU_ENTRY_POINT_RENAME(ucnv_getInvalidChars)
#define ucnv_getInvalidUChars U_ICU_ENTRY_POINT_RENAME(ucnv_getInvalidUChars)
#define ucnv_getMaxCharSize U_ICU_ENTRY_POINT_RENAME(ucnv_getMaxCharSize)
#define ucnv_getMinCharSize U_ICU_ENTRY_POINT_RENAME(ucnv_getMinCharSize)
#define ucnv_getName U_ICU_ENTRY_POINT_RENAME(ucnv_getName)
#define ucnv_getNextUChar U_ICU_ENTRY_POINT_RENAME(ucnv_getNextUChar)
#define ucnv_getNonSurrogateUnicodeSet U_ICU_ENTRY_POINT_RENAME(ucnv_getNonSurrogateUnicodeSet)
#define ucnv_getPlatform U_ICU_ENTRY_POINT_RENAME(ucnv_getPlatform)
#define ucnv_getStandard U_ICU_ENTRY_POINT_RENAME(ucnv_getStandard)
#define ucnv_getStandardName U_ICU_ENTRY_POINT_RENAME(ucnv_getStandardName)
#define ucnv_getStarters U_ICU_ENTRY_POINT_RENAME(ucnv_getStarters)
#define ucnv_getSubstChars U_ICU_ENTRY_POINT_RENAME(ucnv_getSubstChars)
#define ucnv_getToUCallBack U_ICU_ENTRY_POINT_RENAME(ucnv_getToUCallBack)
#define ucnv_getType U_ICU_ENTRY_POINT_RENAME(ucnv_getType)
#define ucnv_getUnicodeSet U_ICU_ENTRY_POINT_RENAME(ucnv_getUnicodeSet)
#define ucnv_incrementRefCount U_ICU_ENTRY_POINT_RENAME(ucnv_incrementRefCount)
#define ucnv_io_countKnownConverters U_ICU_ENTRY_POINT_RENAME(ucnv_io_countKnownConverters)
#define ucnv_io_getConverterName U_ICU_ENTRY_POINT_RENAME(ucnv_io_getConverterName)
#define ucnv_io_stripASCIIForCompare U_ICU_ENTRY_POINT_RENAME(ucnv_io_stripASCIIForCompare)
#define ucnv_io_stripEBCDICForCompare U_ICU_ENTRY_POINT_RENAME(ucnv_io_stripEBCDICForCompare)
#define ucnv_isAmbiguous U_ICU_ENTRY_POINT_RENAME(ucnv_isAmbiguous)
#define ucnv_isFixedWidth U_ICU_ENTRY_POINT_RENAME(ucnv_isFixedWidth)
#define ucnv_load U_ICU_ENTRY_POINT_RENAME(ucnv_load)
#define ucnv_loadSharedData U_ICU_ENTRY_POINT_RENAME(ucnv_loadSharedData)
#define ucnv_open U_ICU_ENTRY_POINT_RENAME(ucnv_open)
#define ucnv_openAllNames U_ICU_ENTRY_POINT_RENAME(ucnv_openAllNames)
#define ucnv_openCCSID U_ICU_ENTRY_POINT_RENAME(ucnv_openCCSID)
#define ucnv_openPackage U_ICU_ENTRY_POINT_RENAME(ucnv_openPackage)
#define ucnv_openStandardNames U_ICU_ENTRY_POINT_RENAME(ucnv_openStandardNames)
#define ucnv_openU U_ICU_ENTRY_POINT_RENAME(ucnv_openU)
#define ucnv_reset U_ICU_ENTRY_POINT_RENAME(ucnv_reset)
#define ucnv_resetFromUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_resetFromUnicode)
#define ucnv_resetToUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_resetToUnicode)
#define ucnv_safeClone U_ICU_ENTRY_POINT_RENAME(ucnv_safeClone)
#define ucnv_setDefaultName U_ICU_ENTRY_POINT_RENAME(ucnv_setDefaultName)
#define ucnv_setFallback U_ICU_ENTRY_POINT_RENAME(ucnv_setFallback)
#define ucnv_setFromUCallBack U_ICU_ENTRY_POINT_RENAME(ucnv_setFromUCallBack)
#define ucnv_setSubstChars U_ICU_ENTRY_POINT_RENAME(ucnv_setSubstChars)
#define ucnv_setSubstString U_ICU_ENTRY_POINT_RENAME(ucnv_setSubstString)
#define ucnv_setToUCallBack U_ICU_ENTRY_POINT_RENAME(ucnv_setToUCallBack)
#define ucnv_swap U_ICU_ENTRY_POINT_RENAME(ucnv_swap)
#define ucnv_swapAliases U_ICU_ENTRY_POINT_RENAME(ucnv_swapAliases)
#define ucnv_toAlgorithmic U_ICU_ENTRY_POINT_RENAME(ucnv_toAlgorithmic)
#define ucnv_toUChars U_ICU_ENTRY_POINT_RENAME(ucnv_toUChars)
#define ucnv_toUCountPending U_ICU_ENTRY_POINT_RENAME(ucnv_toUCountPending)
#define ucnv_toUWriteCodePoint U_ICU_ENTRY_POINT_RENAME(ucnv_toUWriteCodePoint)
#define ucnv_toUWriteUChars U_ICU_ENTRY_POINT_RENAME(ucnv_toUWriteUChars)
#define ucnv_toUnicode U_ICU_ENTRY_POINT_RENAME(ucnv_toUnicode)
#define ucnv_unload U_ICU_ENTRY_POINT_RENAME(ucnv_unload)
#define ucnv_unloadSharedDataIfReady U_ICU_ENTRY_POINT_RENAME(ucnv_unloadSharedDataIfReady)
#define ucnv_usesFallback U_ICU_ENTRY_POINT_RENAME(ucnv_usesFallback)
#define ucnvsel_close U_ICU_ENTRY_POINT_RENAME(ucnvsel_close)
#define ucnvsel_open U_ICU_ENTRY_POINT_RENAME(ucnvsel_open)
#define ucnvsel_openFromSerialized U_ICU_ENTRY_POINT_RENAME(ucnvsel_openFromSerialized)
#define ucnvsel_selectForString U_ICU_ENTRY_POINT_RENAME(ucnvsel_selectForString)
#define ucnvsel_selectForUTF8 U_ICU_ENTRY_POINT_RENAME(ucnvsel_selectForUTF8)
#define ucnvsel_serialize U_ICU_ENTRY_POINT_RENAME(ucnvsel_serialize)
#define ucol_cloneBinary U_ICU_ENTRY_POINT_RENAME(ucol_cloneBinary)
#define ucol_close U_ICU_ENTRY_POINT_RENAME(ucol_close)
#define ucol_closeElements U_ICU_ENTRY_POINT_RENAME(ucol_closeElements)
#define ucol_countAvailable U_ICU_ENTRY_POINT_RENAME(ucol_countAvailable)
#define ucol_equal U_ICU_ENTRY_POINT_RENAME(ucol_equal)
#define ucol_equals U_ICU_ENTRY_POINT_RENAME(ucol_equals)
#define ucol_getAttribute U_ICU_ENTRY_POINT_RENAME(ucol_getAttribute)
#define ucol_getAvailable U_ICU_ENTRY_POINT_RENAME(ucol_getAvailable)
#define ucol_getBound U_ICU_ENTRY_POINT_RENAME(ucol_getBound)
#define ucol_getContractions U_ICU_ENTRY_POINT_RENAME(ucol_getContractions)
#define ucol_getContractionsAndExpansions U_ICU_ENTRY_POINT_RENAME(ucol_getContractionsAndExpansions)
#define ucol_getDisplayName U_ICU_ENTRY_POINT_RENAME(ucol_getDisplayName)
#define ucol_getEquivalentReorderCodes U_ICU_ENTRY_POINT_RENAME(ucol_getEquivalentReorderCodes)
#define ucol_getFunctionalEquivalent U_ICU_ENTRY_POINT_RENAME(ucol_getFunctionalEquivalent)
#define ucol_getKeywordValues U_ICU_ENTRY_POINT_RENAME(ucol_getKeywordValues)
#define ucol_getKeywordValuesForLocale U_ICU_ENTRY_POINT_RENAME(ucol_getKeywordValuesForLocale)
#define ucol_getKeywords U_ICU_ENTRY_POINT_RENAME(ucol_getKeywords)
#define ucol_getLocale U_ICU_ENTRY_POINT_RENAME(ucol_getLocale)
#define ucol_getLocaleByType U_ICU_ENTRY_POINT_RENAME(ucol_getLocaleByType)
#define ucol_getMaxExpansion U_ICU_ENTRY_POINT_RENAME(ucol_getMaxExpansion)
#define ucol_getMaxVariable U_ICU_ENTRY_POINT_RENAME(ucol_getMaxVariable)
#define ucol_getOffset U_ICU_ENTRY_POINT_RENAME(ucol_getOffset)
#define ucol_getReorderCodes U_ICU_ENTRY_POINT_RENAME(ucol_getReorderCodes)
#define ucol_getRules U_ICU_ENTRY_POINT_RENAME(ucol_getRules)
#define ucol_getRulesEx U_ICU_ENTRY_POINT_RENAME(ucol_getRulesEx)
#define ucol_getShortDefinitionString U_ICU_ENTRY_POINT_RENAME(ucol_getShortDefinitionString)
#define ucol_getSortKey U_ICU_ENTRY_POINT_RENAME(ucol_getSortKey)
#define ucol_getStrength U_ICU_ENTRY_POINT_RENAME(ucol_getStrength)
#define ucol_getTailoredSet U_ICU_ENTRY_POINT_RENAME(ucol_getTailoredSet)
#define ucol_getUCAVersion U_ICU_ENTRY_POINT_RENAME(ucol_getUCAVersion)
#define ucol_getUnsafeSet U_ICU_ENTRY_POINT_RENAME(ucol_getUnsafeSet)
#define ucol_getVariableTop U_ICU_ENTRY_POINT_RENAME(ucol_getVariableTop)
#define ucol_getVersion U_ICU_ENTRY_POINT_RENAME(ucol_getVersion)
#define ucol_greater U_ICU_ENTRY_POINT_RENAME(ucol_greater)
#define ucol_greaterOrEqual U_ICU_ENTRY_POINT_RENAME(ucol_greaterOrEqual)
#define ucol_keyHashCode U_ICU_ENTRY_POINT_RENAME(ucol_keyHashCode)
#define ucol_looksLikeCollationBinary U_ICU_ENTRY_POINT_RENAME(ucol_looksLikeCollationBinary)
#define ucol_mergeSortkeys U_ICU_ENTRY_POINT_RENAME(ucol_mergeSortkeys)
#define ucol_next U_ICU_ENTRY_POINT_RENAME(ucol_next)
#define ucol_nextSortKeyPart U_ICU_ENTRY_POINT_RENAME(ucol_nextSortKeyPart)
#define ucol_normalizeShortDefinitionString U_ICU_ENTRY_POINT_RENAME(ucol_normalizeShortDefinitionString)
#define ucol_open U_ICU_ENTRY_POINT_RENAME(ucol_open)
#define ucol_openAvailableLocales U_ICU_ENTRY_POINT_RENAME(ucol_openAvailableLocales)
#define ucol_openBinary U_ICU_ENTRY_POINT_RENAME(ucol_openBinary)
#define ucol_openElements U_ICU_ENTRY_POINT_RENAME(ucol_openElements)
#define ucol_openFromShortString U_ICU_ENTRY_POINT_RENAME(ucol_openFromShortString)
#define ucol_openRules U_ICU_ENTRY_POINT_RENAME(ucol_openRules)
#define ucol_prepareShortStringOpen U_ICU_ENTRY_POINT_RENAME(ucol_prepareShortStringOpen)
#define ucol_previous U_ICU_ENTRY_POINT_RENAME(ucol_previous)
#define ucol_primaryOrder U_ICU_ENTRY_POINT_RENAME(ucol_primaryOrder)
#define ucol_reset U_ICU_ENTRY_POINT_RENAME(ucol_reset)
#define ucol_restoreVariableTop U_ICU_ENTRY_POINT_RENAME(ucol_restoreVariableTop)
#define ucol_safeClone U_ICU_ENTRY_POINT_RENAME(ucol_safeClone)
#define ucol_secondaryOrder U_ICU_ENTRY_POINT_RENAME(ucol_secondaryOrder)
#define ucol_setAttribute U_ICU_ENTRY_POINT_RENAME(ucol_setAttribute)
#define ucol_setMaxVariable U_ICU_ENTRY_POINT_RENAME(ucol_setMaxVariable)
#define ucol_setOffset U_ICU_ENTRY_POINT_RENAME(ucol_setOffset)
#define ucol_setReorderCodes U_ICU_ENTRY_POINT_RENAME(ucol_setReorderCodes)
#define ucol_setStrength U_ICU_ENTRY_POINT_RENAME(ucol_setStrength)
#define ucol_setText U_ICU_ENTRY_POINT_RENAME(ucol_setText)
#define ucol_setVariableTop U_ICU_ENTRY_POINT_RENAME(ucol_setVariableTop)
#define ucol_strcoll U_ICU_ENTRY_POINT_RENAME(ucol_strcoll)
#define ucol_strcollIter U_ICU_ENTRY_POINT_RENAME(ucol_strcollIter)
#define ucol_strcollUTF8 U_ICU_ENTRY_POINT_RENAME(ucol_strcollUTF8)
#define ucol_swap U_ICU_ENTRY_POINT_RENAME(ucol_swap)
#define ucol_swapInverseUCA U_ICU_ENTRY_POINT_RENAME(ucol_swapInverseUCA)
#define ucol_tertiaryOrder U_ICU_ENTRY_POINT_RENAME(ucol_tertiaryOrder)
#define ucpmap_get U_ICU_ENTRY_POINT_RENAME(ucpmap_get)
#define ucpmap_getRange U_ICU_ENTRY_POINT_RENAME(ucpmap_getRange)
#define ucptrie_close U_ICU_ENTRY_POINT_RENAME(ucptrie_close)
#define ucptrie_get U_ICU_ENTRY_POINT_RENAME(ucptrie_get)
#define ucptrie_getRange U_ICU_ENTRY_POINT_RENAME(ucptrie_getRange)
#define ucptrie_getType U_ICU_ENTRY_POINT_RENAME(ucptrie_getType)
#define ucptrie_getValueWidth U_ICU_ENTRY_POINT_RENAME(ucptrie_getValueWidth)
#define ucptrie_internalGetRange U_ICU_ENTRY_POINT_RENAME(ucptrie_internalGetRange)
#define ucptrie_internalSmallIndex U_ICU_ENTRY_POINT_RENAME(ucptrie_internalSmallIndex)
#define ucptrie_internalSmallU8Index U_ICU_ENTRY_POINT_RENAME(ucptrie_internalSmallU8Index)
#define ucptrie_internalU8PrevIndex U_ICU_ENTRY_POINT_RENAME(ucptrie_internalU8PrevIndex)
#define ucptrie_openFromBinary U_ICU_ENTRY_POINT_RENAME(ucptrie_openFromBinary)
#define ucptrie_swap U_ICU_ENTRY_POINT_RENAME(ucptrie_swap)
#define ucptrie_toBinary U_ICU_ENTRY_POINT_RENAME(ucptrie_toBinary)
#define ucsdet_close U_ICU_ENTRY_POINT_RENAME(ucsdet_close)
#define ucsdet_detect U_ICU_ENTRY_POINT_RENAME(ucsdet_detect)
#define ucsdet_detectAll U_ICU_ENTRY_POINT_RENAME(ucsdet_detectAll)
#define ucsdet_enableInputFilter U_ICU_ENTRY_POINT_RENAME(ucsdet_enableInputFilter)
#define ucsdet_getAllDetectableCharsets U_ICU_ENTRY_POINT_RENAME(ucsdet_getAllDetectableCharsets)
#define ucsdet_getConfidence U_ICU_ENTRY_POINT_RENAME(ucsdet_getConfidence)
#define ucsdet_getDetectableCharsets U_ICU_ENTRY_POINT_RENAME(ucsdet_getDetectableCharsets)
#define ucsdet_getLanguage U_ICU_ENTRY_POINT_RENAME(ucsdet_getLanguage)
#define ucsdet_getName U_ICU_ENTRY_POINT_RENAME(ucsdet_getName)
#define ucsdet_getUChars U_ICU_ENTRY_POINT_RENAME(ucsdet_getUChars)
#define ucsdet_isInputFilterEnabled U_ICU_ENTRY_POINT_RENAME(ucsdet_isInputFilterEnabled)
#define ucsdet_open U_ICU_ENTRY_POINT_RENAME(ucsdet_open)
#define ucsdet_setDeclaredEncoding U_ICU_ENTRY_POINT_RENAME(ucsdet_setDeclaredEncoding)
#define ucsdet_setDetectableCharset U_ICU_ENTRY_POINT_RENAME(ucsdet_setDetectableCharset)
#define ucsdet_setText U_ICU_ENTRY_POINT_RENAME(ucsdet_setText)
#define ucurr_countCurrencies U_ICU_ENTRY_POINT_RENAME(ucurr_countCurrencies)
#define ucurr_forLocale U_ICU_ENTRY_POINT_RENAME(ucurr_forLocale)
#define ucurr_forLocaleAndDate U_ICU_ENTRY_POINT_RENAME(ucurr_forLocaleAndDate)
#define ucurr_getDefaultFractionDigits U_ICU_ENTRY_POINT_RENAME(ucurr_getDefaultFractionDigits)
#define ucurr_getDefaultFractionDigitsForUsage U_ICU_ENTRY_POINT_RENAME(ucurr_getDefaultFractionDigitsForUsage)
#define ucurr_getKeywordValuesForLocale U_ICU_ENTRY_POINT_RENAME(ucurr_getKeywordValuesForLocale)
#define ucurr_getName U_ICU_ENTRY_POINT_RENAME(ucurr_getName)
#define ucurr_getNumericCode U_ICU_ENTRY_POINT_RENAME(ucurr_getNumericCode)
#define ucurr_getPluralName U_ICU_ENTRY_POINT_RENAME(ucurr_getPluralName)
#define ucurr_getRoundingIncrement U_ICU_ENTRY_POINT_RENAME(ucurr_getRoundingIncrement)
#define ucurr_getRoundingIncrementForUsage U_ICU_ENTRY_POINT_RENAME(ucurr_getRoundingIncrementForUsage)
#define ucurr_isAvailable U_ICU_ENTRY_POINT_RENAME(ucurr_isAvailable)
#define ucurr_openISOCurrencies U_ICU_ENTRY_POINT_RENAME(ucurr_openISOCurrencies)
#define ucurr_register U_ICU_ENTRY_POINT_RENAME(ucurr_register)
#define ucurr_unregister U_ICU_ENTRY_POINT_RENAME(ucurr_unregister)
#define udat_adoptNumberFormat U_ICU_ENTRY_POINT_RENAME(udat_adoptNumberFormat)
#define udat_adoptNumberFormatForFields U_ICU_ENTRY_POINT_RENAME(udat_adoptNumberFormatForFields)
#define udat_applyPattern U_ICU_ENTRY_POINT_RENAME(udat_applyPattern)
#define udat_applyPatternRelative U_ICU_ENTRY_POINT_RENAME(udat_applyPatternRelative)
#define udat_clone U_ICU_ENTRY_POINT_RENAME(udat_clone)
#define udat_close U_ICU_ENTRY_POINT_RENAME(udat_close)
#define udat_countAvailable U_ICU_ENTRY_POINT_RENAME(udat_countAvailable)
#define udat_countSymbols U_ICU_ENTRY_POINT_RENAME(udat_countSymbols)
#define udat_format U_ICU_ENTRY_POINT_RENAME(udat_format)
#define udat_formatCalendar U_ICU_ENTRY_POINT_RENAME(udat_formatCalendar)
#define udat_formatCalendarForFields U_ICU_ENTRY_POINT_RENAME(udat_formatCalendarForFields)
#define udat_formatForFields U_ICU_ENTRY_POINT_RENAME(udat_formatForFields)
#define udat_get2DigitYearStart U_ICU_ENTRY_POINT_RENAME(udat_get2DigitYearStart)
#define udat_getAvailable U_ICU_ENTRY_POINT_RENAME(udat_getAvailable)
#define udat_getBooleanAttribute U_ICU_ENTRY_POINT_RENAME(udat_getBooleanAttribute)
#define udat_getCalendar U_ICU_ENTRY_POINT_RENAME(udat_getCalendar)
#define udat_getContext U_ICU_ENTRY_POINT_RENAME(udat_getContext)
#define udat_getLocaleByType U_ICU_ENTRY_POINT_RENAME(udat_getLocaleByType)
#define udat_getNumberFormat U_ICU_ENTRY_POINT_RENAME(udat_getNumberFormat)
#define udat_getNumberFormatForField U_ICU_ENTRY_POINT_RENAME(udat_getNumberFormatForField)
#define udat_getSymbols U_ICU_ENTRY_POINT_RENAME(udat_getSymbols)
#define udat_isLenient U_ICU_ENTRY_POINT_RENAME(udat_isLenient)
#define udat_open U_ICU_ENTRY_POINT_RENAME(udat_open)
#define udat_parse U_ICU_ENTRY_POINT_RENAME(udat_parse)
#define udat_parseCalendar U_ICU_ENTRY_POINT_RENAME(udat_parseCalendar)
#define udat_registerOpener U_ICU_ENTRY_POINT_RENAME(udat_registerOpener)
#define udat_set2DigitYearStart U_ICU_ENTRY_POINT_RENAME(udat_set2DigitYearStart)
#define udat_setBooleanAttribute U_ICU_ENTRY_POINT_RENAME(udat_setBooleanAttribute)
#define udat_setCalendar U_ICU_ENTRY_POINT_RENAME(udat_setCalendar)
#define udat_setContext U_ICU_ENTRY_POINT_RENAME(udat_setContext)
#define udat_setLenient U_ICU_ENTRY_POINT_RENAME(udat_setLenient)
#define udat_setNumberFormat U_ICU_ENTRY_POINT_RENAME(udat_setNumberFormat)
#define udat_setSymbols U_ICU_ENTRY_POINT_RENAME(udat_setSymbols)
#define udat_toCalendarDateField U_ICU_ENTRY_POINT_RENAME(udat_toCalendarDateField)
#define udat_toPattern U_ICU_ENTRY_POINT_RENAME(udat_toPattern)
#define udat_toPatternRelativeDate U_ICU_ENTRY_POINT_RENAME(udat_toPatternRelativeDate)
#define udat_toPatternRelativeTime U_ICU_ENTRY_POINT_RENAME(udat_toPatternRelativeTime)
#define udat_unregisterOpener U_ICU_ENTRY_POINT_RENAME(udat_unregisterOpener)
#define udata_checkCommonData U_ICU_ENTRY_POINT_RENAME(udata_checkCommonData)
#define udata_close U_ICU_ENTRY_POINT_RENAME(udata_close)
#define udata_closeSwapper U_ICU_ENTRY_POINT_RENAME(udata_closeSwapper)
#define udata_getHeaderSize U_ICU_ENTRY_POINT_RENAME(udata_getHeaderSize)
#define udata_getInfo U_ICU_ENTRY_POINT_RENAME(udata_getInfo)
#define udata_getInfoSize U_ICU_ENTRY_POINT_RENAME(udata_getInfoSize)
#define udata_getLength U_ICU_ENTRY_POINT_RENAME(udata_getLength)
#define udata_getMemory U_ICU_ENTRY_POINT_RENAME(udata_getMemory)
#define udata_getRawMemory U_ICU_ENTRY_POINT_RENAME(udata_getRawMemory)
#define udata_open U_ICU_ENTRY_POINT_RENAME(udata_open)
#define udata_openChoice U_ICU_ENTRY_POINT_RENAME(udata_openChoice)
#define udata_openSwapper U_ICU_ENTRY_POINT_RENAME(udata_openSwapper)
#define udata_openSwapperForInputData U_ICU_ENTRY_POINT_RENAME(udata_openSwapperForInputData)
#define udata_printError U_ICU_ENTRY_POINT_RENAME(udata_printError)
#define udata_readInt16 U_ICU_ENTRY_POINT_RENAME(udata_readInt16)
#define udata_readInt32 U_ICU_ENTRY_POINT_RENAME(udata_readInt32)
#define udata_setAppData U_ICU_ENTRY_POINT_RENAME(udata_setAppData)
#define udata_setCommonData U_ICU_ENTRY_POINT_RENAME(udata_setCommonData)
#define udata_setFileAccess U_ICU_ENTRY_POINT_RENAME(udata_setFileAccess)
#define udata_swapDataHeader U_ICU_ENTRY_POINT_RENAME(udata_swapDataHeader)
#define udata_swapInvStringBlock U_ICU_ENTRY_POINT_RENAME(udata_swapInvStringBlock)
#define udatpg_addPattern U_ICU_ENTRY_POINT_RENAME(udatpg_addPattern)
#define udatpg_clone U_ICU_ENTRY_POINT_RENAME(udatpg_clone)
#define udatpg_close U_ICU_ENTRY_POINT_RENAME(udatpg_close)
#define udatpg_getAppendItemFormat U_ICU_ENTRY_POINT_RENAME(udatpg_getAppendItemFormat)
#define udatpg_getAppendItemName U_ICU_ENTRY_POINT_RENAME(udatpg_getAppendItemName)
#define udatpg_getBaseSkeleton U_ICU_ENTRY_POINT_RENAME(udatpg_getBaseSkeleton)
#define udatpg_getBestPattern U_ICU_ENTRY_POINT_RENAME(udatpg_getBestPattern)
#define udatpg_getBestPatternWithOptions U_ICU_ENTRY_POINT_RENAME(udatpg_getBestPatternWithOptions)
#define udatpg_getDateTimeFormat U_ICU_ENTRY_POINT_RENAME(udatpg_getDateTimeFormat)
#define udatpg_getDecimal U_ICU_ENTRY_POINT_RENAME(udatpg_getDecimal)
#define udatpg_getDefaultHourCycle U_ICU_ENTRY_POINT_RENAME(udatpg_getDefaultHourCycle)
#define udatpg_getFieldDisplayName U_ICU_ENTRY_POINT_RENAME(udatpg_getFieldDisplayName)
#define udatpg_getPatternForSkeleton U_ICU_ENTRY_POINT_RENAME(udatpg_getPatternForSkeleton)
#define udatpg_getSkeleton U_ICU_ENTRY_POINT_RENAME(udatpg_getSkeleton)
#define udatpg_open U_ICU_ENTRY_POINT_RENAME(udatpg_open)
#define udatpg_openBaseSkeletons U_ICU_ENTRY_POINT_RENAME(udatpg_openBaseSkeletons)
#define udatpg_openEmpty U_ICU_ENTRY_POINT_RENAME(udatpg_openEmpty)
#define udatpg_openSkeletons U_ICU_ENTRY_POINT_RENAME(udatpg_openSkeletons)
#define udatpg_replaceFieldTypes U_ICU_ENTRY_POINT_RENAME(udatpg_replaceFieldTypes)
#define udatpg_replaceFieldTypesWithOptions U_ICU_ENTRY_POINT_RENAME(udatpg_replaceFieldTypesWithOptions)
#define udatpg_setAppendItemFormat U_ICU_ENTRY_POINT_RENAME(udatpg_setAppendItemFormat)
#define udatpg_setAppendItemName U_ICU_ENTRY_POINT_RENAME(udatpg_setAppendItemName)
#define udatpg_setDateTimeFormat U_ICU_ENTRY_POINT_RENAME(udatpg_setDateTimeFormat)
#define udatpg_setDecimal U_ICU_ENTRY_POINT_RENAME(udatpg_setDecimal)
#define udict_swap U_ICU_ENTRY_POINT_RENAME(udict_swap)
#define udtitvfmt_close U_ICU_ENTRY_POINT_RENAME(udtitvfmt_close)
#define udtitvfmt_closeResult U_ICU_ENTRY_POINT_RENAME(udtitvfmt_closeResult)
#define udtitvfmt_format U_ICU_ENTRY_POINT_RENAME(udtitvfmt_format)
#define udtitvfmt_formatCalendarToResult U_ICU_ENTRY_POINT_RENAME(udtitvfmt_formatCalendarToResult)
#define udtitvfmt_formatToResult U_ICU_ENTRY_POINT_RENAME(udtitvfmt_formatToResult)
#define udtitvfmt_getContext U_ICU_ENTRY_POINT_RENAME(udtitvfmt_getContext)
#define udtitvfmt_open U_ICU_ENTRY_POINT_RENAME(udtitvfmt_open)
#define udtitvfmt_openResult U_ICU_ENTRY_POINT_RENAME(udtitvfmt_openResult)
#define udtitvfmt_resultAsValue U_ICU_ENTRY_POINT_RENAME(udtitvfmt_resultAsValue)
#define udtitvfmt_setContext U_ICU_ENTRY_POINT_RENAME(udtitvfmt_setContext)
#define uenum_close U_ICU_ENTRY_POINT_RENAME(uenum_close)
#define uenum_count U_ICU_ENTRY_POINT_RENAME(uenum_count)
#define uenum_next U_ICU_ENTRY_POINT_RENAME(uenum_next)
#define uenum_nextDefault U_ICU_ENTRY_POINT_RENAME(uenum_nextDefault)
#define uenum_openCharStringsEnumeration U_ICU_ENTRY_POINT_RENAME(uenum_openCharStringsEnumeration)
#define uenum_openFromStringEnumeration U_ICU_ENTRY_POINT_RENAME(uenum_openFromStringEnumeration)
#define uenum_openUCharStringsEnumeration U_ICU_ENTRY_POINT_RENAME(uenum_openUCharStringsEnumeration)
#define uenum_reset U_ICU_ENTRY_POINT_RENAME(uenum_reset)
#define uenum_unext U_ICU_ENTRY_POINT_RENAME(uenum_unext)
#define uenum_unextDefault U_ICU_ENTRY_POINT_RENAME(uenum_unextDefault)
#define ufieldpositer_close U_ICU_ENTRY_POINT_RENAME(ufieldpositer_close)
#define ufieldpositer_next U_ICU_ENTRY_POINT_RENAME(ufieldpositer_next)
#define ufieldpositer_open U_ICU_ENTRY_POINT_RENAME(ufieldpositer_open)
#define ufile_getch U_ICU_ENTRY_POINT_RENAME(ufile_getch)
#define ufile_getch32 U_ICU_ENTRY_POINT_RENAME(ufile_getch32)
#define ufmt_close U_ICU_ENTRY_POINT_RENAME(ufmt_close)
#define ufmt_getArrayItemByIndex U_ICU_ENTRY_POINT_RENAME(ufmt_getArrayItemByIndex)
#define ufmt_getArrayLength U_ICU_ENTRY_POINT_RENAME(ufmt_getArrayLength)
#define ufmt_getDate U_ICU_ENTRY_POINT_RENAME(ufmt_getDate)
#define ufmt_getDecNumChars U_ICU_ENTRY_POINT_RENAME(ufmt_getDecNumChars)
#define ufmt_getDouble U_ICU_ENTRY_POINT_RENAME(ufmt_getDouble)
#define ufmt_getInt64 U_ICU_ENTRY_POINT_RENAME(ufmt_getInt64)
#define ufmt_getLong U_ICU_ENTRY_POINT_RENAME(ufmt_getLong)
#define ufmt_getObject U_ICU_ENTRY_POINT_RENAME(ufmt_getObject)
#define ufmt_getType U_ICU_ENTRY_POINT_RENAME(ufmt_getType)
#define ufmt_getUChars U_ICU_ENTRY_POINT_RENAME(ufmt_getUChars)
#define ufmt_isNumeric U_ICU_ENTRY_POINT_RENAME(ufmt_isNumeric)
#define ufmt_open U_ICU_ENTRY_POINT_RENAME(ufmt_open)
#define ufmtval_getString U_ICU_ENTRY_POINT_RENAME(ufmtval_getString)
#define ufmtval_nextPosition U_ICU_ENTRY_POINT_RENAME(ufmtval_nextPosition)
#define ugender_getInstance U_ICU_ENTRY_POINT_RENAME(ugender_getInstance)
#define ugender_getListGender U_ICU_ENTRY_POINT_RENAME(ugender_getListGender)
#define uhash_close U_ICU_ENTRY_POINT_RENAME(uhash_close)
#define uhash_compareCaselessUnicodeString U_ICU_ENTRY_POINT_RENAME(uhash_compareCaselessUnicodeString)
#define uhash_compareChars U_ICU_ENTRY_POINT_RENAME(uhash_compareChars)
#define uhash_compareIChars U_ICU_ENTRY_POINT_RENAME(uhash_compareIChars)
#define uhash_compareLong U_ICU_ENTRY_POINT_RENAME(uhash_compareLong)
#define uhash_compareScriptSet U_ICU_ENTRY_POINT_RENAME(uhash_compareScriptSet)
#define uhash_compareUChars U_ICU_ENTRY_POINT_RENAME(uhash_compareUChars)
#define uhash_compareUnicodeString U_ICU_ENTRY_POINT_RENAME(uhash_compareUnicodeString)
#define uhash_containsKey U_ICU_ENTRY_POINT_RENAME(uhash_containsKey)
#define uhash_count U_ICU_ENTRY_POINT_RENAME(uhash_count)
#define uhash_deleteHashtable U_ICU_ENTRY_POINT_RENAME(uhash_deleteHashtable)
#define uhash_deleteScriptSet U_ICU_ENTRY_POINT_RENAME(uhash_deleteScriptSet)
#define uhash_equals U_ICU_ENTRY_POINT_RENAME(uhash_equals)
#define uhash_equalsScriptSet U_ICU_ENTRY_POINT_RENAME(uhash_equalsScriptSet)
#define uhash_find U_ICU_ENTRY_POINT_RENAME(uhash_find)
#define uhash_get U_ICU_ENTRY_POINT_RENAME(uhash_get)
#define uhash_geti U_ICU_ENTRY_POINT_RENAME(uhash_geti)
#define uhash_getiAndFound U_ICU_ENTRY_POINT_RENAME(uhash_getiAndFound)
#define uhash_hashCaselessUnicodeString U_ICU_ENTRY_POINT_RENAME(uhash_hashCaselessUnicodeString)
#define uhash_hashChars U_ICU_ENTRY_POINT_RENAME(uhash_hashChars)
#define uhash_hashIChars U_ICU_ENTRY_POINT_RENAME(uhash_hashIChars)
#define uhash_hashLong U_ICU_ENTRY_POINT_RENAME(uhash_hashLong)
#define uhash_hashScriptSet U_ICU_ENTRY_POINT_RENAME(uhash_hashScriptSet)
#define uhash_hashUChars U_ICU_ENTRY_POINT_RENAME(uhash_hashUChars)
#define uhash_hashUnicodeString U_ICU_ENTRY_POINT_RENAME(uhash_hashUnicodeString)
#define uhash_icontainsKey U_ICU_ENTRY_POINT_RENAME(uhash_icontainsKey)
#define uhash_iget U_ICU_ENTRY_POINT_RENAME(uhash_iget)
#define uhash_igeti U_ICU_ENTRY_POINT_RENAME(uhash_igeti)
#define uhash_igetiAndFound U_ICU_ENTRY_POINT_RENAME(uhash_igetiAndFound)
#define uhash_init U_ICU_ENTRY_POINT_RENAME(uhash_init)
#define uhash_initSize U_ICU_ENTRY_POINT_RENAME(uhash_initSize)
#define uhash_iput U_ICU_ENTRY_POINT_RENAME(uhash_iput)
#define uhash_iputi U_ICU_ENTRY_POINT_RENAME(uhash_iputi)
#define uhash_iputiAllowZero U_ICU_ENTRY_POINT_RENAME(uhash_iputiAllowZero)
#define uhash_iremove U_ICU_ENTRY_POINT_RENAME(uhash_iremove)
#define uhash_iremovei U_ICU_ENTRY_POINT_RENAME(uhash_iremovei)
#define uhash_nextElement U_ICU_ENTRY_POINT_RENAME(uhash_nextElement)
#define uhash_open U_ICU_ENTRY_POINT_RENAME(uhash_open)
#define uhash_openSize U_ICU_ENTRY_POINT_RENAME(uhash_openSize)
#define uhash_put U_ICU_ENTRY_POINT_RENAME(uhash_put)
#define uhash_puti U_ICU_ENTRY_POINT_RENAME(uhash_puti)
#define uhash_putiAllowZero U_ICU_ENTRY_POINT_RENAME(uhash_putiAllowZero)
#define uhash_remove U_ICU_ENTRY_POINT_RENAME(uhash_remove)
#define uhash_removeAll U_ICU_ENTRY_POINT_RENAME(uhash_removeAll)
#define uhash_removeElement U_ICU_ENTRY_POINT_RENAME(uhash_removeElement)
#define uhash_removei U_ICU_ENTRY_POINT_RENAME(uhash_removei)
#define uhash_setKeyComparator U_ICU_ENTRY_POINT_RENAME(uhash_setKeyComparator)
#define uhash_setKeyDeleter U_ICU_ENTRY_POINT_RENAME(uhash_setKeyDeleter)
#define uhash_setKeyHasher U_ICU_ENTRY_POINT_RENAME(uhash_setKeyHasher)
#define uhash_setResizePolicy U_ICU_ENTRY_POINT_RENAME(uhash_setResizePolicy)
#define uhash_setValueComparator U_ICU_ENTRY_POINT_RENAME(uhash_setValueComparator)
#define uhash_setValueDeleter U_ICU_ENTRY_POINT_RENAME(uhash_setValueDeleter)
#define uidna_IDNToASCII U_ICU_ENTRY_POINT_RENAME(uidna_IDNToASCII)
#define uidna_IDNToUnicode U_ICU_ENTRY_POINT_RENAME(uidna_IDNToUnicode)
#define uidna_close U_ICU_ENTRY_POINT_RENAME(uidna_close)
#define uidna_compare U_ICU_ENTRY_POINT_RENAME(uidna_compare)
#define uidna_labelToASCII U_ICU_ENTRY_POINT_RENAME(uidna_labelToASCII)
#define uidna_labelToASCII_UTF8 U_ICU_ENTRY_POINT_RENAME(uidna_labelToASCII_UTF8)
#define uidna_labelToUnicode U_ICU_ENTRY_POINT_RENAME(uidna_labelToUnicode)
#define uidna_labelToUnicodeUTF8 U_ICU_ENTRY_POINT_RENAME(uidna_labelToUnicodeUTF8)
#define uidna_nameToASCII U_ICU_ENTRY_POINT_RENAME(uidna_nameToASCII)
#define uidna_nameToASCII_UTF8 U_ICU_ENTRY_POINT_RENAME(uidna_nameToASCII_UTF8)
#define uidna_nameToUnicode U_ICU_ENTRY_POINT_RENAME(uidna_nameToUnicode)
#define uidna_nameToUnicodeUTF8 U_ICU_ENTRY_POINT_RENAME(uidna_nameToUnicodeUTF8)
#define uidna_openUTS46 U_ICU_ENTRY_POINT_RENAME(uidna_openUTS46)
#define uidna_toASCII U_ICU_ENTRY_POINT_RENAME(uidna_toASCII)
#define uidna_toUnicode U_ICU_ENTRY_POINT_RENAME(uidna_toUnicode)
#define uiter_current32 U_ICU_ENTRY_POINT_RENAME(uiter_current32)
#define uiter_getState U_ICU_ENTRY_POINT_RENAME(uiter_getState)
#define uiter_next32 U_ICU_ENTRY_POINT_RENAME(uiter_next32)
#define uiter_previous32 U_ICU_ENTRY_POINT_RENAME(uiter_previous32)
#define uiter_setCharacterIterator U_ICU_ENTRY_POINT_RENAME(uiter_setCharacterIterator)
#define uiter_setReplaceable U_ICU_ENTRY_POINT_RENAME(uiter_setReplaceable)
#define uiter_setState U_ICU_ENTRY_POINT_RENAME(uiter_setState)
#define uiter_setString U_ICU_ENTRY_POINT_RENAME(uiter_setString)
#define uiter_setUTF16BE U_ICU_ENTRY_POINT_RENAME(uiter_setUTF16BE)
#define uiter_setUTF8 U_ICU_ENTRY_POINT_RENAME(uiter_setUTF8)
#define uldn_close U_ICU_ENTRY_POINT_RENAME(uldn_close)
#define uldn_getContext U_ICU_ENTRY_POINT_RENAME(uldn_getContext)
#define uldn_getDialectHandling U_ICU_ENTRY_POINT_RENAME(uldn_getDialectHandling)
#define uldn_getLocale U_ICU_ENTRY_POINT_RENAME(uldn_getLocale)
#define uldn_keyDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_keyDisplayName)
#define uldn_keyValueDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_keyValueDisplayName)
#define uldn_languageDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_languageDisplayName)
#define uldn_localeDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_localeDisplayName)
#define uldn_open U_ICU_ENTRY_POINT_RENAME(uldn_open)
#define uldn_openForContext U_ICU_ENTRY_POINT_RENAME(uldn_openForContext)
#define uldn_regionDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_regionDisplayName)
#define uldn_scriptCodeDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_scriptCodeDisplayName)
#define uldn_scriptDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_scriptDisplayName)
#define uldn_variantDisplayName U_ICU_ENTRY_POINT_RENAME(uldn_variantDisplayName)
#define ulist_addItemBeginList U_ICU_ENTRY_POINT_RENAME(ulist_addItemBeginList)
#define ulist_addItemEndList U_ICU_ENTRY_POINT_RENAME(ulist_addItemEndList)
#define ulist_close_keyword_values_iterator U_ICU_ENTRY_POINT_RENAME(ulist_close_keyword_values_iterator)
#define ulist_containsString U_ICU_ENTRY_POINT_RENAME(ulist_containsString)
#define ulist_count_keyword_values U_ICU_ENTRY_POINT_RENAME(ulist_count_keyword_values)
#define ulist_createEmptyList U_ICU_ENTRY_POINT_RENAME(ulist_createEmptyList)
#define ulist_deleteList U_ICU_ENTRY_POINT_RENAME(ulist_deleteList)
#define ulist_getListFromEnum U_ICU_ENTRY_POINT_RENAME(ulist_getListFromEnum)
#define ulist_getListSize U_ICU_ENTRY_POINT_RENAME(ulist_getListSize)
#define ulist_getNext U_ICU_ENTRY_POINT_RENAME(ulist_getNext)
#define ulist_next_keyword_value U_ICU_ENTRY_POINT_RENAME(ulist_next_keyword_value)
#define ulist_removeString U_ICU_ENTRY_POINT_RENAME(ulist_removeString)
#define ulist_resetList U_ICU_ENTRY_POINT_RENAME(ulist_resetList)
#define ulist_reset_keyword_values_iterator U_ICU_ENTRY_POINT_RENAME(ulist_reset_keyword_values_iterator)
#define ulistfmt_close U_ICU_ENTRY_POINT_RENAME(ulistfmt_close)
#define ulistfmt_closeResult U_ICU_ENTRY_POINT_RENAME(ulistfmt_closeResult)
#define ulistfmt_format U_ICU_ENTRY_POINT_RENAME(ulistfmt_format)
#define ulistfmt_formatStringsToResult U_ICU_ENTRY_POINT_RENAME(ulistfmt_formatStringsToResult)
#define ulistfmt_open U_ICU_ENTRY_POINT_RENAME(ulistfmt_open)
#define ulistfmt_openForType U_ICU_ENTRY_POINT_RENAME(ulistfmt_openForType)
#define ulistfmt_openResult U_ICU_ENTRY_POINT_RENAME(ulistfmt_openResult)
#define ulistfmt_resultAsValue U_ICU_ENTRY_POINT_RENAME(ulistfmt_resultAsValue)
#define uloc_acceptLanguage U_ICU_ENTRY_POINT_RENAME(uloc_acceptLanguage)
#define uloc_acceptLanguageFromHTTP U_ICU_ENTRY_POINT_RENAME(uloc_acceptLanguageFromHTTP)
#define uloc_addLikelySubtags U_ICU_ENTRY_POINT_RENAME(uloc_addLikelySubtags)
#define uloc_canonicalize U_ICU_ENTRY_POINT_RENAME(uloc_canonicalize)
#define uloc_countAvailable U_ICU_ENTRY_POINT_RENAME(uloc_countAvailable)
#define uloc_forLanguageTag U_ICU_ENTRY_POINT_RENAME(uloc_forLanguageTag)
#define uloc_getAvailable U_ICU_ENTRY_POINT_RENAME(uloc_getAvailable)
#define uloc_getBaseName U_ICU_ENTRY_POINT_RENAME(uloc_getBaseName)
#define uloc_getCharacterOrientation U_ICU_ENTRY_POINT_RENAME(uloc_getCharacterOrientation)
#define uloc_getCountry U_ICU_ENTRY_POINT_RENAME(uloc_getCountry)
#define uloc_getCurrentCountryID U_ICU_ENTRY_POINT_RENAME(uloc_getCurrentCountryID)
#define uloc_getCurrentLanguageID U_ICU_ENTRY_POINT_RENAME(uloc_getCurrentLanguageID)
#define uloc_getDefault U_ICU_ENTRY_POINT_RENAME(uloc_getDefault)
#define uloc_getDisplayCountry U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayCountry)
#define uloc_getDisplayKeyword U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayKeyword)
#define uloc_getDisplayKeywordValue U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayKeywordValue)
#define uloc_getDisplayLanguage U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayLanguage)
#define uloc_getDisplayName U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayName)
#define uloc_getDisplayScript U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayScript)
#define uloc_getDisplayVariant U_ICU_ENTRY_POINT_RENAME(uloc_getDisplayVariant)
#define uloc_getISO3Country U_ICU_ENTRY_POINT_RENAME(uloc_getISO3Country)
#define uloc_getISO3Language U_ICU_ENTRY_POINT_RENAME(uloc_getISO3Language)
#define uloc_getISOCountries U_ICU_ENTRY_POINT_RENAME(uloc_getISOCountries)
#define uloc_getISOLanguages U_ICU_ENTRY_POINT_RENAME(uloc_getISOLanguages)
#define uloc_getKeywordValue U_ICU_ENTRY_POINT_RENAME(uloc_getKeywordValue)
#define uloc_getLCID U_ICU_ENTRY_POINT_RENAME(uloc_getLCID)
#define uloc_getLanguage U_ICU_ENTRY_POINT_RENAME(uloc_getLanguage)
#define uloc_getLineOrientation U_ICU_ENTRY_POINT_RENAME(uloc_getLineOrientation)
#define uloc_getLocaleForLCID U_ICU_ENTRY_POINT_RENAME(uloc_getLocaleForLCID)
#define uloc_getName U_ICU_ENTRY_POINT_RENAME(uloc_getName)
#define uloc_getParent U_ICU_ENTRY_POINT_RENAME(uloc_getParent)
#define uloc_getScript U_ICU_ENTRY_POINT_RENAME(uloc_getScript)
#define uloc_getTableStringWithFallback U_ICU_ENTRY_POINT_RENAME(uloc_getTableStringWithFallback)
#define uloc_getVariant U_ICU_ENTRY_POINT_RENAME(uloc_getVariant)
#define uloc_isRightToLeft U_ICU_ENTRY_POINT_RENAME(uloc_isRightToLeft)
#define uloc_minimizeSubtags U_ICU_ENTRY_POINT_RENAME(uloc_minimizeSubtags)
#define uloc_openAvailableByType U_ICU_ENTRY_POINT_RENAME(uloc_openAvailableByType)
#define uloc_openKeywordList U_ICU_ENTRY_POINT_RENAME(uloc_openKeywordList)
#define uloc_openKeywords U_ICU_ENTRY_POINT_RENAME(uloc_openKeywords)
#define uloc_setDefault U_ICU_ENTRY_POINT_RENAME(uloc_setDefault)
#define uloc_setKeywordValue U_ICU_ENTRY_POINT_RENAME(uloc_setKeywordValue)
#define uloc_toLanguageTag U_ICU_ENTRY_POINT_RENAME(uloc_toLanguageTag)
#define uloc_toLegacyKey U_ICU_ENTRY_POINT_RENAME(uloc_toLegacyKey)
#define uloc_toLegacyType U_ICU_ENTRY_POINT_RENAME(uloc_toLegacyType)
#define uloc_toUnicodeLocaleKey U_ICU_ENTRY_POINT_RENAME(uloc_toUnicodeLocaleKey)
#define uloc_toUnicodeLocaleType U_ICU_ENTRY_POINT_RENAME(uloc_toUnicodeLocaleType)
#define ulocdata_close U_ICU_ENTRY_POINT_RENAME(ulocdata_close)
#define ulocdata_getCLDRVersion U_ICU_ENTRY_POINT_RENAME(ulocdata_getCLDRVersion)
#define ulocdata_getDelimiter U_ICU_ENTRY_POINT_RENAME(ulocdata_getDelimiter)
#define ulocdata_getExemplarSet U_ICU_ENTRY_POINT_RENAME(ulocdata_getExemplarSet)
#define ulocdata_getLocaleDisplayPattern U_ICU_ENTRY_POINT_RENAME(ulocdata_getLocaleDisplayPattern)
#define ulocdata_getLocaleSeparator U_ICU_ENTRY_POINT_RENAME(ulocdata_getLocaleSeparator)
#define ulocdata_getMeasurementSystem U_ICU_ENTRY_POINT_RENAME(ulocdata_getMeasurementSystem)
#define ulocdata_getNoSubstitute U_ICU_ENTRY_POINT_RENAME(ulocdata_getNoSubstitute)
#define ulocdata_getPaperSize U_ICU_ENTRY_POINT_RENAME(ulocdata_getPaperSize)
#define ulocdata_open U_ICU_ENTRY_POINT_RENAME(ulocdata_open)
#define ulocdata_setNoSubstitute U_ICU_ENTRY_POINT_RENAME(ulocdata_setNoSubstitute)
#define ulocimp_addLikelySubtags U_ICU_ENTRY_POINT_RENAME(ulocimp_addLikelySubtags)
#define ulocimp_canonicalize U_ICU_ENTRY_POINT_RENAME(ulocimp_canonicalize)
#define ulocimp_forLanguageTag U_ICU_ENTRY_POINT_RENAME(ulocimp_forLanguageTag)
#define ulocimp_getBaseName U_ICU_ENTRY_POINT_RENAME(ulocimp_getBaseName)
#define ulocimp_getCountry U_ICU_ENTRY_POINT_RENAME(ulocimp_getCountry)
#define ulocimp_getKeywordValue U_ICU_ENTRY_POINT_RENAME(ulocimp_getKeywordValue)
#define ulocimp_getKeywords U_ICU_ENTRY_POINT_RENAME(ulocimp_getKeywords)
#define ulocimp_getKnownCanonicalizedLocaleForTest U_ICU_ENTRY_POINT_RENAME(ulocimp_getKnownCanonicalizedLocaleForTest)
#define ulocimp_getLanguage U_ICU_ENTRY_POINT_RENAME(ulocimp_getLanguage)
#define ulocimp_getName U_ICU_ENTRY_POINT_RENAME(ulocimp_getName)
#define ulocimp_getRegionForSupplementalData U_ICU_ENTRY_POINT_RENAME(ulocimp_getRegionForSupplementalData)
#define ulocimp_getScript U_ICU_ENTRY_POINT_RENAME(ulocimp_getScript)
#define ulocimp_isCanonicalizedLocaleForTest U_ICU_ENTRY_POINT_RENAME(ulocimp_isCanonicalizedLocaleForTest)
#define ulocimp_minimizeSubtags U_ICU_ENTRY_POINT_RENAME(ulocimp_minimizeSubtags)
#define ulocimp_toBcpKey U_ICU_ENTRY_POINT_RENAME(ulocimp_toBcpKey)
#define ulocimp_toBcpType U_ICU_ENTRY_POINT_RENAME(ulocimp_toBcpType)
#define ulocimp_toLanguageTag U_ICU_ENTRY_POINT_RENAME(ulocimp_toLanguageTag)
#define ulocimp_toLegacyKey U_ICU_ENTRY_POINT_RENAME(ulocimp_toLegacyKey)
#define ulocimp_toLegacyType U_ICU_ENTRY_POINT_RENAME(ulocimp_toLegacyType)
#define ultag_getTKeyStart U_ICU_ENTRY_POINT_RENAME(ultag_getTKeyStart)
#define ultag_isExtensionSubtags U_ICU_ENTRY_POINT_RENAME(ultag_isExtensionSubtags)
#define ultag_isLanguageSubtag U_ICU_ENTRY_POINT_RENAME(ultag_isLanguageSubtag)
#define ultag_isPrivateuseValueSubtags U_ICU_ENTRY_POINT_RENAME(ultag_isPrivateuseValueSubtags)
#define ultag_isRegionSubtag U_ICU_ENTRY_POINT_RENAME(ultag_isRegionSubtag)
#define ultag_isScriptSubtag U_ICU_ENTRY_POINT_RENAME(ultag_isScriptSubtag)
#define ultag_isTransformedExtensionSubtags U_ICU_ENTRY_POINT_RENAME(ultag_isTransformedExtensionSubtags)
#define ultag_isUnicodeExtensionSubtags U_ICU_ENTRY_POINT_RENAME(ultag_isUnicodeExtensionSubtags)
#define ultag_isUnicodeLocaleAttribute U_ICU_ENTRY_POINT_RENAME(ultag_isUnicodeLocaleAttribute)
#define ultag_isUnicodeLocaleAttributes U_ICU_ENTRY_POINT_RENAME(ultag_isUnicodeLocaleAttributes)
#define ultag_isUnicodeLocaleKey U_ICU_ENTRY_POINT_RENAME(ultag_isUnicodeLocaleKey)
#define ultag_isUnicodeLocaleType U_ICU_ENTRY_POINT_RENAME(ultag_isUnicodeLocaleType)
#define ultag_isVariantSubtags U_ICU_ENTRY_POINT_RENAME(ultag_isVariantSubtags)
#define umeas_getPrefixBase U_ICU_ENTRY_POINT_RENAME(umeas_getPrefixBase)
#define umeas_getPrefixPower U_ICU_ENTRY_POINT_RENAME(umeas_getPrefixPower)
#define umsg_applyPattern U_ICU_ENTRY_POINT_RENAME(umsg_applyPattern)
#define umsg_autoQuoteApostrophe U_ICU_ENTRY_POINT_RENAME(umsg_autoQuoteApostrophe)
#define umsg_clone U_ICU_ENTRY_POINT_RENAME(umsg_clone)
#define umsg_close U_ICU_ENTRY_POINT_RENAME(umsg_close)
#define umsg_format U_ICU_ENTRY_POINT_RENAME(umsg_format)
#define umsg_getLocale U_ICU_ENTRY_POINT_RENAME(umsg_getLocale)
#define umsg_open U_ICU_ENTRY_POINT_RENAME(umsg_open)
#define umsg_parse U_ICU_ENTRY_POINT_RENAME(umsg_parse)
#define umsg_setLocale U_ICU_ENTRY_POINT_RENAME(umsg_setLocale)
#define umsg_toPattern U_ICU_ENTRY_POINT_RENAME(umsg_toPattern)
#define umsg_vformat U_ICU_ENTRY_POINT_RENAME(umsg_vformat)
#define umsg_vparse U_ICU_ENTRY_POINT_RENAME(umsg_vparse)
#define umtx_lock U_ICU_ENTRY_POINT_RENAME(umtx_lock)
#define umtx_unlock U_ICU_ENTRY_POINT_RENAME(umtx_unlock)
#define umutablecptrie_buildImmutable U_ICU_ENTRY_POINT_RENAME(umutablecptrie_buildImmutable)
#define umutablecptrie_clone U_ICU_ENTRY_POINT_RENAME(umutablecptrie_clone)
#define umutablecptrie_close U_ICU_ENTRY_POINT_RENAME(umutablecptrie_close)
#define umutablecptrie_fromUCPMap U_ICU_ENTRY_POINT_RENAME(umutablecptrie_fromUCPMap)
#define umutablecptrie_fromUCPTrie U_ICU_ENTRY_POINT_RENAME(umutablecptrie_fromUCPTrie)
#define umutablecptrie_get U_ICU_ENTRY_POINT_RENAME(umutablecptrie_get)
#define umutablecptrie_getRange U_ICU_ENTRY_POINT_RENAME(umutablecptrie_getRange)
#define umutablecptrie_open U_ICU_ENTRY_POINT_RENAME(umutablecptrie_open)
#define umutablecptrie_set U_ICU_ENTRY_POINT_RENAME(umutablecptrie_set)
#define umutablecptrie_setRange U_ICU_ENTRY_POINT_RENAME(umutablecptrie_setRange)
#define uniset_getUnicode32Instance U_ICU_ENTRY_POINT_RENAME(uniset_getUnicode32Instance)
#define unorm2_append U_ICU_ENTRY_POINT_RENAME(unorm2_append)
#define unorm2_close U_ICU_ENTRY_POINT_RENAME(unorm2_close)
#define unorm2_composePair U_ICU_ENTRY_POINT_RENAME(unorm2_composePair)
#define unorm2_getCombiningClass U_ICU_ENTRY_POINT_RENAME(unorm2_getCombiningClass)
#define unorm2_getDecomposition U_ICU_ENTRY_POINT_RENAME(unorm2_getDecomposition)
#define unorm2_getInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getInstance)
#define unorm2_getNFCInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getNFCInstance)
#define unorm2_getNFDInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getNFDInstance)
#define unorm2_getNFKCCasefoldInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getNFKCCasefoldInstance)
#define unorm2_getNFKCInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getNFKCInstance)
#define unorm2_getNFKDInstance U_ICU_ENTRY_POINT_RENAME(unorm2_getNFKDInstance)
#define unorm2_getRawDecomposition U_ICU_ENTRY_POINT_RENAME(unorm2_getRawDecomposition)
#define unorm2_hasBoundaryAfter U_ICU_ENTRY_POINT_RENAME(unorm2_hasBoundaryAfter)
#define unorm2_hasBoundaryBefore U_ICU_ENTRY_POINT_RENAME(unorm2_hasBoundaryBefore)
#define unorm2_isInert U_ICU_ENTRY_POINT_RENAME(unorm2_isInert)
#define unorm2_isNormalized U_ICU_ENTRY_POINT_RENAME(unorm2_isNormalized)
#define unorm2_normalize U_ICU_ENTRY_POINT_RENAME(unorm2_normalize)
#define unorm2_normalizeSecondAndAppend U_ICU_ENTRY_POINT_RENAME(unorm2_normalizeSecondAndAppend)
#define unorm2_openFiltered U_ICU_ENTRY_POINT_RENAME(unorm2_openFiltered)
#define unorm2_quickCheck U_ICU_ENTRY_POINT_RENAME(unorm2_quickCheck)
#define unorm2_spanQuickCheckYes U_ICU_ENTRY_POINT_RENAME(unorm2_spanQuickCheckYes)
#define unorm2_swap U_ICU_ENTRY_POINT_RENAME(unorm2_swap)
#define unorm_compare U_ICU_ENTRY_POINT_RENAME(unorm_compare)
#define unorm_concatenate U_ICU_ENTRY_POINT_RENAME(unorm_concatenate)
#define unorm_getFCD16 U_ICU_ENTRY_POINT_RENAME(unorm_getFCD16)
#define unorm_getQuickCheck U_ICU_ENTRY_POINT_RENAME(unorm_getQuickCheck)
#define unorm_isNormalized U_ICU_ENTRY_POINT_RENAME(unorm_isNormalized)
#define unorm_isNormalizedWithOptions U_ICU_ENTRY_POINT_RENAME(unorm_isNormalizedWithOptions)
#define unorm_next U_ICU_ENTRY_POINT_RENAME(unorm_next)
#define unorm_normalize U_ICU_ENTRY_POINT_RENAME(unorm_normalize)
#define unorm_previous U_ICU_ENTRY_POINT_RENAME(unorm_previous)
#define unorm_quickCheck U_ICU_ENTRY_POINT_RENAME(unorm_quickCheck)
#define unorm_quickCheckWithOptions U_ICU_ENTRY_POINT_RENAME(unorm_quickCheckWithOptions)
#define unum_applyPattern U_ICU_ENTRY_POINT_RENAME(unum_applyPattern)
#define unum_clone U_ICU_ENTRY_POINT_RENAME(unum_clone)
#define unum_close U_ICU_ENTRY_POINT_RENAME(unum_close)
#define unum_countAvailable U_ICU_ENTRY_POINT_RENAME(unum_countAvailable)
#define unum_format U_ICU_ENTRY_POINT_RENAME(unum_format)
#define unum_formatDecimal U_ICU_ENTRY_POINT_RENAME(unum_formatDecimal)
#define unum_formatDouble U_ICU_ENTRY_POINT_RENAME(unum_formatDouble)
#define unum_formatDoubleCurrency U_ICU_ENTRY_POINT_RENAME(unum_formatDoubleCurrency)
#define unum_formatDoubleForFields U_ICU_ENTRY_POINT_RENAME(unum_formatDoubleForFields)
#define unum_formatInt64 U_ICU_ENTRY_POINT_RENAME(unum_formatInt64)
#define unum_formatUFormattable U_ICU_ENTRY_POINT_RENAME(unum_formatUFormattable)
#define unum_getAttribute U_ICU_ENTRY_POINT_RENAME(unum_getAttribute)
#define unum_getAvailable U_ICU_ENTRY_POINT_RENAME(unum_getAvailable)
#define unum_getContext U_ICU_ENTRY_POINT_RENAME(unum_getContext)
#define unum_getDoubleAttribute U_ICU_ENTRY_POINT_RENAME(unum_getDoubleAttribute)
#define unum_getLocaleByType U_ICU_ENTRY_POINT_RENAME(unum_getLocaleByType)
#define unum_getSymbol U_ICU_ENTRY_POINT_RENAME(unum_getSymbol)
#define unum_getTextAttribute U_ICU_ENTRY_POINT_RENAME(unum_getTextAttribute)
#define unum_open U_ICU_ENTRY_POINT_RENAME(unum_open)
#define unum_parse U_ICU_ENTRY_POINT_RENAME(unum_parse)
#define unum_parseDecimal U_ICU_ENTRY_POINT_RENAME(unum_parseDecimal)
#define unum_parseDouble U_ICU_ENTRY_POINT_RENAME(unum_parseDouble)
#define unum_parseDoubleCurrency U_ICU_ENTRY_POINT_RENAME(unum_parseDoubleCurrency)
#define unum_parseInt64 U_ICU_ENTRY_POINT_RENAME(unum_parseInt64)
#define unum_parseToUFormattable U_ICU_ENTRY_POINT_RENAME(unum_parseToUFormattable)
#define unum_setAttribute U_ICU_ENTRY_POINT_RENAME(unum_setAttribute)
#define unum_setContext U_ICU_ENTRY_POINT_RENAME(unum_setContext)
#define unum_setDoubleAttribute U_ICU_ENTRY_POINT_RENAME(unum_setDoubleAttribute)
#define unum_setSymbol U_ICU_ENTRY_POINT_RENAME(unum_setSymbol)
#define unum_setTextAttribute U_ICU_ENTRY_POINT_RENAME(unum_setTextAttribute)
#define unum_toPattern U_ICU_ENTRY_POINT_RENAME(unum_toPattern)
#define unumf_close U_ICU_ENTRY_POINT_RENAME(unumf_close)
#define unumf_closeResult U_ICU_ENTRY_POINT_RENAME(unumf_closeResult)
#define unumf_formatDecimal U_ICU_ENTRY_POINT_RENAME(unumf_formatDecimal)
#define unumf_formatDouble U_ICU_ENTRY_POINT_RENAME(unumf_formatDouble)
#define unumf_formatInt U_ICU_ENTRY_POINT_RENAME(unumf_formatInt)
#define unumf_openForSkeletonAndLocale U_ICU_ENTRY_POINT_RENAME(unumf_openForSkeletonAndLocale)
#define unumf_openForSkeletonAndLocaleWithError U_ICU_ENTRY_POINT_RENAME(unumf_openForSkeletonAndLocaleWithError)
#define unumf_openResult U_ICU_ENTRY_POINT_RENAME(unumf_openResult)
#define unumf_resultAsValue U_ICU_ENTRY_POINT_RENAME(unumf_resultAsValue)
#define unumf_resultGetAllFieldPositions U_ICU_ENTRY_POINT_RENAME(unumf_resultGetAllFieldPositions)
#define unumf_resultNextFieldPosition U_ICU_ENTRY_POINT_RENAME(unumf_resultNextFieldPosition)
#define unumf_resultToDecimalNumber U_ICU_ENTRY_POINT_RENAME(unumf_resultToDecimalNumber)
#define unumf_resultToString U_ICU_ENTRY_POINT_RENAME(unumf_resultToString)
#define unumrf_close U_ICU_ENTRY_POINT_RENAME(unumrf_close)
#define unumrf_closeResult U_ICU_ENTRY_POINT_RENAME(unumrf_closeResult)
#define unumrf_formatDecimalRange U_ICU_ENTRY_POINT_RENAME(unumrf_formatDecimalRange)
#define unumrf_formatDoubleRange U_ICU_ENTRY_POINT_RENAME(unumrf_formatDoubleRange)
#define unumrf_openForSkeletonWithCollapseAndIdentityFallback U_ICU_ENTRY_POINT_RENAME(unumrf_openForSkeletonWithCollapseAndIdentityFallback)
#define unumrf_openResult U_ICU_ENTRY_POINT_RENAME(unumrf_openResult)
#define unumrf_resultAsValue U_ICU_ENTRY_POINT_RENAME(unumrf_resultAsValue)
#define unumrf_resultGetFirstDecimalNumber U_ICU_ENTRY_POINT_RENAME(unumrf_resultGetFirstDecimalNumber)
#define unumrf_resultGetIdentityResult U_ICU_ENTRY_POINT_RENAME(unumrf_resultGetIdentityResult)
#define unumrf_resultGetSecondDecimalNumber U_ICU_ENTRY_POINT_RENAME(unumrf_resultGetSecondDecimalNumber)
#define unumsys_close U_ICU_ENTRY_POINT_RENAME(unumsys_close)
#define unumsys_getDescription U_ICU_ENTRY_POINT_RENAME(unumsys_getDescription)
#define unumsys_getName U_ICU_ENTRY_POINT_RENAME(unumsys_getName)
#define unumsys_getRadix U_ICU_ENTRY_POINT_RENAME(unumsys_getRadix)
#define unumsys_isAlgorithmic U_ICU_ENTRY_POINT_RENAME(unumsys_isAlgorithmic)
#define unumsys_open U_ICU_ENTRY_POINT_RENAME(unumsys_open)
#define unumsys_openAvailableNames U_ICU_ENTRY_POINT_RENAME(unumsys_openAvailableNames)
#define unumsys_openByName U_ICU_ENTRY_POINT_RENAME(unumsys_openByName)
#define uplrules_close U_ICU_ENTRY_POINT_RENAME(uplrules_close)
#define uplrules_getKeywords U_ICU_ENTRY_POINT_RENAME(uplrules_getKeywords)
#define uplrules_open U_ICU_ENTRY_POINT_RENAME(uplrules_open)
#define uplrules_openForType U_ICU_ENTRY_POINT_RENAME(uplrules_openForType)
#define uplrules_select U_ICU_ENTRY_POINT_RENAME(uplrules_select)
#define uplrules_selectForRange U_ICU_ENTRY_POINT_RENAME(uplrules_selectForRange)
#define uplrules_selectFormatted U_ICU_ENTRY_POINT_RENAME(uplrules_selectFormatted)
#define uplrules_selectWithFormat U_ICU_ENTRY_POINT_RENAME(uplrules_selectWithFormat)
#define uplug_closeLibrary U_ICU_ENTRY_POINT_RENAME(uplug_closeLibrary)
#define uplug_findLibrary U_ICU_ENTRY_POINT_RENAME(uplug_findLibrary)
#define uplug_getConfiguration U_ICU_ENTRY_POINT_RENAME(uplug_getConfiguration)
#define uplug_getContext U_ICU_ENTRY_POINT_RENAME(uplug_getContext)
#define uplug_getCurrentLevel U_ICU_ENTRY_POINT_RENAME(uplug_getCurrentLevel)
#define uplug_getLibrary U_ICU_ENTRY_POINT_RENAME(uplug_getLibrary)
#define uplug_getLibraryName U_ICU_ENTRY_POINT_RENAME(uplug_getLibraryName)
#define uplug_getPlugInternal U_ICU_ENTRY_POINT_RENAME(uplug_getPlugInternal)
#define uplug_getPlugLevel U_ICU_ENTRY_POINT_RENAME(uplug_getPlugLevel)
#define uplug_getPlugLoadStatus U_ICU_ENTRY_POINT_RENAME(uplug_getPlugLoadStatus)
#define uplug_getPlugName U_ICU_ENTRY_POINT_RENAME(uplug_getPlugName)
#define uplug_getPluginFile U_ICU_ENTRY_POINT_RENAME(uplug_getPluginFile)
#define uplug_getSymbolName U_ICU_ENTRY_POINT_RENAME(uplug_getSymbolName)
#define uplug_init U_ICU_ENTRY_POINT_RENAME(uplug_init)
#define uplug_loadPlugFromEntrypoint U_ICU_ENTRY_POINT_RENAME(uplug_loadPlugFromEntrypoint)
#define uplug_loadPlugFromLibrary U_ICU_ENTRY_POINT_RENAME(uplug_loadPlugFromLibrary)
#define uplug_nextPlug U_ICU_ENTRY_POINT_RENAME(uplug_nextPlug)
#define uplug_openLibrary U_ICU_ENTRY_POINT_RENAME(uplug_openLibrary)
#define uplug_removePlug U_ICU_ENTRY_POINT_RENAME(uplug_removePlug)
#define uplug_setContext U_ICU_ENTRY_POINT_RENAME(uplug_setContext)
#define uplug_setPlugLevel U_ICU_ENTRY_POINT_RENAME(uplug_setPlugLevel)
#define uplug_setPlugName U_ICU_ENTRY_POINT_RENAME(uplug_setPlugName)
#define uplug_setPlugNoUnload U_ICU_ENTRY_POINT_RENAME(uplug_setPlugNoUnload)
#define uprops_addPropertyStarts U_ICU_ENTRY_POINT_RENAME(uprops_addPropertyStarts)
#define uprops_getSource U_ICU_ENTRY_POINT_RENAME(uprops_getSource)
#define upropsvec_addPropertyStarts U_ICU_ENTRY_POINT_RENAME(upropsvec_addPropertyStarts)
#define uprv_add32_overflow U_ICU_ENTRY_POINT_RENAME(uprv_add32_overflow)
#define uprv_aestrncpy U_ICU_ENTRY_POINT_RENAME(uprv_aestrncpy)
#define uprv_asciiFromEbcdic U_ICU_ENTRY_POINT_RENAME(uprv_asciiFromEbcdic)
#define uprv_asciitolower U_ICU_ENTRY_POINT_RENAME(uprv_asciitolower)
#define uprv_calloc U_ICU_ENTRY_POINT_RENAME(uprv_calloc)
#define uprv_ceil U_ICU_ENTRY_POINT_RENAME(uprv_ceil)
#define uprv_compareASCIIPropertyNames U_ICU_ENTRY_POINT_RENAME(uprv_compareASCIIPropertyNames)
#define uprv_compareEBCDICPropertyNames U_ICU_ENTRY_POINT_RENAME(uprv_compareEBCDICPropertyNames)
#define uprv_compareInvAscii U_ICU_ENTRY_POINT_RENAME(uprv_compareInvAscii)
#define uprv_compareInvEbcdic U_ICU_ENTRY_POINT_RENAME(uprv_compareInvEbcdic)
#define uprv_compareInvEbcdicAsAscii U_ICU_ENTRY_POINT_RENAME(uprv_compareInvEbcdicAsAscii)
#define uprv_convertToLCID U_ICU_ENTRY_POINT_RENAME(uprv_convertToLCID)
#define uprv_convertToLCIDPlatform U_ICU_ENTRY_POINT_RENAME(uprv_convertToLCIDPlatform)
#define uprv_convertToPosix U_ICU_ENTRY_POINT_RENAME(uprv_convertToPosix)
#define uprv_copyAscii U_ICU_ENTRY_POINT_RENAME(uprv_copyAscii)
#define uprv_copyEbcdic U_ICU_ENTRY_POINT_RENAME(uprv_copyEbcdic)
#define uprv_decContextClearStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextClearStatus)
#define uprv_decContextDefault U_ICU_ENTRY_POINT_RENAME(uprv_decContextDefault)
#define uprv_decContextGetRounding U_ICU_ENTRY_POINT_RENAME(uprv_decContextGetRounding)
#define uprv_decContextGetStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextGetStatus)
#define uprv_decContextRestoreStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextRestoreStatus)
#define uprv_decContextSaveStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextSaveStatus)
#define uprv_decContextSetRounding U_ICU_ENTRY_POINT_RENAME(uprv_decContextSetRounding)
#define uprv_decContextSetStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextSetStatus)
#define uprv_decContextSetStatusFromString U_ICU_ENTRY_POINT_RENAME(uprv_decContextSetStatusFromString)
#define uprv_decContextSetStatusFromStringQuiet U_ICU_ENTRY_POINT_RENAME(uprv_decContextSetStatusFromStringQuiet)
#define uprv_decContextSetStatusQuiet U_ICU_ENTRY_POINT_RENAME(uprv_decContextSetStatusQuiet)
#define uprv_decContextStatusToString U_ICU_ENTRY_POINT_RENAME(uprv_decContextStatusToString)
#define uprv_decContextTestSavedStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextTestSavedStatus)
#define uprv_decContextTestStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextTestStatus)
#define uprv_decContextZeroStatus U_ICU_ENTRY_POINT_RENAME(uprv_decContextZeroStatus)
#define uprv_decNumberAbs U_ICU_ENTRY_POINT_RENAME(uprv_decNumberAbs)
#define uprv_decNumberAdd U_ICU_ENTRY_POINT_RENAME(uprv_decNumberAdd)
#define uprv_decNumberAnd U_ICU_ENTRY_POINT_RENAME(uprv_decNumberAnd)
#define uprv_decNumberClassToString U_ICU_ENTRY_POINT_RENAME(uprv_decNumberClassToString)
#define uprv_decNumberCompare U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCompare)
#define uprv_decNumberCompareSignal U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCompareSignal)
#define uprv_decNumberCompareTotal U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCompareTotal)
#define uprv_decNumberCompareTotalMag U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCompareTotalMag)
#define uprv_decNumberCopy U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCopy)
#define uprv_decNumberCopyAbs U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCopyAbs)
#define uprv_decNumberCopyNegate U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCopyNegate)
#define uprv_decNumberCopySign U_ICU_ENTRY_POINT_RENAME(uprv_decNumberCopySign)
#define uprv_decNumberDivide U_ICU_ENTRY_POINT_RENAME(uprv_decNumberDivide)
#define uprv_decNumberDivideInteger U_ICU_ENTRY_POINT_RENAME(uprv_decNumberDivideInteger)
#define uprv_decNumberExp U_ICU_ENTRY_POINT_RENAME(uprv_decNumberExp)
#define uprv_decNumberFMA U_ICU_ENTRY_POINT_RENAME(uprv_decNumberFMA)
#define uprv_decNumberFromInt32 U_ICU_ENTRY_POINT_RENAME(uprv_decNumberFromInt32)
#define uprv_decNumberFromString U_ICU_ENTRY_POINT_RENAME(uprv_decNumberFromString)
#define uprv_decNumberFromUInt32 U_ICU_ENTRY_POINT_RENAME(uprv_decNumberFromUInt32)
#define uprv_decNumberGetBCD U_ICU_ENTRY_POINT_RENAME(uprv_decNumberGetBCD)
#define uprv_decNumberInvert U_ICU_ENTRY_POINT_RENAME(uprv_decNumberInvert)
#define uprv_decNumberIsNormal U_ICU_ENTRY_POINT_RENAME(uprv_decNumberIsNormal)
#define uprv_decNumberIsSubnormal U_ICU_ENTRY_POINT_RENAME(uprv_decNumberIsSubnormal)
#define uprv_decNumberLn U_ICU_ENTRY_POINT_RENAME(uprv_decNumberLn)
#define uprv_decNumberLog10 U_ICU_ENTRY_POINT_RENAME(uprv_decNumberLog10)
#define uprv_decNumberLogB U_ICU_ENTRY_POINT_RENAME(uprv_decNumberLogB)
#define uprv_decNumberMax U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMax)
#define uprv_decNumberMaxMag U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMaxMag)
#define uprv_decNumberMin U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMin)
#define uprv_decNumberMinMag U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMinMag)
#define uprv_decNumberMinus U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMinus)
#define uprv_decNumberMultiply U_ICU_ENTRY_POINT_RENAME(uprv_decNumberMultiply)
#define uprv_decNumberNextMinus U_ICU_ENTRY_POINT_RENAME(uprv_decNumberNextMinus)
#define uprv_decNumberNextPlus U_ICU_ENTRY_POINT_RENAME(uprv_decNumberNextPlus)
#define uprv_decNumberNextToward U_ICU_ENTRY_POINT_RENAME(uprv_decNumberNextToward)
#define uprv_decNumberNormalize U_ICU_ENTRY_POINT_RENAME(uprv_decNumberNormalize)
#define uprv_decNumberOr U_ICU_ENTRY_POINT_RENAME(uprv_decNumberOr)
#define uprv_decNumberPlus U_ICU_ENTRY_POINT_RENAME(uprv_decNumberPlus)
#define uprv_decNumberPower U_ICU_ENTRY_POINT_RENAME(uprv_decNumberPower)
#define uprv_decNumberQuantize U_ICU_ENTRY_POINT_RENAME(uprv_decNumberQuantize)
#define uprv_decNumberReduce U_ICU_ENTRY_POINT_RENAME(uprv_decNumberReduce)
#define uprv_decNumberRemainder U_ICU_ENTRY_POINT_RENAME(uprv_decNumberRemainder)
#define uprv_decNumberRemainderNear U_ICU_ENTRY_POINT_RENAME(uprv_decNumberRemainderNear)
#define uprv_decNumberRescale U_ICU_ENTRY_POINT_RENAME(uprv_decNumberRescale)
#define uprv_decNumberRotate U_ICU_ENTRY_POINT_RENAME(uprv_decNumberRotate)
#define uprv_decNumberSameQuantum U_ICU_ENTRY_POINT_RENAME(uprv_decNumberSameQuantum)
#define uprv_decNumberScaleB U_ICU_ENTRY_POINT_RENAME(uprv_decNumberScaleB)
#define uprv_decNumberSetBCD U_ICU_ENTRY_POINT_RENAME(uprv_decNumberSetBCD)
#define uprv_decNumberShift U_ICU_ENTRY_POINT_RENAME(uprv_decNumberShift)
#define uprv_decNumberSquareRoot U_ICU_ENTRY_POINT_RENAME(uprv_decNumberSquareRoot)
#define uprv_decNumberSubtract U_ICU_ENTRY_POINT_RENAME(uprv_decNumberSubtract)
#define uprv_decNumberToEngString U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToEngString)
#define uprv_decNumberToInt32 U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToInt32)
#define uprv_decNumberToIntegralExact U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToIntegralExact)
#define uprv_decNumberToIntegralValue U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToIntegralValue)
#define uprv_decNumberToString U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToString)
#define uprv_decNumberToUInt32 U_ICU_ENTRY_POINT_RENAME(uprv_decNumberToUInt32)
#define uprv_decNumberTrim U_ICU_ENTRY_POINT_RENAME(uprv_decNumberTrim)
#define uprv_decNumberVersion U_ICU_ENTRY_POINT_RENAME(uprv_decNumberVersion)
#define uprv_decNumberXor U_ICU_ENTRY_POINT_RENAME(uprv_decNumberXor)
#define uprv_decNumberZero U_ICU_ENTRY_POINT_RENAME(uprv_decNumberZero)
#define uprv_deleteConditionalCE32 U_ICU_ENTRY_POINT_RENAME(uprv_deleteConditionalCE32)
#define uprv_deleteUObject U_ICU_ENTRY_POINT_RENAME(uprv_deleteUObject)
#define uprv_dl_close U_ICU_ENTRY_POINT_RENAME(uprv_dl_close)
#define uprv_dl_open U_ICU_ENTRY_POINT_RENAME(uprv_dl_open)
#define uprv_dlsym_func U_ICU_ENTRY_POINT_RENAME(uprv_dlsym_func)
#define uprv_eastrncpy U_ICU_ENTRY_POINT_RENAME(uprv_eastrncpy)
#define uprv_ebcdicFromAscii U_ICU_ENTRY_POINT_RENAME(uprv_ebcdicFromAscii)
#define uprv_ebcdicToAscii U_ICU_ENTRY_POINT_RENAME(uprv_ebcdicToAscii)
#define uprv_ebcdicToLowercaseAscii U_ICU_ENTRY_POINT_RENAME(uprv_ebcdicToLowercaseAscii)
#define uprv_ebcdictolower U_ICU_ENTRY_POINT_RENAME(uprv_ebcdictolower)
#define uprv_fabs U_ICU_ENTRY_POINT_RENAME(uprv_fabs)
#define uprv_floor U_ICU_ENTRY_POINT_RENAME(uprv_floor)
#define uprv_fmax U_ICU_ENTRY_POINT_RENAME(uprv_fmax)
#define uprv_fmin U_ICU_ENTRY_POINT_RENAME(uprv_fmin)
#define uprv_fmod U_ICU_ENTRY_POINT_RENAME(uprv_fmod)
#define uprv_free U_ICU_ENTRY_POINT_RENAME(uprv_free)
#define uprv_getCharNameCharacters U_ICU_ENTRY_POINT_RENAME(uprv_getCharNameCharacters)
#define uprv_getDefaultLocaleID U_ICU_ENTRY_POINT_RENAME(uprv_getDefaultLocaleID)
#define uprv_getInfinity U_ICU_ENTRY_POINT_RENAME(uprv_getInfinity)
#define uprv_getMaxCharNameLength U_ICU_ENTRY_POINT_RENAME(uprv_getMaxCharNameLength)
#define uprv_getMaxValues U_ICU_ENTRY_POINT_RENAME(uprv_getMaxValues)
#define uprv_getNaN U_ICU_ENTRY_POINT_RENAME(uprv_getNaN)
#define uprv_getRawUTCtime U_ICU_ENTRY_POINT_RENAME(uprv_getRawUTCtime)
#define uprv_getStaticCurrencyName U_ICU_ENTRY_POINT_RENAME(uprv_getStaticCurrencyName)
#define uprv_getUTCtime U_ICU_ENTRY_POINT_RENAME(uprv_getUTCtime)
#define uprv_int32Comparator U_ICU_ENTRY_POINT_RENAME(uprv_int32Comparator)
#define uprv_isASCIILetter U_ICU_ENTRY_POINT_RENAME(uprv_isASCIILetter)
#define uprv_isEbcdicAtSign U_ICU_ENTRY_POINT_RENAME(uprv_isEbcdicAtSign)
#define uprv_isInfinite U_ICU_ENTRY_POINT_RENAME(uprv_isInfinite)
#define uprv_isInvariantString U_ICU_ENTRY_POINT_RENAME(uprv_isInvariantString)
#define uprv_isInvariantUString U_ICU_ENTRY_POINT_RENAME(uprv_isInvariantUString)
#define uprv_isNaN U_ICU_ENTRY_POINT_RENAME(uprv_isNaN)
#define uprv_isNegativeInfinity U_ICU_ENTRY_POINT_RENAME(uprv_isNegativeInfinity)
#define uprv_isPositiveInfinity U_ICU_ENTRY_POINT_RENAME(uprv_isPositiveInfinity)
#define uprv_itou U_ICU_ENTRY_POINT_RENAME(uprv_itou)
#define uprv_log U_ICU_ENTRY_POINT_RENAME(uprv_log)
#define uprv_malloc U_ICU_ENTRY_POINT_RENAME(uprv_malloc)
#define uprv_mapFile U_ICU_ENTRY_POINT_RENAME(uprv_mapFile)
#define uprv_max U_ICU_ENTRY_POINT_RENAME(uprv_max)
#define uprv_maxMantissa U_ICU_ENTRY_POINT_RENAME(uprv_maxMantissa)
#define uprv_maximumPtr U_ICU_ENTRY_POINT_RENAME(uprv_maximumPtr)
#define uprv_min U_ICU_ENTRY_POINT_RENAME(uprv_min)
#define uprv_modf U_ICU_ENTRY_POINT_RENAME(uprv_modf)
#define uprv_mul32_overflow U_ICU_ENTRY_POINT_RENAME(uprv_mul32_overflow)
#define uprv_parseCurrency U_ICU_ENTRY_POINT_RENAME(uprv_parseCurrency)
#define uprv_pathIsAbsolute U_ICU_ENTRY_POINT_RENAME(uprv_pathIsAbsolute)
#define uprv_pow U_ICU_ENTRY_POINT_RENAME(uprv_pow)
#define uprv_pow10 U_ICU_ENTRY_POINT_RENAME(uprv_pow10)
#define uprv_realloc U_ICU_ENTRY_POINT_RENAME(uprv_realloc)
#define uprv_round U_ICU_ENTRY_POINT_RENAME(uprv_round)
#define uprv_sortArray U_ICU_ENTRY_POINT_RENAME(uprv_sortArray)
#define uprv_stableBinarySearch U_ICU_ENTRY_POINT_RENAME(uprv_stableBinarySearch)
#define uprv_strCompare U_ICU_ENTRY_POINT_RENAME(uprv_strCompare)
#define uprv_strdup U_ICU_ENTRY_POINT_RENAME(uprv_strdup)
#define uprv_stricmp U_ICU_ENTRY_POINT_RENAME(uprv_stricmp)
#define uprv_strndup U_ICU_ENTRY_POINT_RENAME(uprv_strndup)
#define uprv_strnicmp U_ICU_ENTRY_POINT_RENAME(uprv_strnicmp)
#define uprv_syntaxError U_ICU_ENTRY_POINT_RENAME(uprv_syntaxError)
#define uprv_timezone U_ICU_ENTRY_POINT_RENAME(uprv_timezone)
#define uprv_toupper U_ICU_ENTRY_POINT_RENAME(uprv_toupper)
#define uprv_trunc U_ICU_ENTRY_POINT_RENAME(uprv_trunc)
#define uprv_tzname U_ICU_ENTRY_POINT_RENAME(uprv_tzname)
#define uprv_tzname_clear_cache U_ICU_ENTRY_POINT_RENAME(uprv_tzname_clear_cache)
#define uprv_tzset U_ICU_ENTRY_POINT_RENAME(uprv_tzset)
#define uprv_uint16Comparator U_ICU_ENTRY_POINT_RENAME(uprv_uint16Comparator)
#define uprv_uint32Comparator U_ICU_ENTRY_POINT_RENAME(uprv_uint32Comparator)
#define uprv_unmapFile U_ICU_ENTRY_POINT_RENAME(uprv_unmapFile)
#define upvec_cloneArray U_ICU_ENTRY_POINT_RENAME(upvec_cloneArray)
#define upvec_close U_ICU_ENTRY_POINT_RENAME(upvec_close)
#define upvec_compact U_ICU_ENTRY_POINT_RENAME(upvec_compact)
#define upvec_compactToUTrie2Handler U_ICU_ENTRY_POINT_RENAME(upvec_compactToUTrie2Handler)
#define upvec_compactToUTrie2WithRowIndexes U_ICU_ENTRY_POINT_RENAME(upvec_compactToUTrie2WithRowIndexes)
#define upvec_getArray U_ICU_ENTRY_POINT_RENAME(upvec_getArray)
#define upvec_getRow U_ICU_ENTRY_POINT_RENAME(upvec_getRow)
#define upvec_getValue U_ICU_ENTRY_POINT_RENAME(upvec_getValue)
#define upvec_open U_ICU_ENTRY_POINT_RENAME(upvec_open)
#define upvec_setValue U_ICU_ENTRY_POINT_RENAME(upvec_setValue)
#define uregex_appendReplacement U_ICU_ENTRY_POINT_RENAME(uregex_appendReplacement)
#define uregex_appendReplacementUText U_ICU_ENTRY_POINT_RENAME(uregex_appendReplacementUText)
#define uregex_appendTail U_ICU_ENTRY_POINT_RENAME(uregex_appendTail)
#define uregex_appendTailUText U_ICU_ENTRY_POINT_RENAME(uregex_appendTailUText)
#define uregex_clone U_ICU_ENTRY_POINT_RENAME(uregex_clone)
#define uregex_close U_ICU_ENTRY_POINT_RENAME(uregex_close)
#define uregex_end U_ICU_ENTRY_POINT_RENAME(uregex_end)
#define uregex_end64 U_ICU_ENTRY_POINT_RENAME(uregex_end64)
#define uregex_find U_ICU_ENTRY_POINT_RENAME(uregex_find)
#define uregex_find64 U_ICU_ENTRY_POINT_RENAME(uregex_find64)
#define uregex_findNext U_ICU_ENTRY_POINT_RENAME(uregex_findNext)
#define uregex_flags U_ICU_ENTRY_POINT_RENAME(uregex_flags)
#define uregex_getFindProgressCallback U_ICU_ENTRY_POINT_RENAME(uregex_getFindProgressCallback)
#define uregex_getMatchCallback U_ICU_ENTRY_POINT_RENAME(uregex_getMatchCallback)
#define uregex_getStackLimit U_ICU_ENTRY_POINT_RENAME(uregex_getStackLimit)
#define uregex_getText U_ICU_ENTRY_POINT_RENAME(uregex_getText)
#define uregex_getTimeLimit U_ICU_ENTRY_POINT_RENAME(uregex_getTimeLimit)
#define uregex_getUText U_ICU_ENTRY_POINT_RENAME(uregex_getUText)
#define uregex_group U_ICU_ENTRY_POINT_RENAME(uregex_group)
#define uregex_groupCount U_ICU_ENTRY_POINT_RENAME(uregex_groupCount)
#define uregex_groupNumberFromCName U_ICU_ENTRY_POINT_RENAME(uregex_groupNumberFromCName)
#define uregex_groupNumberFromName U_ICU_ENTRY_POINT_RENAME(uregex_groupNumberFromName)
#define uregex_groupUText U_ICU_ENTRY_POINT_RENAME(uregex_groupUText)
#define uregex_hasAnchoringBounds U_ICU_ENTRY_POINT_RENAME(uregex_hasAnchoringBounds)
#define uregex_hasTransparentBounds U_ICU_ENTRY_POINT_RENAME(uregex_hasTransparentBounds)
#define uregex_hitEnd U_ICU_ENTRY_POINT_RENAME(uregex_hitEnd)
#define uregex_lookingAt U_ICU_ENTRY_POINT_RENAME(uregex_lookingAt)
#define uregex_lookingAt64 U_ICU_ENTRY_POINT_RENAME(uregex_lookingAt64)
#define uregex_matches U_ICU_ENTRY_POINT_RENAME(uregex_matches)
#define uregex_matches64 U_ICU_ENTRY_POINT_RENAME(uregex_matches64)
#define uregex_open U_ICU_ENTRY_POINT_RENAME(uregex_open)
#define uregex_openC U_ICU_ENTRY_POINT_RENAME(uregex_openC)
#define uregex_openUText U_ICU_ENTRY_POINT_RENAME(uregex_openUText)
#define uregex_pattern U_ICU_ENTRY_POINT_RENAME(uregex_pattern)
#define uregex_patternUText U_ICU_ENTRY_POINT_RENAME(uregex_patternUText)
#define uregex_refreshUText U_ICU_ENTRY_POINT_RENAME(uregex_refreshUText)
#define uregex_regionEnd U_ICU_ENTRY_POINT_RENAME(uregex_regionEnd)
#define uregex_regionEnd64 U_ICU_ENTRY_POINT_RENAME(uregex_regionEnd64)
#define uregex_regionStart U_ICU_ENTRY_POINT_RENAME(uregex_regionStart)
#define uregex_regionStart64 U_ICU_ENTRY_POINT_RENAME(uregex_regionStart64)
#define uregex_replaceAll U_ICU_ENTRY_POINT_RENAME(uregex_replaceAll)
#define uregex_replaceAllUText U_ICU_ENTRY_POINT_RENAME(uregex_replaceAllUText)
#define uregex_replaceFirst U_ICU_ENTRY_POINT_RENAME(uregex_replaceFirst)
#define uregex_replaceFirstUText U_ICU_ENTRY_POINT_RENAME(uregex_replaceFirstUText)
#define uregex_requireEnd U_ICU_ENTRY_POINT_RENAME(uregex_requireEnd)
#define uregex_reset U_ICU_ENTRY_POINT_RENAME(uregex_reset)
#define uregex_reset64 U_ICU_ENTRY_POINT_RENAME(uregex_reset64)
#define uregex_setFindProgressCallback U_ICU_ENTRY_POINT_RENAME(uregex_setFindProgressCallback)
#define uregex_setMatchCallback U_ICU_ENTRY_POINT_RENAME(uregex_setMatchCallback)
#define uregex_setRegion U_ICU_ENTRY_POINT_RENAME(uregex_setRegion)
#define uregex_setRegion64 U_ICU_ENTRY_POINT_RENAME(uregex_setRegion64)
#define uregex_setRegionAndStart U_ICU_ENTRY_POINT_RENAME(uregex_setRegionAndStart)
#define uregex_setStackLimit U_ICU_ENTRY_POINT_RENAME(uregex_setStackLimit)
#define uregex_setText U_ICU_ENTRY_POINT_RENAME(uregex_setText)
#define uregex_setTimeLimit U_ICU_ENTRY_POINT_RENAME(uregex_setTimeLimit)
#define uregex_setUText U_ICU_ENTRY_POINT_RENAME(uregex_setUText)
#define uregex_split U_ICU_ENTRY_POINT_RENAME(uregex_split)
#define uregex_splitUText U_ICU_ENTRY_POINT_RENAME(uregex_splitUText)
#define uregex_start U_ICU_ENTRY_POINT_RENAME(uregex_start)
#define uregex_start64 U_ICU_ENTRY_POINT_RENAME(uregex_start64)
#define uregex_ucstr_unescape_charAt U_ICU_ENTRY_POINT_RENAME(uregex_ucstr_unescape_charAt)
#define uregex_useAnchoringBounds U_ICU_ENTRY_POINT_RENAME(uregex_useAnchoringBounds)
#define uregex_useTransparentBounds U_ICU_ENTRY_POINT_RENAME(uregex_useTransparentBounds)
#define uregex_utext_unescape_charAt U_ICU_ENTRY_POINT_RENAME(uregex_utext_unescape_charAt)
#define uregion_areEqual U_ICU_ENTRY_POINT_RENAME(uregion_areEqual)
#define uregion_contains U_ICU_ENTRY_POINT_RENAME(uregion_contains)
#define uregion_getAvailable U_ICU_ENTRY_POINT_RENAME(uregion_getAvailable)
#define uregion_getContainedRegions U_ICU_ENTRY_POINT_RENAME(uregion_getContainedRegions)
#define uregion_getContainedRegionsOfType U_ICU_ENTRY_POINT_RENAME(uregion_getContainedRegionsOfType)
#define uregion_getContainingRegion U_ICU_ENTRY_POINT_RENAME(uregion_getContainingRegion)
#define uregion_getContainingRegionOfType U_ICU_ENTRY_POINT_RENAME(uregion_getContainingRegionOfType)
#define uregion_getNumericCode U_ICU_ENTRY_POINT_RENAME(uregion_getNumericCode)
#define uregion_getPreferredValues U_ICU_ENTRY_POINT_RENAME(uregion_getPreferredValues)
#define uregion_getRegionCode U_ICU_ENTRY_POINT_RENAME(uregion_getRegionCode)
#define uregion_getRegionFromCode U_ICU_ENTRY_POINT_RENAME(uregion_getRegionFromCode)
#define uregion_getRegionFromNumericCode U_ICU_ENTRY_POINT_RENAME(uregion_getRegionFromNumericCode)
#define uregion_getType U_ICU_ENTRY_POINT_RENAME(uregion_getType)
#define ureldatefmt_close U_ICU_ENTRY_POINT_RENAME(ureldatefmt_close)
#define ureldatefmt_closeResult U_ICU_ENTRY_POINT_RENAME(ureldatefmt_closeResult)
#define ureldatefmt_combineDateAndTime U_ICU_ENTRY_POINT_RENAME(ureldatefmt_combineDateAndTime)
#define ureldatefmt_format U_ICU_ENTRY_POINT_RENAME(ureldatefmt_format)
#define ureldatefmt_formatNumeric U_ICU_ENTRY_POINT_RENAME(ureldatefmt_formatNumeric)
#define ureldatefmt_formatNumericToResult U_ICU_ENTRY_POINT_RENAME(ureldatefmt_formatNumericToResult)
#define ureldatefmt_formatToResult U_ICU_ENTRY_POINT_RENAME(ureldatefmt_formatToResult)
#define ureldatefmt_open U_ICU_ENTRY_POINT_RENAME(ureldatefmt_open)
#define ureldatefmt_openResult U_ICU_ENTRY_POINT_RENAME(ureldatefmt_openResult)
#define ureldatefmt_resultAsValue U_ICU_ENTRY_POINT_RENAME(ureldatefmt_resultAsValue)
#define ures_close U_ICU_ENTRY_POINT_RENAME(ures_close)
#define ures_copyResb U_ICU_ENTRY_POINT_RENAME(ures_copyResb)
#define ures_countArrayItems U_ICU_ENTRY_POINT_RENAME(ures_countArrayItems)
#define ures_findResource U_ICU_ENTRY_POINT_RENAME(ures_findResource)
#define ures_findSubResource U_ICU_ENTRY_POINT_RENAME(ures_findSubResource)
#define ures_getAllItemsWithFallback U_ICU_ENTRY_POINT_RENAME(ures_getAllItemsWithFallback)
#define ures_getBinary U_ICU_ENTRY_POINT_RENAME(ures_getBinary)
#define ures_getByIndex U_ICU_ENTRY_POINT_RENAME(ures_getByIndex)
#define ures_getByKey U_ICU_ENTRY_POINT_RENAME(ures_getByKey)
#define ures_getByKeyWithFallback U_ICU_ENTRY_POINT_RENAME(ures_getByKeyWithFallback)
#define ures_getFunctionalEquivalent U_ICU_ENTRY_POINT_RENAME(ures_getFunctionalEquivalent)
#define ures_getInt U_ICU_ENTRY_POINT_RENAME(ures_getInt)
#define ures_getIntVector U_ICU_ENTRY_POINT_RENAME(ures_getIntVector)
#define ures_getKey U_ICU_ENTRY_POINT_RENAME(ures_getKey)
#define ures_getKeywordValues U_ICU_ENTRY_POINT_RENAME(ures_getKeywordValues)
#define ures_getLocale U_ICU_ENTRY_POINT_RENAME(ures_getLocale)
#define ures_getLocaleByType U_ICU_ENTRY_POINT_RENAME(ures_getLocaleByType)
#define ures_getLocaleInternal U_ICU_ENTRY_POINT_RENAME(ures_getLocaleInternal)
#define ures_getName U_ICU_ENTRY_POINT_RENAME(ures_getName)
#define ures_getNextResource U_ICU_ENTRY_POINT_RENAME(ures_getNextResource)
#define ures_getNextString U_ICU_ENTRY_POINT_RENAME(ures_getNextString)
#define ures_getSize U_ICU_ENTRY_POINT_RENAME(ures_getSize)
#define ures_getString U_ICU_ENTRY_POINT_RENAME(ures_getString)
#define ures_getStringByIndex U_ICU_ENTRY_POINT_RENAME(ures_getStringByIndex)
#define ures_getStringByKey U_ICU_ENTRY_POINT_RENAME(ures_getStringByKey)
#define ures_getStringByKeyWithFallback U_ICU_ENTRY_POINT_RENAME(ures_getStringByKeyWithFallback)
#define ures_getType U_ICU_ENTRY_POINT_RENAME(ures_getType)
#define ures_getUInt U_ICU_ENTRY_POINT_RENAME(ures_getUInt)
#define ures_getUTF8String U_ICU_ENTRY_POINT_RENAME(ures_getUTF8String)
#define ures_getUTF8StringByIndex U_ICU_ENTRY_POINT_RENAME(ures_getUTF8StringByIndex)
#define ures_getUTF8StringByKey U_ICU_ENTRY_POINT_RENAME(ures_getUTF8StringByKey)
#define ures_getValueWithFallback U_ICU_ENTRY_POINT_RENAME(ures_getValueWithFallback)
#define ures_getVersion U_ICU_ENTRY_POINT_RENAME(ures_getVersion)
#define ures_getVersionByKey U_ICU_ENTRY_POINT_RENAME(ures_getVersionByKey)
#define ures_getVersionNumber U_ICU_ENTRY_POINT_RENAME(ures_getVersionNumber)
#define ures_getVersionNumberInternal U_ICU_ENTRY_POINT_RENAME(ures_getVersionNumberInternal)
#define ures_hasNext U_ICU_ENTRY_POINT_RENAME(ures_hasNext)
#define ures_initStackObject U_ICU_ENTRY_POINT_RENAME(ures_initStackObject)
#define ures_open U_ICU_ENTRY_POINT_RENAME(ures_open)
#define ures_openAvailableLocales U_ICU_ENTRY_POINT_RENAME(ures_openAvailableLocales)
#define ures_openDirect U_ICU_ENTRY_POINT_RENAME(ures_openDirect)
#define ures_openDirectFillIn U_ICU_ENTRY_POINT_RENAME(ures_openDirectFillIn)
#define ures_openFillIn U_ICU_ENTRY_POINT_RENAME(ures_openFillIn)
#define ures_openNoDefault U_ICU_ENTRY_POINT_RENAME(ures_openNoDefault)
#define ures_openU U_ICU_ENTRY_POINT_RENAME(ures_openU)
#define ures_resetIterator U_ICU_ENTRY_POINT_RENAME(ures_resetIterator)
#define ures_swap U_ICU_ENTRY_POINT_RENAME(ures_swap)
#define uscript_breaksBetweenLetters U_ICU_ENTRY_POINT_RENAME(uscript_breaksBetweenLetters)
#define uscript_closeRun U_ICU_ENTRY_POINT_RENAME(uscript_closeRun)
#define uscript_getCode U_ICU_ENTRY_POINT_RENAME(uscript_getCode)
#define uscript_getName U_ICU_ENTRY_POINT_RENAME(uscript_getName)
#define uscript_getSampleString U_ICU_ENTRY_POINT_RENAME(uscript_getSampleString)
#define uscript_getSampleUnicodeString U_ICU_ENTRY_POINT_RENAME(uscript_getSampleUnicodeString)
#define uscript_getScript U_ICU_ENTRY_POINT_RENAME(uscript_getScript)
#define uscript_getScriptExtensions U_ICU_ENTRY_POINT_RENAME(uscript_getScriptExtensions)
#define uscript_getShortName U_ICU_ENTRY_POINT_RENAME(uscript_getShortName)
#define uscript_getUsage U_ICU_ENTRY_POINT_RENAME(uscript_getUsage)
#define uscript_hasScript U_ICU_ENTRY_POINT_RENAME(uscript_hasScript)
#define uscript_isCased U_ICU_ENTRY_POINT_RENAME(uscript_isCased)
#define uscript_isRightToLeft U_ICU_ENTRY_POINT_RENAME(uscript_isRightToLeft)
#define uscript_nextRun U_ICU_ENTRY_POINT_RENAME(uscript_nextRun)
#define uscript_openRun U_ICU_ENTRY_POINT_RENAME(uscript_openRun)
#define uscript_resetRun U_ICU_ENTRY_POINT_RENAME(uscript_resetRun)
#define uscript_setRunText U_ICU_ENTRY_POINT_RENAME(uscript_setRunText)
#define usearch_close U_ICU_ENTRY_POINT_RENAME(usearch_close)
#define usearch_first U_ICU_ENTRY_POINT_RENAME(usearch_first)
#define usearch_following U_ICU_ENTRY_POINT_RENAME(usearch_following)
#define usearch_getAttribute U_ICU_ENTRY_POINT_RENAME(usearch_getAttribute)
#define usearch_getBreakIterator U_ICU_ENTRY_POINT_RENAME(usearch_getBreakIterator)
#define usearch_getCollator U_ICU_ENTRY_POINT_RENAME(usearch_getCollator)
#define usearch_getMatchedLength U_ICU_ENTRY_POINT_RENAME(usearch_getMatchedLength)
#define usearch_getMatchedStart U_ICU_ENTRY_POINT_RENAME(usearch_getMatchedStart)
#define usearch_getMatchedText U_ICU_ENTRY_POINT_RENAME(usearch_getMatchedText)
#define usearch_getOffset U_ICU_ENTRY_POINT_RENAME(usearch_getOffset)
#define usearch_getPattern U_ICU_ENTRY_POINT_RENAME(usearch_getPattern)
#define usearch_getText U_ICU_ENTRY_POINT_RENAME(usearch_getText)
#define usearch_handleNextCanonical U_ICU_ENTRY_POINT_RENAME(usearch_handleNextCanonical)
#define usearch_handleNextExact U_ICU_ENTRY_POINT_RENAME(usearch_handleNextExact)
#define usearch_handlePreviousCanonical U_ICU_ENTRY_POINT_RENAME(usearch_handlePreviousCanonical)
#define usearch_handlePreviousExact U_ICU_ENTRY_POINT_RENAME(usearch_handlePreviousExact)
#define usearch_last U_ICU_ENTRY_POINT_RENAME(usearch_last)
#define usearch_next U_ICU_ENTRY_POINT_RENAME(usearch_next)
#define usearch_open U_ICU_ENTRY_POINT_RENAME(usearch_open)
#define usearch_openFromCollator U_ICU_ENTRY_POINT_RENAME(usearch_openFromCollator)
#define usearch_preceding U_ICU_ENTRY_POINT_RENAME(usearch_preceding)
#define usearch_previous U_ICU_ENTRY_POINT_RENAME(usearch_previous)
#define usearch_reset U_ICU_ENTRY_POINT_RENAME(usearch_reset)
#define usearch_search U_ICU_ENTRY_POINT_RENAME(usearch_search)
#define usearch_searchBackwards U_ICU_ENTRY_POINT_RENAME(usearch_searchBackwards)
#define usearch_setAttribute U_ICU_ENTRY_POINT_RENAME(usearch_setAttribute)
#define usearch_setBreakIterator U_ICU_ENTRY_POINT_RENAME(usearch_setBreakIterator)
#define usearch_setCollator U_ICU_ENTRY_POINT_RENAME(usearch_setCollator)
#define usearch_setOffset U_ICU_ENTRY_POINT_RENAME(usearch_setOffset)
#define usearch_setPattern U_ICU_ENTRY_POINT_RENAME(usearch_setPattern)
#define usearch_setText U_ICU_ENTRY_POINT_RENAME(usearch_setText)
#define uset_add U_ICU_ENTRY_POINT_RENAME(uset_add)
#define uset_addAll U_ICU_ENTRY_POINT_RENAME(uset_addAll)
#define uset_addAllCodePoints U_ICU_ENTRY_POINT_RENAME(uset_addAllCodePoints)
#define uset_addRange U_ICU_ENTRY_POINT_RENAME(uset_addRange)
#define uset_addString U_ICU_ENTRY_POINT_RENAME(uset_addString)
#define uset_applyIntPropertyValue U_ICU_ENTRY_POINT_RENAME(uset_applyIntPropertyValue)
#define uset_applyPattern U_ICU_ENTRY_POINT_RENAME(uset_applyPattern)
#define uset_applyPropertyAlias U_ICU_ENTRY_POINT_RENAME(uset_applyPropertyAlias)
#define uset_charAt U_ICU_ENTRY_POINT_RENAME(uset_charAt)
#define uset_clear U_ICU_ENTRY_POINT_RENAME(uset_clear)
#define uset_clone U_ICU_ENTRY_POINT_RENAME(uset_clone)
#define uset_cloneAsThawed U_ICU_ENTRY_POINT_RENAME(uset_cloneAsThawed)
#define uset_close U_ICU_ENTRY_POINT_RENAME(uset_close)
#define uset_closeOver U_ICU_ENTRY_POINT_RENAME(uset_closeOver)
#define uset_compact U_ICU_ENTRY_POINT_RENAME(uset_compact)
#define uset_complement U_ICU_ENTRY_POINT_RENAME(uset_complement)
#define uset_complementAll U_ICU_ENTRY_POINT_RENAME(uset_complementAll)
#define uset_complementAllCodePoints U_ICU_ENTRY_POINT_RENAME(uset_complementAllCodePoints)
#define uset_complementRange U_ICU_ENTRY_POINT_RENAME(uset_complementRange)
#define uset_complementString U_ICU_ENTRY_POINT_RENAME(uset_complementString)
#define uset_contains U_ICU_ENTRY_POINT_RENAME(uset_contains)
#define uset_containsAll U_ICU_ENTRY_POINT_RENAME(uset_containsAll)
#define uset_containsAllCodePoints U_ICU_ENTRY_POINT_RENAME(uset_containsAllCodePoints)
#define uset_containsNone U_ICU_ENTRY_POINT_RENAME(uset_containsNone)
#define uset_containsRange U_ICU_ENTRY_POINT_RENAME(uset_containsRange)
#define uset_containsSome U_ICU_ENTRY_POINT_RENAME(uset_containsSome)
#define uset_containsString U_ICU_ENTRY_POINT_RENAME(uset_containsString)
#define uset_equals U_ICU_ENTRY_POINT_RENAME(uset_equals)
#define uset_freeze U_ICU_ENTRY_POINT_RENAME(uset_freeze)
#define uset_getItem U_ICU_ENTRY_POINT_RENAME(uset_getItem)
#define uset_getItemCount U_ICU_ENTRY_POINT_RENAME(uset_getItemCount)
#define uset_getSerializedRange U_ICU_ENTRY_POINT_RENAME(uset_getSerializedRange)
#define uset_getSerializedRangeCount U_ICU_ENTRY_POINT_RENAME(uset_getSerializedRangeCount)
#define uset_getSerializedSet U_ICU_ENTRY_POINT_RENAME(uset_getSerializedSet)
#define uset_indexOf U_ICU_ENTRY_POINT_RENAME(uset_indexOf)
#define uset_isEmpty U_ICU_ENTRY_POINT_RENAME(uset_isEmpty)
#define uset_isFrozen U_ICU_ENTRY_POINT_RENAME(uset_isFrozen)
#define uset_open U_ICU_ENTRY_POINT_RENAME(uset_open)
#define uset_openEmpty U_ICU_ENTRY_POINT_RENAME(uset_openEmpty)
#define uset_openPattern U_ICU_ENTRY_POINT_RENAME(uset_openPattern)
#define uset_openPatternOptions U_ICU_ENTRY_POINT_RENAME(uset_openPatternOptions)
#define uset_remove U_ICU_ENTRY_POINT_RENAME(uset_remove)
#define uset_removeAll U_ICU_ENTRY_POINT_RENAME(uset_removeAll)
#define uset_removeAllCodePoints U_ICU_ENTRY_POINT_RENAME(uset_removeAllCodePoints)
#define uset_removeAllStrings U_ICU_ENTRY_POINT_RENAME(uset_removeAllStrings)
#define uset_removeRange U_ICU_ENTRY_POINT_RENAME(uset_removeRange)
#define uset_removeString U_ICU_ENTRY_POINT_RENAME(uset_removeString)
#define uset_resemblesPattern U_ICU_ENTRY_POINT_RENAME(uset_resemblesPattern)
#define uset_retain U_ICU_ENTRY_POINT_RENAME(uset_retain)
#define uset_retainAll U_ICU_ENTRY_POINT_RENAME(uset_retainAll)
#define uset_retainAllCodePoints U_ICU_ENTRY_POINT_RENAME(uset_retainAllCodePoints)
#define uset_retainString U_ICU_ENTRY_POINT_RENAME(uset_retainString)
#define uset_serialize U_ICU_ENTRY_POINT_RENAME(uset_serialize)
#define uset_serializedContains U_ICU_ENTRY_POINT_RENAME(uset_serializedContains)
#define uset_set U_ICU_ENTRY_POINT_RENAME(uset_set)
#define uset_setSerializedToOne U_ICU_ENTRY_POINT_RENAME(uset_setSerializedToOne)
#define uset_size U_ICU_ENTRY_POINT_RENAME(uset_size)
#define uset_span U_ICU_ENTRY_POINT_RENAME(uset_span)
#define uset_spanBack U_ICU_ENTRY_POINT_RENAME(uset_spanBack)
#define uset_spanBackUTF8 U_ICU_ENTRY_POINT_RENAME(uset_spanBackUTF8)
#define uset_spanUTF8 U_ICU_ENTRY_POINT_RENAME(uset_spanUTF8)
#define uset_toPattern U_ICU_ENTRY_POINT_RENAME(uset_toPattern)
#define uspoof_areConfusable U_ICU_ENTRY_POINT_RENAME(uspoof_areConfusable)
#define uspoof_areConfusableUTF8 U_ICU_ENTRY_POINT_RENAME(uspoof_areConfusableUTF8)
#define uspoof_areConfusableUnicodeString U_ICU_ENTRY_POINT_RENAME(uspoof_areConfusableUnicodeString)
#define uspoof_check U_ICU_ENTRY_POINT_RENAME(uspoof_check)
#define uspoof_check2 U_ICU_ENTRY_POINT_RENAME(uspoof_check2)
#define uspoof_check2UTF8 U_ICU_ENTRY_POINT_RENAME(uspoof_check2UTF8)
#define uspoof_check2UnicodeString U_ICU_ENTRY_POINT_RENAME(uspoof_check2UnicodeString)
#define uspoof_checkUTF8 U_ICU_ENTRY_POINT_RENAME(uspoof_checkUTF8)
#define uspoof_checkUnicodeString U_ICU_ENTRY_POINT_RENAME(uspoof_checkUnicodeString)
#define uspoof_clone U_ICU_ENTRY_POINT_RENAME(uspoof_clone)
#define uspoof_close U_ICU_ENTRY_POINT_RENAME(uspoof_close)
#define uspoof_closeCheckResult U_ICU_ENTRY_POINT_RENAME(uspoof_closeCheckResult)
#define uspoof_getAllowedChars U_ICU_ENTRY_POINT_RENAME(uspoof_getAllowedChars)
#define uspoof_getAllowedLocales U_ICU_ENTRY_POINT_RENAME(uspoof_getAllowedLocales)
#define uspoof_getAllowedUnicodeSet U_ICU_ENTRY_POINT_RENAME(uspoof_getAllowedUnicodeSet)
#define uspoof_getCheckResultChecks U_ICU_ENTRY_POINT_RENAME(uspoof_getCheckResultChecks)
#define uspoof_getCheckResultNumerics U_ICU_ENTRY_POINT_RENAME(uspoof_getCheckResultNumerics)
#define uspoof_getCheckResultRestrictionLevel U_ICU_ENTRY_POINT_RENAME(uspoof_getCheckResultRestrictionLevel)
#define uspoof_getChecks U_ICU_ENTRY_POINT_RENAME(uspoof_getChecks)
#define uspoof_getInclusionSet U_ICU_ENTRY_POINT_RENAME(uspoof_getInclusionSet)
#define uspoof_getInclusionUnicodeSet U_ICU_ENTRY_POINT_RENAME(uspoof_getInclusionUnicodeSet)
#define uspoof_getRecommendedSet U_ICU_ENTRY_POINT_RENAME(uspoof_getRecommendedSet)
#define uspoof_getRecommendedUnicodeSet U_ICU_ENTRY_POINT_RENAME(uspoof_getRecommendedUnicodeSet)
#define uspoof_getRestrictionLevel U_ICU_ENTRY_POINT_RENAME(uspoof_getRestrictionLevel)
#define uspoof_getSkeleton U_ICU_ENTRY_POINT_RENAME(uspoof_getSkeleton)
#define uspoof_getSkeletonUTF8 U_ICU_ENTRY_POINT_RENAME(uspoof_getSkeletonUTF8)
#define uspoof_getSkeletonUnicodeString U_ICU_ENTRY_POINT_RENAME(uspoof_getSkeletonUnicodeString)
#define uspoof_internalInitStatics U_ICU_ENTRY_POINT_RENAME(uspoof_internalInitStatics)
#define uspoof_open U_ICU_ENTRY_POINT_RENAME(uspoof_open)
#define uspoof_openCheckResult U_ICU_ENTRY_POINT_RENAME(uspoof_openCheckResult)
#define uspoof_openFromSerialized U_ICU_ENTRY_POINT_RENAME(uspoof_openFromSerialized)
#define uspoof_openFromSource U_ICU_ENTRY_POINT_RENAME(uspoof_openFromSource)
#define uspoof_serialize U_ICU_ENTRY_POINT_RENAME(uspoof_serialize)
#define uspoof_setAllowedChars U_ICU_ENTRY_POINT_RENAME(uspoof_setAllowedChars)
#define uspoof_setAllowedLocales U_ICU_ENTRY_POINT_RENAME(uspoof_setAllowedLocales)
#define uspoof_setAllowedUnicodeSet U_ICU_ENTRY_POINT_RENAME(uspoof_setAllowedUnicodeSet)
#define uspoof_setChecks U_ICU_ENTRY_POINT_RENAME(uspoof_setChecks)
#define uspoof_setRestrictionLevel U_ICU_ENTRY_POINT_RENAME(uspoof_setRestrictionLevel)
#define uspoof_swap U_ICU_ENTRY_POINT_RENAME(uspoof_swap)
#define usprep_close U_ICU_ENTRY_POINT_RENAME(usprep_close)
#define usprep_open U_ICU_ENTRY_POINT_RENAME(usprep_open)
#define usprep_openByType U_ICU_ENTRY_POINT_RENAME(usprep_openByType)
#define usprep_prepare U_ICU_ENTRY_POINT_RENAME(usprep_prepare)
#define usprep_swap U_ICU_ENTRY_POINT_RENAME(usprep_swap)
#define ustr_hashCharsN U_ICU_ENTRY_POINT_RENAME(ustr_hashCharsN)
#define ustr_hashICharsN U_ICU_ENTRY_POINT_RENAME(ustr_hashICharsN)
#define ustr_hashUCharsN U_ICU_ENTRY_POINT_RENAME(ustr_hashUCharsN)
#define ustrcase_getCaseLocale U_ICU_ENTRY_POINT_RENAME(ustrcase_getCaseLocale)
#define ustrcase_getTitleBreakIterator U_ICU_ENTRY_POINT_RENAME(ustrcase_getTitleBreakIterator)
#define ustrcase_internalFold U_ICU_ENTRY_POINT_RENAME(ustrcase_internalFold)
#define ustrcase_internalToLower U_ICU_ENTRY_POINT_RENAME(ustrcase_internalToLower)
#define ustrcase_internalToTitle U_ICU_ENTRY_POINT_RENAME(ustrcase_internalToTitle)
#define ustrcase_internalToUpper U_ICU_ENTRY_POINT_RENAME(ustrcase_internalToUpper)
#define ustrcase_map U_ICU_ENTRY_POINT_RENAME(ustrcase_map)
#define ustrcase_mapWithOverlap U_ICU_ENTRY_POINT_RENAME(ustrcase_mapWithOverlap)
#define utext_char32At U_ICU_ENTRY_POINT_RENAME(utext_char32At)
#define utext_clone U_ICU_ENTRY_POINT_RENAME(utext_clone)
#define utext_close U_ICU_ENTRY_POINT_RENAME(utext_close)
#define utext_copy U_ICU_ENTRY_POINT_RENAME(utext_copy)
#define utext_current32 U_ICU_ENTRY_POINT_RENAME(utext_current32)
#define utext_equals U_ICU_ENTRY_POINT_RENAME(utext_equals)
#define utext_extract U_ICU_ENTRY_POINT_RENAME(utext_extract)
#define utext_freeze U_ICU_ENTRY_POINT_RENAME(utext_freeze)
#define utext_getNativeIndex U_ICU_ENTRY_POINT_RENAME(utext_getNativeIndex)
#define utext_getPreviousNativeIndex U_ICU_ENTRY_POINT_RENAME(utext_getPreviousNativeIndex)
#define utext_hasMetaData U_ICU_ENTRY_POINT_RENAME(utext_hasMetaData)
#define utext_isLengthExpensive U_ICU_ENTRY_POINT_RENAME(utext_isLengthExpensive)
#define utext_isWritable U_ICU_ENTRY_POINT_RENAME(utext_isWritable)
#define utext_moveIndex32 U_ICU_ENTRY_POINT_RENAME(utext_moveIndex32)
#define utext_nativeLength U_ICU_ENTRY_POINT_RENAME(utext_nativeLength)
#define utext_next32 U_ICU_ENTRY_POINT_RENAME(utext_next32)
#define utext_next32From U_ICU_ENTRY_POINT_RENAME(utext_next32From)
#define utext_openCharacterIterator U_ICU_ENTRY_POINT_RENAME(utext_openCharacterIterator)
#define utext_openConstUnicodeString U_ICU_ENTRY_POINT_RENAME(utext_openConstUnicodeString)
#define utext_openReplaceable U_ICU_ENTRY_POINT_RENAME(utext_openReplaceable)
#define utext_openUChars U_ICU_ENTRY_POINT_RENAME(utext_openUChars)
#define utext_openUTF8 U_ICU_ENTRY_POINT_RENAME(utext_openUTF8)
#define utext_openUnicodeString U_ICU_ENTRY_POINT_RENAME(utext_openUnicodeString)
#define utext_previous32 U_ICU_ENTRY_POINT_RENAME(utext_previous32)
#define utext_previous32From U_ICU_ENTRY_POINT_RENAME(utext_previous32From)
#define utext_replace U_ICU_ENTRY_POINT_RENAME(utext_replace)
#define utext_setNativeIndex U_ICU_ENTRY_POINT_RENAME(utext_setNativeIndex)
#define utext_setup U_ICU_ENTRY_POINT_RENAME(utext_setup)
#define utf8_appendCharSafeBody U_ICU_ENTRY_POINT_RENAME(utf8_appendCharSafeBody)
#define utf8_back1SafeBody U_ICU_ENTRY_POINT_RENAME(utf8_back1SafeBody)
#define utf8_countTrailBytes U_ICU_ENTRY_POINT_RENAME(utf8_countTrailBytes)
#define utf8_nextCharSafeBody U_ICU_ENTRY_POINT_RENAME(utf8_nextCharSafeBody)
#define utf8_prevCharSafeBody U_ICU_ENTRY_POINT_RENAME(utf8_prevCharSafeBody)
#define utmscale_fromInt64 U_ICU_ENTRY_POINT_RENAME(utmscale_fromInt64)
#define utmscale_getTimeScaleValue U_ICU_ENTRY_POINT_RENAME(utmscale_getTimeScaleValue)
#define utmscale_toInt64 U_ICU_ENTRY_POINT_RENAME(utmscale_toInt64)
#define utrace_cleanup U_ICU_ENTRY_POINT_RENAME(utrace_cleanup)
#define utrace_data U_ICU_ENTRY_POINT_RENAME(utrace_data)
#define utrace_entry U_ICU_ENTRY_POINT_RENAME(utrace_entry)
#define utrace_exit U_ICU_ENTRY_POINT_RENAME(utrace_exit)
#define utrace_format U_ICU_ENTRY_POINT_RENAME(utrace_format)
#define utrace_functionName U_ICU_ENTRY_POINT_RENAME(utrace_functionName)
#define utrace_getFunctions U_ICU_ENTRY_POINT_RENAME(utrace_getFunctions)
#define utrace_getLevel U_ICU_ENTRY_POINT_RENAME(utrace_getLevel)
#define utrace_setFunctions U_ICU_ENTRY_POINT_RENAME(utrace_setFunctions)
#define utrace_setLevel U_ICU_ENTRY_POINT_RENAME(utrace_setLevel)
#define utrace_vformat U_ICU_ENTRY_POINT_RENAME(utrace_vformat)
#define utrans_clone U_ICU_ENTRY_POINT_RENAME(utrans_clone)
#define utrans_close U_ICU_ENTRY_POINT_RENAME(utrans_close)
#define utrans_countAvailableIDs U_ICU_ENTRY_POINT_RENAME(utrans_countAvailableIDs)
#define utrans_getAvailableID U_ICU_ENTRY_POINT_RENAME(utrans_getAvailableID)
#define utrans_getID U_ICU_ENTRY_POINT_RENAME(utrans_getID)
#define utrans_getSourceSet U_ICU_ENTRY_POINT_RENAME(utrans_getSourceSet)
#define utrans_getUnicodeID U_ICU_ENTRY_POINT_RENAME(utrans_getUnicodeID)
#define utrans_open U_ICU_ENTRY_POINT_RENAME(utrans_open)
#define utrans_openIDs U_ICU_ENTRY_POINT_RENAME(utrans_openIDs)
#define utrans_openInverse U_ICU_ENTRY_POINT_RENAME(utrans_openInverse)
#define utrans_openU U_ICU_ENTRY_POINT_RENAME(utrans_openU)
#define utrans_register U_ICU_ENTRY_POINT_RENAME(utrans_register)
#define utrans_rep_caseContextIterator U_ICU_ENTRY_POINT_RENAME(utrans_rep_caseContextIterator)
#define utrans_setFilter U_ICU_ENTRY_POINT_RENAME(utrans_setFilter)
#define utrans_stripRules U_ICU_ENTRY_POINT_RENAME(utrans_stripRules)
#define utrans_toRules U_ICU_ENTRY_POINT_RENAME(utrans_toRules)
#define utrans_trans U_ICU_ENTRY_POINT_RENAME(utrans_trans)
#define utrans_transIncremental U_ICU_ENTRY_POINT_RENAME(utrans_transIncremental)
#define utrans_transIncrementalUChars U_ICU_ENTRY_POINT_RENAME(utrans_transIncrementalUChars)
#define utrans_transUChars U_ICU_ENTRY_POINT_RENAME(utrans_transUChars)
#define utrans_transliterator_cleanup U_ICU_ENTRY_POINT_RENAME(utrans_transliterator_cleanup)
#define utrans_unregister U_ICU_ENTRY_POINT_RENAME(utrans_unregister)
#define utrans_unregisterID U_ICU_ENTRY_POINT_RENAME(utrans_unregisterID)
#define utrie2_clone U_ICU_ENTRY_POINT_RENAME(utrie2_clone)
#define utrie2_cloneAsThawed U_ICU_ENTRY_POINT_RENAME(utrie2_cloneAsThawed)
#define utrie2_close U_ICU_ENTRY_POINT_RENAME(utrie2_close)
#define utrie2_enum U_ICU_ENTRY_POINT_RENAME(utrie2_enum)
#define utrie2_enumForLeadSurrogate U_ICU_ENTRY_POINT_RENAME(utrie2_enumForLeadSurrogate)
#define utrie2_freeze U_ICU_ENTRY_POINT_RENAME(utrie2_freeze)
#define utrie2_fromUTrie U_ICU_ENTRY_POINT_RENAME(utrie2_fromUTrie)
#define utrie2_get32 U_ICU_ENTRY_POINT_RENAME(utrie2_get32)
#define utrie2_get32FromLeadSurrogateCodeUnit U_ICU_ENTRY_POINT_RENAME(utrie2_get32FromLeadSurrogateCodeUnit)
#define utrie2_internalU8NextIndex U_ICU_ENTRY_POINT_RENAME(utrie2_internalU8NextIndex)
#define utrie2_internalU8PrevIndex U_ICU_ENTRY_POINT_RENAME(utrie2_internalU8PrevIndex)
#define utrie2_isFrozen U_ICU_ENTRY_POINT_RENAME(utrie2_isFrozen)
#define utrie2_open U_ICU_ENTRY_POINT_RENAME(utrie2_open)
#define utrie2_openDummy U_ICU_ENTRY_POINT_RENAME(utrie2_openDummy)
#define utrie2_openFromSerialized U_ICU_ENTRY_POINT_RENAME(utrie2_openFromSerialized)
#define utrie2_serialize U_ICU_ENTRY_POINT_RENAME(utrie2_serialize)
#define utrie2_set32 U_ICU_ENTRY_POINT_RENAME(utrie2_set32)
#define utrie2_set32ForLeadSurrogateCodeUnit U_ICU_ENTRY_POINT_RENAME(utrie2_set32ForLeadSurrogateCodeUnit)
#define utrie2_setRange32 U_ICU_ENTRY_POINT_RENAME(utrie2_setRange32)
#define utrie2_swap U_ICU_ENTRY_POINT_RENAME(utrie2_swap)
#define utrie_clone U_ICU_ENTRY_POINT_RENAME(utrie_clone)
#define utrie_close U_ICU_ENTRY_POINT_RENAME(utrie_close)
#define utrie_defaultGetFoldingOffset U_ICU_ENTRY_POINT_RENAME(utrie_defaultGetFoldingOffset)
#define utrie_enum U_ICU_ENTRY_POINT_RENAME(utrie_enum)
#define utrie_get32 U_ICU_ENTRY_POINT_RENAME(utrie_get32)
#define utrie_getData U_ICU_ENTRY_POINT_RENAME(utrie_getData)
#define utrie_open U_ICU_ENTRY_POINT_RENAME(utrie_open)
#define utrie_serialize U_ICU_ENTRY_POINT_RENAME(utrie_serialize)
#define utrie_set32 U_ICU_ENTRY_POINT_RENAME(utrie_set32)
#define utrie_setRange32 U_ICU_ENTRY_POINT_RENAME(utrie_setRange32)
#define utrie_swap U_ICU_ENTRY_POINT_RENAME(utrie_swap)
#define utrie_swapAnyVersion U_ICU_ENTRY_POINT_RENAME(utrie_swapAnyVersion)
#define utrie_unserialize U_ICU_ENTRY_POINT_RENAME(utrie_unserialize)
#define utrie_unserializeDummy U_ICU_ENTRY_POINT_RENAME(utrie_unserializeDummy)
#define vzone_clone U_ICU_ENTRY_POINT_RENAME(vzone_clone)
#define vzone_close U_ICU_ENTRY_POINT_RENAME(vzone_close)
#define vzone_countTransitionRules U_ICU_ENTRY_POINT_RENAME(vzone_countTransitionRules)
#define vzone_equals U_ICU_ENTRY_POINT_RENAME(vzone_equals)
#define vzone_getDynamicClassID U_ICU_ENTRY_POINT_RENAME(vzone_getDynamicClassID)
#define vzone_getLastModified U_ICU_ENTRY_POINT_RENAME(vzone_getLastModified)
#define vzone_getNextTransition U_ICU_ENTRY_POINT_RENAME(vzone_getNextTransition)
#define vzone_getOffset U_ICU_ENTRY_POINT_RENAME(vzone_getOffset)
#define vzone_getOffset2 U_ICU_ENTRY_POINT_RENAME(vzone_getOffset2)
#define vzone_getOffset3 U_ICU_ENTRY_POINT_RENAME(vzone_getOffset3)
#define vzone_getPreviousTransition U_ICU_ENTRY_POINT_RENAME(vzone_getPreviousTransition)
#define vzone_getRawOffset U_ICU_ENTRY_POINT_RENAME(vzone_getRawOffset)
#define vzone_getStaticClassID U_ICU_ENTRY_POINT_RENAME(vzone_getStaticClassID)
#define vzone_getTZURL U_ICU_ENTRY_POINT_RENAME(vzone_getTZURL)
#define vzone_hasSameRules U_ICU_ENTRY_POINT_RENAME(vzone_hasSameRules)
#define vzone_inDaylightTime U_ICU_ENTRY_POINT_RENAME(vzone_inDaylightTime)
#define vzone_openData U_ICU_ENTRY_POINT_RENAME(vzone_openData)
#define vzone_openID U_ICU_ENTRY_POINT_RENAME(vzone_openID)
#define vzone_setLastModified U_ICU_ENTRY_POINT_RENAME(vzone_setLastModified)
#define vzone_setRawOffset U_ICU_ENTRY_POINT_RENAME(vzone_setRawOffset)
#define vzone_setTZURL U_ICU_ENTRY_POINT_RENAME(vzone_setTZURL)
#define vzone_useDaylightTime U_ICU_ENTRY_POINT_RENAME(vzone_useDaylightTime)
#define vzone_write U_ICU_ENTRY_POINT_RENAME(vzone_write)
#define vzone_writeFromStart U_ICU_ENTRY_POINT_RENAME(vzone_writeFromStart)
#define vzone_writeSimple U_ICU_ENTRY_POINT_RENAME(vzone_writeSimple)
#define zrule_close U_ICU_ENTRY_POINT_RENAME(zrule_close)
#define zrule_equals U_ICU_ENTRY_POINT_RENAME(zrule_equals)
#define zrule_getDSTSavings U_ICU_ENTRY_POINT_RENAME(zrule_getDSTSavings)
#define zrule_getName U_ICU_ENTRY_POINT_RENAME(zrule_getName)
#define zrule_getRawOffset U_ICU_ENTRY_POINT_RENAME(zrule_getRawOffset)
#define zrule_isEquivalentTo U_ICU_ENTRY_POINT_RENAME(zrule_isEquivalentTo)
#define ztrans_adoptFrom U_ICU_ENTRY_POINT_RENAME(ztrans_adoptFrom)
#define ztrans_adoptTo U_ICU_ENTRY_POINT_RENAME(ztrans_adoptTo)
#define ztrans_clone U_ICU_ENTRY_POINT_RENAME(ztrans_clone)
#define ztrans_close U_ICU_ENTRY_POINT_RENAME(ztrans_close)
#define ztrans_equals U_ICU_ENTRY_POINT_RENAME(ztrans_equals)
#define ztrans_getDynamicClassID U_ICU_ENTRY_POINT_RENAME(ztrans_getDynamicClassID)
#define ztrans_getFrom U_ICU_ENTRY_POINT_RENAME(ztrans_getFrom)
#define ztrans_getStaticClassID U_ICU_ENTRY_POINT_RENAME(ztrans_getStaticClassID)
#define ztrans_getTime U_ICU_ENTRY_POINT_RENAME(ztrans_getTime)
#define ztrans_getTo U_ICU_ENTRY_POINT_RENAME(ztrans_getTo)
#define ztrans_open U_ICU_ENTRY_POINT_RENAME(ztrans_open)
#define ztrans_openEmpty U_ICU_ENTRY_POINT_RENAME(ztrans_openEmpty)
#define ztrans_setFrom U_ICU_ENTRY_POINT_RENAME(ztrans_setFrom)
#define ztrans_setTime U_ICU_ENTRY_POINT_RENAME(ztrans_setTime)
#define ztrans_setTo U_ICU_ENTRY_POINT_RENAME(ztrans_setTo)

#endif /* !(defined(_MSC_VER) && defined(__INTELLISENSE__)) */
#endif /* U_DISABLE_RENAMING */
#endif /* URENAME_H */

