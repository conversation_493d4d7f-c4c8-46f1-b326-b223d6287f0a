/*
 * DO NOT EDIT.  THIS FILE IS GENERATED FROM ../../client/citicore/fxcore.idl
 */

#ifndef __gen_fxcore_h__
#define __gen_fxcore_h__

/* For IDL files that don't want to include root IDL files. */
#ifndef NS_NO_VTABLE
#define NS_NO_VTABLE
#endif
#if 0
typedef bool  bool;

typedef uint8_t  uint8_t;

typedef uint16_t  uint16_t;

typedef uint16_t  char16_t;

typedef uint32_t  uint32_t;

typedef uint64_t  uint64_t;

typedef int16_t  int16_t;

typedef int32_t  int32_t;

typedef int64_t  int64_t;

typedef uint32_t  fxrefcnt;

typedef uint32_t  fxresult;

typedef uint32_t  size_t;

#endif
#include <om/core.h>

#endif /* __gen_fxcore_h__ */
