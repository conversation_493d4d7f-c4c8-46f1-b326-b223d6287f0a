# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves basic function.arguments functionality when said functionality is used from inside and outside getters and from inlined code, all at once.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b0, c0], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: ], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: [object Object], b0, c0], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b1, c1], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: ], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: [object Object], b1, c1], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b2, c2], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: ], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: [object Object], b2, c2], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b3, c3], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: ], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: [object Object], b3, c3], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b4, c4], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: ], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: [object Object], b4, c4], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b5, c5], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: ], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: [object Object], b5, c5], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b6, c6], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: ], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: [object Object], b6, c6], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b7, c7], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: ], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: [object Object], b7, c7], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b8, c8], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: ], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: [object Object], b8, c8], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b9, c9], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: ], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: [object Object], b9, c9], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b10, c10], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: ], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: [object Object], b10, c10], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b11, c11], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: ], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: [object Object], b11, c11], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b12, c12], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: ], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: [object Object], b12, c12], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b13, c13], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: ], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: [object Object], b13, c13], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b14, c14], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: ], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: [object Object], b14, c14], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b15, c15], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: ], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: [object Object], b15, c15], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b16, c16], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: ], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: [object Object], b16, c16], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b17, c17], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: ], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: [object Object], b17, c17], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b18, c18], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: ], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: [object Object], b18, c18], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b19, c19], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: ], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: [object Object], b19, c19], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b20, c20], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: ], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: [object Object], b20, c20], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b21, c21], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: ], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: [object Object], b21, c21], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b22, c22], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: ], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: [object Object], b22, c22], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b23, c23], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: ], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: [object Object], b23, c23], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b24, c24], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: ], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: [object Object], b24, c24], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b25, c25], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: ], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: [object Object], b25, c25], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b26, c26], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: ], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: [object Object], b26, c26], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b27, c27], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: ], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: [object Object], b27, c27], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b28, c28], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: ], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: [object Object], b28, c28], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b29, c29], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: ], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: [object Object], b29, c29], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b30, c30], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: ], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: [object Object], b30, c30], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b31, c31], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: ], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: [object Object], b31, c31], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b32, c32], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: ], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: [object Object], b32, c32], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b33, c33], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: ], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: [object Object], b33, c33], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b34, c34], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: ], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: [object Object], b34, c34], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b35, c35], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: ], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: [object Object], b35, c35], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b36, c36], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: ], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: [object Object], b36, c36], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b37, c37], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: ], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: [object Object], b37, c37], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b38, c38], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: ], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: [object Object], b38, c38], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b39, c39], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: ], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: [object Object], b39, c39], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b40, c40], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: ], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: [object Object], b40, c40], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b41, c41], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: ], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: [object Object], b41, c41], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b42, c42], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: ], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: [object Object], b42, c42], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b43, c43], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: ], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: [object Object], b43, c43], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b44, c44], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: ], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: [object Object], b44, c44], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b45, c45], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: ], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: [object Object], b45, c45], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b46, c46], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: ], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: [object Object], b46, c46], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b47, c47], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: ], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: [object Object], b47, c47], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b48, c48], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: ], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: [object Object], b48, c48], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b49, c49], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: ], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: [object Object], b49, c49], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b50, c50], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: ], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: [object Object], b50, c50], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b51, c51], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: ], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: [object Object], b51, c51], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b52, c52], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: ], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: [object Object], b52, c52], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b53, c53], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: ], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: [object Object], b53, c53], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b54, c54], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: ], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: [object Object], b54, c54], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b55, c55], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: ], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: [object Object], b55, c55], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b56, c56], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: ], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: [object Object], b56, c56], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b57, c57], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: ], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: [object Object], b57, c57], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b58, c58], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: ], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: [object Object], b58, c58], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b59, c59], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: ], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: [object Object], b59, c59], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b60, c60], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: ], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: [object Object], b60, c60], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b61, c61], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: ], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: [object Object], b61, c61], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b62, c62], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: ], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: [object Object], b62, c62], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b63, c63], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: ], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: [object Object], b63, c63], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b64, c64], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: ], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: [object Object], b64, c64], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b65, c65], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: ], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: [object Object], b65, c65], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b66, c66], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: ], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: [object Object], b66, c66], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b67, c67], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: ], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: [object Object], b67, c67], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b68, c68], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: ], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: [object Object], b68, c68], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b69, c69], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: ], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: [object Object], b69, c69], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b70, c70], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: ], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: [object Object], b70, c70], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b71, c71], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: ], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: [object Object], b71, c71], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b72, c72], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: ], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: [object Object], b72, c72], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b73, c73], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: ], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: [object Object], b73, c73], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b74, c74], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: ], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: [object Object], b74, c74], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b75, c75], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: ], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: [object Object], b75, c75], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b76, c76], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: ], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: [object Object], b76, c76], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b77, c77], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: ], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: [object Object], b77, c77], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b78, c78], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: ], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: [object Object], b78, c78], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b79, c79], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: ], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: [object Object], b79, c79], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b80, c80], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: ], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: [object Object], b80, c80], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b81, c81], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: ], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: [object Object], b81, c81], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b82, c82], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: ], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: [object Object], b82, c82], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b83, c83], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: ], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: [object Object], b83, c83], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b84, c84], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: ], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: [object Object], b84, c84], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b85, c85], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: ], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: [object Object], b85, c85], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b86, c86], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: ], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: [object Object], b86, c86], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b87, c87], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: ], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: [object Object], b87, c87], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b88, c88], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: ], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: [object Object], b88, c88], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b89, c89], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: ], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: [object Object], b89, c89], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b90, c90], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: ], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: [object Object], b90, c90], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b91, c91], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: ], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: [object Object], b91, c91], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b92, c92], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: ], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: [object Object], b92, c92], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b93, c93], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: ], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: [object Object], b93, c93], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b94, c94], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: ], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: [object Object], b94, c94], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b95, c95], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: ], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: [object Object], b95, c95], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b96, c96], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: ], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: [object Object], b96, c96], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b97, c97], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: ], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: [object Object], b97, c97], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b98, c98], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: ], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: [object Object], b98, c98], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b99, c99], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: ], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: [object Object], b99, c99], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b100, c100], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: ], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: [object Object], b100, c100], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b101, c101], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: ], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: [object Object], b101, c101], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b102, c102], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: ], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: [object Object], b102, c102], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b103, c103], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: ], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: [object Object], b103, c103], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b104, c104], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: ], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: [object Object], b104, c104], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b105, c105], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: ], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: [object Object], b105, c105], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b106, c106], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: ], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: [object Object], b106, c106], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b107, c107], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: ], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: [object Object], b107, c107], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b108, c108], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: ], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: [object Object], b108, c108], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b109, c109], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: ], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: [object Object], b109, c109], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b110, c110], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: ], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: [object Object], b110, c110], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b111, c111], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: ], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: [object Object], b111, c111], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b112, c112], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: ], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: [object Object], b112, c112], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b113, c113], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: ], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: [object Object], b113, c113], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b114, c114], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: ], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: [object Object], b114, c114], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b115, c115], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: ], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: [object Object], b115, c115], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b116, c116], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: ], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: [object Object], b116, c116], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b117, c117], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: ], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: [object Object], b117, c117], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b118, c118], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: ], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: [object Object], b118, c118], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b119, c119], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: ], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: [object Object], b119, c119], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b120, c120], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: ], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: [object Object], b120, c120], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b121, c121], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: ], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: [object Object], b121, c121], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b122, c122], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: ], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: [object Object], b122, c122], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b123, c123], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: ], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: [object Object], b123, c123], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b124, c124], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: ], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: [object Object], b124, c124], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b125, c125], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: ], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: [object Object], b125, c125], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b126, c126], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: ], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: [object Object], b126, c126], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b127, c127], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: ], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: [object Object], b127, c127], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b128, c128], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: ], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: [object Object], b128, c128], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b129, c129], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: ], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: [object Object], b129, c129], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b130, c130], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: ], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: [object Object], b130, c130], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b131, c131], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: ], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: [object Object], b131, c131], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b132, c132], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: ], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: [object Object], b132, c132], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b133, c133], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: ], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: [object Object], b133, c133], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b134, c134], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: ], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: [object Object], b134, c134], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b135, c135], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: ], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: [object Object], b135, c135], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b136, c136], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: ], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: [object Object], b136, c136], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b137, c137], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: ], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: [object Object], b137, c137], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b138, c138], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: ], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: [object Object], b138, c138], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b139, c139], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: ], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: [object Object], b139, c139], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b140, c140], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: ], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: [object Object], b140, c140], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b141, c141], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: ], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: [object Object], b141, c141], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b142, c142], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: ], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: [object Object], b142, c142], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b143, c143], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: ], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: [object Object], b143, c143], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b144, c144], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: ], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: [object Object], b144, c144], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b145, c145], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: ], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: [object Object], b145, c145], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b146, c146], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: ], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: [object Object], b146, c146], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b147, c147], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: ], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: [object Object], b147, c147], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b148, c148], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: ], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: [object Object], b148, c148], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b149, c149], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: ], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: [object Object], b149, c149], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b150, c150], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: ], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: [object Object], b150, c150], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b151, c151], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: ], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: [object Object], b151, c151], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b152, c152], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: ], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: [object Object], b152, c152], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b153, c153], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: ], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: [object Object], b153, c153], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b154, c154], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: ], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: [object Object], b154, c154], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b155, c155], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: ], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: [object Object], b155, c155], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b156, c156], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: ], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: [object Object], b156, c156], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b157, c157], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: ], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: [object Object], b157, c157], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b158, c158], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: ], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: [object Object], b158, c158], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b159, c159], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: ], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: [object Object], b159, c159], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b160, c160], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: ], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: [object Object], b160, c160], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b161, c161], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: ], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: [object Object], b161, c161], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b162, c162], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: ], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: [object Object], b162, c162], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b163, c163], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: ], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: [object Object], b163, c163], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b164, c164], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: ], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: [object Object], b164, c164], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b165, c165], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: ], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: [object Object], b165, c165], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b166, c166], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: ], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: [object Object], b166, c166], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b167, c167], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: ], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: [object Object], b167, c167], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b168, c168], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: ], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: [object Object], b168, c168], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b169, c169], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: ], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: [object Object], b169, c169], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b170, c170], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: ], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: [object Object], b170, c170], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b171, c171], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: ], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: [object Object], b171, c171], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b172, c172], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: ], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: [object Object], b172, c172], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b173, c173], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: ], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: [object Object], b173, c173], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b174, c174], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: ], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: [object Object], b174, c174], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b175, c175], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: ], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: [object Object], b175, c175], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b176, c176], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: ], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: [object Object], b176, c176], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b177, c177], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: ], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: [object Object], b177, c177], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b178, c178], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: ], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: [object Object], b178, c178], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b179, c179], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: ], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: [object Object], b179, c179], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b180, c180], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: ], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: [object Object], b180, c180], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b181, c181], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: ], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: [object Object], b181, c181], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b182, c182], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: ], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: [object Object], b182, c182], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b183, c183], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: ], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: [object Object], b183, c183], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b184, c184], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: ], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: [object Object], b184, c184], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b185, c185], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: ], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: [object Object], b185, c185], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b186, c186], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: ], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: [object Object], b186, c186], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b187, c187], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: ], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: [object Object], b187, c187], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b188, c188], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: ], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: [object Object], b188, c188], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b189, c189], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: ], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: [object Object], b189, c189], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b190, c190], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: ], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: [object Object], b190, c190], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b191, c191], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: ], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: [object Object], b191, c191], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b192, c192], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: ], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: [object Object], b192, c192], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b193, c193], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: ], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: [object Object], b193, c193], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b194, c194], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: ], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: [object Object], b194, c194], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b195, c195], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: ], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: [object Object], b195, c195], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b196, c196], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: ], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: [object Object], b196, c196], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b197, c197], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: ], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: [object Object], b197, c197], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b198, c198], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: ], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: [object Object], b198, c198], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments],[object Arguments]: [[object Arguments]: [object Object], b199, c199], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: ], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: [object Object], b199, c199], [[object Arguments]: ], [[object Arguments]: 42, 56]]"
PASS successfullyParsed is true

TEST COMPLETE

