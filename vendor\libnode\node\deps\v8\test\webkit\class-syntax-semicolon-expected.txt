Tests for ES6 class syntax containing semicolon in the class body

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS class A { foo() ; { } } threw exception SyntaxError: Unexpected token ';'.
PASS class A { get foo;() { } } threw exception SyntaxError: Unexpected token ';'.
PASS class A { get foo() ; { } } threw exception SyntaxError: Unexpected token ';'.
PASS class A { set foo;(x) { } } threw exception SyntaxError: Unexpected token ';'.
PASS class A { set foo(x) ; { } } threw exception SyntaxError: Unexpected token ';'.
PASS class A { ; } did not throw exception.
PASS class A { foo() { } ; } did not throw exception.
PASS class A { get foo() { } ; } did not throw exception.
PASS class A { set foo(x) { } ; } did not throw exception.
PASS class A { static foo() { } ; } did not throw exception.
PASS class A { static get foo() { } ; } did not throw exception.
PASS class A { static set foo(x) { } ; } did not throw exception.
PASS class A { ; foo() { } } did not throw exception.
PASS class A { ; get foo() { } } did not throw exception.
PASS class A { ; set foo(x) { } } did not throw exception.
PASS class A { ; static foo() { } } did not throw exception.
PASS class A { ; static get foo() { } } did not throw exception.
PASS class A { ; static set foo(x) { } } did not throw exception.
PASS class A { foo() { } ; foo() {} } did not throw exception.
PASS class A { foo() { } ; get foo() {} } did not throw exception.
PASS class A { foo() { } ; set foo(x) {} } did not throw exception.
PASS class A { foo() { } ; static foo() {} } did not throw exception.
PASS class A { foo() { } ; static get foo() {} } did not throw exception.
PASS class A { foo() { } ; static set foo(x) {} } did not throw exception.
PASS class A { get foo() { } ; foo() {} } did not throw exception.
PASS class A { get foo() { } ; get foo() {} } did not throw exception.
PASS class A { get foo() { } ; set foo(x) {} } did not throw exception.
PASS class A { get foo() { } ; static foo() {} } did not throw exception.
PASS class A { get foo() { } ; static get foo() {} } did not throw exception.
PASS class A { get foo() { } ; static set foo(x) {} } did not throw exception.
PASS class A { set foo(x) { } ; foo() {} } did not throw exception.
PASS class A { set foo(x) { } ; get foo() {} } did not throw exception.
PASS class A { set foo(x) { } ; set foo(x) {} } did not throw exception.
PASS class A { set foo(x) { } ; static foo() {} } did not throw exception.
PASS class A { set foo(x) { } ; static get foo() {} } did not throw exception.
PASS class A { set foo(x) { } ; static set foo(x) {} } did not throw exception.
PASS class A { static foo() { } ; foo() {} } did not throw exception.
PASS class A { static foo() { } ; get foo() {} } did not throw exception.
PASS class A { static foo() { } ; set foo(x) {} } did not throw exception.
PASS class A { static foo() { } ; static foo() {} } did not throw exception.
PASS class A { static foo() { } ; static get foo() {} } did not throw exception.
PASS class A { static foo() { } ; static set foo(x) {} } did not throw exception.
PASS class A { static get foo() { } ; foo() {} } did not throw exception.
PASS class A { static get foo() { } ; get foo() {} } did not throw exception.
PASS class A { static get foo() { } ; set foo(x) {} } did not throw exception.
PASS class A { static get foo() { } ; static foo() {} } did not throw exception.
PASS class A { static get foo() { } ; static get foo() {} } did not throw exception.
PASS class A { static get foo() { } ; static set foo(x) {} } did not throw exception.
PASS class A { static set foo(x) { } ; foo() {} } did not throw exception.
PASS class A { static set foo(x) { } ; get foo() {} } did not throw exception.
PASS class A { static set foo(x) { } ; set foo(x) {} } did not throw exception.
PASS class A { static set foo(x) { } ; static foo() {} } did not throw exception.
PASS class A { static set foo(x) { } ; static get foo() {} } did not throw exception.
PASS class A { static set foo(x) { } ; static set foo(x) {} } did not throw exception.
PASS successfullyParsed is true

TEST COMPLETE
