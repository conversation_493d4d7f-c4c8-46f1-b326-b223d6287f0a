#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function Outer() {
    var outerVar = 1;
    function Inner(innerArg) {
      this.innerFunc = function() { return outerVar * innerArg; }
    }
    this.getInnerFunc = function() { return new Inner(1).innerFunc; }
  }
  var f = new Outer().getInnerFunc();
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 11
bytecodes: [
  /*  102 S> */ B(LdaImmutableContextSlot), R(context), U8(2), U8(1),
                B(Star0),
                B(LdaImmutableCurrentContextSlot), U8(2),
  /*  118 E> */ B(Mul), R(0), U8(0),
  /*  129 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function Outer() {
    var outerVar = 1;
    function Inner(innerArg) {
      this.innerFunc = function() { outerVar = innerArg; }
    }
    this.getInnerFunc = function() { return new Inner(1).innerFunc; }
  }
  var f = new Outer().getInnerFunc();
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 8
bytecodes: [
  /*  102 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
  /*  111 E> */ B(StaContextSlot), R(context), U8(2), U8(1),
                B(LdaUndefined),
  /*  123 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

