---
ns: CFX
apiset: server
---
## SET_VEHICLE_NUMBER_PLATE_TEXT

```c
void SET_VEHICLE_NUMBER_PLATE_TEXT(Vehicle vehicle, char* plateText);
```

SET_VEHICLE_NUMBER_PLATE_TEXT

**This is the server-side RPC native equivalent of the client native [SET\_VEHICLE\_NUMBER\_PLATE\_TEXT](?_0x95A88F0B409CDA47).**

## Parameters
* **vehicle**: The vehicle to set the plate for
* **plateText**: The text to set the plate to, 8 chars maximum

