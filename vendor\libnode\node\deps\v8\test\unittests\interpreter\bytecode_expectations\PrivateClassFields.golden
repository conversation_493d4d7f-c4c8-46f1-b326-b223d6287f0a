#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      #a;
      constructor() {
        this.#a = 1;
      }
    }
  
    class B {
      #a = 1;
    }
    new A;
    new B;
  }
"
frame size: 7
parameter count: 1
bytecode array length: 112
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaConstant), U8(2),
                B(Star4),
                B(LdaConstant), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(Mov), <PERSON>(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(SetNamedProperty), R(3), U8(5), U8(0),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(6),
                B(PushContext), R(2),
                B(LdaConstant), U8(2),
                B(Star4),
                B(LdaConstant), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(8), U8(2), U8(2),
                B(Star3),
                B(LdaConstant), U8(7),
                B(Star4),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(CreateClosure), U8(9), U8(3), U8(2),
                B(SetNamedProperty), R(3), U8(5), U8(2),
                B(PopContext), R(2),
                B(Mov), R(5), R(1),
  /*  136 S> */ B(Ldar), R(0),
  /*  136 E> */ B(Construct), R(0), R(0), U8(0), U8(4),
  /*  145 S> */ B(Ldar), R(1),
  /*  145 E> */ B(Construct), R(1), R(0), U8(0), U8(6),
                B(LdaUndefined),
  /*  154 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#a"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A extends class {} {
      #a;
      constructor() {
        super();
        this.#a = 1;
      }
    }
  
    class B extends class {} {
      #a = 1;
      #b = this.#a;
      foo() { return this.#a; }
      bar(v) { this.#b = v; }
      constructor() {
        super();
        this.foo();
        this.bar(3);
      }
    }
  
    class C extends B {
      #a = 2;
      constructor() {
        (() => super())();
      }
    }
  
    new A;
    new B;
    new C;
  };
"
frame size: 12
parameter count: 1
bytecode array length: 229
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(3),
                B(LdaConstant), U8(2),
                B(Star5),
                B(LdaConstant), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(5), U8(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(4), U8(0), U8(2),
                B(Star8),
                B(LdaConstant), U8(3),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(5), U8(1), U8(2),
                B(Star4),
                B(LdaConstant), U8(1),
                B(Star5),
                B(Mov), R(4), R(6),
                B(Mov), R(10), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(3),
                B(CreateClosure), U8(6), U8(2), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(0),
                B(PopContext), R(3),
                B(Mov), R(6), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(8),
                B(PushContext), R(3),
                B(LdaConstant), U8(2),
                B(Star5),
                B(LdaConstant), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(5), U8(1),
                B(StaCurrentContextSlot), U8(2),
                B(LdaConstant), U8(10),
                B(Star5),
                B(LdaConstant), U8(10),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(5), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(12), U8(3), U8(2),
                B(Star8),
                B(LdaConstant), U8(11),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(13), U8(4), U8(2),
                B(Star4),
                B(LdaConstant), U8(9),
                B(Star5),
                B(CreateClosure), U8(14), U8(5), U8(2),
                B(Star8),
                B(CreateClosure), U8(15), U8(6), U8(2),
                B(Star9),
                B(Mov), R(4), R(6),
                B(Mov), R(10), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(5),
                B(CreateClosure), U8(16), U8(7), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(2),
                B(PopContext), R(3),
                B(Mov), R(6), R(1),
  /*  140 E> */ B(CreateBlockContext), U8(17),
                B(PushContext), R(3),
                B(LdaConstant), U8(2),
                B(Star5),
                B(LdaConstant), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateNameSymbol), R(5), U8(1),
                B(StaCurrentContextSlot), U8(2),
  /*  356 E> */ B(CreateClosure), U8(19), U8(8), U8(2),
                B(Star4),
                B(LdaConstant), U8(18),
                B(Star5),
                B(Mov), R(4), R(6),
                B(Mov), R(1), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(3),
                B(CreateClosure), U8(20), U8(9), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(4),
                B(PopContext), R(3),
                B(Mov), R(6), R(2),
  /*  430 S> */ B(Ldar), R(0),
  /*  430 E> */ B(Construct), R(0), R(0), U8(0), U8(6),
  /*  439 S> */ B(Ldar), R(1),
  /*  439 E> */ B(Construct), R(1), R(0), U8(0), U8(8),
  /*  448 S> */ B(Ldar), R(2),
  /*  448 E> */ B(Construct), R(2), R(0), U8(0), U8(10),
                B(LdaUndefined),
  /*  458 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#a"],
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#b"],
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

