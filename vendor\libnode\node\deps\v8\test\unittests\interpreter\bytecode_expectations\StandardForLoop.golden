#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f() {
    for (let x = 0; x < 10; ++x) { let y = x; }
  }
  f();
"
frame size: 2
parameter count: 1
bytecode array length: 23
bytecodes: [
  /*   30 S> */ B(LdaZero),
                B(Star0),
  /*   35 S> */ B(LdaSmi), I8(10),
  /*   35 E> */ B(TestLessThan), R(0), U8(0),
                B(JumpIfFalse), U8(14),
  /*   56 S> */ B(Mov), R(0), R(1),
  /*   43 S> */ B(Ldar), R(1),
                B(Inc), U8(1),
                B(Star0),
  /*   17 E> */ B(JumpLoop), U8(15), I8(0), U8(2),
                B(LdaUndefined),
  /*   61 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    for (let x = 0; x < 10; ++x) { eval('1'); }
  }
  f();
"
frame size: 15
parameter count: 1
bytecode array length: 151
bytecodes: [
  /*   10 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(4),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(3),
                B(StaCurrentContextSlot), U8(4),
                B(CreateBlockContext), U8(1),
                B(PushContext), R(5),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*   30 S> */ B(LdaZero),
  /*   30 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaCurrentContextSlot), U8(2),
                B(Star0),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   59 E> */ B(CreateBlockContext), U8(2),
                B(PushContext), R(6),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(2),
                B(LdaSmi), I8(1),
                B(TestEqual), R(1), U8(0),
                B(JumpIfFalse), U8(6),
                B(LdaZero),
                B(Star1),
                B(Jump), U8(8),
  /*   43 S> */ B(LdaCurrentContextSlot), U8(2),
                B(Inc), U8(1),
  /*   43 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaSmi), I8(1),
                B(Star2),
  /*   35 S> */ B(LdaCurrentContextSlot), U8(2),
                B(Star7),
                B(LdaSmi), I8(10),
  /*   35 E> */ B(TestLessThan), R(7), U8(2),
                B(JumpIfFalse), U8(4),
                B(Jump), U8(6),
                B(PopContext), R(6),
                B(Jump), U8(70),
                B(LdaSmi), I8(1),
                B(TestEqual), R(2), U8(3),
                B(JumpIfFalse), U8(46),
  /*   48 S> */ B(LdaLookupGlobalSlot), U8(3), U8(4), U8(3),
                B(Star7),
                B(LdaConstant), U8(4),
                B(Star8),
                B(LdaZero),
                B(Star12),
                B(LdaSmi), I8(31),
                B(Star13),
                B(LdaSmi), I8(48),
                B(Star14),
                B(Mov), R(7), R(9),
                B(Mov), R(8), R(10),
                B(Mov), R(closure), R(11),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(9), U8(6),
                B(Star7),
  /*   48 E> */ B(CallUndefinedReceiver1), R(7), R(8), U8(6),
                B(LdaZero),
                B(Star2),
                B(LdaCurrentContextSlot), U8(2),
                B(Star0),
  /*   17 E> */ B(JumpLoop), U8(47), I8(1), U8(8),
                B(LdaSmi), I8(1),
                B(TestEqual), R(2), U8(9),
                B(JumpIfFalse), U8(6),
                B(PopContext), R(6),
                B(Jump), U8(8),
                B(PopContext), R(6),
                B(JumpLoop), U8(111), I8(0), U8(10),
                B(PopContext), R(5),
                B(LdaUndefined),
  /*   61 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  SCOPE_INFO_TYPE,
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["1"],
]
handlers: [
]

---
snippet: "
  function f() {
    for (let x = 0; x < 10; ++x) { (function() { return x; })(); }
  }
  f();
"
frame size: 6
parameter count: 1
bytecode array length: 96
bytecodes: [
  /*   30 S> */ B(LdaZero),
                B(Star3),
                B(Star0),
                B(LdaSmi), I8(1),
                B(Star1),
  /*   78 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(4),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(2),
                B(LdaSmi), I8(1),
                B(TestEqual), R(1), U8(0),
                B(JumpIfFalse), U8(6),
                B(LdaZero),
                B(Star1),
                B(Jump), U8(8),
  /*   43 S> */ B(LdaCurrentContextSlot), U8(2),
                B(Inc), U8(1),
  /*   43 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaSmi), I8(1),
                B(Star2),
  /*   35 S> */ B(LdaCurrentContextSlot), U8(2),
                B(Star5),
                B(LdaSmi), I8(10),
  /*   35 E> */ B(TestLessThan), R(5), U8(2),
                B(JumpIfFalse), U8(4),
                B(Jump), U8(6),
                B(PopContext), R(4),
                B(Jump), U8(43),
                B(LdaSmi), I8(1),
                B(TestEqual), R(2), U8(3),
                B(JumpIfFalse), U8(19),
  /*   48 S> */ B(CreateClosure), U8(1), U8(0), U8(2),
                B(Star5),
  /*   74 E> */ B(CallUndefinedReceiver0), R(5), U8(4),
                B(LdaZero),
                B(Star2),
                B(LdaCurrentContextSlot), U8(2),
                B(Star0),
  /*   17 E> */ B(JumpLoop), U8(20), I8(1), U8(6),
                B(LdaSmi), I8(1),
                B(TestEqual), R(2), U8(7),
                B(JumpIfFalse), U8(6),
                B(PopContext), R(4),
                B(Jump), U8(8),
                B(PopContext), R(4),
                B(JumpLoop), U8(84), I8(0), U8(8),
                B(LdaUndefined),
  /*   80 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  function f() {
    for (let { x, y } = { x: 0, y: 3 }; y > 0; --y) { let z = x + y; }
  }
  f();
"
frame size: 4
parameter count: 1
bytecode array length: 38
bytecodes: [
  /*   37 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
                B(Star3),
  /*   28 E> */ B(GetNamedProperty), R(3), U8(1), U8(1),
                B(Star0),
  /*   31 E> */ B(GetNamedProperty), R(3), U8(2), U8(3),
                B(Star1),
  /*   55 S> */ B(LdaZero),
  /*   55 E> */ B(TestGreaterThan), R(1), U8(5),
                B(JumpIfFalse), U8(17),
  /*   75 S> */ B(Ldar), R(1),
  /*   77 E> */ B(Add), R(0), U8(6),
                B(Star2),
  /*   62 S> */ B(Ldar), R(1),
                B(Dec), U8(7),
                B(Star1),
  /*   17 E> */ B(JumpLoop), U8(17), I8(0), U8(8),
                B(LdaUndefined),
  /*   84 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["y"],
]
handlers: [
]

---
snippet: "
  function* f() {
    for (let x = 0; x < 10; ++x) { let y = x; }
  }
  f();
"
frame size: 5
parameter count: 1
bytecode array length: 62
bytecodes: [
                B(SwitchOnGeneratorState), R(0), U8(0), U8(1),
                B(Mov), R(closure), R(3),
                B(Mov), R(this), R(4),
  /*   11 E> */ B(InvokeIntrinsic), U8(Runtime::k_CreateJSGeneratorObject), R(3), U8(2),
                B(Star0),
  /*   11 E> */ B(SuspendGenerator), R(0), R(0), U8(3), U8(0),
                B(ResumeGenerator), R(0), R(0), U8(3),
                B(Star3),
                B(InvokeIntrinsic), U8(Runtime::k_GeneratorGetResumeMode), R(0), U8(1),
                B(SwitchOnSmiNoFeedback), U8(1), U8(2), I8(0),
                B(Ldar), R(3),
  /*   11 E> */ B(Throw),
                B(Ldar), R(3),
                B(Return),
  /*   31 S> */ B(LdaZero),
                B(Star1),
  /*   36 S> */ B(LdaSmi), I8(10),
  /*   36 E> */ B(TestLessThan), R(1), U8(0),
                B(JumpIfFalse), U8(14),
  /*   57 S> */ B(Mov), R(1), R(2),
  /*   44 S> */ B(Ldar), R(2),
                B(Inc), U8(1),
                B(Star1),
  /*   18 E> */ B(JumpLoop), U8(15), I8(0), U8(2),
                B(LdaUndefined),
  /*   62 S> */ B(Return),
]
constant pool: [
  Smi [20],
  Smi [10],
  Smi [7],
]
handlers: [
]

---
snippet: "
  function* f() {
    for (let x = 0; x < 10; ++x) yield x;
  }
  f();
"
frame size: 4
parameter count: 1
bytecode array length: 92
bytecodes: [
                B(SwitchOnGeneratorState), R(0), U8(0), U8(2),
                B(Mov), R(closure), R(2),
                B(Mov), R(this), R(3),
  /*   11 E> */ B(InvokeIntrinsic), U8(Runtime::k_CreateJSGeneratorObject), R(2), U8(2),
                B(Star0),
  /*   11 E> */ B(SuspendGenerator), R(0), R(0), U8(2), U8(0),
                B(ResumeGenerator), R(0), R(0), U8(2),
                B(Star2),
                B(InvokeIntrinsic), U8(Runtime::k_GeneratorGetResumeMode), R(0), U8(1),
                B(SwitchOnSmiNoFeedback), U8(2), U8(2), I8(0),
                B(Ldar), R(2),
  /*   11 E> */ B(Throw),
                B(Ldar), R(2),
                B(Return),
  /*   31 S> */ B(LdaZero),
                B(Star1),
  /*   36 S> */ B(LdaSmi), I8(10),
  /*   36 E> */ B(TestLessThan), R(1), U8(0),
                B(JumpIfFalse), U8(44),
  /*   47 S> */ B(LdaFalse),
                B(Star3),
                B(Mov), R(1), R(2),
                B(InvokeIntrinsic), U8(Runtime::k_CreateIterResultObject), R(2), U8(2),
  /*   47 E> */ B(SuspendGenerator), R(0), R(0), U8(2), U8(1),
                B(ResumeGenerator), R(0), R(0), U8(2),
                B(Star2),
                B(InvokeIntrinsic), U8(Runtime::k_GeneratorGetResumeMode), R(0), U8(1),
                B(SwitchOnSmiNoFeedback), U8(4), U8(2), I8(0),
                B(Ldar), R(2),
  /*   47 E> */ B(Throw),
                B(Ldar), R(2),
                B(Return),
  /*   44 S> */ B(Ldar), R(1),
                B(Inc), U8(1),
                B(Star1),
  /*   18 E> */ B(JumpLoop), U8(45), I8(0), U8(2),
                B(LdaUndefined),
  /*   56 S> */ B(Return),
]
constant pool: [
  Smi [20],
  Smi [62],
  Smi [10],
  Smi [7],
  Smi [10],
  Smi [7],
]
handlers: [
]

---
snippet: "
  async function f() {
    for (let x = 0; x < 10; ++x) { let y = x; }
  }
  f();
"
frame size: 7
parameter count: 1
bytecode array length: 67
bytecodes: [
                B(Mov), R(closure), R(3),
                B(Mov), R(this), R(4),
  /*   16 E> */ B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionEnter), R(3), U8(2),
                B(Star0),
                B(Mov), R(context), R(3),
  /*   36 S> */ B(LdaZero),
                B(Star1),
  /*   41 S> */ B(LdaSmi), I8(10),
  /*   41 E> */ B(TestLessThan), R(1), U8(0),
                B(JumpIfFalse), U8(14),
  /*   62 S> */ B(Mov), R(1), R(2),
  /*   49 S> */ B(Ldar), R(2),
                B(Inc), U8(1),
                B(Star1),
  /*   23 E> */ B(JumpLoop), U8(15), I8(0), U8(2),
                B(LdaUndefined),
                B(Star5),
                B(Mov), R(0), R(4),
                B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionResolve), R(4), U8(2),
  /*   67 S> */ B(Return),
                B(Star4),
                B(CreateCatchContext), R(4), U8(0),
                B(Star3),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(3),
                B(PushContext), R(4),
                B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star6),
                B(Mov), R(0), R(5),
                B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionReject), R(5), U8(2),
                B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
  [14, 45, 45],
]

---
snippet: "
  async function f() {
    for (let x = 0; x < 10; ++x) await x;
  }
  f();
"
frame size: 6
parameter count: 1
bytecode array length: 101
bytecodes: [
                B(SwitchOnGeneratorState), R(0), U8(0), U8(1),
                B(Mov), R(closure), R(2),
                B(Mov), R(this), R(3),
  /*   16 E> */ B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionEnter), R(2), U8(2),
                B(Star0),
                B(Mov), R(context), R(2),
  /*   36 S> */ B(LdaZero),
                B(Star1),
  /*   41 S> */ B(LdaSmi), I8(10),
  /*   41 E> */ B(TestLessThan), R(1), U8(0),
                B(JumpIfFalse), U8(44),
  /*   52 S> */ B(Mov), R(0), R(3),
                B(Mov), R(1), R(4),
                B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionAwaitUncaught), R(3), U8(2),
  /*   52 E> */ B(SuspendGenerator), R(0), R(0), U8(3), U8(0),
                B(ResumeGenerator), R(0), R(0), U8(3),
                B(Star3),
                B(InvokeIntrinsic), U8(Runtime::k_GeneratorGetResumeMode), R(0), U8(1),
                B(Star4),
                B(LdaZero),
                B(TestReferenceEqual), R(4),
                B(JumpIfTrue), U8(5),
                B(Ldar), R(3),
                B(ReThrow),
  /*   49 S> */ B(Ldar), R(1),
                B(Inc), U8(1),
                B(Star1),
  /*   23 E> */ B(JumpLoop), U8(45), I8(0), U8(2),
                B(LdaUndefined),
                B(Star4),
                B(Mov), R(0), R(3),
                B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionResolve), R(3), U8(2),
  /*   61 S> */ B(Return),
                B(Star3),
                B(CreateCatchContext), R(3), U8(1),
                B(Star2),
                B(LdaTheHole),
                B(SetPendingMessage),
                B(Ldar), R(2),
                B(PushContext), R(3),
                B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star5),
                B(Mov), R(0), R(4),
                B(InvokeIntrinsic), U8(Runtime::k_AsyncFunctionReject), R(4), U8(2),
                B(Return),
]
constant pool: [
  Smi [42],
  SCOPE_INFO_TYPE,
]
handlers: [
  [18, 79, 79],
]

