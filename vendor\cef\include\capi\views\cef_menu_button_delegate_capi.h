// Copyright (c) 2022 Marshall <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=ca7948602a0d20a5bd0271065d79e8679898eff6$
//

#ifndef CEF_INCLUDE_CAPI_VIEWS_CEF_MENU_BUTTON_DELEGATE_CAPI_H_
#define CEF_INCLUDE_CAPI_VIEWS_CEF_MENU_BUTTON_DELEGATE_CAPI_H_
#pragma once

#include "include/capi/views/cef_button_delegate_capi.h"

#ifdef __cplusplus
extern "C" {
#endif

struct _cef_menu_button_t;

///
// MenuButton pressed lock is released when this object is destroyed.
///
typedef struct _cef_menu_button_pressed_lock_t {
  ///
  // Base structure.
  ///
  cef_base_ref_counted_t base;
} cef_menu_button_pressed_lock_t;

///
// Implement this structure to handle MenuButton events. The functions of this
// structure will be called on the browser process UI thread unless otherwise
// indicated.
///
typedef struct _cef_menu_button_delegate_t {
  ///
  // Base structure.
  ///
  cef_button_delegate_t base;

  ///
  // Called when |button| is pressed. Call cef_menu_button_t::show_menu() to
  // show a popup menu at |screen_point|. When showing a custom popup such as a
  // window keep a reference to |button_pressed_lock| until the popup is hidden
  // to maintain the pressed button state.
  ///
  void(CEF_CALLBACK* on_menu_button_pressed)(
      struct _cef_menu_button_delegate_t* self,
      struct _cef_menu_button_t* menu_button,
      const cef_point_t* screen_point,
      struct _cef_menu_button_pressed_lock_t* button_pressed_lock);
} cef_menu_button_delegate_t;

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_VIEWS_CEF_MENU_BUTTON_DELEGATE_CAPI_H_
