#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f() {
    if (0) {
      return 1;
    } else {
      return -1;
    }
  };
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   55 S> */ B(LdaSmi), I8(-1),
  /*   65 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    if ('lucky') {
      return 1;
    } else {
      return -1;
    }
  };
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   36 S> */ B(LdaSmi), I8(1),
  /*   45 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    if (false) {
      return 1;
    } else {
      return -1;
    }
  };
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   59 S> */ B(LdaSmi), I8(-1),
  /*   69 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    if (false) {
      return 1;
    }
  };
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   17 S> */ B(LdaUndefined),
  /*   48 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    var a = 1;
    if (a) {
      a += 1;
    } else {
      return 2;
    }
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 18
bytecodes: [
  /*   25 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   30 S> */ B(JumpIfToBooleanFalse), U8(10),
  /*   43 S> */ B(Ldar), R(0),
                B(AddSmi), I8(1), U8(0),
                B(Star0),
                B(Jump), U8(5),
  /*   66 S> */ B(LdaSmi), I8(2),
  /*   75 S> */ B(Return),
                B(LdaUndefined),
  /*   80 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a) {
    if (a <= 0) {
      return 200;
    } else {
      return -200;
    }
  };
  f(99);
"
frame size: 0
parameter count: 2
bytecode array length: 16
bytecodes: [
  /*   18 S> */ B(LdaZero),
  /*   24 E> */ B(TestLessThanOrEqual), R(arg0), U8(0),
                B(JumpIfFalse), U8(7),
  /*   36 S> */ B(Wide), B(LdaSmi), I16(200),
  /*   47 S> */ B(Return),
  /*   63 S> */ B(Wide), B(LdaSmi), I16(-200),
  /*   75 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a, b) { if (a in b) { return 200; } }f('prop', { prop: 'yes'});
"
frame size: 0
parameter count: 3
bytecode array length: 14
bytecodes: [
  /*   19 S> */ B(Ldar), R(arg1),
  /*   25 E> */ B(TestIn), R(arg0), U8(0),
                B(JumpIfFalse), U8(7),
  /*   33 S> */ B(Wide), B(LdaSmi), I16(200),
  /*   44 S> */ B(Return),
                B(LdaUndefined),
  /*   47 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(z) { var a = 0; var b = 0; if (a === 0.01) {
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
   return 200; } else { return -200; } } f(0.001);
"
frame size: 2
parameter count: 2
bytecode array length: 24
bytecodes: [
  /*   24 S> */ B(LdaZero),
                B(Star0),
  /*   35 S> */ B(LdaZero),
                B(Star1),
  /*   38 S> */ B(LdaConstant), U8(0),
  /*   44 E> */ B(TestEqualStrict), R(0), U8(0),
                B(JumpIfFalse), U8(10),
  /*   58 S> */ B(Mov), R(0), R(1),
  /* 1081 S> */ B(Wide), B(LdaSmi), I16(200),
  /* 1092 S> */ B(Return),
  /* 1102 S> */ B(Wide), B(LdaSmi), I16(-200),
  /* 1114 S> */ B(Return),
]
constant pool: [
  HEAP_NUMBER_TYPE [0.01],
]
handlers: [
]

---
snippet: "
  function f() {
    var a = 0; var b = 0;
    if (a) {
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    b = a; a = b;
    return 200; } else { return -200; }
  };
  f();
"
frame size: 2
parameter count: 1
bytecode array length: 21
bytecodes: [
  /*   25 S> */ B(LdaZero),
                B(Star0),
  /*   36 S> */ B(LdaZero),
                B(Star1),
  /*   41 S> */ B(Ldar), R(0),
                B(JumpIfToBooleanFalse), U8(10),
  /*   52 S> */ B(Mov), R(0), R(1),
  /* 1076 S> */ B(Wide), B(LdaSmi), I16(200),
  /* 1087 S> */ B(Return),
  /* 1097 S> */ B(Wide), B(LdaSmi), I16(-200),
  /* 1109 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a, b) {
    if (a == b) { return 1; }
    if (a === b) { return 1; }
    if (a < b) { return 1; }
    if (a > b) { return 1; }
    if (a <= b) { return 1; }
    if (a >= b) { return 1; }
    if (a in b) { return 1; }
    if (a instanceof b) { return 1; }
    return 0;
  }
  f(1, 1);
"
frame size: 0
parameter count: 3
bytecode array length: 82
bytecodes: [
  /*   21 S> */ B(Ldar), R(arg1),
  /*   27 E> */ B(TestEqual), R(arg0), U8(0),
                B(JumpIfFalse), U8(5),
  /*   35 S> */ B(LdaSmi), I8(1),
  /*   44 S> */ B(Return),
  /*   49 S> */ B(Ldar), R(arg1),
  /*   55 E> */ B(TestEqualStrict), R(arg0), U8(1),
                B(JumpIfFalse), U8(5),
  /*   64 S> */ B(LdaSmi), I8(1),
  /*   73 S> */ B(Return),
  /*   78 S> */ B(Ldar), R(arg1),
  /*   84 E> */ B(TestLessThan), R(arg0), U8(2),
                B(JumpIfFalse), U8(5),
  /*   91 S> */ B(LdaSmi), I8(1),
  /*  100 S> */ B(Return),
  /*  105 S> */ B(Ldar), R(arg1),
  /*  111 E> */ B(TestGreaterThan), R(arg0), U8(3),
                B(JumpIfFalse), U8(5),
  /*  118 S> */ B(LdaSmi), I8(1),
  /*  127 S> */ B(Return),
  /*  132 S> */ B(Ldar), R(arg1),
  /*  138 E> */ B(TestLessThanOrEqual), R(arg0), U8(4),
                B(JumpIfFalse), U8(5),
  /*  146 S> */ B(LdaSmi), I8(1),
  /*  155 S> */ B(Return),
  /*  160 S> */ B(Ldar), R(arg1),
  /*  166 E> */ B(TestGreaterThanOrEqual), R(arg0), U8(5),
                B(JumpIfFalse), U8(5),
  /*  174 S> */ B(LdaSmi), I8(1),
  /*  183 S> */ B(Return),
  /*  188 S> */ B(Ldar), R(arg1),
  /*  194 E> */ B(TestIn), R(arg0), U8(6),
                B(JumpIfFalse), U8(5),
  /*  202 S> */ B(LdaSmi), I8(1),
  /*  211 S> */ B(Return),
  /*  216 S> */ B(Ldar), R(arg1),
  /*  222 E> */ B(TestInstanceOf), R(arg0), U8(8),
                B(JumpIfFalse), U8(5),
  /*  238 S> */ B(LdaSmi), I8(1),
  /*  247 S> */ B(Return),
  /*  252 S> */ B(LdaZero),
  /*  261 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f() {
    var a = 0;
    if (a) {
      return 20;
    } else {
      return -20;
    }
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 10
bytecodes: [
  /*   25 S> */ B(LdaZero),
                B(Star0),
  /*   30 S> */ B(JumpIfToBooleanFalse), U8(5),
  /*   43 S> */ B(LdaSmi), I8(20),
  /*   53 S> */ B(Return),
  /*   69 S> */ B(LdaSmi), I8(-20),
  /*   80 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a, b) {
    if (a == b || a < 0) {
      return 1;
    } else if (a > 0 && b > 0) {
      return 0;
    } else {
      return -1;
    }
  };
  f(-1, 1);
"
frame size: 0
parameter count: 3
bytecode array length: 33
bytecodes: [
  /*   21 S> */ B(Ldar), R(arg1),
  /*   27 E> */ B(TestEqual), R(arg0), U8(0),
                B(JumpIfTrue), U8(8),
                B(LdaZero),
  /*   37 E> */ B(TestLessThan), R(arg0), U8(1),
                B(JumpIfFalse), U8(5),
  /*   48 S> */ B(LdaSmi), I8(1),
  /*   57 S> */ B(Return),
  /*   67 S> */ B(LdaZero),
  /*   73 E> */ B(TestGreaterThan), R(arg0), U8(2),
                B(JumpIfFalse), U8(10),
                B(LdaZero),
  /*   82 E> */ B(TestGreaterThan), R(arg1), U8(3),
                B(JumpIfFalse), U8(4),
  /*   93 S> */ B(LdaZero),
  /*  102 S> */ B(Return),
  /*  118 S> */ B(LdaSmi), I8(-1),
  /*  128 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

