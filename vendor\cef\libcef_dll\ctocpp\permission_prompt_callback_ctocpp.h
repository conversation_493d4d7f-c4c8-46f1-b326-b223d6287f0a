// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=bdf5b8df2a3d5555f42982cd741ae13f3d1a67d1$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_PERMISSION_PROMPT_CALLBACK_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_PERMISSION_PROMPT_CALLBACK_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_permission_handler_capi.h"
#include "include/cef_permission_handler.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefPermissionPromptCallbackCToCpp
    : public CefCToCppRefCounted<CefPermissionPromptCallbackCToCpp,
                                 CefPermissionPromptCallback,
                                 cef_permission_prompt_callback_t> {
 public:
  CefPermissionPromptCallbackCToCpp();
  virtual ~CefPermissionPromptCallbackCToCpp();

  // CefPermissionPromptCallback methods.
  void Continue(cef_permission_request_result_t result) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_PERMISSION_PROMPT_CALLBACK_CTOCPP_H_
