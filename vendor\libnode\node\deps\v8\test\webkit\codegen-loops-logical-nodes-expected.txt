# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests loop codegen when the condition is a logical node.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS while_or_eq() is true
PASS while_or_neq() is true
PASS while_or_less() is true
PASS while_or_lesseq() is true
PASS while_and_eq() is true
PASS while_and_neq() is true
PASS while_and_less() is true
PASS while_and_lesseq() is true
PASS for_or_eq() is true
PASS for_or_neq() is true
PASS for_or_less() is true
PASS for_or_lesseq() is true
PASS for_and_eq() is true
PASS for_and_neq() is true
PASS for_and_less() is true
PASS for_and_lesseq() is true
PASS dowhile_or_eq() is true
PASS dowhile_or_neq() is true
PASS dowhile_or_less() is true
PASS dowhile_or_lesseq() is true
PASS dowhile_and_eq() is true
PASS dowhile_and_neq() is true
PASS dowhile_and_less() is true
PASS dowhile_and_lesseq() is true
PASS while_not_or_eq() is false
PASS while_not_or_neq() is false
PASS while_not_or_less() is false
PASS while_not_or_lesseq() is false
PASS while_not_and_eq() is false
PASS while_not_and_neq() is false
PASS while_not_and_less() is false
PASS while_not_and_lesseq() is false
PASS for_not_or_eq() is false
PASS for_not_or_neq() is false
PASS for_not_or_less() is false
PASS for_not_or_lesseq() is false
PASS for_not_and_eq() is false
PASS for_not_and_neq() is false
PASS for_not_and_less() is false
PASS for_not_and_lesseq() is false
PASS dowhile_not_or_eq() is false
PASS dowhile_not_or_neq() is false
PASS dowhile_not_or_less() is false
PASS dowhile_not_or_lesseq() is false
PASS dowhile_not_and_eq() is false
PASS dowhile_not_and_neq() is false
PASS dowhile_not_and_less() is false
PASS dowhile_not_and_lesseq() is false
PASS float_while_or_eq() is true
PASS float_while_or_neq() is true
PASS float_while_or_less() is true
PASS float_while_or_lesseq() is true
PASS float_while_and_eq() is true
PASS float_while_and_neq() is true
PASS float_while_and_less() is true
PASS float_while_and_lesseq() is true
PASS float_for_or_eq() is true
PASS float_for_or_neq() is true
PASS float_for_or_less() is true
PASS float_for_or_lesseq() is true
PASS float_for_and_eq() is true
PASS float_for_and_neq() is true
PASS float_for_and_less() is true
PASS float_for_and_lesseq() is true
PASS float_dowhile_or_eq() is true
PASS float_dowhile_or_neq() is true
PASS float_dowhile_or_less() is true
PASS float_dowhile_or_lesseq() is true
PASS float_dowhile_and_eq() is true
PASS float_dowhile_and_neq() is true
PASS float_dowhile_and_less() is true
PASS float_dowhile_and_lesseq() is true
PASS float_while_not_or_eq() is false
PASS float_while_not_or_neq() is false
PASS float_while_not_or_less() is false
PASS float_while_not_or_lesseq() is false
PASS float_while_not_and_eq() is false
PASS float_while_not_and_neq() is false
PASS float_while_not_and_less() is false
PASS float_while_not_and_lesseq() is false
PASS float_for_not_or_eq() is false
PASS float_for_not_or_neq() is false
PASS float_for_not_or_less() is false
PASS float_for_not_or_lesseq() is false
PASS float_for_not_and_eq() is false
PASS float_for_not_and_neq() is false
PASS float_for_not_and_less() is false
PASS float_for_not_and_lesseq() is false
PASS float_dowhile_not_or_eq() is false
PASS float_dowhile_not_or_neq() is false
PASS float_dowhile_not_or_less() is false
PASS float_dowhile_not_or_lesseq() is false
PASS float_dowhile_not_and_eq() is false
PASS float_dowhile_not_and_neq() is false
PASS float_dowhile_not_and_less() is false
PASS successfullyParsed is true

TEST COMPLETE

