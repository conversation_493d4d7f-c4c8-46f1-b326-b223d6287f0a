#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var x = 0;
  while (x != 10) {
    x = x + 10;
  }
  return x;
"
frame size: 1
parameter count: 1
bytecode array length: 22
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   54 S> */ B(LdaSmi), I8(10),
  /*   54 E> */ B(TestEqual), R(0), U8(0),
                B(JumpIfTrue), U8(12),
  /*   65 S> */ B(Ldar), R(0),
  /*   71 E> */ B(AddSmi), I8(10), U8(1),
                B(Star0),
  /*   45 E> */ B(JumpLoop), U8(13), I8(0), U8(2),
  /*   79 S> */ B(Ldar), R(0),
  /*   88 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = false;
  do {
    x = !x;
  } while(x == false);
  return x;
"
frame size: 1
parameter count: 1
bytecode array length: 19
bytecodes: [
  /*   42 S> */ B(LdaFalse),
                B(Star0),
  /*   56 S> */ B(Ldar), R(0),
                B(ToBooleanLogicalNot),
                B(Star0),
  /*   74 S> */ B(LdaFalse),
  /*   74 E> */ B(TestEqual), R(0), U8(0),
                B(JumpIfFalse), U8(6),
  /*   49 E> */ B(JumpLoop), U8(10), I8(0), U8(1),
  /*   85 S> */ B(Ldar), R(0),
  /*   94 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 101;
  return void(x * 3);
"
frame size: 1
parameter count: 1
bytecode array length: 8
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(101),
                B(Star0),
  /*   61 S> */ B(MulSmi), I8(3), U8(0),
                B(LdaUndefined),
  /*   66 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1234;
  var y = void (x * x - 1);
  return y;
"
frame size: 2
parameter count: 1
bytecode array length: 14
bytecodes: [
  /*   42 S> */ B(Wide), B(LdaSmi), I16(1234),
                B(Star0),
  /*   64 S> */ B(Mul), R(0), U8(1),
  /*   68 E> */ B(SubSmi), I8(1), U8(0),
                B(LdaUndefined),
                B(Star1),
  /*   83 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 13;
  return ~x;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(13),
                B(Star0),
  /*   53 S> */ B(BitwiseNot), U8(0),
  /*   56 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 13;
  return +x;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(13),
                B(Star0),
  /*   53 S> */ B(ToNumber), U8(0),
  /*   56 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 13;
  return -x;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(13),
                B(Star0),
  /*   53 S> */ B(Negate), U8(0),
  /*   56 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

