#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  var test;
  (function() {
    class A {
      constructor(...args) { this.baseArgs = args; }
    }
    class B extends A {}
    test = new B(1, 2, 3).constructor;
  })();
"
frame size: 7
parameter count: 1
bytecode array length: 31
bytecodes: [
                B(Mov), R(closure), R(1),
  /*   93 S> */ B(FindNonDefaultConstructorOrConstruct), R(1), R(0), R(5),
                B(Ldar), R(5),
                B(Mov), R(1), R(2),
                B(Mov), R(0), R(3),
                B(Mov), R(6), R(4),
                B(JumpIfTrue), U8(10),
                B(ThrowIfNotSuperConstructor), R(4),
                B(Ldar), R(3),
  /*   93 E> */ B(ConstructForwardAllArgs), R(4), U8(0),
                B(Star4),
                B(Ldar), R(4),
  /*   93 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var test;
  (function() {
    class A {
      constructor(...args) { this.baseArgs = args; }
    }
    class B extends A {
      constructor(...args) { super(1, ...args); }
    }
    test = new B(1, 2, 3).constructor;
  })();
"
frame size: 12
parameter count: 1
bytecode array length: 51
bytecodes: [
  /*  128 E> */ B(CreateRestParameter),
                B(Star3),
                B(Mov), R(closure), R(1),
                B(Mov), R(3), R(2),
  /*  140 S> */ B(LdaSmi), I8(1),
                B(Star7),
  /*  140 E> */ B(FindNonDefaultConstructorOrConstruct), R(closure), R(0), R(10),
                B(Mov), R(3), R(8),
                B(Ldar), R(10),
                B(Mov), R(1), R(5),
                B(Mov), R(0), R(9),
                B(Mov), R(11), R(6),
                B(JumpIfTrue), U8(12),
                B(ThrowIfNotSuperConstructor), R(6),
                B(Ldar), R(9),
  /*  140 E> */ B(ConstructWithSpread), R(6), R(7), U8(2), U8(0),
                B(Star6),
                B(Ldar), R(this),
                B(ThrowSuperAlreadyCalledIfNotHole),
                B(Mov), R(6), R(this),
                B(Ldar), R(this),
                B(ThrowSuperNotCalledIfHole),
  /*  159 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var test;
  (function() {
    class A {
      constructor(...args) { this.baseArgs = args; }
    }
    class B extends A {
      constructor(...args) { super(1, ...args, 1); }
    }
    test = new B(1, 2, 3).constructor;
  })();
"
frame size: 12
parameter count: 1
bytecode array length: 101
bytecodes: [
  /*  128 E> */ B(CreateRestParameter),
                B(Star3),
                B(Mov), R(closure), R(1),
                B(Mov), R(3), R(2),
  /*  140 S> */ B(CreateArrayLiteral), U8(0), U8(0), U8(37),
                B(Star7),
                B(LdaSmi), I8(1),
                B(Star8),
  /*  152 E> */ B(GetIterator), R(3), U8(1), U8(3),
                B(Star10),
                B(GetNamedProperty), R(10), U8(1), U8(5),
                B(Star9),
                B(Mov), R(1), R(5),
                B(CallProperty0), R(9), R(10), U8(14),
                B(Star11),
                B(JumpIfJSReceiver), U8(7),
                B(CallRuntime), U16(Runtime::kThrowIteratorResultNotAnObject), R(11), U8(1),
                B(GetNamedProperty), R(11), U8(2), U8(16),
                B(JumpIfToBooleanTrue), U8(19),
                B(GetNamedProperty), R(11), U8(3), U8(7),
                B(StaInArrayLiteral), R(7), R(8), U8(12),
                B(Ldar), R(8),
                B(Inc), U8(11),
                B(Star8),
                B(JumpLoop), U8(31), I8(0), U8(18),
                B(LdaSmi), I8(1),
                B(StaInArrayLiteral), R(7), R(8), U8(12),
  /*  140 E> */ B(FindNonDefaultConstructorOrConstruct), R(5), R(0), R(9),
                B(Ldar), R(9),
                B(Mov), R(0), R(8),
                B(Mov), R(10), R(6),
                B(JumpIfTrue), U8(9),
                B(ThrowIfNotSuperConstructor), R(6),
                B(CallJSRuntime), U8(%reflect_construct), R(6), U8(3),
                B(Star6),
                B(Ldar), R(this),
                B(ThrowSuperAlreadyCalledIfNotHole),
                B(Mov), R(6), R(this),
                B(Ldar), R(this),
                B(ThrowSuperNotCalledIfHole),
  /*  162 S> */ B(Return),
]
constant pool: [
  ARRAY_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["next"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["done"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["value"],
]
handlers: [
]

