// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=84ca9a25ae345642994cc1b44cd71f90e7406f19$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_REGISTRATION_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_REGISTRATION_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_registration_capi.h"
#include "include/cef_registration.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefRegistrationCToCpp : public CefCToCppRefCounted<CefRegistrationCToCpp,
                                                         CefRegistration,
                                                         cef_registration_t> {
 public:
  CefRegistrationCToCpp();
  virtual ~CefRegistrationCToCpp();

  // CefRegistration methods.
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_REGISTRATION_CTOCPP_H_
