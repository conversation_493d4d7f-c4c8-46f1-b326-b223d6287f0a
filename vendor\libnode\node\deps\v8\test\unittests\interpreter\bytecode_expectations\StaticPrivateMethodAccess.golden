#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  class A {
    static #a() { return 1; }
    static test() { return this.#a(); }
  }
  
  var test = A.test;
  test();
"
frame size: 4
parameter count: 1
bytecode array length: 32
bytecodes: [
  /*   56 S> */ B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(1),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
  /*   68 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
                B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star0),
  /*   70 E> */ B(CallAnyReceiver), R(0), <PERSON>(1), U8(1), U8(0),
  /*   73 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["A"],
]
handlers: [
]

---
snippet: "
  class B {
    static #b() { return 1; }
    static test() { this.#b = 1; }
  }
  
  var test = B.test;
  test();
"
frame size: 4
parameter count: 1
bytecode array length: 40
bytecodes: [
  /*   56 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
                B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
  /*   61 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
                B(Wide), B(LdaSmi), I16(318),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["B"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#b"],
]
handlers: [
]

---
snippet: "
  class C {
    static #c() { return 1; }
    static test() { this.#c++; }
  }
  
  var test = C.test;
  test();
"
frame size: 3
parameter count: 1
bytecode array length: 37
bytecodes: [
  /*   56 S> */ B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star1),
                B(LdaConstant), U8(0),
                B(Star2),
  /*   61 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
                B(Wide), B(LdaSmi), I16(318),
                B(Star1),
                B(LdaConstant), U8(1),
                B(Star2),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["C"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#c"],
]
handlers: [
]

---
snippet: "
  class D {
    static get #d() { return 1; }
    static set #d(val) { }
  
    static test() {
      this.#d++;
      this.#d = 1;
      return this.#d;
    }
  }
  
  var test = D.test;
  test();
"
frame size: 5
parameter count: 1
bytecode array length: 127
bytecodes: [
  /*   90 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
                B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
  /*   95 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
                B(CallRuntime), U16(Runtime::kLoadPrivateGetter), R(1), U8(1),
                B(Star2),
                B(CallProperty0), R(2), R(0), U8(0),
                B(Inc), U8(2),
                B(Star2),
  /*   97 E> */ B(CallRuntime), U16(Runtime::kLoadPrivateSetter), R(1), U8(1),
                B(Star3),
                B(CallProperty1), R(3), R(0), R(2), U8(3),
  /*  105 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
                B(LdaSmi), I8(1),
                B(Star2),
                B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star3),
                B(LdaConstant), U8(0),
                B(Star4),
  /*  110 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(3), U8(2),
                B(Throw),
                B(CallRuntime), U16(Runtime::kLoadPrivateSetter), R(1), U8(1),
                B(Star3),
                B(CallProperty1), R(3), R(0), R(2), U8(5),
  /*  122 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
                B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
  /*  134 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
                B(CallRuntime), U16(Runtime::kLoadPrivateGetter), R(1), U8(1),
                B(Star2),
                B(CallProperty0), R(2), R(0), U8(7),
  /*  137 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["D"],
]
handlers: [
]

---
snippet: "
  class E {
    static get #e() { return 1; }
    static test() { this.#e++; }
  }
  var test = E.test;
  test();
"
frame size: 3
parameter count: 1
bytecode array length: 37
bytecodes: [
  /*   60 S> */ B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star1),
                B(LdaConstant), U8(0),
                B(Star2),
  /*   65 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
                B(Wide), B(LdaSmi), I16(320),
                B(Star1),
                B(LdaConstant), U8(1),
                B(Star2),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["E"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#e"],
]
handlers: [
]

---
snippet: "
  class F {
    static set #f(val) { }
    static test() { this.#f++; }
  }
  var test = F.test;
  test();
"
frame size: 3
parameter count: 1
bytecode array length: 37
bytecodes: [
  /*   53 S> */ B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star1),
                B(LdaConstant), U8(0),
                B(Star2),
  /*   58 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
                B(Wide), B(LdaSmi), I16(319),
                B(Star1),
                B(LdaConstant), U8(1),
                B(Star2),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["F"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#f"],
]
handlers: [
]

---
snippet: "
  class G {
    static get #d() { return 1; }
    static test() { this.#d = 1; }
  }
  var test = G.test;
  test();
"
frame size: 4
parameter count: 1
bytecode array length: 40
bytecodes: [
  /*   60 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star1),
                B(LdaCurrentContextSlot), U8(3),
                B(TestReferenceEqual), R(this),
                B(Mov), R(this), R(0),
                B(JumpIfTrue), U8(16),
                B(Wide), B(LdaSmi), I16(312),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
  /*   65 E> */ B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
                B(Wide), B(LdaSmi), I16(320),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["G"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#d"],
]
handlers: [
]

---
snippet: "
  class H {
    set #h(val) { }
    static test() { this.#h; }
  }
  var test = H.test;
  test();
"
frame size: 3
parameter count: 1
bytecode array length: 19
bytecodes: [
  /*   46 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   51 E> */ B(GetKeyedProperty), R(this), U8(0),
                B(Wide), B(LdaSmi), I16(319),
                B(Star1),
                B(LdaConstant), U8(0),
                B(Star2),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(1), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#h"],
]
handlers: [
]

