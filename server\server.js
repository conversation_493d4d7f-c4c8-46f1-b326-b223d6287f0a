const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Path to the FiveM release directory
const FIVEM_RELEASE_PATH = path.join(__dirname, '..', 'code', 'bin', 'five', 'release');
const CONTENT_PATH = path.join(__dirname, 'content');

// Ensure content directory exists
fs.ensureDirSync(CONTENT_PATH);

// Mock cache data structure
const mockCacheData = {
  "fivereborn": {
    "production": {
      version: "1000000",
      files: []
    }
  }
};

// Helper function to calculate file hash with retry mechanism
function calculateFileHash(filePath, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
      if (error.code === 'EBUSY' && i < retries - 1) {
        console.warn(`File busy, retrying in 100ms: ${path.basename(filePath)} (attempt ${i + 1}/${retries})`);
        // Wait a bit before retry
        const start = Date.now();
        while (Date.now() - start < 100) {
          // Busy wait for 100ms
        }
        continue;
      }

      if (error.code === 'EBUSY') {
        console.warn(`Skipping busy file: ${path.basename(filePath)}`);
        return null;
      }

      console.error(`Error calculating hash for ${filePath}:`, error.message);
      return null;
    }
  }
  return null;
}

// Helper function to check if file should be included
function shouldIncludeFile(fileName) {
  const ext = path.extname(fileName).toLowerCase();

  // Include important file types
  const allowedExtensions = ['.dll', '.exe', '.bin', '.json', '.xml', '.txt', '.cfg', '.com'];
  if (!allowedExtensions.includes(ext)) {
    return false;
  }

  // Skip temporary and debug files
  const skipPatterns = [
    /\.tmp$/i,
    /\.log$/i,
    /\.pdb$/i,
    /\.exp$/i,
    /\.lib$/i,
    /\.formaldev$/i
  ];

  for (const pattern of skipPatterns) {
    if (pattern.test(fileName)) {
      return false;
    }
  }

  return true;
}

// Helper function to scan FiveM release directory and build file list
function buildFileList() {
  const files = [];
  let scannedCount = 0;
  let includedCount = 0;

  try {
    const scanDirectory = (dirPath, relativePath = '') => {
      const items = fs.readdirSync(dirPath);

      items.forEach(item => {
        const fullPath = path.join(dirPath, item);
        const relativeFilePath = path.join(relativePath, item).replace(/\\/g, '/');

        try {
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            // Skip certain directories
            if (!['dbg', 'gen', 'mods'].includes(item)) {
              scanDirectory(fullPath, relativeFilePath);
            }
          } else if (stat.isFile()) {
            scannedCount++;

            if (shouldIncludeFile(item)) {
              const hash = calculateFileHash(fullPath);
              if (hash) {
                files.push({
                  name: relativeFilePath,
                  hash256: hash,
                  localSize: stat.size,
                  downloadSize: stat.size,
                  algorithm: 0 // No compression
                });
                includedCount++;
              }
            }
          }
        } catch (error) {
          console.warn(`Skipping ${item}: ${error.message}`);
        }
      });
    };

    if (fs.existsSync(FIVEM_RELEASE_PATH)) {
      console.log(`Scanning directory: ${FIVEM_RELEASE_PATH}`);
      scanDirectory(FIVEM_RELEASE_PATH);
      console.log(`Scanned ${scannedCount} files, included ${includedCount} files`);
    } else {
      console.error(`Release directory not found: ${FIVEM_RELEASE_PATH}`);
    }
  } catch (error) {
    console.error('Error building file list:', error);
  }

  return files;
}

// Initialize cache data with actual files
function initializeCacheData() {
  console.log('Scanning FiveM release directory...');
  const files = buildFileList();
  mockCacheData.fivereborn.production.files = files;
  console.log(`Found ${files.length} files to serve`);
}

// API Routes

// Get cache heads (version info)
app.get('/updates/heads/:cacheName/:channel', (req, res) => {
  const { cacheName, channel } = req.params;

  console.log(`Request for cache heads: ${cacheName}/${channel}`);

  if (mockCacheData[cacheName] && mockCacheData[cacheName][channel]) {
    const cacheInfo = mockCacheData[cacheName][channel];

    // Generate a mock manifest hash
    const manifestHash = crypto.createHash('sha256').update(JSON.stringify(cacheInfo.files)).digest('hex');

    // Return version info with headers that client expects
    res.set({
      'x-amz-meta-branch-version': cacheInfo.version,
      'x-amz-meta-branch-manifest': manifestHash,
      'x-amz-meta-bootstrap-version': cacheInfo.version,
      'x-amz-meta-bootstrap-size': '1000000',
      'x-amz-meta-bootstrap-object': 'mock-bootstrap-object'
    });

    res.send(cacheInfo.version);
  } else {
    res.status(404).send('Cache not found');
  }
});

// Get cache content (file list)
app.get('/updates/caches/:cacheName/:channel', (req, res) => {
  const { cacheName, channel } = req.params;
  
  console.log(`Request for cache content: ${cacheName}/${channel}`);
  
  if (mockCacheData[cacheName] && mockCacheData[cacheName][channel]) {
    const cacheInfo = mockCacheData[cacheName][channel];
    
    // Build XML response similar to CFX format
    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<Cache>\n';
    
    cacheInfo.files.forEach(file => {
      xmlContent += `  <File name="${file.name}" hash256="${file.hash256}" localSize="${file.localSize}" downloadSize="${file.downloadSize}" algorithm="${file.algorithm}" />\n`;
    });
    
    xmlContent += '</Cache>';
    
    res.set('Content-Type', 'application/xml');
    res.send(xmlContent);
  } else {
    res.status(404).send('Cache not found');
  }
});

// Serve actual files and manifests
app.get('/updates/:hash1/:hash2/:fullHash', (req, res) => {
  const { fullHash } = req.params;
  const originalHash = fullHash.replace(/\.(xz|gz)$/, ''); // Remove compression suffix

  console.log(`Request for object with hash: ${originalHash}`);

  // Check if this is a manifest request (hash matches our generated manifest hash)
  const manifestHash = crypto.createHash('sha256').update(JSON.stringify(mockCacheData.fivereborn.production.files)).digest('hex');

  if (originalHash === manifestHash) {
    console.log('Serving manifest file');

    // Build XML manifest similar to CFX format
    let xmlContent = '<?xml version="1.0" encoding="UTF-8"?>\n<Cache>\n';

    mockCacheData.fivereborn.production.files.forEach(file => {
      // Convert hash to binary format that client expects
      const hashBytes = Buffer.from(file.hash256, 'hex');
      const hashBase64 = hashBytes.toString('base64');

      xmlContent += `  <File name="${file.name}" hash256="${hashBase64}" localSize="${file.localSize}" downloadSize="${file.downloadSize}" algorithm="${file.algorithm}" />\n`;
    });

    xmlContent += '</Cache>';

    res.set('Content-Type', 'application/xml');
    res.send(xmlContent);
    return;
  }

  // Find file by hash
  const allFiles = mockCacheData.fivereborn.production.files;
  const file = allFiles.find(f => f.hash256 === originalHash);

  if (file) {
    const filePath = path.join(FIVEM_RELEASE_PATH, file.name);

    if (fs.existsSync(filePath)) {
      console.log(`Serving file: ${file.name}`);
      res.sendFile(filePath);
    } else {
      console.error(`File not found: ${filePath}`);
      res.status(404).send('File not found on disk');
    }
  } else {
    console.error(`Hash not found: ${originalHash}`);
    res.status(404).send('Hash not found');
  }
});

// Health check endpoint
app.get('/health', (_, res) => {
  res.json({
    status: 'ok',
    message: 'GangHaiCity Content Server is running',
    filesCount: mockCacheData.fivereborn.production.files.length
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`GangHaiCity Content Server running on http://localhost:${PORT}`);
  console.log('Initializing cache data...');
  initializeCacheData();
  console.log('Server ready to serve content!');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down GangHaiCity Content Server...');
  process.exit(0);
});
