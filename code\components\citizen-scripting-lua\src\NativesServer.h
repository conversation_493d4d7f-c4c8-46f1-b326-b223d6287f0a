int Lua_Native_0xc6f43d0e(lua_State* L) // AddBlipForCoord
{
	static LuaNativeWrapper nW(0x00000000c6f43d0e);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<float>(L, 1)); // x
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // y
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // z
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c6f43d0e)
	nCtx.Invoke(L, 0x00000000c6f43d0e);
	LUA_EXC_WRAP_END(0x00000000c6f43d0e)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x30822554(lua_State* L) // AddBlipForEntity
{
	static LuaNativeWrapper nW(0x0000000030822554);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000030822554)
	nCtx.Invoke(L, 0x0000000030822554);
	LUA_EXC_WRAP_END(0x0000000030822554)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x4626756c(lua_State* L) // AddBlipForRadius
{
	static LuaNativeWrapper nW(0x000000004626756c);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<float>(L, 1)); // posX
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // posY
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // posZ
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // radius
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004626756c)
	nCtx.Invoke(L, 0x000000004626756c);
	LUA_EXC_WRAP_END(0x000000004626756c)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x70559ac7(lua_State* L) // AddPedDecorationFromHashes
{
	static LuaNativeWrapper nW(0x0000000070559ac7);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // collection
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<uint32_t>(L, 3)); // overlay
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000070559ac7)
	nCtx.Invoke(L, 0x0000000070559ac7);
	LUA_EXC_WRAP_END(0x0000000070559ac7)
	return 0;
}

int Lua_Native_0xc1c0855a(lua_State* L) // ApplyForceToEntity
{
	static LuaNativeWrapper nW(0x00000000c1c0855a);
	LuaNativeContext nCtx(&nW, 14);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // forceType
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // x
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // y
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // z
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // offX
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<float>(L, 7)); // offY
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // offZ
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<lua_Integer>(L, 9)); // nComponent
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<bool>(L, 10)); // bLocalForce
	nCtx.SetArgument(10, LuaArgumentParser::ParseArgument<bool>(L, 11)); // bLocalOffset
	nCtx.SetArgument(11, LuaArgumentParser::ParseArgument<bool>(L, 12)); // bScaleByMass
	nCtx.SetArgument(12, LuaArgumentParser::ParseArgument<bool>(L, 13)); // bPlayAudio
	nCtx.SetArgument(13, LuaArgumentParser::ParseArgument<bool>(L, 14)); // bScaleByTimeWarp
	nCtx.SetArgument(14, uintptr_t(0));
	nCtx.SetArgument(15, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c1c0855a)
	nCtx.Invoke(L, 0x00000000c1c0855a);
	LUA_EXC_WRAP_END(0x00000000c1c0855a)
	return 0;
}

int Lua_Native_0xfa29d35d(lua_State* L) // CancelEvent
{
	static LuaNativeWrapper nW(0x00000000fa29d35d);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fa29d35d)
	nCtx.Invoke(L, 0x00000000fa29d35d);
	LUA_EXC_WRAP_END(0x00000000fa29d35d)
	return 0;
}

int Lua_Native_0x429461c3(lua_State* L) // CanPlayerStartCommerceSession
{
	static LuaNativeWrapper nW(0x00000000429461c3);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000429461c3)
	nCtx.Invoke(L, 0x00000000429461c3);
	LUA_EXC_WRAP_END(0x00000000429461c3)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x2d23d743(lua_State* L) // ClearPedProp
{
	static LuaNativeWrapper nW(0x000000002d23d743);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // propId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002d23d743)
	nCtx.Invoke(L, 0x000000002d23d743);
	LUA_EXC_WRAP_END(0x000000002d23d743)
	return 0;
}

int Lua_Native_0xa635f451(lua_State* L) // ClearPedSecondaryTask
{
	static LuaNativeWrapper nW(0x00000000a635f451);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a635f451)
	nCtx.Invoke(L, 0x00000000a635f451);
	LUA_EXC_WRAP_END(0x00000000a635f451)
	return 0;
}

int Lua_Native_0xde3316ab(lua_State* L) // ClearPedTasks
{
	static LuaNativeWrapper nW(0x00000000de3316ab);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000de3316ab)
	nCtx.Invoke(L, 0x00000000de3316ab);
	LUA_EXC_WRAP_END(0x00000000de3316ab)
	return 0;
}

int Lua_Native_0xbc045625(lua_State* L) // ClearPedTasksImmediately
{
	static LuaNativeWrapper nW(0x00000000bc045625);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000bc045625)
	nCtx.Invoke(L, 0x00000000bc045625);
	LUA_EXC_WRAP_END(0x00000000bc045625)
	return 0;
}

int Lua_Native_0x54ea5bcc(lua_State* L) // ClearPlayerWantedLevel
{
	static LuaNativeWrapper nW(0x0000000054ea5bcc);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // player
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000054ea5bcc)
	nCtx.Invoke(L, 0x0000000054ea5bcc);
	LUA_EXC_WRAP_END(0x0000000054ea5bcc)
	return 0;
}

int Lua_Native_0x2f7aa05c(lua_State* L) // CreateObject
{
	static LuaNativeWrapper nW(0x000000002f7aa05c);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<uint32_t>(L, 1)); // modelHash
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // isNetwork
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // netMissionEntity
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // doorFlag
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002f7aa05c)
	nCtx.Invoke(L, 0x000000002f7aa05c);
	LUA_EXC_WRAP_END(0x000000002f7aa05c)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x58040420(lua_State* L) // CreateObjectNoOffset
{
	static LuaNativeWrapper nW(0x0000000058040420);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<uint32_t>(L, 1)); // modelHash
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // isNetwork
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // netMissionEntity
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // doorFlag
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000058040420)
	nCtx.Invoke(L, 0x0000000058040420);
	LUA_EXC_WRAP_END(0x0000000058040420)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x389ef71(lua_State* L) // CreatePed
{
	static LuaNativeWrapper nW(0x000000000389ef71);
	LuaNativeContext nCtx(&nW, 8);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // pedType
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // modelHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // x
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // y
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // z
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // heading
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // isNetwork
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<bool>(L, 8)); // bScriptHostPed
	nCtx.SetArgument(8, uintptr_t(0));
	nCtx.SetArgument(9, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000389ef71)
	nCtx.Invoke(L, 0x000000000389ef71);
	LUA_EXC_WRAP_END(0x000000000389ef71)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x3000f092(lua_State* L) // CreatePedInsideVehicle
{
	static LuaNativeWrapper nW(0x000000003000f092);
	LuaNativeContext nCtx(&nW, 6);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // pedType
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<uint32_t>(L, 3)); // modelHash
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // seat
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // isNetwork
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // bScriptHostPed
	nCtx.SetArgument(6, uintptr_t(0));
	nCtx.SetArgument(7, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003000f092)
	nCtx.Invoke(L, 0x000000003000f092);
	LUA_EXC_WRAP_END(0x000000003000f092)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xdd75460a(lua_State* L) // CreateVehicle
{
	static LuaNativeWrapper nW(0x00000000dd75460a);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<uint32_t>(L, 1)); // modelHash
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // heading
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // isNetwork
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // netMissionEntity
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dd75460a)
	nCtx.Invoke(L, 0x00000000dd75460a);
	LUA_EXC_WRAP_END(0x00000000dd75460a)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x6ae51d4b(lua_State* L) // CreateVehicleServerSetter
{
	static LuaNativeWrapper nW(0x000000006ae51d4b);
	LuaNativeContext nCtx(&nW, 6);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<uint32_t>(L, 1)); // modelHash
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // type
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // x
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // y
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // z
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // heading
	nCtx.SetArgument(6, uintptr_t(0));
	nCtx.SetArgument(7, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006ae51d4b)
	nCtx.Invoke(L, 0x000000006ae51d4b);
	LUA_EXC_WRAP_END(0x000000006ae51d4b)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xfaa3d236(lua_State* L) // DeleteEntity
{
	static LuaNativeWrapper nW(0x00000000faa3d236);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000faa3d236)
	nCtx.Invoke(L, 0x00000000faa3d236);
	LUA_EXC_WRAP_END(0x00000000faa3d236)
	return 0;
}

int Lua_Native_0x1e86f206(lua_State* L) // DeleteFunctionReference
{
	static LuaNativeWrapper nW(0x000000001e86f206);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // referenceIdentity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001e86f206)
	nCtx.Invoke(L, 0x000000001e86f206);
	LUA_EXC_WRAP_END(0x000000001e86f206)
	return 0;
}

int Lua_Native_0x7389b5df(lua_State* L) // DeleteResourceKvp
{
	static LuaNativeWrapper nW(0x000000007389b5df);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007389b5df)
	nCtx.Invoke(L, 0x000000007389b5df);
	LUA_EXC_WRAP_END(0x000000007389b5df)
	return 0;
}

int Lua_Native_0x4152c90(lua_State* L) // DeleteResourceKvpNoSync
{
	static LuaNativeWrapper nW(0x0000000004152c90);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000004152c90)
	nCtx.Invoke(L, 0x0000000004152c90);
	LUA_EXC_WRAP_END(0x0000000004152c90)
	return 0;
}

int Lua_Native_0x523ba3da(lua_State* L) // DeleteTrain
{
	static LuaNativeWrapper nW(0x00000000523ba3da);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000523ba3da)
	nCtx.Invoke(L, 0x00000000523ba3da);
	LUA_EXC_WRAP_END(0x00000000523ba3da)
	return 0;
}

int Lua_Native_0x43f15989(lua_State* L) // DoesBoatSinkWhenWrecked
{
	static LuaNativeWrapper nW(0x0000000043f15989);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000043f15989)
	nCtx.Invoke(L, 0x0000000043f15989);
	LUA_EXC_WRAP_END(0x0000000043f15989)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x3ac90869(lua_State* L) // DoesEntityExist
{
	static LuaNativeWrapper nW(0x000000003ac90869);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003ac90869)
	nCtx.Invoke(L, 0x000000003ac90869);
	LUA_EXC_WRAP_END(0x000000003ac90869)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x12038599(lua_State* L) // DoesPlayerExist
{
	static LuaNativeWrapper nW(0x0000000012038599);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000012038599)
	nCtx.Invoke(L, 0x0000000012038599);
	LUA_EXC_WRAP_END(0x0000000012038599)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x167aba27(lua_State* L) // DoesPlayerOwnSku
{
	static LuaNativeWrapper nW(0x00000000167aba27);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // skuId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000167aba27)
	nCtx.Invoke(L, 0x00000000167aba27);
	LUA_EXC_WRAP_END(0x00000000167aba27)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xdef0480b(lua_State* L) // DoesPlayerOwnSkuExt
{
	static LuaNativeWrapper nW(0x00000000def0480b);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // skuId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000def0480b)
	nCtx.Invoke(L, 0x00000000def0480b);
	LUA_EXC_WRAP_END(0x00000000def0480b)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x77cc80dc(lua_State* L) // DoesTrainStopAtStations
{
	static LuaNativeWrapper nW(0x0000000077cc80dc);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000077cc80dc)
	nCtx.Invoke(L, 0x0000000077cc80dc);
	LUA_EXC_WRAP_END(0x0000000077cc80dc)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xba0613e1(lua_State* L) // DropPlayer
{
	static LuaNativeWrapper nW(0x00000000ba0613e1);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // reason
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ba0613e1)
	nCtx.Invoke(L, 0x00000000ba0613e1);
	LUA_EXC_WRAP_END(0x00000000ba0613e1)
	return 0;
}

int Lua_Native_0xf4e2079d(lua_State* L) // DuplicateFunctionReference
{
	static LuaNativeWrapper nW(0x00000000f4e2079d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // referenceIdentity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f4e2079d)
	nCtx.Invoke(L, 0x00000000f4e2079d);
	LUA_EXC_WRAP_END(0x00000000f4e2079d)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xf97b1c93(lua_State* L) // EnableEnhancedHostSupport
{
	static LuaNativeWrapper nW(0x00000000f97b1c93);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<bool>(L, 1)); // enabled
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f97b1c93)
	nCtx.Invoke(L, 0x00000000f97b1c93);
	LUA_EXC_WRAP_END(0x00000000f97b1c93)
	return 0;
}

int Lua_Native_0xb3210203(lua_State* L) // EndFindKvp
{
	static LuaNativeWrapper nW(0x00000000b3210203);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // handle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b3210203)
	nCtx.Invoke(L, 0x00000000b3210203);
	LUA_EXC_WRAP_END(0x00000000b3210203)
	return 0;
}

int Lua_Native_0x3bb78f05(lua_State* L) // EnsureEntityStateBag
{
	static LuaNativeWrapper nW(0x000000003bb78f05);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003bb78f05)
	nCtx.Invoke(L, 0x000000003bb78f05);
	LUA_EXC_WRAP_END(0x000000003bb78f05)
	return 0;
}

int Lua_Native_0x561c060b(lua_State* L) // ExecuteCommand
{
	static LuaNativeWrapper nW(0x00000000561c060b);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // commandString
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000561c060b)
	nCtx.Invoke(L, 0x00000000561c060b);
	LUA_EXC_WRAP_END(0x00000000561c060b)
	return 0;
}

int Lua_Native_0xbd7bebc5(lua_State* L) // FindKvp
{
	static LuaNativeWrapper nW(0x00000000bd7bebc5);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // handle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000bd7bebc5)
	nCtx.Invoke(L, 0x00000000bd7bebc5);
	LUA_EXC_WRAP_END(0x00000000bd7bebc5)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x13b6855d(lua_State* L) // FlagServerAsPrivate
{
	static LuaNativeWrapper nW(0x0000000013b6855d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<bool>(L, 1)); // private_
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000013b6855d)
	nCtx.Invoke(L, 0x0000000013b6855d);
	LUA_EXC_WRAP_END(0x0000000013b6855d)
	return 0;
}

int Lua_Native_0xe27c97a0(lua_State* L) // FlushResourceKvp
{
	static LuaNativeWrapper nW(0x00000000e27c97a0);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e27c97a0)
	nCtx.Invoke(L, 0x00000000e27c97a0);
	LUA_EXC_WRAP_END(0x00000000e27c97a0)
	return 0;
}

int Lua_Native_0x65c16d57(lua_State* L) // FreezeEntityPosition
{
	static LuaNativeWrapper nW(0x0000000065c16d57);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // toggle
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000065c16d57)
	nCtx.Invoke(L, 0x0000000065c16d57);
	LUA_EXC_WRAP_END(0x0000000065c16d57)
	return 0;
}

int Lua_Native_0x62fc38d0(lua_State* L) // GetAirDragMultiplierForPlayersVehicle
{
	static LuaNativeWrapper nW(0x0000000062fc38d0);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000062fc38d0)
	nCtx.Invoke(L, 0x0000000062fc38d0);
	LUA_EXC_WRAP_END(0x0000000062fc38d0)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x6886c3fe(lua_State* L) // GetAllObjects
{
	static LuaNativeWrapper nW(0x000000006886c3fe);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006886c3fe)
	nCtx.Invoke(L, 0x000000006886c3fe);
	LUA_EXC_WRAP_END(0x000000006886c3fe)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0xb8584fef(lua_State* L) // GetAllPeds
{
	static LuaNativeWrapper nW(0x00000000b8584fef);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b8584fef)
	nCtx.Invoke(L, 0x00000000b8584fef);
	LUA_EXC_WRAP_END(0x00000000b8584fef)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x332169f5(lua_State* L) // GetAllVehicles
{
	static LuaNativeWrapper nW(0x00000000332169f5);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000332169f5)
	nCtx.Invoke(L, 0x00000000332169f5);
	LUA_EXC_WRAP_END(0x00000000332169f5)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x72ff2e73(lua_State* L) // GetBlipSprite
{
	static LuaNativeWrapper nW(0x0000000072ff2e73);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // self
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000072ff2e73)
	nCtx.Invoke(L, 0x0000000072ff2e73);
	LUA_EXC_WRAP_END(0x0000000072ff2e73)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe57429fa(lua_State* L) // GetConsoleBuffer
{
	static LuaNativeWrapper nW(0x00000000e57429fa);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e57429fa)
	nCtx.Invoke(L, 0x00000000e57429fa);
	LUA_EXC_WRAP_END(0x00000000e57429fa)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x6ccd2564(lua_State* L) // GetConvar
{
	static LuaNativeWrapper nW(0x000000006ccd2564);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // default_
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006ccd2564)
	nCtx.Invoke(L, 0x000000006ccd2564);
	LUA_EXC_WRAP_END(0x000000006ccd2564)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x7e8ebfe5(lua_State* L) // GetConvarBool
{
	static LuaNativeWrapper nW(0x000000007e8ebfe5);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // defaultValue
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007e8ebfe5)
	nCtx.Invoke(L, 0x000000007e8ebfe5);
	LUA_EXC_WRAP_END(0x000000007e8ebfe5)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x9e666d(lua_State* L) // GetConvarFloat
{
	static LuaNativeWrapper nW(0x00000000009e666d);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // defaultValue
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000009e666d)
	nCtx.Invoke(L, 0x00000000009e666d);
	LUA_EXC_WRAP_END(0x00000000009e666d)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x935c0ab2(lua_State* L) // GetConvarInt
{
	static LuaNativeWrapper nW(0x00000000935c0ab2);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // default_
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000935c0ab2)
	nCtx.Invoke(L, 0x00000000935c0ab2);
	LUA_EXC_WRAP_END(0x00000000935c0ab2)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xb0237302(lua_State* L) // GetCurrentPedWeapon
{
	static LuaNativeWrapper nW(0x00000000b0237302);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b0237302)
	nCtx.Invoke(L, 0x00000000b0237302);
	LUA_EXC_WRAP_END(0x00000000b0237302)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe5e9ebbb(lua_State* L) // GetCurrentResourceName
{
	static LuaNativeWrapper nW(0x00000000e5e9ebbb);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e5e9ebbb)
	nCtx.Invoke(L, 0x00000000e5e9ebbb);
	LUA_EXC_WRAP_END(0x00000000e5e9ebbb)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xfe1589f9(lua_State* L) // GetEntityAttachedTo
{
	static LuaNativeWrapper nW(0x00000000fe1589f9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fe1589f9)
	nCtx.Invoke(L, 0x00000000fe1589f9);
	LUA_EXC_WRAP_END(0x00000000fe1589f9)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe8c0c629(lua_State* L) // GetEntityCollisionDisabled
{
	static LuaNativeWrapper nW(0x00000000e8c0c629);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e8c0c629)
	nCtx.Invoke(L, 0x00000000e8c0c629);
	LUA_EXC_WRAP_END(0x00000000e8c0c629)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x1647f1cb(lua_State* L) // GetEntityCoords
{
	static LuaNativeWrapper nW(0x000000001647f1cb);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001647f1cb)
	nCtx.Invoke(L, 0x000000001647f1cb);
	LUA_EXC_WRAP_END(0x000000001647f1cb)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0x4bdf1867(lua_State* L) // GetEntityFromStateBagName
{
	static LuaNativeWrapper nW(0x000000004bdf1867);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004bdf1867)
	nCtx.Invoke(L, 0x000000004bdf1867);
	LUA_EXC_WRAP_END(0x000000004bdf1867)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x972cc383(lua_State* L) // GetEntityHeading
{
	static LuaNativeWrapper nW(0x00000000972cc383);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000972cc383)
	nCtx.Invoke(L, 0x00000000972cc383);
	LUA_EXC_WRAP_END(0x00000000972cc383)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x8e3222b7(lua_State* L) // GetEntityHealth
{
	static LuaNativeWrapper nW(0x000000008e3222b7);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008e3222b7)
	nCtx.Invoke(L, 0x000000008e3222b7);
	LUA_EXC_WRAP_END(0x000000008e3222b7)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xc7ae6aa1(lua_State* L) // GetEntityMaxHealth
{
	static LuaNativeWrapper nW(0x00000000c7ae6aa1);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c7ae6aa1)
	nCtx.Invoke(L, 0x00000000c7ae6aa1);
	LUA_EXC_WRAP_END(0x00000000c7ae6aa1)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xdafcb3ec(lua_State* L) // GetEntityModel
{
	static LuaNativeWrapper nW(0x00000000dafcb3ec);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dafcb3ec)
	nCtx.Invoke(L, 0x00000000dafcb3ec);
	LUA_EXC_WRAP_END(0x00000000dafcb3ec)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xd16ea02f(lua_State* L) // GetEntityOrphanMode
{
	static LuaNativeWrapper nW(0x00000000d16ea02f);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d16ea02f)
	nCtx.Invoke(L, 0x00000000d16ea02f);
	LUA_EXC_WRAP_END(0x00000000d16ea02f)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xfc30ddff(lua_State* L) // GetEntityPopulationType
{
	static LuaNativeWrapper nW(0x00000000fc30ddff);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fc30ddff)
	nCtx.Invoke(L, 0x00000000fc30ddff);
	LUA_EXC_WRAP_END(0x00000000fc30ddff)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x91b38fb6(lua_State* L) // GetEntityRemoteSyncedScenesAllowed
{
	static LuaNativeWrapper nW(0x0000000091b38fb6);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000091b38fb6)
	nCtx.Invoke(L, 0x0000000091b38fb6);
	LUA_EXC_WRAP_END(0x0000000091b38fb6)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x8ff45b04(lua_State* L) // GetEntityRotation
{
	static LuaNativeWrapper nW(0x000000008ff45b04);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008ff45b04)
	nCtx.Invoke(L, 0x000000008ff45b04);
	LUA_EXC_WRAP_END(0x000000008ff45b04)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0x9bf8a73f(lua_State* L) // GetEntityRotationVelocity
{
	static LuaNativeWrapper nW(0x000000009bf8a73f);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009bf8a73f)
	nCtx.Invoke(L, 0x000000009bf8a73f);
	LUA_EXC_WRAP_END(0x000000009bf8a73f)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0xed4b0486(lua_State* L) // GetEntityRoutingBucket
{
	static LuaNativeWrapper nW(0x00000000ed4b0486);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ed4b0486)
	nCtx.Invoke(L, 0x00000000ed4b0486);
	LUA_EXC_WRAP_END(0x00000000ed4b0486)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xb7f70784(lua_State* L) // GetEntityScript
{
	static LuaNativeWrapper nW(0x00000000b7f70784);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b7f70784)
	nCtx.Invoke(L, 0x00000000b7f70784);
	LUA_EXC_WRAP_END(0x00000000b7f70784)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x9e1e4798(lua_State* L) // GetEntitySpeed
{
	static LuaNativeWrapper nW(0x000000009e1e4798);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009e1e4798)
	nCtx.Invoke(L, 0x000000009e1e4798);
	LUA_EXC_WRAP_END(0x000000009e1e4798)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xb1bd08d(lua_State* L) // GetEntityType
{
	static LuaNativeWrapper nW(0x000000000b1bd08d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000b1bd08d)
	nCtx.Invoke(L, 0x000000000b1bd08d);
	LUA_EXC_WRAP_END(0x000000000b1bd08d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xc14c9b6b(lua_State* L) // GetEntityVelocity
{
	static LuaNativeWrapper nW(0x00000000c14c9b6b);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c14c9b6b)
	nCtx.Invoke(L, 0x00000000c14c9b6b);
	LUA_EXC_WRAP_END(0x00000000c14c9b6b)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0x804b9f7b(lua_State* L) // GetGameBuildNumber
{
	static LuaNativeWrapper nW(0x00000000804b9f7b);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000804b9f7b)
	nCtx.Invoke(L, 0x00000000804b9f7b);
	LUA_EXC_WRAP_END(0x00000000804b9f7b)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe8eaa18b(lua_State* L) // GetGameName
{
	static LuaNativeWrapper nW(0x00000000e8eaa18b);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e8eaa18b)
	nCtx.Invoke(L, 0x00000000e8eaa18b);
	LUA_EXC_WRAP_END(0x00000000e8eaa18b)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x2b9d4f50(lua_State* L) // GetGamePool
{
	static LuaNativeWrapper nW(0x000000002b9d4f50);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // poolName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002b9d4f50)
	nCtx.Invoke(L, 0x000000002b9d4f50);
	LUA_EXC_WRAP_END(0x000000002b9d4f50)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0xa4ea0691(lua_State* L) // GetGameTimer
{
	static LuaNativeWrapper nW(0x00000000a4ea0691);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a4ea0691)
	nCtx.Invoke(L, 0x00000000a4ea0691);
	LUA_EXC_WRAP_END(0x00000000a4ea0691)
	int64_t retval = nCtx.GetResult<int64_t>();
	LuaArgumentParser::PushObject<int64_t>(L, retval);
	return 1;
}

int Lua_Native_0x98eff6f1(lua_State* L) // GetHashKey
{
	static LuaNativeWrapper nW(0x0000000098eff6f1);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // model
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000098eff6f1)
	nCtx.Invoke(L, 0x0000000098eff6f1);
	LUA_EXC_WRAP_END(0x0000000098eff6f1)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xa886495d(lua_State* L) // GetHeliBodyHealth
{
	static LuaNativeWrapper nW(0x00000000a886495d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a886495d)
	nCtx.Invoke(L, 0x00000000a886495d);
	LUA_EXC_WRAP_END(0x00000000a886495d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x82afc0a3(lua_State* L) // GetHeliDisableExplodeFromBodyDamage
{
	static LuaNativeWrapper nW(0x0000000082afc0a3);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000082afc0a3)
	nCtx.Invoke(L, 0x0000000082afc0a3);
	LUA_EXC_WRAP_END(0x0000000082afc0a3)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xa0fa0354(lua_State* L) // GetHeliEngineHealth
{
	static LuaNativeWrapper nW(0x00000000a0fa0354);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a0fa0354)
	nCtx.Invoke(L, 0x00000000a0fa0354);
	LUA_EXC_WRAP_END(0x00000000a0fa0354)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xd4ec7858(lua_State* L) // GetHeliGasTankHealth
{
	static LuaNativeWrapper nW(0x00000000d4ec7858);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d4ec7858)
	nCtx.Invoke(L, 0x00000000d4ec7858);
	LUA_EXC_WRAP_END(0x00000000d4ec7858)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xc37d668(lua_State* L) // GetHeliMainRotorDamageScale
{
	static LuaNativeWrapper nW(0x000000000c37d668);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000c37d668)
	nCtx.Invoke(L, 0x000000000c37d668);
	LUA_EXC_WRAP_END(0x000000000c37d668)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xf01e2aab(lua_State* L) // GetHeliMainRotorHealth
{
	static LuaNativeWrapper nW(0x00000000f01e2aab);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f01e2aab)
	nCtx.Invoke(L, 0x00000000f01e2aab);
	LUA_EXC_WRAP_END(0x00000000f01e2aab)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x1944ac95(lua_State* L) // GetHeliPitchControl
{
	static LuaNativeWrapper nW(0x000000001944ac95);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001944ac95)
	nCtx.Invoke(L, 0x000000001944ac95);
	LUA_EXC_WRAP_END(0x000000001944ac95)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xc40161e2(lua_State* L) // GetHeliRearRotorDamageScale
{
	static LuaNativeWrapper nW(0x00000000c40161e2);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c40161e2)
	nCtx.Invoke(L, 0x00000000c40161e2);
	LUA_EXC_WRAP_END(0x00000000c40161e2)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x33ee6e2b(lua_State* L) // GetHeliRearRotorHealth
{
	static LuaNativeWrapper nW(0x0000000033ee6e2b);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000033ee6e2b)
	nCtx.Invoke(L, 0x0000000033ee6e2b);
	LUA_EXC_WRAP_END(0x0000000033ee6e2b)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x12948de9(lua_State* L) // GetHeliRollControl
{
	static LuaNativeWrapper nW(0x0000000012948de9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000012948de9)
	nCtx.Invoke(L, 0x0000000012948de9);
	LUA_EXC_WRAP_END(0x0000000012948de9)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x22239130(lua_State* L) // GetHeliTailRotorDamageScale
{
	static LuaNativeWrapper nW(0x0000000022239130);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000022239130)
	nCtx.Invoke(L, 0x0000000022239130);
	LUA_EXC_WRAP_END(0x0000000022239130)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xa41bc13d(lua_State* L) // GetHeliTailRotorHealth
{
	static LuaNativeWrapper nW(0x00000000a41bc13d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a41bc13d)
	nCtx.Invoke(L, 0x00000000a41bc13d);
	LUA_EXC_WRAP_END(0x00000000a41bc13d)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x8e86238d(lua_State* L) // GetHeliThrottleControl
{
	static LuaNativeWrapper nW(0x000000008e86238d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008e86238d)
	nCtx.Invoke(L, 0x000000008e86238d);
	LUA_EXC_WRAP_END(0x000000008e86238d)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x8fdc0768(lua_State* L) // GetHeliYawControl
{
	static LuaNativeWrapper nW(0x000000008fdc0768);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008fdc0768)
	nCtx.Invoke(L, 0x000000008fdc0768);
	LUA_EXC_WRAP_END(0x000000008fdc0768)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x5f70f5a3(lua_State* L) // GetHostId
{
	static LuaNativeWrapper nW(0x000000005f70f5a3);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000005f70f5a3)
	nCtx.Invoke(L, 0x000000005f70f5a3);
	LUA_EXC_WRAP_END(0x000000005f70f5a3)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x9f1c4383(lua_State* L) // GetInstanceId
{
	static LuaNativeWrapper nW(0x000000009f1c4383);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009f1c4383)
	nCtx.Invoke(L, 0x000000009f1c4383);
	LUA_EXC_WRAP_END(0x000000009f1c4383)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x4d52fe5b(lua_State* L) // GetInvokingResource
{
	static LuaNativeWrapper nW(0x000000004d52fe5b);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004d52fe5b)
	nCtx.Invoke(L, 0x000000004d52fe5b);
	LUA_EXC_WRAP_END(0x000000004d52fe5b)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x3efe38d1(lua_State* L) // GetIsHeliEngineRunning
{
	static LuaNativeWrapper nW(0x000000003efe38d1);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003efe38d1)
	nCtx.Invoke(L, 0x000000003efe38d1);
	LUA_EXC_WRAP_END(0x000000003efe38d1)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x7dc6d022(lua_State* L) // GetIsVehicleEngineRunning
{
	static LuaNativeWrapper nW(0x000000007dc6d022);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007dc6d022)
	nCtx.Invoke(L, 0x000000007dc6d022);
	LUA_EXC_WRAP_END(0x000000007dc6d022)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xd7ec8760(lua_State* L) // GetIsVehiclePrimaryColourCustom
{
	static LuaNativeWrapper nW(0x00000000d7ec8760);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d7ec8760)
	nCtx.Invoke(L, 0x00000000d7ec8760);
	LUA_EXC_WRAP_END(0x00000000d7ec8760)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x288ad228(lua_State* L) // GetIsVehicleSecondaryColourCustom
{
	static LuaNativeWrapper nW(0x00000000288ad228);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000288ad228)
	nCtx.Invoke(L, 0x00000000288ad228);
	LUA_EXC_WRAP_END(0x00000000288ad228)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xa6f02670(lua_State* L) // GetLandingGearState
{
	static LuaNativeWrapper nW(0x00000000a6f02670);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a6f02670)
	nCtx.Invoke(L, 0x00000000a6f02670);
	LUA_EXC_WRAP_END(0x00000000a6f02670)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xf7c6792d(lua_State* L) // GetLastPedInVehicleSeat
{
	static LuaNativeWrapper nW(0x00000000f7c6792d);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // seatIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f7c6792d)
	nCtx.Invoke(L, 0x00000000f7c6792d);
	LUA_EXC_WRAP_END(0x00000000f7c6792d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x23b2a641(lua_State* L) // GetNetTypeFromEntity
{
	static LuaNativeWrapper nW(0x0000000023b2a641);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000023b2a641)
	nCtx.Invoke(L, 0x0000000023b2a641);
	LUA_EXC_WRAP_END(0x0000000023b2a641)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xff7f66ab(lua_State* L) // GetNumPlayerIdentifiers
{
	static LuaNativeWrapper nW(0x00000000ff7f66ab);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ff7f66ab)
	nCtx.Invoke(L, 0x00000000ff7f66ab);
	LUA_EXC_WRAP_END(0x00000000ff7f66ab)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x63d13184(lua_State* L) // GetNumPlayerIndices
{
	static LuaNativeWrapper nW(0x0000000063d13184);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000063d13184)
	nCtx.Invoke(L, 0x0000000063d13184);
	LUA_EXC_WRAP_END(0x0000000063d13184)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x619e4a3d(lua_State* L) // GetNumPlayerTokens
{
	static LuaNativeWrapper nW(0x00000000619e4a3d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000619e4a3d)
	nCtx.Invoke(L, 0x00000000619e4a3d);
	LUA_EXC_WRAP_END(0x00000000619e4a3d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x863f27b(lua_State* L) // GetNumResources
{
	static LuaNativeWrapper nW(0x000000000863f27b);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000863f27b)
	nCtx.Invoke(L, 0x000000000863f27b);
	LUA_EXC_WRAP_END(0x000000000863f27b)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x776e864(lua_State* L) // GetNumResourceMetadata
{
	static LuaNativeWrapper nW(0x000000000776e864);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // metadataKey
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000776e864)
	nCtx.Invoke(L, 0x000000000776e864);
	LUA_EXC_WRAP_END(0x000000000776e864)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x23473ea4(lua_State* L) // GetPasswordHash
{
	static LuaNativeWrapper nW(0x0000000023473ea4);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // password
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000023473ea4)
	nCtx.Invoke(L, 0x0000000023473ea4);
	LUA_EXC_WRAP_END(0x0000000023473ea4)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x2ce311a7(lua_State* L) // GetPedArmour
{
	static LuaNativeWrapper nW(0x000000002ce311a7);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002ce311a7)
	nCtx.Invoke(L, 0x000000002ce311a7);
	LUA_EXC_WRAP_END(0x000000002ce311a7)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x63458c27(lua_State* L) // GetPedCauseOfDeath
{
	static LuaNativeWrapper nW(0x0000000063458c27);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000063458c27)
	nCtx.Invoke(L, 0x0000000063458c27);
	LUA_EXC_WRAP_END(0x0000000063458c27)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xc182f76e(lua_State* L) // GetPedDesiredHeading
{
	static LuaNativeWrapper nW(0x00000000c182f76e);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c182f76e)
	nCtx.Invoke(L, 0x00000000c182f76e);
	LUA_EXC_WRAP_END(0x00000000c182f76e)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x388fde9a(lua_State* L) // GetPedInVehicleSeat
{
	static LuaNativeWrapper nW(0x00000000388fde9a);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // seatIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000388fde9a)
	nCtx.Invoke(L, 0x00000000388fde9a);
	LUA_EXC_WRAP_END(0x00000000388fde9a)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xa45b6c8d(lua_State* L) // GetPedMaxHealth
{
	static LuaNativeWrapper nW(0x00000000a45b6c8d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a45b6c8d)
	nCtx.Invoke(L, 0x00000000a45b6c8d);
	LUA_EXC_WRAP_END(0x00000000a45b6c8d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x354f283c(lua_State* L) // GetPedRelationshipGroupHash
{
	static LuaNativeWrapper nW(0x00000000354f283c);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000354f283c)
	nCtx.Invoke(L, 0x00000000354f283c);
	LUA_EXC_WRAP_END(0x00000000354f283c)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x84fe084(lua_State* L) // GetPedScriptTaskCommand
{
	static LuaNativeWrapper nW(0x00000000084fe084);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000084fe084)
	nCtx.Invoke(L, 0x00000000084fe084);
	LUA_EXC_WRAP_END(0x00000000084fe084)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x44b0e5e2(lua_State* L) // GetPedScriptTaskStage
{
	static LuaNativeWrapper nW(0x0000000044b0e5e2);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000044b0e5e2)
	nCtx.Invoke(L, 0x0000000044b0e5e2);
	LUA_EXC_WRAP_END(0x0000000044b0e5e2)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x535db43f(lua_State* L) // GetPedSourceOfDamage
{
	static LuaNativeWrapper nW(0x00000000535db43f);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000535db43f)
	nCtx.Invoke(L, 0x00000000535db43f);
	LUA_EXC_WRAP_END(0x00000000535db43f)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x84adf9eb(lua_State* L) // GetPedSourceOfDeath
{
	static LuaNativeWrapper nW(0x0000000084adf9eb);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000084adf9eb)
	nCtx.Invoke(L, 0x0000000084adf9eb);
	LUA_EXC_WRAP_END(0x0000000084adf9eb)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x7f4563d3(lua_State* L) // GetPedSpecificTaskType
{
	static LuaNativeWrapper nW(0x000000007f4563d3);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // index
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007f4563d3)
	nCtx.Invoke(L, 0x000000007f4563d3);
	LUA_EXC_WRAP_END(0x000000007f4563d3)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x40321b83(lua_State* L) // GetPedStealthMovement
{
	static LuaNativeWrapper nW(0x0000000040321b83);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000040321b83)
	nCtx.Invoke(L, 0x0000000040321b83);
	LUA_EXC_WRAP_END(0x0000000040321b83)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x433c765d(lua_State* L) // GetPlayerCameraRotation
{
	static LuaNativeWrapper nW(0x00000000433c765d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000433c765d)
	nCtx.Invoke(L, 0x00000000433c765d);
	LUA_EXC_WRAP_END(0x00000000433c765d)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0xfee404f9(lua_State* L) // GetPlayerEndpoint
{
	static LuaNativeWrapper nW(0x00000000fee404f9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fee404f9)
	nCtx.Invoke(L, 0x00000000fee404f9);
	LUA_EXC_WRAP_END(0x00000000fee404f9)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x98d244(lua_State* L) // GetPlayerFakeWantedLevel
{
	static LuaNativeWrapper nW(0x000000000098d244);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000098d244)
	nCtx.Invoke(L, 0x000000000098d244);
	LUA_EXC_WRAP_END(0x000000000098d244)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x586f80ff(lua_State* L) // GetPlayerFocusPos
{
	static LuaNativeWrapper nW(0x00000000586f80ff);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000586f80ff)
	nCtx.Invoke(L, 0x00000000586f80ff);
	LUA_EXC_WRAP_END(0x00000000586f80ff)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0xc8a9ce08(lua_State* L) // GetPlayerFromIndex
{
	static LuaNativeWrapper nW(0x00000000c8a9ce08);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // index
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c8a9ce08)
	nCtx.Invoke(L, 0x00000000c8a9ce08);
	LUA_EXC_WRAP_END(0x00000000c8a9ce08)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xa56135e0(lua_State* L) // GetPlayerFromStateBagName
{
	static LuaNativeWrapper nW(0x00000000a56135e0);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a56135e0)
	nCtx.Invoke(L, 0x00000000a56135e0);
	LUA_EXC_WRAP_END(0x00000000a56135e0)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe52d9680(lua_State* L) // GetPlayerGuid
{
	static LuaNativeWrapper nW(0x00000000e52d9680);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e52d9680)
	nCtx.Invoke(L, 0x00000000e52d9680);
	LUA_EXC_WRAP_END(0x00000000e52d9680)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x7302dbcf(lua_State* L) // GetPlayerIdentifier
{
	static LuaNativeWrapper nW(0x000000007302dbcf);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // identiferIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007302dbcf)
	nCtx.Invoke(L, 0x000000007302dbcf);
	LUA_EXC_WRAP_END(0x000000007302dbcf)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xa61c8fc6(lua_State* L) // GetPlayerIdentifierByType
{
	static LuaNativeWrapper nW(0x00000000a61c8fc6);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // identifierType
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a61c8fc6)
	nCtx.Invoke(L, 0x00000000a61c8fc6);
	LUA_EXC_WRAP_END(0x00000000a61c8fc6)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x680c90ee(lua_State* L) // GetPlayerInvincible
{
	static LuaNativeWrapper nW(0x00000000680c90ee);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000680c90ee)
	nCtx.Invoke(L, 0x00000000680c90ee);
	LUA_EXC_WRAP_END(0x00000000680c90ee)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x427e8e6a(lua_State* L) // GetPlayerLastMsg
{
	static LuaNativeWrapper nW(0x00000000427e8e6a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000427e8e6a)
	nCtx.Invoke(L, 0x00000000427e8e6a);
	LUA_EXC_WRAP_END(0x00000000427e8e6a)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x2a50657(lua_State* L) // GetPlayerMaxArmour
{
	static LuaNativeWrapper nW(0x0000000002a50657);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000002a50657)
	nCtx.Invoke(L, 0x0000000002a50657);
	LUA_EXC_WRAP_END(0x0000000002a50657)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x8154e470(lua_State* L) // GetPlayerMaxHealth
{
	static LuaNativeWrapper nW(0x000000008154e470);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008154e470)
	nCtx.Invoke(L, 0x000000008154e470);
	LUA_EXC_WRAP_END(0x000000008154e470)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x8689a825(lua_State* L) // GetPlayerMeleeWeaponDamageModifier
{
	static LuaNativeWrapper nW(0x000000008689a825);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerId
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008689a825)
	nCtx.Invoke(L, 0x000000008689a825);
	LUA_EXC_WRAP_END(0x000000008689a825)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x406b4b20(lua_State* L) // GetPlayerName
{
	static LuaNativeWrapper nW(0x00000000406b4b20);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000406b4b20)
	nCtx.Invoke(L, 0x00000000406b4b20);
	LUA_EXC_WRAP_END(0x00000000406b4b20)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x6e31e993(lua_State* L) // GetPlayerPed
{
	static LuaNativeWrapper nW(0x000000006e31e993);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006e31e993)
	nCtx.Invoke(L, 0x000000006e31e993);
	LUA_EXC_WRAP_END(0x000000006e31e993)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x9a928294(lua_State* L) // GetPlayerPeerStatistics
{
	static LuaNativeWrapper nW(0x000000009a928294);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // peerStatistic
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009a928294)
	nCtx.Invoke(L, 0x000000009a928294);
	LUA_EXC_WRAP_END(0x000000009a928294)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xff1290d4(lua_State* L) // GetPlayerPing
{
	static LuaNativeWrapper nW(0x00000000ff1290d4);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ff1290d4)
	nCtx.Invoke(L, 0x00000000ff1290d4);
	LUA_EXC_WRAP_END(0x00000000ff1290d4)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x52441c34(lua_State* L) // GetPlayerRoutingBucket
{
	static LuaNativeWrapper nW(0x0000000052441c34);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000052441c34)
	nCtx.Invoke(L, 0x0000000052441c34);
	LUA_EXC_WRAP_END(0x0000000052441c34)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x9873e404(lua_State* L) // GetPlayerTeam
{
	static LuaNativeWrapper nW(0x000000009873e404);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009873e404)
	nCtx.Invoke(L, 0x000000009873e404);
	LUA_EXC_WRAP_END(0x000000009873e404)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x7ade63e1(lua_State* L) // GetPlayerTimeInPursuit
{
	static LuaNativeWrapper nW(0x000000007ade63e1);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // lastPursuit
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007ade63e1)
	nCtx.Invoke(L, 0x000000007ade63e1);
	LUA_EXC_WRAP_END(0x000000007ade63e1)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x67d2e605(lua_State* L) // GetPlayerTimeOnline
{
	static LuaNativeWrapper nW(0x0000000067d2e605);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000067d2e605)
	nCtx.Invoke(L, 0x0000000067d2e605);
	LUA_EXC_WRAP_END(0x0000000067d2e605)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x54c06897(lua_State* L) // GetPlayerToken
{
	static LuaNativeWrapper nW(0x0000000054c06897);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // index
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000054c06897)
	nCtx.Invoke(L, 0x0000000054c06897);
	LUA_EXC_WRAP_END(0x0000000054c06897)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x821f2d2c(lua_State* L) // GetPlayerWantedCentrePosition
{
	static LuaNativeWrapper nW(0x00000000821f2d2c);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000821f2d2c)
	nCtx.Invoke(L, 0x00000000821f2d2c);
	LUA_EXC_WRAP_END(0x00000000821f2d2c)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0xbdcdd163(lua_State* L) // GetPlayerWantedLevel
{
	static LuaNativeWrapper nW(0x00000000bdcdd163);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000bdcdd163)
	nCtx.Invoke(L, 0x00000000bdcdd163);
	LUA_EXC_WRAP_END(0x00000000bdcdd163)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x2a3d7cda(lua_State* L) // GetPlayerWeaponDamageModifier
{
	static LuaNativeWrapper nW(0x000000002a3d7cda);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerId
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002a3d7cda)
	nCtx.Invoke(L, 0x000000002a3d7cda);
	LUA_EXC_WRAP_END(0x000000002a3d7cda)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xf1543251(lua_State* L) // GetPlayerWeaponDefenseModifier
{
	static LuaNativeWrapper nW(0x00000000f1543251);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerId
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f1543251)
	nCtx.Invoke(L, 0x00000000f1543251);
	LUA_EXC_WRAP_END(0x00000000f1543251)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x986b65ff(lua_State* L) // GetPlayerWeaponDefenseModifier2
{
	static LuaNativeWrapper nW(0x00000000986b65ff);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerId
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000986b65ff)
	nCtx.Invoke(L, 0x00000000986b65ff);
	LUA_EXC_WRAP_END(0x00000000986b65ff)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0xd4bef069(lua_State* L) // GetRegisteredCommands
{
	static LuaNativeWrapper nW(0x00000000d4bef069);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d4bef069)
	nCtx.Invoke(L, 0x00000000d4bef069);
	LUA_EXC_WRAP_END(0x00000000d4bef069)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x387246b7(lua_State* L) // GetResourceByFindIndex
{
	static LuaNativeWrapper nW(0x00000000387246b7);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // findIndex
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000387246b7)
	nCtx.Invoke(L, 0x00000000387246b7);
	LUA_EXC_WRAP_END(0x00000000387246b7)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x97628584(lua_State* L) // GetResourceCommands
{
	static LuaNativeWrapper nW(0x0000000097628584);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resource
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000097628584)
	nCtx.Invoke(L, 0x0000000097628584);
	LUA_EXC_WRAP_END(0x0000000097628584)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x35bdceea(lua_State* L) // GetResourceKvpFloat
{
	static LuaNativeWrapper nW(0x0000000035bdceea);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000035bdceea)
	nCtx.Invoke(L, 0x0000000035bdceea);
	LUA_EXC_WRAP_END(0x0000000035bdceea)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x557b586a(lua_State* L) // GetResourceKvpInt
{
	static LuaNativeWrapper nW(0x00000000557b586a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000557b586a)
	nCtx.Invoke(L, 0x00000000557b586a);
	LUA_EXC_WRAP_END(0x00000000557b586a)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x5240da5a(lua_State* L) // GetResourceKvpString
{
	static LuaNativeWrapper nW(0x000000005240da5a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000005240da5a)
	nCtx.Invoke(L, 0x000000005240da5a);
	LUA_EXC_WRAP_END(0x000000005240da5a)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x964bab1d(lua_State* L) // GetResourceMetadata
{
	static LuaNativeWrapper nW(0x00000000964bab1d);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // metadataKey
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // index
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000964bab1d)
	nCtx.Invoke(L, 0x00000000964bab1d);
	LUA_EXC_WRAP_END(0x00000000964bab1d)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x61dcf017(lua_State* L) // GetResourcePath
{
	static LuaNativeWrapper nW(0x0000000061dcf017);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000061dcf017)
	nCtx.Invoke(L, 0x0000000061dcf017);
	LUA_EXC_WRAP_END(0x0000000061dcf017)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x4039b485(lua_State* L) // GetResourceState
{
	static LuaNativeWrapper nW(0x000000004039b485);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004039b485)
	nCtx.Invoke(L, 0x000000004039b485);
	LUA_EXC_WRAP_END(0x000000004039b485)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xd240123e(lua_State* L) // GetSelectedPedWeapon
{
	static LuaNativeWrapper nW(0x00000000d240123e);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d240123e)
	nCtx.Invoke(L, 0x00000000d240123e);
	LUA_EXC_WRAP_END(0x00000000d240123e)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x78d864c7(lua_State* L) // GetStateBagKeys
{
	static LuaNativeWrapper nW(0x0000000078d864c7);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000078d864c7)
	nCtx.Invoke(L, 0x0000000078d864c7);
	LUA_EXC_WRAP_END(0x0000000078d864c7)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x637f4c75(lua_State* L) // GetStateBagValue
{
	static LuaNativeWrapper nW(0x00000000637f4c75);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // key
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000637f4c75)
	nCtx.Invoke(L, 0x00000000637f4c75);
	LUA_EXC_WRAP_END(0x00000000637f4c75)
	const scrObject retval = nCtx.GetResult<const scrObject>();
	LuaArgumentParser::PushObject<const scrObject&>(L, retval);
	return 1;
}

int Lua_Native_0x1c939e87(lua_State* L) // GetThrusterSideRcsThrottle
{
	static LuaNativeWrapper nW(0x000000001c939e87);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // jetpack
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001c939e87)
	nCtx.Invoke(L, 0x000000001c939e87);
	LUA_EXC_WRAP_END(0x000000001c939e87)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x94e24c96(lua_State* L) // GetThrusterThrottle
{
	static LuaNativeWrapper nW(0x0000000094e24c96);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // jetpack
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000094e24c96)
	nCtx.Invoke(L, 0x0000000094e24c96);
	LUA_EXC_WRAP_END(0x0000000094e24c96)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x456e34a(lua_State* L) // GetTrainBackwardCarriage
{
	static LuaNativeWrapper nW(0x000000000456e34a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000456e34a)
	nCtx.Invoke(L, 0x000000000456e34a);
	LUA_EXC_WRAP_END(0x000000000456e34a)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x95070fa(lua_State* L) // GetTrainCarriageEngine
{
	static LuaNativeWrapper nW(0x00000000095070fa);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000095070fa)
	nCtx.Invoke(L, 0x00000000095070fa);
	LUA_EXC_WRAP_END(0x00000000095070fa)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x4b8285cf(lua_State* L) // GetTrainCarriageIndex
{
	static LuaNativeWrapper nW(0x000000004b8285cf);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004b8285cf)
	nCtx.Invoke(L, 0x000000004b8285cf);
	LUA_EXC_WRAP_END(0x000000004b8285cf)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xa4921ef5(lua_State* L) // GetTrainCruiseSpeed
{
	static LuaNativeWrapper nW(0x00000000a4921ef5);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a4921ef5)
	nCtx.Invoke(L, 0x00000000a4921ef5);
	LUA_EXC_WRAP_END(0x00000000a4921ef5)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x8daf79b6(lua_State* L) // GetTrainDirection
{
	static LuaNativeWrapper nW(0x000000008daf79b6);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008daf79b6)
	nCtx.Invoke(L, 0x000000008daf79b6);
	LUA_EXC_WRAP_END(0x000000008daf79b6)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x24dc88d9(lua_State* L) // GetTrainForwardCarriage
{
	static LuaNativeWrapper nW(0x0000000024dc88d9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000024dc88d9)
	nCtx.Invoke(L, 0x0000000024dc88d9);
	LUA_EXC_WRAP_END(0x0000000024dc88d9)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x81b50033(lua_State* L) // GetTrainState
{
	static LuaNativeWrapper nW(0x0000000081b50033);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000081b50033)
	nCtx.Invoke(L, 0x0000000081b50033);
	LUA_EXC_WRAP_END(0x0000000081b50033)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x9aa339d(lua_State* L) // GetTrainTrackIndex
{
	static LuaNativeWrapper nW(0x0000000009aa339d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000009aa339d)
	nCtx.Invoke(L, 0x0000000009aa339d);
	LUA_EXC_WRAP_END(0x0000000009aa339d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x2b2fcc28(lua_State* L) // GetVehicleBodyHealth
{
	static LuaNativeWrapper nW(0x000000002b2fcc28);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002b2fcc28)
	nCtx.Invoke(L, 0x000000002b2fcc28);
	LUA_EXC_WRAP_END(0x000000002b2fcc28)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x40d82d88(lua_State* L) // GetVehicleColours
{
	int32_t ref_colorPrimary;
	int32_t ref_colorSecondary;

	static LuaNativeWrapper nW(0x0000000040d82d88);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_colorPrimary);
	nCtx.SetArgument(2, &ref_colorSecondary);
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000040d82d88)
	nCtx.Invoke(L, 0x0000000040d82d88);
	LUA_EXC_WRAP_END(0x0000000040d82d88)
	LuaArgumentParser::PushObject<int32_t>(L, ref_colorPrimary);
	LuaArgumentParser::PushObject<int32_t>(L, ref_colorSecondary);
	return 2;
}

int Lua_Native_0x1c2b9fef(lua_State* L) // GetVehicleCustomPrimaryColour
{
	int32_t ref_r;
	int32_t ref_g;
	int32_t ref_b;

	static LuaNativeWrapper nW(0x000000001c2b9fef);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_r);
	nCtx.SetArgument(2, &ref_g);
	nCtx.SetArgument(3, &ref_b);
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001c2b9fef)
	nCtx.Invoke(L, 0x000000001c2b9fef);
	LUA_EXC_WRAP_END(0x000000001c2b9fef)
	LuaArgumentParser::PushObject<int32_t>(L, ref_r);
	LuaArgumentParser::PushObject<int32_t>(L, ref_g);
	LuaArgumentParser::PushObject<int32_t>(L, ref_b);
	return 3;
}

int Lua_Native_0x3ff247a2(lua_State* L) // GetVehicleCustomSecondaryColour
{
	int32_t ref_r;
	int32_t ref_g;
	int32_t ref_b;

	static LuaNativeWrapper nW(0x000000003ff247a2);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_r);
	nCtx.SetArgument(2, &ref_g);
	nCtx.SetArgument(3, &ref_b);
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003ff247a2)
	nCtx.Invoke(L, 0x000000003ff247a2);
	LUA_EXC_WRAP_END(0x000000003ff247a2)
	LuaArgumentParser::PushObject<int32_t>(L, ref_r);
	LuaArgumentParser::PushObject<int32_t>(L, ref_g);
	LuaArgumentParser::PushObject<int32_t>(L, ref_b);
	return 3;
}

int Lua_Native_0xfd15c065(lua_State* L) // GetVehicleDirtLevel
{
	static LuaNativeWrapper nW(0x00000000fd15c065);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fd15c065)
	nCtx.Invoke(L, 0x00000000fd15c065);
	LUA_EXC_WRAP_END(0x00000000fd15c065)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x1dc50247(lua_State* L) // GetVehicleDoorsLockedForPlayer
{
	static LuaNativeWrapper nW(0x000000001dc50247);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001dc50247)
	nCtx.Invoke(L, 0x000000001dc50247);
	LUA_EXC_WRAP_END(0x000000001dc50247)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xd72cef2(lua_State* L) // GetVehicleDoorLockStatus
{
	static LuaNativeWrapper nW(0x000000000d72cef2);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000d72cef2)
	nCtx.Invoke(L, 0x000000000d72cef2);
	LUA_EXC_WRAP_END(0x000000000d72cef2)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x6e35c49c(lua_State* L) // GetVehicleDoorStatus
{
	static LuaNativeWrapper nW(0x000000006e35c49c);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // doorIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006e35c49c)
	nCtx.Invoke(L, 0x000000006e35c49c);
	LUA_EXC_WRAP_END(0x000000006e35c49c)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x8880038a(lua_State* L) // GetVehicleEngineHealth
{
	static LuaNativeWrapper nW(0x000000008880038a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008880038a)
	nCtx.Invoke(L, 0x000000008880038a);
	LUA_EXC_WRAP_END(0x000000008880038a)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x80e4659b(lua_State* L) // GetVehicleExtraColours
{
	int32_t ref_pearlescentColor;
	int32_t ref_wheelColor;

	static LuaNativeWrapper nW(0x0000000080e4659b);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_pearlescentColor);
	nCtx.SetArgument(2, &ref_wheelColor);
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000080e4659b)
	nCtx.Invoke(L, 0x0000000080e4659b);
	LUA_EXC_WRAP_END(0x0000000080e4659b)
	LuaArgumentParser::PushObject<int32_t>(L, ref_pearlescentColor);
	LuaArgumentParser::PushObject<int32_t>(L, ref_wheelColor);
	return 2;
}

int Lua_Native_0xad40ad55(lua_State* L) // GetVehicleFlightNozzlePosition
{
	static LuaNativeWrapper nW(0x00000000ad40ad55);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ad40ad55)
	nCtx.Invoke(L, 0x00000000ad40ad55);
	LUA_EXC_WRAP_END(0x00000000ad40ad55)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x483b013c(lua_State* L) // GetVehicleHandbrake
{
	static LuaNativeWrapper nW(0x00000000483b013c);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000483b013c)
	nCtx.Invoke(L, 0x00000000483b013c);
	LUA_EXC_WRAP_END(0x00000000483b013c)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xd7147656(lua_State* L) // GetVehicleHeadlightsColour
{
	static LuaNativeWrapper nW(0x00000000d7147656);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d7147656)
	nCtx.Invoke(L, 0x00000000d7147656);
	LUA_EXC_WRAP_END(0x00000000d7147656)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xfbde9fd8(lua_State* L) // GetVehicleHomingLockonState
{
	static LuaNativeWrapper nW(0x00000000fbde9fd8);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fbde9fd8)
	nCtx.Invoke(L, 0x00000000fbde9fd8);
	LUA_EXC_WRAP_END(0x00000000fbde9fd8)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xdea49773(lua_State* L) // GetVehicleHornType
{
	static LuaNativeWrapper nW(0x00000000dea49773);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dea49773)
	nCtx.Invoke(L, 0x00000000dea49773);
	LUA_EXC_WRAP_END(0x00000000dea49773)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x7c278621(lua_State* L) // GetVehicleLightsState
{
	bool ref_lightsOn;
	bool ref_highbeamsOn;

	static LuaNativeWrapper nW(0x000000007c278621);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_lightsOn);
	nCtx.SetArgument(2, &ref_highbeamsOn);
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007c278621)
	nCtx.Invoke(L, 0x000000007c278621);
	LUA_EXC_WRAP_END(0x000000007c278621)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	LuaArgumentParser::PushObject<bool>(L, ref_lightsOn);
	LuaArgumentParser::PushObject<bool>(L, ref_highbeamsOn);
	return 3;
}

int Lua_Native_0xec82a51d(lua_State* L) // GetVehicleLivery
{
	static LuaNativeWrapper nW(0x00000000ec82a51d);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ec82a51d)
	nCtx.Invoke(L, 0x00000000ec82a51d);
	LUA_EXC_WRAP_END(0x00000000ec82a51d)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x4a557117(lua_State* L) // GetVehicleLockOnTarget
{
	static LuaNativeWrapper nW(0x000000004a557117);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004a557117)
	nCtx.Invoke(L, 0x000000004a557117);
	LUA_EXC_WRAP_END(0x000000004a557117)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xd9319dcb(lua_State* L) // GetVehicleNeonColour
{
	int32_t ref_red;
	int32_t ref_green;
	int32_t ref_blue;

	static LuaNativeWrapper nW(0x00000000d9319dcb);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_red);
	nCtx.SetArgument(2, &ref_green);
	nCtx.SetArgument(3, &ref_blue);
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d9319dcb)
	nCtx.Invoke(L, 0x00000000d9319dcb);
	LUA_EXC_WRAP_END(0x00000000d9319dcb)
	LuaArgumentParser::PushObject<int32_t>(L, ref_red);
	LuaArgumentParser::PushObject<int32_t>(L, ref_green);
	LuaArgumentParser::PushObject<int32_t>(L, ref_blue);
	return 3;
}

int Lua_Native_0x684bdbf2(lua_State* L) // GetVehicleNeonEnabled
{
	static LuaNativeWrapper nW(0x00000000684bdbf2);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // neonIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000684bdbf2)
	nCtx.Invoke(L, 0x00000000684bdbf2);
	LUA_EXC_WRAP_END(0x00000000684bdbf2)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xe8522d58(lua_State* L) // GetVehicleNumberPlateText
{
	static LuaNativeWrapper nW(0x00000000e8522d58);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e8522d58)
	nCtx.Invoke(L, 0x00000000e8522d58);
	LUA_EXC_WRAP_END(0x00000000e8522d58)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x499747b6(lua_State* L) // GetVehicleNumberPlateTextIndex
{
	static LuaNativeWrapper nW(0x00000000499747b6);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000499747b6)
	nCtx.Invoke(L, 0x00000000499747b6);
	LUA_EXC_WRAP_END(0x00000000499747b6)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xafe92319(lua_State* L) // GetVehiclePedIsIn
{
	static LuaNativeWrapper nW(0x00000000afe92319);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // lastVehicle
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000afe92319)
	nCtx.Invoke(L, 0x00000000afe92319);
	LUA_EXC_WRAP_END(0x00000000afe92319)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xe41595ce(lua_State* L) // GetVehiclePetrolTankHealth
{
	static LuaNativeWrapper nW(0x00000000e41595ce);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e41595ce)
	nCtx.Invoke(L, 0x00000000e41595ce);
	LUA_EXC_WRAP_END(0x00000000e41595ce)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x57037960(lua_State* L) // GetVehicleRadioStationIndex
{
	static LuaNativeWrapper nW(0x0000000057037960);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000057037960)
	nCtx.Invoke(L, 0x0000000057037960);
	LUA_EXC_WRAP_END(0x0000000057037960)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x872cf42(lua_State* L) // GetVehicleRoofLivery
{
	static LuaNativeWrapper nW(0x000000000872cf42);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000872cf42)
	nCtx.Invoke(L, 0x000000000872cf42);
	LUA_EXC_WRAP_END(0x000000000872cf42)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x1382fcea(lua_State* L) // GetVehicleSteeringAngle
{
	static LuaNativeWrapper nW(0x000000001382fcea);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001382fcea)
	nCtx.Invoke(L, 0x000000001382fcea);
	LUA_EXC_WRAP_END(0x000000001382fcea)
	float retval = nCtx.GetResult<float>();
	LuaArgumentParser::PushObject<float>(L, retval);
	return 1;
}

int Lua_Native_0x9963d5f9(lua_State* L) // GetVehicleTotalRepairs
{
	static LuaNativeWrapper nW(0x000000009963d5f9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009963d5f9)
	nCtx.Invoke(L, 0x000000009963d5f9);
	LUA_EXC_WRAP_END(0x000000009963d5f9)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xa273060e(lua_State* L) // GetVehicleType
{
	static LuaNativeWrapper nW(0x00000000a273060e);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a273060e)
	nCtx.Invoke(L, 0x00000000a273060e);
	LUA_EXC_WRAP_END(0x00000000a273060e)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x75280015(lua_State* L) // GetVehicleTyreSmokeColor
{
	int32_t ref_r;
	int32_t ref_g;
	int32_t ref_b;

	static LuaNativeWrapper nW(0x0000000075280015);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, &ref_r);
	nCtx.SetArgument(2, &ref_g);
	nCtx.SetArgument(3, &ref_b);
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000075280015)
	nCtx.Invoke(L, 0x0000000075280015);
	LUA_EXC_WRAP_END(0x0000000075280015)
	LuaArgumentParser::PushObject<int32_t>(L, ref_r);
	LuaArgumentParser::PushObject<int32_t>(L, ref_g);
	LuaArgumentParser::PushObject<int32_t>(L, ref_b);
	return 3;
}

int Lua_Native_0xda58d7ae(lua_State* L) // GetVehicleWheelType
{
	static LuaNativeWrapper nW(0x00000000da58d7ae);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000da58d7ae)
	nCtx.Invoke(L, 0x00000000da58d7ae);
	LUA_EXC_WRAP_END(0x00000000da58d7ae)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x13d53892(lua_State* L) // GetVehicleWindowTint
{
	static LuaNativeWrapper nW(0x0000000013d53892);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000013d53892)
	nCtx.Invoke(L, 0x0000000013d53892);
	LUA_EXC_WRAP_END(0x0000000013d53892)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x3e1e286d(lua_State* L) // GiveWeaponComponentToPed
{
	static LuaNativeWrapper nW(0x000000003e1e286d);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<uint32_t>(L, 3)); // componentHash
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003e1e286d)
	nCtx.Invoke(L, 0x000000003e1e286d);
	LUA_EXC_WRAP_END(0x000000003e1e286d)
	return 0;
}

int Lua_Native_0xc4d88a85(lua_State* L) // GiveWeaponToPed
{
	static LuaNativeWrapper nW(0x00000000c4d88a85);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // ammoCount
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<bool>(L, 4)); // isHidden
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // bForceInHand
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c4d88a85)
	nCtx.Invoke(L, 0x00000000c4d88a85);
	LUA_EXC_WRAP_END(0x00000000c4d88a85)
	return 0;
}

int Lua_Native_0x9c9a3be0(lua_State* L) // HasEntityBeenMarkedAsNoLongerNeeded
{
	static LuaNativeWrapper nW(0x000000009c9a3be0);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009c9a3be0)
	nCtx.Invoke(L, 0x000000009c9a3be0);
	LUA_EXC_WRAP_END(0x000000009c9a3be0)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xb8af3137(lua_State* L) // HasVehicleBeenDamagedByBullets
{
	static LuaNativeWrapper nW(0x00000000b8af3137);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b8af3137)
	nCtx.Invoke(L, 0x00000000b8af3137);
	LUA_EXC_WRAP_END(0x00000000b8af3137)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xe4e83a5b(lua_State* L) // HasVehicleBeenOwnedByPlayer
{
	static LuaNativeWrapper nW(0x00000000e4e83a5b);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e4e83a5b)
	nCtx.Invoke(L, 0x00000000e4e83a5b);
	LUA_EXC_WRAP_END(0x00000000e4e83a5b)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x7ebb9929(lua_State* L) // IsAceAllowed
{
	static LuaNativeWrapper nW(0x000000007ebb9929);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // object
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007ebb9929)
	nCtx.Invoke(L, 0x000000007ebb9929);
	LUA_EXC_WRAP_END(0x000000007ebb9929)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xd5c39ee6(lua_State* L) // IsBoatAnchoredAndFrozen
{
	static LuaNativeWrapper nW(0x00000000d5c39ee6);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d5c39ee6)
	nCtx.Invoke(L, 0x00000000d5c39ee6);
	LUA_EXC_WRAP_END(0x00000000d5c39ee6)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x9049db44(lua_State* L) // IsBoatWrecked
{
	static LuaNativeWrapper nW(0x000000009049db44);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009049db44)
	nCtx.Invoke(L, 0x000000009049db44);
	LUA_EXC_WRAP_END(0x000000009049db44)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xcf24c52e(lua_State* L) // IsDuplicityVersion
{
	static LuaNativeWrapper nW(0x00000000cf24c52e);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000cf24c52e)
	nCtx.Invoke(L, 0x00000000cf24c52e);
	LUA_EXC_WRAP_END(0x00000000cf24c52e)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xedbe6add(lua_State* L) // IsEntityPositionFrozen
{
	static LuaNativeWrapper nW(0x00000000edbe6add);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000edbe6add)
	nCtx.Invoke(L, 0x00000000edbe6add);
	LUA_EXC_WRAP_END(0x00000000edbe6add)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x120b4ed5(lua_State* L) // IsEntityVisible
{
	static LuaNativeWrapper nW(0x00000000120b4ed5);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000120b4ed5)
	nCtx.Invoke(L, 0x00000000120b4ed5);
	LUA_EXC_WRAP_END(0x00000000120b4ed5)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x76876154(lua_State* L) // IsFlashLightOn
{
	static LuaNativeWrapper nW(0x0000000076876154);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000076876154)
	nCtx.Invoke(L, 0x0000000076876154);
	LUA_EXC_WRAP_END(0x0000000076876154)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x23e46bd7(lua_State* L) // IsHeliTailBoomBreakable
{
	static LuaNativeWrapper nW(0x0000000023e46bd7);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000023e46bd7)
	nCtx.Invoke(L, 0x0000000023e46bd7);
	LUA_EXC_WRAP_END(0x0000000023e46bd7)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x2c59f987(lua_State* L) // IsHeliTailBoomBroken
{
	static LuaNativeWrapper nW(0x000000002c59f987);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // heli
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002c59f987)
	nCtx.Invoke(L, 0x000000002c59f987);
	LUA_EXC_WRAP_END(0x000000002c59f987)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x404794ca(lua_State* L) // IsPedAPlayer
{
	static LuaNativeWrapper nW(0x00000000404794ca);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000404794ca)
	nCtx.Invoke(L, 0x00000000404794ca);
	LUA_EXC_WRAP_END(0x00000000404794ca)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x25865633(lua_State* L) // IsPedHandcuffed
{
	static LuaNativeWrapper nW(0x0000000025865633);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000025865633)
	nCtx.Invoke(L, 0x0000000025865633);
	LUA_EXC_WRAP_END(0x0000000025865633)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xc833bbe1(lua_State* L) // IsPedRagdoll
{
	static LuaNativeWrapper nW(0x00000000c833bbe1);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c833bbe1)
	nCtx.Invoke(L, 0x00000000c833bbe1);
	LUA_EXC_WRAP_END(0x00000000c833bbe1)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xefeed13c(lua_State* L) // IsPedStrafing
{
	static LuaNativeWrapper nW(0x00000000efeed13c);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000efeed13c)
	nCtx.Invoke(L, 0x00000000efeed13c);
	LUA_EXC_WRAP_END(0x00000000efeed13c)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x5ae7eda2(lua_State* L) // IsPedUsingActionMode
{
	static LuaNativeWrapper nW(0x000000005ae7eda2);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000005ae7eda2)
	nCtx.Invoke(L, 0x000000005ae7eda2);
	LUA_EXC_WRAP_END(0x000000005ae7eda2)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xdedae23d(lua_State* L) // IsPlayerAceAllowed
{
	static LuaNativeWrapper nW(0x00000000dedae23d);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // object
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dedae23d)
	nCtx.Invoke(L, 0x00000000dedae23d);
	LUA_EXC_WRAP_END(0x00000000dedae23d)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xbefe93f4(lua_State* L) // IsPlayerCommerceInfoLoaded
{
	static LuaNativeWrapper nW(0x00000000befe93f4);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000befe93f4)
	nCtx.Invoke(L, 0x00000000befe93f4);
	LUA_EXC_WRAP_END(0x00000000befe93f4)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x1d14f4fe(lua_State* L) // IsPlayerCommerceInfoLoadedExt
{
	static LuaNativeWrapper nW(0x000000001d14f4fe);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001d14f4fe)
	nCtx.Invoke(L, 0x000000001d14f4fe);
	LUA_EXC_WRAP_END(0x000000001d14f4fe)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x89a3881a(lua_State* L) // IsPlayerEvadingWantedLevel
{
	static LuaNativeWrapper nW(0x0000000089a3881a);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000089a3881a)
	nCtx.Invoke(L, 0x0000000089a3881a);
	LUA_EXC_WRAP_END(0x0000000089a3881a)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x1f14f2ac(lua_State* L) // IsPlayerInFreeCamMode
{
	static LuaNativeWrapper nW(0x000000001f14f2ac);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001f14f2ac)
	nCtx.Invoke(L, 0x000000001f14f2ac);
	LUA_EXC_WRAP_END(0x000000001f14f2ac)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xc7d2c20c(lua_State* L) // IsPlayerUsingSuperJump
{
	static LuaNativeWrapper nW(0x00000000c7d2c20c);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c7d2c20c)
	nCtx.Invoke(L, 0x00000000c7d2c20c);
	LUA_EXC_WRAP_END(0x00000000c7d2c20c)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x37cf52ce(lua_State* L) // IsPrincipalAceAllowed
{
	static LuaNativeWrapper nW(0x0000000037cf52ce);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // principal
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // object
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000037cf52ce)
	nCtx.Invoke(L, 0x0000000037cf52ce);
	LUA_EXC_WRAP_END(0x0000000037cf52ce)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xfa9336e5(lua_State* L) // IsTrainCaboose
{
	static LuaNativeWrapper nW(0x00000000fa9336e5);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // train
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fa9336e5)
	nCtx.Invoke(L, 0x00000000fa9336e5);
	LUA_EXC_WRAP_END(0x00000000fa9336e5)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xbb340d04(lua_State* L) // IsVehicleEngineStarting
{
	static LuaNativeWrapper nW(0x00000000bb340d04);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000bb340d04)
	nCtx.Invoke(L, 0x00000000bb340d04);
	LUA_EXC_WRAP_END(0x00000000bb340d04)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x42098b5(lua_State* L) // IsVehicleExtraTurnedOn
{
	static LuaNativeWrapper nW(0x00000000042098b5);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // extraId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000042098b5)
	nCtx.Invoke(L, 0x00000000042098b5);
	LUA_EXC_WRAP_END(0x00000000042098b5)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x25eb5873(lua_State* L) // IsVehicleSirenOn
{
	static LuaNativeWrapper nW(0x0000000025eb5873);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000025eb5873)
	nCtx.Invoke(L, 0x0000000025eb5873);
	LUA_EXC_WRAP_END(0x0000000025eb5873)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x48c80210(lua_State* L) // IsVehicleTyreBurst
{
	static LuaNativeWrapper nW(0x0000000048c80210);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // wheelID
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // completely
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000048c80210)
	nCtx.Invoke(L, 0x0000000048c80210);
	LUA_EXC_WRAP_END(0x0000000048c80210)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xac4ef23d(lua_State* L) // IsVehicleWindowIntact
{
	static LuaNativeWrapper nW(0x00000000ac4ef23d);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // windowIndex
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ac4ef23d)
	nCtx.Invoke(L, 0x00000000ac4ef23d);
	LUA_EXC_WRAP_END(0x00000000ac4ef23d)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xa8f63eab(lua_State* L) // LoadPlayerCommerceData
{
	static LuaNativeWrapper nW(0x00000000a8f63eab);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a8f63eab)
	nCtx.Invoke(L, 0x00000000a8f63eab);
	LUA_EXC_WRAP_END(0x00000000a8f63eab)
	return 0;
}

int Lua_Native_0x7995539e(lua_State* L) // LoadPlayerCommerceDataExt
{
	static LuaNativeWrapper nW(0x000000007995539e);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007995539e)
	nCtx.Invoke(L, 0x000000007995539e);
	LUA_EXC_WRAP_END(0x000000007995539e)
	return 0;
}

int Lua_Native_0x76a9ee1f(lua_State* L) // LoadResourceFile
{
	static LuaNativeWrapper nW(0x0000000076a9ee1f);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // fileName
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000076a9ee1f)
	nCtx.Invoke(L, 0x0000000076a9ee1f);
	LUA_EXC_WRAP_END(0x0000000076a9ee1f)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0x262663c5(lua_State* L) // MumbleCreateChannel
{
	static LuaNativeWrapper nW(0x00000000262663c5);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // id
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000262663c5)
	nCtx.Invoke(L, 0x00000000262663c5);
	LUA_EXC_WRAP_END(0x00000000262663c5)
	return 0;
}

int Lua_Native_0x1d5d50c2(lua_State* L) // MumbleIsPlayerMuted
{
	static LuaNativeWrapper nW(0x000000001d5d50c2);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001d5d50c2)
	nCtx.Invoke(L, 0x000000001d5d50c2);
	LUA_EXC_WRAP_END(0x000000001d5d50c2)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xcc6c2eb1(lua_State* L) // MumbleSetPlayerMuted
{
	static LuaNativeWrapper nW(0x00000000cc6c2eb1);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // toggle
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000cc6c2eb1)
	nCtx.Invoke(L, 0x00000000cc6c2eb1);
	LUA_EXC_WRAP_END(0x00000000cc6c2eb1)
	return 0;
}

int Lua_Native_0x5b912c3f(lua_State* L) // NetworkGetEntityFromNetworkId
{
	static LuaNativeWrapper nW(0x000000005b912c3f);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // netId
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000005b912c3f)
	nCtx.Invoke(L, 0x000000005b912c3f);
	LUA_EXC_WRAP_END(0x000000005b912c3f)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x526fee31(lua_State* L) // NetworkGetEntityOwner
{
	static LuaNativeWrapper nW(0x00000000526fee31);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000526fee31)
	nCtx.Invoke(L, 0x00000000526fee31);
	LUA_EXC_WRAP_END(0x00000000526fee31)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x1e546224(lua_State* L) // NetworkGetFirstEntityOwner
{
	static LuaNativeWrapper nW(0x000000001e546224);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001e546224)
	nCtx.Invoke(L, 0x000000001e546224);
	LUA_EXC_WRAP_END(0x000000001e546224)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x9e35dab6(lua_State* L) // NetworkGetNetworkIdFromEntity
{
	static LuaNativeWrapper nW(0x000000009e35dab6);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009e35dab6)
	nCtx.Invoke(L, 0x000000009e35dab6);
	LUA_EXC_WRAP_END(0x000000009e35dab6)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xffeef513(lua_State* L) // NetworkGetVoiceProximityOverrideForPlayer
{
	static LuaNativeWrapper nW(0x00000000ffeef513);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ffeef513)
	nCtx.Invoke(L, 0x00000000ffeef513);
	LUA_EXC_WRAP_END(0x00000000ffeef513)
	const scrVectorLua retval = nCtx.GetResult<const scrVectorLua>();
	LuaArgumentParser::PushObject<const scrVectorLua&>(L, retval);
	return 1;
}

int Lua_Native_0x8e8cc653(lua_State* L) // PerformHttpRequestInternal
{
	static LuaNativeWrapper nW(0x000000008e8cc653);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // requestData
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // requestDataLength
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008e8cc653)
	nCtx.Invoke(L, 0x000000008e8cc653);
	LUA_EXC_WRAP_END(0x000000008e8cc653)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x90892ded(lua_State* L) // PrintStructuredTrace
{
	static LuaNativeWrapper nW(0x0000000090892ded);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // jsonString
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000090892ded)
	nCtx.Invoke(L, 0x0000000090892ded);
	LUA_EXC_WRAP_END(0x0000000090892ded)
	return 0;
}

int Lua_Native_0xc795a4a9(lua_State* L) // ProfilerEnterScope
{
	static LuaNativeWrapper nW(0x00000000c795a4a9);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // scopeName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c795a4a9)
	nCtx.Invoke(L, 0x00000000c795a4a9);
	LUA_EXC_WRAP_END(0x00000000c795a4a9)
	return 0;
}

int Lua_Native_0xb39ca35c(lua_State* L) // ProfilerExitScope
{
	static LuaNativeWrapper nW(0x00000000b39ca35c);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b39ca35c)
	nCtx.Invoke(L, 0x00000000b39ca35c);
	LUA_EXC_WRAP_END(0x00000000b39ca35c)
	return 0;
}

int Lua_Native_0xf8b7d7bb(lua_State* L) // ProfilerIsRecording
{
	static LuaNativeWrapper nW(0x00000000f8b7d7bb);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f8b7d7bb)
	nCtx.Invoke(L, 0x00000000f8b7d7bb);
	LUA_EXC_WRAP_END(0x00000000f8b7d7bb)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x9862b266(lua_State* L) // RegisterResourceAsset
{
	static LuaNativeWrapper nW(0x000000009862b266);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // fileName
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009862b266)
	nCtx.Invoke(L, 0x000000009862b266);
	LUA_EXC_WRAP_END(0x000000009862b266)
	const char* retval = nCtx.GetResult<const char*>();
	LuaArgumentParser::PushObject<const char*>(L, retval);
	return 1;
}

int Lua_Native_0xd233a168(lua_State* L) // RegisterResourceAsEventHandler
{
	static LuaNativeWrapper nW(0x00000000d233a168);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // eventName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d233a168)
	nCtx.Invoke(L, 0x00000000d233a168);
	LUA_EXC_WRAP_END(0x00000000d233a168)
	return 0;
}

int Lua_Native_0xa44ce817(lua_State* L) // RemoveAllPedWeapons
{
	static LuaNativeWrapper nW(0x00000000a44ce817);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // p1
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a44ce817)
	nCtx.Invoke(L, 0x00000000a44ce817);
	LUA_EXC_WRAP_END(0x00000000a44ce817)
	return 0;
}

int Lua_Native_0xeac49841(lua_State* L) // RemoveConvarChangeListener
{
	static LuaNativeWrapper nW(0x00000000eac49841);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // cookie
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000eac49841)
	nCtx.Invoke(L, 0x00000000eac49841);
	LUA_EXC_WRAP_END(0x00000000eac49841)
	return 0;
}

int Lua_Native_0xd36be661(lua_State* L) // RemoveStateBagChangeHandler
{
	static LuaNativeWrapper nW(0x00000000d36be661);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // cookie
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d36be661)
	nCtx.Invoke(L, 0x00000000d36be661);
	LUA_EXC_WRAP_END(0x00000000d36be661)
	return 0;
}

int Lua_Native_0x412aa00d(lua_State* L) // RemoveWeaponComponentFromPed
{
	static LuaNativeWrapper nW(0x00000000412aa00d);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<uint32_t>(L, 3)); // componentHash
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000412aa00d)
	nCtx.Invoke(L, 0x00000000412aa00d);
	LUA_EXC_WRAP_END(0x00000000412aa00d)
	return 0;
}

int Lua_Native_0x9c37f220(lua_State* L) // RemoveWeaponFromPed
{
	static LuaNativeWrapper nW(0x000000009c37f220);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009c37f220)
	nCtx.Invoke(L, 0x000000009c37f220);
	LUA_EXC_WRAP_END(0x000000009c37f220)
	return 0;
}

int Lua_Native_0x96f93cce(lua_State* L) // RequestPlayerCommerceSession
{
	static LuaNativeWrapper nW(0x0000000096f93cce);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // skuId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000096f93cce)
	nCtx.Invoke(L, 0x0000000096f93cce);
	LUA_EXC_WRAP_END(0x0000000096f93cce)
	return 0;
}

int Lua_Native_0xa09e7e7b(lua_State* L) // SaveResourceFile
{
	static LuaNativeWrapper nW(0x00000000a09e7e7b);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // fileName
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // data
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // dataLength
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a09e7e7b)
	nCtx.Invoke(L, 0x00000000a09e7e7b);
	LUA_EXC_WRAP_END(0x00000000a09e7e7b)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xb88a73ad(lua_State* L) // ScheduleResourceTick
{
	static LuaNativeWrapper nW(0x00000000b88a73ad);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b88a73ad)
	nCtx.Invoke(L, 0x00000000b88a73ad);
	LUA_EXC_WRAP_END(0x00000000b88a73ad)
	return 0;
}

int Lua_Native_0x8dbbb0b9(lua_State* L) // SetBlipSprite
{
	static LuaNativeWrapper nW(0x000000008dbbb0b9);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // blip
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // spriteId
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008dbbb0b9)
	nCtx.Invoke(L, 0x000000008dbbb0b9);
	LUA_EXC_WRAP_END(0x000000008dbbb0b9)
	return 0;
}

int Lua_Native_0x341b16d2(lua_State* L) // SetConvar
{
	static LuaNativeWrapper nW(0x00000000341b16d2);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000341b16d2)
	nCtx.Invoke(L, 0x00000000341b16d2);
	LUA_EXC_WRAP_END(0x00000000341b16d2)
	return 0;
}

int Lua_Native_0xf292858c(lua_State* L) // SetConvarReplicated
{
	static LuaNativeWrapper nW(0x00000000f292858c);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f292858c)
	nCtx.Invoke(L, 0x00000000f292858c);
	LUA_EXC_WRAP_END(0x00000000f292858c)
	return 0;
}

int Lua_Native_0x9338d547(lua_State* L) // SetConvarServerInfo
{
	static LuaNativeWrapper nW(0x000000009338d547);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // varName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009338d547)
	nCtx.Invoke(L, 0x000000009338d547);
	LUA_EXC_WRAP_END(0x000000009338d547)
	return 0;
}

int Lua_Native_0xb8278882(lua_State* L) // SetCurrentPedWeapon
{
	static LuaNativeWrapper nW(0x00000000b8278882);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // bForceInHand
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b8278882)
	nCtx.Invoke(L, 0x00000000b8278882);
	LUA_EXC_WRAP_END(0x00000000b8278882)
	return 0;
}

int Lua_Native_0xdf70b41b(lua_State* L) // SetEntityCoords
{
	static LuaNativeWrapper nW(0x00000000df70b41b);
	LuaNativeContext nCtx(&nW, 8);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // xPos
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // yPos
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // zPos
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // alive
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // deadFlag
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // ragdollFlag
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<bool>(L, 8)); // clearArea
	nCtx.SetArgument(8, uintptr_t(0));
	nCtx.SetArgument(9, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000df70b41b)
	nCtx.Invoke(L, 0x00000000df70b41b);
	LUA_EXC_WRAP_END(0x00000000df70b41b)
	return 0;
}

int Lua_Native_0xd3a183a3(lua_State* L) // SetEntityDistanceCullingRadius
{
	static LuaNativeWrapper nW(0x00000000d3a183a3);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // radius
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d3a183a3)
	nCtx.Invoke(L, 0x00000000d3a183a3);
	LUA_EXC_WRAP_END(0x00000000d3a183a3)
	return 0;
}

int Lua_Native_0xe0ff064d(lua_State* L) // SetEntityHeading
{
	static LuaNativeWrapper nW(0x00000000e0ff064d);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // heading
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e0ff064d)
	nCtx.Invoke(L, 0x00000000e0ff064d);
	LUA_EXC_WRAP_END(0x00000000e0ff064d)
	return 0;
}

int Lua_Native_0x9f7f8d36(lua_State* L) // SetEntityIgnoreRequestControlFilter
{
	static LuaNativeWrapper nW(0x000000009f7f8d36);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // ignore
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009f7f8d36)
	nCtx.Invoke(L, 0x000000009f7f8d36);
	LUA_EXC_WRAP_END(0x000000009f7f8d36)
	return 0;
}

int Lua_Native_0x489e9162(lua_State* L) // SetEntityOrphanMode
{
	static LuaNativeWrapper nW(0x00000000489e9162);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // orphanMode
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000489e9162)
	nCtx.Invoke(L, 0x00000000489e9162);
	LUA_EXC_WRAP_END(0x00000000489e9162)
	return 0;
}

int Lua_Native_0xd3fc9d88(lua_State* L) // SetEntityRemoteSyncedScenesAllowed
{
	static LuaNativeWrapper nW(0x00000000d3fc9d88);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // allow
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d3fc9d88)
	nCtx.Invoke(L, 0x00000000d3fc9d88);
	LUA_EXC_WRAP_END(0x00000000d3fc9d88)
	return 0;
}

int Lua_Native_0xa345efe(lua_State* L) // SetEntityRotation
{
	static LuaNativeWrapper nW(0x000000000a345efe);
	LuaNativeContext nCtx(&nW, 6);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // pitch
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // roll
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // yaw
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // rotationOrder
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // bDeadCheck
	nCtx.SetArgument(6, uintptr_t(0));
	nCtx.SetArgument(7, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000a345efe)
	nCtx.Invoke(L, 0x000000000a345efe);
	LUA_EXC_WRAP_END(0x000000000a345efe)
	return 0;
}

int Lua_Native_0x635e5289(lua_State* L) // SetEntityRoutingBucket
{
	static LuaNativeWrapper nW(0x00000000635e5289);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // bucket
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000635e5289)
	nCtx.Invoke(L, 0x00000000635e5289);
	LUA_EXC_WRAP_END(0x00000000635e5289)
	return 0;
}

int Lua_Native_0xff5a1988(lua_State* L) // SetEntityVelocity
{
	static LuaNativeWrapper nW(0x00000000ff5a1988);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ff5a1988)
	nCtx.Invoke(L, 0x00000000ff5a1988);
	LUA_EXC_WRAP_END(0x00000000ff5a1988)
	return 0;
}

int Lua_Native_0xf90b7469(lua_State* L) // SetGameType
{
	static LuaNativeWrapper nW(0x00000000f90b7469);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // gametypeName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f90b7469)
	nCtx.Invoke(L, 0x00000000f90b7469);
	LUA_EXC_WRAP_END(0x00000000f90b7469)
	return 0;
}

int Lua_Native_0xb7ba82dc(lua_State* L) // SetMapName
{
	static LuaNativeWrapper nW(0x00000000b7ba82dc);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // mapName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b7ba82dc)
	nCtx.Invoke(L, 0x00000000b7ba82dc);
	LUA_EXC_WRAP_END(0x00000000b7ba82dc)
	return 0;
}

int Lua_Native_0xbf90df1a(lua_State* L) // SetPedAmmo
{
	static LuaNativeWrapper nW(0x00000000bf90df1a);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // weaponHash
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // ammo
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000bf90df1a)
	nCtx.Invoke(L, 0x00000000bf90df1a);
	LUA_EXC_WRAP_END(0x00000000bf90df1a)
	return 0;
}

int Lua_Native_0x4e3a0cc4(lua_State* L) // SetPedArmour
{
	static LuaNativeWrapper nW(0x000000004e3a0cc4);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // amount
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004e3a0cc4)
	nCtx.Invoke(L, 0x000000004e3a0cc4);
	LUA_EXC_WRAP_END(0x000000004e3a0cc4)
	return 0;
}

int Lua_Native_0xcf1384c4(lua_State* L) // SetPedCanRagdoll
{
	static LuaNativeWrapper nW(0x00000000cf1384c4);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // toggle
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000cf1384c4)
	nCtx.Invoke(L, 0x00000000cf1384c4);
	LUA_EXC_WRAP_END(0x00000000cf1384c4)
	return 0;
}

int Lua_Native_0xd4f7b05c(lua_State* L) // SetPedComponentVariation
{
	static LuaNativeWrapper nW(0x00000000d4f7b05c);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // componentId
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // drawableId
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // textureId
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // paletteId
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d4f7b05c)
	nCtx.Invoke(L, 0x00000000d4f7b05c);
	LUA_EXC_WRAP_END(0x00000000d4f7b05c)
	return 0;
}

int Lua_Native_0x9cfbe10d(lua_State* L) // SetPedConfigFlag
{
	static LuaNativeWrapper nW(0x000000009cfbe10d);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // flagId
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // value
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009cfbe10d)
	nCtx.Invoke(L, 0x000000009cfbe10d);
	LUA_EXC_WRAP_END(0x000000009cfbe10d)
	return 0;
}

int Lua_Native_0xc866a984(lua_State* L) // SetPedDefaultComponentVariation
{
	static LuaNativeWrapper nW(0x00000000c866a984);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c866a984)
	nCtx.Invoke(L, 0x00000000c866a984);
	LUA_EXC_WRAP_END(0x00000000c866a984)
	return 0;
}

int Lua_Native_0xa23fe32c(lua_State* L) // SetPedHairTint
{
	static LuaNativeWrapper nW(0x00000000a23fe32c);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // colorID
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // highlightColorID
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a23fe32c)
	nCtx.Invoke(L, 0x00000000a23fe32c);
	LUA_EXC_WRAP_END(0x00000000a23fe32c)
	return 0;
}

int Lua_Native_0x60746b88(lua_State* L) // SetPedHeadBlendData
{
	static LuaNativeWrapper nW(0x0000000060746b88);
	LuaNativeContext nCtx(&nW, 11);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // shapeFirstID
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // shapeSecondID
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // shapeThirdID
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // skinFirstID
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<lua_Integer>(L, 6)); // skinSecondID
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<lua_Integer>(L, 7)); // skinThirdID
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // shapeMix
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<float>(L, 9)); // skinMix
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<float>(L, 10)); // thirdMix
	nCtx.SetArgument(10, LuaArgumentParser::ParseArgument<bool>(L, 11)); // isParent
	nCtx.SetArgument(11, uintptr_t(0));
	nCtx.SetArgument(12, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000060746b88)
	nCtx.Invoke(L, 0x0000000060746b88);
	LUA_EXC_WRAP_END(0x0000000060746b88)
	return 0;
}

int Lua_Native_0xd28dba90(lua_State* L) // SetPedHeadOverlay
{
	static LuaNativeWrapper nW(0x00000000d28dba90);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // overlayID
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // index
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // opacity
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d28dba90)
	nCtx.Invoke(L, 0x00000000d28dba90);
	LUA_EXC_WRAP_END(0x00000000d28dba90)
	return 0;
}

int Lua_Native_0x7500c79(lua_State* L) // SetPedIntoVehicle
{
	static LuaNativeWrapper nW(0x0000000007500c79);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // vehicle
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // seatIndex
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000007500c79)
	nCtx.Invoke(L, 0x0000000007500c79);
	LUA_EXC_WRAP_END(0x0000000007500c79)
	return 0;
}

int Lua_Native_0x829f2e2(lua_State* L) // SetPedPropIndex
{
	static LuaNativeWrapper nW(0x000000000829f2e2);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // componentId
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // drawableId
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // textureId
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // attach
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000829f2e2)
	nCtx.Invoke(L, 0x000000000829f2e2);
	LUA_EXC_WRAP_END(0x000000000829f2e2)
	return 0;
}

int Lua_Native_0x4111ba46(lua_State* L) // SetPedRandomComponentVariation
{
	static LuaNativeWrapper nW(0x000000004111ba46);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // p1
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004111ba46)
	nCtx.Invoke(L, 0x000000004111ba46);
	LUA_EXC_WRAP_END(0x000000004111ba46)
	return 0;
}

int Lua_Native_0xe3318e0e(lua_State* L) // SetPedRandomProps
{
	static LuaNativeWrapper nW(0x00000000e3318e0e);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000e3318e0e)
	nCtx.Invoke(L, 0x00000000e3318e0e);
	LUA_EXC_WRAP_END(0x00000000e3318e0e)
	return 0;
}

int Lua_Native_0xcff6ff66(lua_State* L) // SetPedResetFlag
{
	static LuaNativeWrapper nW(0x00000000cff6ff66);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // flagId
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // doReset
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000cff6ff66)
	nCtx.Invoke(L, 0x00000000cff6ff66);
	LUA_EXC_WRAP_END(0x00000000cff6ff66)
	return 0;
}

int Lua_Native_0x83cb5052(lua_State* L) // SetPedToRagdoll
{
	static LuaNativeWrapper nW(0x0000000083cb5052);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // minTime
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // maxTime
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // ragdollType
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // bAbortIfInjured
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<bool>(L, 6)); // bAbortIfDead
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // bForceScriptControl
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000083cb5052)
	nCtx.Invoke(L, 0x0000000083cb5052);
	LUA_EXC_WRAP_END(0x0000000083cb5052)
	return 0;
}

int Lua_Native_0xfa12e286(lua_State* L) // SetPedToRagdollWithFall
{
	static LuaNativeWrapper nW(0x00000000fa12e286);
	LuaNativeContext nCtx(&nW, 14);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // minTime
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // maxTime
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // nFallType
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // dirX
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // dirY
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<float>(L, 7)); // dirZ
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // fGroundHeight
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<float>(L, 9)); // grab1X
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<float>(L, 10)); // grab1Y
	nCtx.SetArgument(10, LuaArgumentParser::ParseArgument<float>(L, 11)); // grab1Z
	nCtx.SetArgument(11, LuaArgumentParser::ParseArgument<float>(L, 12)); // grab2X
	nCtx.SetArgument(12, LuaArgumentParser::ParseArgument<float>(L, 13)); // grab2Y
	nCtx.SetArgument(13, LuaArgumentParser::ParseArgument<float>(L, 14)); // grab2Z
	nCtx.SetArgument(14, uintptr_t(0));
	nCtx.SetArgument(15, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000fa12e286)
	nCtx.Invoke(L, 0x00000000fa12e286);
	LUA_EXC_WRAP_END(0x00000000fa12e286)
	return 0;
}

int Lua_Native_0xd17afcd8(lua_State* L) // SetPlayerControl
{
	static LuaNativeWrapper nW(0x00000000d17afcd8);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // player
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // bHasControl
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // flags
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000d17afcd8)
	nCtx.Invoke(L, 0x00000000d17afcd8);
	LUA_EXC_WRAP_END(0x00000000d17afcd8)
	return 0;
}

int Lua_Native_0x8a2fbad4(lua_State* L) // SetPlayerCullingRadius
{
	static LuaNativeWrapper nW(0x000000008a2fbad4);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // radius
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008a2fbad4)
	nCtx.Invoke(L, 0x000000008a2fbad4);
	LUA_EXC_WRAP_END(0x000000008a2fbad4)
	return 0;
}

int Lua_Native_0xdfb9a2a2(lua_State* L) // SetPlayerInvincible
{
	static LuaNativeWrapper nW(0x00000000dfb9a2a2);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // player
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // bInvincible
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dfb9a2a2)
	nCtx.Invoke(L, 0x00000000dfb9a2a2);
	LUA_EXC_WRAP_END(0x00000000dfb9a2a2)
	return 0;
}

int Lua_Native_0x774a4c54(lua_State* L) // SetPlayerModel
{
	static LuaNativeWrapper nW(0x00000000774a4c54);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // player
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<uint32_t>(L, 2)); // model
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000774a4c54)
	nCtx.Invoke(L, 0x00000000774a4c54);
	LUA_EXC_WRAP_END(0x00000000774a4c54)
	return 0;
}

int Lua_Native_0x6504eb38(lua_State* L) // SetPlayerRoutingBucket
{
	static LuaNativeWrapper nW(0x000000006504eb38);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // bucket
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006504eb38)
	nCtx.Invoke(L, 0x000000006504eb38);
	LUA_EXC_WRAP_END(0x000000006504eb38)
	return 0;
}

int Lua_Native_0xb7a0914b(lua_State* L) // SetPlayerWantedLevel
{
	static LuaNativeWrapper nW(0x00000000b7a0914b);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // player
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // wantedLevel
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // delayedResponse
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b7a0914b)
	nCtx.Invoke(L, 0x00000000b7a0914b);
	LUA_EXC_WRAP_END(0x00000000b7a0914b)
	return 0;
}

int Lua_Native_0x21c7a35b(lua_State* L) // SetResourceKvp
{
	static LuaNativeWrapper nW(0x0000000021c7a35b);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000021c7a35b)
	nCtx.Invoke(L, 0x0000000021c7a35b);
	LUA_EXC_WRAP_END(0x0000000021c7a35b)
	return 0;
}

int Lua_Native_0x9add2938(lua_State* L) // SetResourceKvpFloat
{
	static LuaNativeWrapper nW(0x000000009add2938);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009add2938)
	nCtx.Invoke(L, 0x000000009add2938);
	LUA_EXC_WRAP_END(0x000000009add2938)
	return 0;
}

int Lua_Native_0x3517bfbe(lua_State* L) // SetResourceKvpFloatNoSync
{
	static LuaNativeWrapper nW(0x000000003517bfbe);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003517bfbe)
	nCtx.Invoke(L, 0x000000003517bfbe);
	LUA_EXC_WRAP_END(0x000000003517bfbe)
	return 0;
}

int Lua_Native_0x6a2b1e8(lua_State* L) // SetResourceKvpInt
{
	static LuaNativeWrapper nW(0x0000000006a2b1e8);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000006a2b1e8)
	nCtx.Invoke(L, 0x0000000006a2b1e8);
	LUA_EXC_WRAP_END(0x0000000006a2b1e8)
	return 0;
}

int Lua_Native_0x26aeb707(lua_State* L) // SetResourceKvpIntNoSync
{
	static LuaNativeWrapper nW(0x0000000026aeb707);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000026aeb707)
	nCtx.Invoke(L, 0x0000000026aeb707);
	LUA_EXC_WRAP_END(0x0000000026aeb707)
	return 0;
}

int Lua_Native_0xcf9a2ff(lua_State* L) // SetResourceKvpNoSync
{
	static LuaNativeWrapper nW(0x000000000cf9a2ff);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // key
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000cf9a2ff)
	nCtx.Invoke(L, 0x000000000cf9a2ff);
	LUA_EXC_WRAP_END(0x000000000cf9a2ff)
	return 0;
}

int Lua_Native_0xa0f2201f(lua_State* L) // SetRoutingBucketEntityLockdownMode
{
	static LuaNativeWrapper nW(0x00000000a0f2201f);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // bucketId
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // mode
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a0f2201f)
	nCtx.Invoke(L, 0x00000000a0f2201f);
	LUA_EXC_WRAP_END(0x00000000a0f2201f)
	return 0;
}

int Lua_Native_0xce51ac2c(lua_State* L) // SetRoutingBucketPopulationEnabled
{
	static LuaNativeWrapper nW(0x00000000ce51ac2c);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // bucketId
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // mode
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ce51ac2c)
	nCtx.Invoke(L, 0x00000000ce51ac2c);
	LUA_EXC_WRAP_END(0x00000000ce51ac2c)
	return 0;
}

int Lua_Native_0x8d50e33a(lua_State* L) // SetStateBagValue
{
	static LuaNativeWrapper nW(0x000000008d50e33a);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // keyName
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // valueData
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // valueLength
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // replicated
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008d50e33a)
	nCtx.Invoke(L, 0x000000008d50e33a);
	LUA_EXC_WRAP_END(0x000000008d50e33a)
	return 0;
}

int Lua_Native_0x24877d84(lua_State* L) // SetVehicleAlarm
{
	static LuaNativeWrapper nW(0x0000000024877d84);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<bool>(L, 2)); // state
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000024877d84)
	nCtx.Invoke(L, 0x0000000024877d84);
	LUA_EXC_WRAP_END(0x0000000024877d84)
	return 0;
}

int Lua_Native_0x920c2517(lua_State* L) // SetVehicleBodyHealth
{
	static LuaNativeWrapper nW(0x00000000920c2517);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // value
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000920c2517)
	nCtx.Invoke(L, 0x00000000920c2517);
	LUA_EXC_WRAP_END(0x00000000920c2517)
	return 0;
}

int Lua_Native_0x57f24253(lua_State* L) // SetVehicleColours
{
	static LuaNativeWrapper nW(0x0000000057f24253);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // colorPrimary
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // colorSecondary
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000057f24253)
	nCtx.Invoke(L, 0x0000000057f24253);
	LUA_EXC_WRAP_END(0x0000000057f24253)
	return 0;
}

int Lua_Native_0xa557aead(lua_State* L) // SetVehicleColourCombination
{
	static LuaNativeWrapper nW(0x00000000a557aead);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // colorCombination
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000a557aead)
	nCtx.Invoke(L, 0x00000000a557aead);
	LUA_EXC_WRAP_END(0x00000000a557aead)
	return 0;
}

int Lua_Native_0x8df9f9bc(lua_State* L) // SetVehicleCustomPrimaryColour
{
	static LuaNativeWrapper nW(0x000000008df9f9bc);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // r
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // g
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // b
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008df9f9bc)
	nCtx.Invoke(L, 0x000000008df9f9bc);
	LUA_EXC_WRAP_END(0x000000008df9f9bc)
	return 0;
}

int Lua_Native_0x9d77259e(lua_State* L) // SetVehicleCustomSecondaryColour
{
	static LuaNativeWrapper nW(0x000000009d77259e);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // r
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // g
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // b
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000009d77259e)
	nCtx.Invoke(L, 0x000000009d77259e);
	LUA_EXC_WRAP_END(0x000000009d77259e)
	return 0;
}

int Lua_Native_0x2b39128b(lua_State* L) // SetVehicleDirtLevel
{
	static LuaNativeWrapper nW(0x000000002b39128b);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // dirtLevel
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002b39128b)
	nCtx.Invoke(L, 0x000000002b39128b);
	LUA_EXC_WRAP_END(0x000000002b39128b)
	return 0;
}

int Lua_Native_0x4cdd35d0(lua_State* L) // SetVehicleDoorsLocked
{
	static LuaNativeWrapper nW(0x000000004cdd35d0);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // doorLockStatus
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000004cdd35d0)
	nCtx.Invoke(L, 0x000000004cdd35d0);
	LUA_EXC_WRAP_END(0x000000004cdd35d0)
	return 0;
}

int Lua_Native_0x8147fea7(lua_State* L) // SetVehicleDoorBroken
{
	static LuaNativeWrapper nW(0x000000008147fea7);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // doorIndex
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<bool>(L, 3)); // deleteDoor
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008147fea7)
	nCtx.Invoke(L, 0x000000008147fea7);
	LUA_EXC_WRAP_END(0x000000008147fea7)
	return 0;
}

int Lua_Native_0x400f9556(lua_State* L) // SetVehicleNumberPlateText
{
	static LuaNativeWrapper nW(0x00000000400f9556);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // plateText
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000400f9556)
	nCtx.Invoke(L, 0x00000000400f9556);
	LUA_EXC_WRAP_END(0x00000000400f9556)
	return 0;
}

int Lua_Native_0xdd379006(lua_State* L) // StartFindKvp
{
	static LuaNativeWrapper nW(0x00000000dd379006);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // prefix
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dd379006)
	nCtx.Invoke(L, 0x00000000dd379006);
	LUA_EXC_WRAP_END(0x00000000dd379006)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0x29b440dc(lua_State* L) // StartResource
{
	static LuaNativeWrapper nW(0x0000000029b440dc);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000029b440dc)
	nCtx.Invoke(L, 0x0000000029b440dc);
	LUA_EXC_WRAP_END(0x0000000029b440dc)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x12a330(lua_State* L) // StateBagHasKey
{
	static LuaNativeWrapper nW(0x000000000012a330);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // bagName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // key
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000000012a330)
	nCtx.Invoke(L, 0x000000000012a330);
	LUA_EXC_WRAP_END(0x000000000012a330)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x21783161(lua_State* L) // StopResource
{
	static LuaNativeWrapper nW(0x0000000021783161);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // resourceName
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000021783161)
	nCtx.Invoke(L, 0x0000000021783161);
	LUA_EXC_WRAP_END(0x0000000021783161)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0xcb0d8932(lua_State* L) // TaskCombatPed
{
	static LuaNativeWrapper nW(0x00000000cb0d8932);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // targetPed
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // p2
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // p3
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000cb0d8932)
	nCtx.Invoke(L, 0x00000000cb0d8932);
	LUA_EXC_WRAP_END(0x00000000cb0d8932)
	return 0;
}

int Lua_Native_0x2b84d1c4(lua_State* L) // TaskDriveBy
{
	static LuaNativeWrapper nW(0x000000002b84d1c4);
	LuaNativeContext nCtx(&nW, 10);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // driverPed
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // targetPed
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // targetVehicle
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // targetX
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // targetY
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // targetZ
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<float>(L, 7)); // distanceToShoot
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<lua_Integer>(L, 8)); // pedAccuracy
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<bool>(L, 9)); // p8
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<uint32_t>(L, 10)); // firingPattern
	nCtx.SetArgument(10, uintptr_t(0));
	nCtx.SetArgument(11, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002b84d1c4)
	nCtx.Invoke(L, 0x000000002b84d1c4);
	LUA_EXC_WRAP_END(0x000000002b84d1c4)
	return 0;
}

int Lua_Native_0xb8689b4e(lua_State* L) // TaskEnterVehicle
{
	static LuaNativeWrapper nW(0x00000000b8689b4e);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // vehicle
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // timeout
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // seatIndex
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // speed
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<lua_Integer>(L, 6)); // flag
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<lua_Integer>(L, 7)); // p6
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000b8689b4e)
	nCtx.Invoke(L, 0x00000000b8689b4e);
	LUA_EXC_WRAP_END(0x00000000b8689b4e)
	return 0;
}

int Lua_Native_0xc1971f30(lua_State* L) // TaskEveryoneLeaveVehicle
{
	static LuaNativeWrapper nW(0x00000000c1971f30);
	LuaNativeContext nCtx(&nW, 1);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // vehicle
	nCtx.SetArgument(1, uintptr_t(0));
	nCtx.SetArgument(2, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000c1971f30)
	nCtx.Invoke(L, 0x00000000c1971f30);
	LUA_EXC_WRAP_END(0x00000000c1971f30)
	return 0;
}

int Lua_Native_0x80a9e7a7(lua_State* L) // TaskGoStraightToCoord
{
	static LuaNativeWrapper nW(0x0000000080a9e7a7);
	LuaNativeContext nCtx(&nW, 8);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // speed
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<lua_Integer>(L, 6)); // timeout
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<float>(L, 7)); // targetHeading
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // distanceToSlide
	nCtx.SetArgument(8, uintptr_t(0));
	nCtx.SetArgument(9, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000080a9e7a7)
	nCtx.Invoke(L, 0x0000000080a9e7a7);
	LUA_EXC_WRAP_END(0x0000000080a9e7a7)
	return 0;
}

int Lua_Native_0xf91df93b(lua_State* L) // TaskGoToCoordAnyMeans
{
	static LuaNativeWrapper nW(0x00000000f91df93b);
	LuaNativeContext nCtx(&nW, 9);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // fMoveBlendRatio
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<lua_Integer>(L, 6)); // vehicle
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<bool>(L, 7)); // bUseLongRangeVehiclePathing
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<lua_Integer>(L, 8)); // drivingFlags
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<float>(L, 9)); // fMaxRangeToShootTargets
	nCtx.SetArgument(9, uintptr_t(0));
	nCtx.SetArgument(10, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000f91df93b)
	nCtx.Invoke(L, 0x00000000f91df93b);
	LUA_EXC_WRAP_END(0x00000000f91df93b)
	return 0;
}

int Lua_Native_0x374827c2(lua_State* L) // TaskGoToEntity
{
	static LuaNativeWrapper nW(0x00000000374827c2);
	LuaNativeContext nCtx(&nW, 7);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // target
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // duration
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // distance
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // speed
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // p5
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<lua_Integer>(L, 7)); // p6
	nCtx.SetArgument(7, uintptr_t(0));
	nCtx.SetArgument(8, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000374827c2)
	nCtx.Invoke(L, 0x00000000374827c2);
	LUA_EXC_WRAP_END(0x00000000374827c2)
	return 0;
}

int Lua_Native_0x8dcc19c5(lua_State* L) // TaskHandsUp
{
	static LuaNativeWrapper nW(0x000000008dcc19c5);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // duration
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // facingPed
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // p3
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<bool>(L, 5)); // p4
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008dcc19c5)
	nCtx.Invoke(L, 0x000000008dcc19c5);
	LUA_EXC_WRAP_END(0x000000008dcc19c5)
	return 0;
}

int Lua_Native_0xdbdd79fa(lua_State* L) // TaskLeaveAnyVehicle
{
	static LuaNativeWrapper nW(0x00000000dbdd79fa);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // p1
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // flags
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000dbdd79fa)
	nCtx.Invoke(L, 0x00000000dbdd79fa);
	LUA_EXC_WRAP_END(0x00000000dbdd79fa)
	return 0;
}

int Lua_Native_0x7b1141c6(lua_State* L) // TaskLeaveVehicle
{
	static LuaNativeWrapper nW(0x000000007b1141c6);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // vehicle
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // flags
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000007b1141c6)
	nCtx.Invoke(L, 0x000000007b1141c6);
	LUA_EXC_WRAP_END(0x000000007b1141c6)
	return 0;
}

int Lua_Native_0x5ab552c6(lua_State* L) // TaskPlayAnim
{
	static LuaNativeWrapper nW(0x000000005ab552c6);
	LuaNativeContext nCtx(&nW, 11);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // animDictionary
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // animationName
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // blendInSpeed
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // blendOutSpeed
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<lua_Integer>(L, 6)); // duration
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<lua_Integer>(L, 7)); // flag
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // playbackRate
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<bool>(L, 9)); // lockX
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<bool>(L, 10)); // lockY
	nCtx.SetArgument(10, LuaArgumentParser::ParseArgument<bool>(L, 11)); // lockZ
	nCtx.SetArgument(11, uintptr_t(0));
	nCtx.SetArgument(12, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000005ab552c6)
	nCtx.Invoke(L, 0x000000005ab552c6);
	LUA_EXC_WRAP_END(0x000000005ab552c6)
	return 0;
}

int Lua_Native_0x3ddeb0e6(lua_State* L) // TaskPlayAnimAdvanced
{
	static LuaNativeWrapper nW(0x000000003ddeb0e6);
	LuaNativeContext nCtx(&nW, 16);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // animDictionary
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // animationName
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // posX
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // posY
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<float>(L, 6)); // posZ
	nCtx.SetArgument(6, LuaArgumentParser::ParseArgument<float>(L, 7)); // rotX
	nCtx.SetArgument(7, LuaArgumentParser::ParseArgument<float>(L, 8)); // rotY
	nCtx.SetArgument(8, LuaArgumentParser::ParseArgument<float>(L, 9)); // rotZ
	nCtx.SetArgument(9, LuaArgumentParser::ParseArgument<float>(L, 10)); // blendInSpeed
	nCtx.SetArgument(10, LuaArgumentParser::ParseArgument<float>(L, 11)); // blendOutSpeed
	nCtx.SetArgument(11, LuaArgumentParser::ParseArgument<lua_Integer>(L, 12)); // duration
	nCtx.SetArgument(12, LuaArgumentParser::ParseArgument<lua_Integer>(L, 13)); // flag
	nCtx.SetArgument(13, LuaArgumentParser::ParseArgument<float>(L, 14)); // animTime
	nCtx.SetArgument(14, LuaArgumentParser::ParseArgument<lua_Integer>(L, 15)); // p14
	nCtx.SetArgument(15, LuaArgumentParser::ParseArgument<lua_Integer>(L, 16)); // p15
	nCtx.SetArgument(16, uintptr_t(0));
	nCtx.SetArgument(17, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000003ddeb0e6)
	nCtx.Invoke(L, 0x000000003ddeb0e6);
	LUA_EXC_WRAP_END(0x000000003ddeb0e6)
	return 0;
}

int Lua_Native_0x8a632bd8(lua_State* L) // TaskReactAndFleePed
{
	static LuaNativeWrapper nW(0x000000008a632bd8);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // fleeTarget
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000008a632bd8)
	nCtx.Invoke(L, 0x000000008a632bd8);
	LUA_EXC_WRAP_END(0x000000008a632bd8)
	return 0;
}

int Lua_Native_0x601c22e3(lua_State* L) // TaskShootAtCoord
{
	static LuaNativeWrapper nW(0x00000000601c22e3);
	LuaNativeContext nCtx(&nW, 6);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // x
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // y
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // z
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // duration
	nCtx.SetArgument(5, LuaArgumentParser::ParseArgument<uint32_t>(L, 6)); // firingPattern
	nCtx.SetArgument(6, uintptr_t(0));
	nCtx.SetArgument(7, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000601c22e3)
	nCtx.Invoke(L, 0x00000000601c22e3);
	LUA_EXC_WRAP_END(0x00000000601c22e3)
	return 0;
}

int Lua_Native_0xac0631c9(lua_State* L) // TaskShootAtEntity
{
	static LuaNativeWrapper nW(0x00000000ac0631c9);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // entity
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // target
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // duration
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<uint32_t>(L, 4)); // firingPattern
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ac0631c9)
	nCtx.Invoke(L, 0x00000000ac0631c9);
	LUA_EXC_WRAP_END(0x00000000ac0631c9)
	return 0;
}

int Lua_Native_0x65d4a35d(lua_State* L) // TaskWarpPedIntoVehicle
{
	static LuaNativeWrapper nW(0x0000000065d4a35d);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // vehicle
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // seatIndex
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000065d4a35d)
	nCtx.Invoke(L, 0x0000000065d4a35d);
	LUA_EXC_WRAP_END(0x0000000065d4a35d)
	return 0;
}

int Lua_Native_0x1e35dbba(lua_State* L) // TempBanPlayer
{
	static LuaNativeWrapper nW(0x000000001e35dbba);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // playerSrc
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // reason
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000001e35dbba)
	nCtx.Invoke(L, 0x000000001e35dbba);
	LUA_EXC_WRAP_END(0x000000001e35dbba)
	return 0;
}

int Lua_Native_0x2f7a49e6(lua_State* L) // TriggerClientEventInternal
{
	static LuaNativeWrapper nW(0x000000002f7a49e6);
	LuaNativeContext nCtx(&nW, 4);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // eventName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // eventTarget
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // eventPayload
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // payloadLength
	nCtx.SetArgument(4, uintptr_t(0));
	nCtx.SetArgument(5, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002f7a49e6)
	nCtx.Invoke(L, 0x000000002f7a49e6);
	LUA_EXC_WRAP_END(0x000000002f7a49e6)
	return 0;
}

int Lua_Native_0x91310870(lua_State* L) // TriggerEventInternal
{
	static LuaNativeWrapper nW(0x0000000091310870);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // eventName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // eventPayload
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // payloadLength
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000091310870)
	nCtx.Invoke(L, 0x0000000091310870);
	LUA_EXC_WRAP_END(0x0000000091310870)
	return 0;
}

int Lua_Native_0x70b35890(lua_State* L) // TriggerLatentClientEventInternal
{
	static LuaNativeWrapper nW(0x0000000070b35890);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // eventName
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // eventTarget
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<const char*>(L, 3)); // eventPayload
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // payloadLength
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // bps
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000070b35890)
	nCtx.Invoke(L, 0x0000000070b35890);
	LUA_EXC_WRAP_END(0x0000000070b35890)
	return 0;
}

int Lua_Native_0x2e310acd(lua_State* L) // VerifyPasswordHash
{
	static LuaNativeWrapper nW(0x000000002e310acd);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<const char*>(L, 1)); // password
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<const char*>(L, 2)); // hash
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000002e310acd)
	nCtx.Invoke(L, 0x000000002e310acd);
	LUA_EXC_WRAP_END(0x000000002e310acd)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x58382a19(lua_State* L) // WasEventCanceled
{
	static LuaNativeWrapper nW(0x0000000058382a19);
	LuaNativeContext nCtx(&nW, 0);
	nCtx.SetArgument(0, uintptr_t(0));
	nCtx.SetArgument(1, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000058382a19)
	nCtx.Invoke(L, 0x0000000058382a19);
	LUA_EXC_WRAP_END(0x0000000058382a19)
	bool retval = nCtx.GetResult<bool>();
	LuaArgumentParser::PushObject<bool>(L, retval);
	return 1;
}

int Lua_Native_0x6228f159(lua_State* L) // AddBlipForArea
{
	static LuaNativeWrapper nW(0x000000006228f159);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<float>(L, 1)); // x
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<float>(L, 2)); // y
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // z
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<float>(L, 4)); // width
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<float>(L, 5)); // height
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006228f159)
	nCtx.Invoke(L, 0x000000006228f159);
	LUA_EXC_WRAP_END(0x000000006228f159)
	int32_t retval = nCtx.GetResult<int32_t>();
	LuaArgumentParser::PushObject<int32_t>(L, retval);
	return 1;
}

int Lua_Native_0xec09db1b(lua_State* L) // SetPedEyeColor
{
	static LuaNativeWrapper nW(0x00000000ec09db1b);
	LuaNativeContext nCtx(&nW, 2);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // index
	nCtx.SetArgument(2, uintptr_t(0));
	nCtx.SetArgument(3, uintptr_t(0));
	LUA_EXC_WRAP_START(0x00000000ec09db1b)
	nCtx.Invoke(L, 0x00000000ec09db1b);
	LUA_EXC_WRAP_END(0x00000000ec09db1b)
	return 0;
}

int Lua_Native_0x6c8d4458(lua_State* L) // SetPedFaceFeature
{
	static LuaNativeWrapper nW(0x000000006c8d4458);
	LuaNativeContext nCtx(&nW, 3);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // index
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<float>(L, 3)); // scale
	nCtx.SetArgument(3, uintptr_t(0));
	nCtx.SetArgument(4, uintptr_t(0));
	LUA_EXC_WRAP_START(0x000000006c8d4458)
	nCtx.Invoke(L, 0x000000006c8d4458);
	LUA_EXC_WRAP_END(0x000000006c8d4458)
	return 0;
}

int Lua_Native_0x78935a27(lua_State* L) // SetPedHeadOverlayColor
{
	static LuaNativeWrapper nW(0x0000000078935a27);
	LuaNativeContext nCtx(&nW, 5);
	nCtx.SetArgument(0, LuaArgumentParser::ParseArgument<lua_Integer>(L, 1)); // ped
	nCtx.SetArgument(1, LuaArgumentParser::ParseArgument<lua_Integer>(L, 2)); // overlayID
	nCtx.SetArgument(2, LuaArgumentParser::ParseArgument<lua_Integer>(L, 3)); // colorType
	nCtx.SetArgument(3, LuaArgumentParser::ParseArgument<lua_Integer>(L, 4)); // colorID
	nCtx.SetArgument(4, LuaArgumentParser::ParseArgument<lua_Integer>(L, 5)); // secondColorID
	nCtx.SetArgument(5, uintptr_t(0));
	nCtx.SetArgument(6, uintptr_t(0));
	LUA_EXC_WRAP_START(0x0000000078935a27)
	nCtx.Invoke(L, 0x0000000078935a27);
	LUA_EXC_WRAP_END(0x0000000078935a27)
	return 0;
}

static const Lua_NativeMap natives = {
	{ "AddBlipForCoord", Lua_Native_0xc6f43d0e },

	{ "AddBlipForEntity", Lua_Native_0x30822554 },

	{ "AddBlipForRadius", Lua_Native_0x4626756c },

	{ "AddPedDecorationFromHashes", Lua_Native_0x70559ac7 },
	{ "ApplyPedOverlay", Lua_Native_0x70559ac7 },
	{ "SetPedDecoration", Lua_Native_0x70559ac7 },

	{ "ApplyForceToEntity", Lua_Native_0xc1c0855a },

	{ "CancelEvent", Lua_Native_0xfa29d35d },

	{ "CanPlayerStartCommerceSession", Lua_Native_0x429461c3 },

	{ "ClearPedProp", Lua_Native_0x2d23d743 },

	{ "ClearPedSecondaryTask", Lua_Native_0xa635f451 },

	{ "ClearPedTasks", Lua_Native_0xde3316ab },

	{ "ClearPedTasksImmediately", Lua_Native_0xbc045625 },

	{ "ClearPlayerWantedLevel", Lua_Native_0x54ea5bcc },

	{ "CreateObject", Lua_Native_0x2f7aa05c },

	{ "CreateObjectNoOffset", Lua_Native_0x58040420 },

	{ "CreatePed", Lua_Native_0x389ef71 },

	{ "CreatePedInsideVehicle", Lua_Native_0x3000f092 },

	{ "CreateVehicle", Lua_Native_0xdd75460a },

	{ "CreateVehicleServerSetter", Lua_Native_0x6ae51d4b },


	{ "DeleteFunctionReference", Lua_Native_0x1e86f206 },

	{ "DeleteResourceKvp", Lua_Native_0x7389b5df },

	{ "DeleteResourceKvpNoSync", Lua_Native_0x4152c90 },

	{ "DeleteTrain", Lua_Native_0x523ba3da },

	{ "DoesBoatSinkWhenWrecked", Lua_Native_0x43f15989 },

	{ "DoesEntityExist", Lua_Native_0x3ac90869 },

	{ "DoesPlayerExist", Lua_Native_0x12038599 },

	{ "DoesPlayerOwnSku", Lua_Native_0x167aba27 },

	{ "DoesPlayerOwnSkuExt", Lua_Native_0xdef0480b },

	{ "DoesTrainStopAtStations", Lua_Native_0x77cc80dc },

	{ "DropPlayer", Lua_Native_0xba0613e1 },

	{ "DuplicateFunctionReference", Lua_Native_0xf4e2079d },

	{ "EnableEnhancedHostSupport", Lua_Native_0xf97b1c93 },

	{ "EndFindKvp", Lua_Native_0xb3210203 },

	{ "EnsureEntityStateBag", Lua_Native_0x3bb78f05 },

	{ "ExecuteCommand", Lua_Native_0x561c060b },

	{ "FindKvp", Lua_Native_0xbd7bebc5 },

	{ "FlagServerAsPrivate", Lua_Native_0x13b6855d },

	{ "FlushResourceKvp", Lua_Native_0xe27c97a0 },

	{ "FreezeEntityPosition", Lua_Native_0x65c16d57 },

	{ "GetAirDragMultiplierForPlayersVehicle", Lua_Native_0x62fc38d0 },

	{ "GetAllObjects", Lua_Native_0x6886c3fe },

	{ "GetAllPeds", Lua_Native_0xb8584fef },

	{ "GetAllVehicles", Lua_Native_0x332169f5 },

	{ "GetBlipSprite", Lua_Native_0x72ff2e73 },

	{ "GetConsoleBuffer", Lua_Native_0xe57429fa },

	{ "GetConvar", Lua_Native_0x6ccd2564 },

	{ "GetConvarBool", Lua_Native_0x7e8ebfe5 },

	{ "GetConvarFloat", Lua_Native_0x9e666d },

	{ "GetConvarInt", Lua_Native_0x935c0ab2 },

	{ "GetCurrentPedWeapon", Lua_Native_0xb0237302 },

	{ "GetCurrentResourceName", Lua_Native_0xe5e9ebbb },

	{ "GetEntityAttachedTo", Lua_Native_0xfe1589f9 },

	{ "GetEntityCollisionDisabled", Lua_Native_0xe8c0c629 },

	{ "GetEntityCoords", Lua_Native_0x1647f1cb },

	{ "GetEntityFromStateBagName", Lua_Native_0x4bdf1867 },

	{ "GetEntityHeading", Lua_Native_0x972cc383 },

	{ "GetEntityHealth", Lua_Native_0x8e3222b7 },

	{ "GetEntityMaxHealth", Lua_Native_0xc7ae6aa1 },

	{ "GetEntityModel", Lua_Native_0xdafcb3ec },

	{ "GetEntityOrphanMode", Lua_Native_0xd16ea02f },

	{ "GetEntityPopulationType", Lua_Native_0xfc30ddff },

	{ "GetEntityRemoteSyncedScenesAllowed", Lua_Native_0x91b38fb6 },

	{ "GetEntityRotation", Lua_Native_0x8ff45b04 },

	{ "GetEntityRotationVelocity", Lua_Native_0x9bf8a73f },

	{ "GetEntityRoutingBucket", Lua_Native_0xed4b0486 },

	{ "GetEntityScript", Lua_Native_0xb7f70784 },

	{ "GetEntitySpeed", Lua_Native_0x9e1e4798 },

	{ "GetEntityType", Lua_Native_0xb1bd08d },

	{ "GetEntityVelocity", Lua_Native_0xc14c9b6b },

	{ "GetGameBuildNumber", Lua_Native_0x804b9f7b },

	{ "GetGameName", Lua_Native_0xe8eaa18b },

	{ "GetGamePool", Lua_Native_0x2b9d4f50 },

	{ "GetGameTimer", Lua_Native_0xa4ea0691 },

	{ "GetHashKey", Lua_Native_0x98eff6f1 },

	{ "GetHeliBodyHealth", Lua_Native_0xa886495d },

	{ "GetHeliDisableExplodeFromBodyDamage", Lua_Native_0x82afc0a3 },

	{ "GetHeliEngineHealth", Lua_Native_0xa0fa0354 },

	{ "GetHeliGasTankHealth", Lua_Native_0xd4ec7858 },

	{ "GetHeliMainRotorDamageScale", Lua_Native_0xc37d668 },

	{ "GetHeliMainRotorHealth", Lua_Native_0xf01e2aab },

	{ "GetHeliPitchControl", Lua_Native_0x1944ac95 },

	{ "GetHeliRearRotorDamageScale", Lua_Native_0xc40161e2 },

	{ "GetHeliRearRotorHealth", Lua_Native_0x33ee6e2b },

	{ "GetHeliRollControl", Lua_Native_0x12948de9 },

	{ "GetHeliTailRotorDamageScale", Lua_Native_0x22239130 },

	{ "GetHeliTailRotorHealth", Lua_Native_0xa41bc13d },

	{ "GetHeliThrottleControl", Lua_Native_0x8e86238d },

	{ "GetHeliYawControl", Lua_Native_0x8fdc0768 },

	{ "GetHostId", Lua_Native_0x5f70f5a3 },

	{ "GetInstanceId", Lua_Native_0x9f1c4383 },

	{ "GetInvokingResource", Lua_Native_0x4d52fe5b },

	{ "GetIsHeliEngineRunning", Lua_Native_0x3efe38d1 },

	{ "GetIsVehicleEngineRunning", Lua_Native_0x7dc6d022 },

	{ "GetIsVehiclePrimaryColourCustom", Lua_Native_0xd7ec8760 },

	{ "GetIsVehicleSecondaryColourCustom", Lua_Native_0x288ad228 },

	{ "GetLandingGearState", Lua_Native_0xa6f02670 },

	{ "GetLastPedInVehicleSeat", Lua_Native_0xf7c6792d },

	{ "GetNetTypeFromEntity", Lua_Native_0x23b2a641 },

	{ "GetNumPlayerIdentifiers", Lua_Native_0xff7f66ab },

	{ "GetNumPlayerIndices", Lua_Native_0x63d13184 },

	{ "GetNumPlayerTokens", Lua_Native_0x619e4a3d },

	{ "GetNumResources", Lua_Native_0x863f27b },

	{ "GetNumResourceMetadata", Lua_Native_0x776e864 },

	{ "GetPasswordHash", Lua_Native_0x23473ea4 },

	{ "GetPedArmour", Lua_Native_0x2ce311a7 },

	{ "GetPedCauseOfDeath", Lua_Native_0x63458c27 },

	{ "GetPedDesiredHeading", Lua_Native_0xc182f76e },

	{ "GetPedInVehicleSeat", Lua_Native_0x388fde9a },

	{ "GetPedMaxHealth", Lua_Native_0xa45b6c8d },

	{ "GetPedRelationshipGroupHash", Lua_Native_0x354f283c },

	{ "GetPedScriptTaskCommand", Lua_Native_0x84fe084 },

	{ "GetPedScriptTaskStage", Lua_Native_0x44b0e5e2 },

	{ "GetPedSourceOfDamage", Lua_Native_0x535db43f },

	{ "GetPedSourceOfDeath", Lua_Native_0x84adf9eb },

	{ "GetPedSpecificTaskType", Lua_Native_0x7f4563d3 },

	{ "GetPedStealthMovement", Lua_Native_0x40321b83 },

	{ "GetPlayerCameraRotation", Lua_Native_0x433c765d },

	{ "GetPlayerEndpoint", Lua_Native_0xfee404f9 },

	{ "GetPlayerFakeWantedLevel", Lua_Native_0x98d244 },

	{ "GetPlayerFocusPos", Lua_Native_0x586f80ff },

	{ "GetPlayerFromIndex", Lua_Native_0xc8a9ce08 },

	{ "GetPlayerFromStateBagName", Lua_Native_0xa56135e0 },

	{ "GetPlayerGuid", Lua_Native_0xe52d9680 },

	{ "GetPlayerIdentifier", Lua_Native_0x7302dbcf },

	{ "GetPlayerIdentifierByType", Lua_Native_0xa61c8fc6 },

	{ "GetPlayerInvincible", Lua_Native_0x680c90ee },

	{ "GetPlayerLastMsg", Lua_Native_0x427e8e6a },

	{ "GetPlayerMaxArmour", Lua_Native_0x2a50657 },

	{ "GetPlayerMaxHealth", Lua_Native_0x8154e470 },

	{ "GetPlayerMeleeWeaponDamageModifier", Lua_Native_0x8689a825 },

	{ "GetPlayerName", Lua_Native_0x406b4b20 },

	{ "GetPlayerPed", Lua_Native_0x6e31e993 },

	{ "GetPlayerPeerStatistics", Lua_Native_0x9a928294 },

	{ "GetPlayerPing", Lua_Native_0xff1290d4 },

	{ "GetPlayerRoutingBucket", Lua_Native_0x52441c34 },

	{ "GetPlayerTeam", Lua_Native_0x9873e404 },

	{ "GetPlayerTimeInPursuit", Lua_Native_0x7ade63e1 },

	{ "GetPlayerTimeOnline", Lua_Native_0x67d2e605 },

	{ "GetPlayerToken", Lua_Native_0x54c06897 },

	{ "GetPlayerWantedCentrePosition", Lua_Native_0x821f2d2c },

	{ "GetPlayerWantedLevel", Lua_Native_0xbdcdd163 },

	{ "GetPlayerWeaponDamageModifier", Lua_Native_0x2a3d7cda },

	{ "GetPlayerWeaponDefenseModifier", Lua_Native_0xf1543251 },

	{ "GetPlayerWeaponDefenseModifier2", Lua_Native_0x986b65ff },

	{ "GetRegisteredCommands", Lua_Native_0xd4bef069 },

	{ "GetResourceByFindIndex", Lua_Native_0x387246b7 },

	{ "GetResourceCommands", Lua_Native_0x97628584 },

	{ "GetResourceKvpFloat", Lua_Native_0x35bdceea },

	{ "GetResourceKvpInt", Lua_Native_0x557b586a },

	{ "GetResourceKvpString", Lua_Native_0x5240da5a },

	{ "GetResourceMetadata", Lua_Native_0x964bab1d },

	{ "GetResourcePath", Lua_Native_0x61dcf017 },

	{ "GetResourceState", Lua_Native_0x4039b485 },

	{ "GetSelectedPedWeapon", Lua_Native_0xd240123e },

	{ "GetStateBagKeys", Lua_Native_0x78d864c7 },

	{ "GetStateBagValue", Lua_Native_0x637f4c75 },

	{ "GetThrusterSideRcsThrottle", Lua_Native_0x1c939e87 },

	{ "GetThrusterThrottle", Lua_Native_0x94e24c96 },

	{ "GetTrainBackwardCarriage", Lua_Native_0x456e34a },

	{ "GetTrainCarriageEngine", Lua_Native_0x95070fa },

	{ "GetTrainCarriageIndex", Lua_Native_0x4b8285cf },

	{ "GetTrainCruiseSpeed", Lua_Native_0xa4921ef5 },

	{ "GetTrainDirection", Lua_Native_0x8daf79b6 },

	{ "GetTrainForwardCarriage", Lua_Native_0x24dc88d9 },

	{ "GetTrainState", Lua_Native_0x81b50033 },

	{ "GetTrainTrackIndex", Lua_Native_0x9aa339d },

	{ "GetVehicleBodyHealth", Lua_Native_0x2b2fcc28 },

	{ "GetVehicleColours", Lua_Native_0x40d82d88 },

	{ "GetVehicleCustomPrimaryColour", Lua_Native_0x1c2b9fef },

	{ "GetVehicleCustomSecondaryColour", Lua_Native_0x3ff247a2 },

	{ "GetVehicleDirtLevel", Lua_Native_0xfd15c065 },

	{ "GetVehicleDoorsLockedForPlayer", Lua_Native_0x1dc50247 },

	{ "GetVehicleDoorLockStatus", Lua_Native_0xd72cef2 },

	{ "GetVehicleDoorStatus", Lua_Native_0x6e35c49c },

	{ "GetVehicleEngineHealth", Lua_Native_0x8880038a },

	{ "GetVehicleExtraColours", Lua_Native_0x80e4659b },

	{ "GetVehicleFlightNozzlePosition", Lua_Native_0xad40ad55 },

	{ "GetVehicleHandbrake", Lua_Native_0x483b013c },

	{ "GetVehicleHeadlightsColour", Lua_Native_0xd7147656 },

	{ "GetVehicleHomingLockonState", Lua_Native_0xfbde9fd8 },

	{ "GetVehicleHornType", Lua_Native_0xdea49773 },

	{ "GetVehicleLightsState", Lua_Native_0x7c278621 },

	{ "GetVehicleLivery", Lua_Native_0xec82a51d },

	{ "GetVehicleLockOnTarget", Lua_Native_0x4a557117 },

	{ "GetVehicleNeonColour", Lua_Native_0xd9319dcb },

	{ "GetVehicleNeonEnabled", Lua_Native_0x684bdbf2 },

	{ "GetVehicleNumberPlateText", Lua_Native_0xe8522d58 },

	{ "GetVehicleNumberPlateTextIndex", Lua_Native_0x499747b6 },

	{ "GetVehiclePedIsIn", Lua_Native_0xafe92319 },

	{ "GetVehiclePetrolTankHealth", Lua_Native_0xe41595ce },

	{ "GetVehicleRadioStationIndex", Lua_Native_0x57037960 },

	{ "GetVehicleRoofLivery", Lua_Native_0x872cf42 },

	{ "GetVehicleSteeringAngle", Lua_Native_0x1382fcea },

	{ "GetVehicleTotalRepairs", Lua_Native_0x9963d5f9 },

	{ "GetVehicleType", Lua_Native_0xa273060e },

	{ "GetVehicleTyreSmokeColor", Lua_Native_0x75280015 },

	{ "GetVehicleWheelType", Lua_Native_0xda58d7ae },

	{ "GetVehicleWindowTint", Lua_Native_0x13d53892 },

	{ "GiveWeaponComponentToPed", Lua_Native_0x3e1e286d },

	{ "GiveWeaponToPed", Lua_Native_0xc4d88a85 },

	{ "HasEntityBeenMarkedAsNoLongerNeeded", Lua_Native_0x9c9a3be0 },

	{ "HasVehicleBeenDamagedByBullets", Lua_Native_0xb8af3137 },

	{ "HasVehicleBeenOwnedByPlayer", Lua_Native_0xe4e83a5b },

	{ "IsAceAllowed", Lua_Native_0x7ebb9929 },

	{ "IsBoatAnchoredAndFrozen", Lua_Native_0xd5c39ee6 },

	{ "IsBoatWrecked", Lua_Native_0x9049db44 },

	{ "IsDuplicityVersion", Lua_Native_0xcf24c52e },

	{ "IsEntityPositionFrozen", Lua_Native_0xedbe6add },

	{ "IsEntityVisible", Lua_Native_0x120b4ed5 },

	{ "IsFlashLightOn", Lua_Native_0x76876154 },

	{ "IsHeliTailBoomBreakable", Lua_Native_0x23e46bd7 },

	{ "IsHeliTailBoomBroken", Lua_Native_0x2c59f987 },

	{ "IsPedAPlayer", Lua_Native_0x404794ca },

	{ "IsPedHandcuffed", Lua_Native_0x25865633 },

	{ "IsPedRagdoll", Lua_Native_0xc833bbe1 },

	{ "IsPedStrafing", Lua_Native_0xefeed13c },

	{ "IsPedUsingActionMode", Lua_Native_0x5ae7eda2 },

	{ "IsPlayerAceAllowed", Lua_Native_0xdedae23d },

	{ "IsPlayerCommerceInfoLoaded", Lua_Native_0xbefe93f4 },

	{ "IsPlayerCommerceInfoLoadedExt", Lua_Native_0x1d14f4fe },

	{ "IsPlayerEvadingWantedLevel", Lua_Native_0x89a3881a },

	{ "IsPlayerInFreeCamMode", Lua_Native_0x1f14f2ac },

	{ "IsPlayerUsingSuperJump", Lua_Native_0xc7d2c20c },

	{ "IsPrincipalAceAllowed", Lua_Native_0x37cf52ce },

	{ "IsTrainCaboose", Lua_Native_0xfa9336e5 },

	{ "IsVehicleEngineStarting", Lua_Native_0xbb340d04 },

	{ "IsVehicleExtraTurnedOn", Lua_Native_0x42098b5 },

	{ "IsVehicleSirenOn", Lua_Native_0x25eb5873 },

	{ "IsVehicleTyreBurst", Lua_Native_0x48c80210 },

	{ "IsVehicleWindowIntact", Lua_Native_0xac4ef23d },

	{ "LoadPlayerCommerceData", Lua_Native_0xa8f63eab },

	{ "LoadPlayerCommerceDataExt", Lua_Native_0x7995539e },

	{ "LoadResourceFile", Lua_Native_0x76a9ee1f },

	{ "MumbleCreateChannel", Lua_Native_0x262663c5 },

	{ "MumbleIsPlayerMuted", Lua_Native_0x1d5d50c2 },

	{ "MumbleSetPlayerMuted", Lua_Native_0xcc6c2eb1 },

	{ "NetworkGetEntityFromNetworkId", Lua_Native_0x5b912c3f },

	{ "NetworkGetEntityOwner", Lua_Native_0x526fee31 },

	{ "NetworkGetFirstEntityOwner", Lua_Native_0x1e546224 },

	{ "NetworkGetNetworkIdFromEntity", Lua_Native_0x9e35dab6 },

	{ "NetworkGetVoiceProximityOverrideForPlayer", Lua_Native_0xffeef513 },

	{ "PerformHttpRequestInternal", Lua_Native_0x8e8cc653 },

	{ "PrintStructuredTrace", Lua_Native_0x90892ded },

	{ "ProfilerEnterScope", Lua_Native_0xc795a4a9 },

	{ "ProfilerExitScope", Lua_Native_0xb39ca35c },

	{ "ProfilerIsRecording", Lua_Native_0xf8b7d7bb },

	{ "RegisterResourceAsset", Lua_Native_0x9862b266 },

	{ "RegisterResourceAsEventHandler", Lua_Native_0xd233a168 },

	{ "RemoveAllPedWeapons", Lua_Native_0xa44ce817 },

	{ "RemoveConvarChangeListener", Lua_Native_0xeac49841 },

	{ "RemoveStateBagChangeHandler", Lua_Native_0xd36be661 },

	{ "RemoveWeaponComponentFromPed", Lua_Native_0x412aa00d },

	{ "RemoveWeaponFromPed", Lua_Native_0x9c37f220 },

	{ "RequestPlayerCommerceSession", Lua_Native_0x96f93cce },

	{ "SaveResourceFile", Lua_Native_0xa09e7e7b },

	{ "ScheduleResourceTick", Lua_Native_0xb88a73ad },

	{ "SetBlipSprite", Lua_Native_0x8dbbb0b9 },

	{ "SetConvar", Lua_Native_0x341b16d2 },

	{ "SetConvarReplicated", Lua_Native_0xf292858c },

	{ "SetConvarServerInfo", Lua_Native_0x9338d547 },

	{ "SetCurrentPedWeapon", Lua_Native_0xb8278882 },

	{ "SetEntityCoords", Lua_Native_0xdf70b41b },

	{ "SetEntityDistanceCullingRadius", Lua_Native_0xd3a183a3 },

	{ "SetEntityHeading", Lua_Native_0xe0ff064d },

	{ "SetEntityIgnoreRequestControlFilter", Lua_Native_0x9f7f8d36 },

	{ "SetEntityOrphanMode", Lua_Native_0x489e9162 },

	{ "SetEntityRemoteSyncedScenesAllowed", Lua_Native_0xd3fc9d88 },

	{ "SetEntityRotation", Lua_Native_0xa345efe },

	{ "SetEntityRoutingBucket", Lua_Native_0x635e5289 },

	{ "SetEntityVelocity", Lua_Native_0xff5a1988 },

	{ "SetGameType", Lua_Native_0xf90b7469 },

	{ "SetMapName", Lua_Native_0xb7ba82dc },

	{ "SetPedAmmo", Lua_Native_0xbf90df1a },

	{ "SetPedArmour", Lua_Native_0x4e3a0cc4 },

	{ "SetPedCanRagdoll", Lua_Native_0xcf1384c4 },

	{ "SetPedComponentVariation", Lua_Native_0xd4f7b05c },

	{ "SetPedConfigFlag", Lua_Native_0x9cfbe10d },

	{ "SetPedDefaultComponentVariation", Lua_Native_0xc866a984 },

	{ "SetPedHairTint", Lua_Native_0xa23fe32c },
	{ "SetPedHairColor", Lua_Native_0xa23fe32c },

	{ "SetPedHeadBlendData", Lua_Native_0x60746b88 },

	{ "SetPedHeadOverlay", Lua_Native_0xd28dba90 },

	{ "SetPedIntoVehicle", Lua_Native_0x7500c79 },

	{ "SetPedPropIndex", Lua_Native_0x829f2e2 },

	{ "SetPedRandomComponentVariation", Lua_Native_0x4111ba46 },

	{ "SetPedRandomProps", Lua_Native_0xe3318e0e },

	{ "SetPedResetFlag", Lua_Native_0xcff6ff66 },

	{ "SetPedToRagdoll", Lua_Native_0x83cb5052 },

	{ "SetPedToRagdollWithFall", Lua_Native_0xfa12e286 },

	{ "SetPlayerControl", Lua_Native_0xd17afcd8 },

	{ "SetPlayerCullingRadius", Lua_Native_0x8a2fbad4 },

	{ "SetPlayerInvincible", Lua_Native_0xdfb9a2a2 },

	{ "SetPlayerModel", Lua_Native_0x774a4c54 },

	{ "SetPlayerRoutingBucket", Lua_Native_0x6504eb38 },

	{ "SetPlayerWantedLevel", Lua_Native_0xb7a0914b },

	{ "SetResourceKvp", Lua_Native_0x21c7a35b },

	{ "SetResourceKvpFloat", Lua_Native_0x9add2938 },

	{ "SetResourceKvpFloatNoSync", Lua_Native_0x3517bfbe },

	{ "SetResourceKvpInt", Lua_Native_0x6a2b1e8 },

	{ "SetResourceKvpIntNoSync", Lua_Native_0x26aeb707 },

	{ "SetResourceKvpNoSync", Lua_Native_0xcf9a2ff },

	{ "SetRoutingBucketEntityLockdownMode", Lua_Native_0xa0f2201f },

	{ "SetRoutingBucketPopulationEnabled", Lua_Native_0xce51ac2c },

	{ "SetStateBagValue", Lua_Native_0x8d50e33a },

	{ "SetVehicleAlarm", Lua_Native_0x24877d84 },

	{ "SetVehicleBodyHealth", Lua_Native_0x920c2517 },

	{ "SetVehicleColours", Lua_Native_0x57f24253 },

	{ "SetVehicleColourCombination", Lua_Native_0xa557aead },

	{ "SetVehicleCustomPrimaryColour", Lua_Native_0x8df9f9bc },

	{ "SetVehicleCustomSecondaryColour", Lua_Native_0x9d77259e },

	{ "SetVehicleDirtLevel", Lua_Native_0x2b39128b },

	{ "SetVehicleDoorsLocked", Lua_Native_0x4cdd35d0 },

	{ "SetVehicleDoorBroken", Lua_Native_0x8147fea7 },

	{ "SetVehicleNumberPlateText", Lua_Native_0x400f9556 },

	{ "StartFindKvp", Lua_Native_0xdd379006 },

	{ "StartResource", Lua_Native_0x29b440dc },

	{ "StateBagHasKey", Lua_Native_0x12a330 },

	{ "StopResource", Lua_Native_0x21783161 },

	{ "TaskCombatPed", Lua_Native_0xcb0d8932 },

	{ "TaskDriveBy", Lua_Native_0x2b84d1c4 },

	{ "TaskEnterVehicle", Lua_Native_0xb8689b4e },

	{ "TaskEveryoneLeaveVehicle", Lua_Native_0xc1971f30 },

	{ "TaskGoStraightToCoord", Lua_Native_0x80a9e7a7 },

	{ "TaskGoToCoordAnyMeans", Lua_Native_0xf91df93b },

	{ "TaskGoToEntity", Lua_Native_0x374827c2 },

	{ "TaskHandsUp", Lua_Native_0x8dcc19c5 },

	{ "TaskLeaveAnyVehicle", Lua_Native_0xdbdd79fa },

	{ "TaskLeaveVehicle", Lua_Native_0x7b1141c6 },

	{ "TaskPlayAnim", Lua_Native_0x5ab552c6 },

	{ "TaskPlayAnimAdvanced", Lua_Native_0x3ddeb0e6 },

	{ "TaskReactAndFleePed", Lua_Native_0x8a632bd8 },

	{ "TaskShootAtCoord", Lua_Native_0x601c22e3 },

	{ "TaskShootAtEntity", Lua_Native_0xac0631c9 },

	{ "TaskWarpPedIntoVehicle", Lua_Native_0x65d4a35d },

	{ "TempBanPlayer", Lua_Native_0x1e35dbba },

	{ "TriggerClientEventInternal", Lua_Native_0x2f7a49e6 },

	{ "TriggerEventInternal", Lua_Native_0x91310870 },

	{ "TriggerLatentClientEventInternal", Lua_Native_0x70b35890 },

	{ "VerifyPasswordHash", Lua_Native_0x2e310acd },

	{ "WasEventCanceled", Lua_Native_0x58382a19 },

	{ "AddBlipForArea", Lua_Native_0x6228f159 },
	{ "N_0xce5d0e5e315db238", Lua_Native_0x6228f159 },

	{ "SetPedEyeColor", Lua_Native_0xec09db1b },

	{ "SetPedFaceFeature", Lua_Native_0x6c8d4458 },

	{ "SetPedHeadOverlayColor", Lua_Native_0x78935a27 },

};
