# GangHaiCity Content Server

Đây là server API local thay thế cho CFX API, đư<PERSON><PERSON> sử dụng để phục vụ các file cần thiết cho GangHaiCity khi lần đầu chạy.

## Tính năng

- Thay thế hoàn toàn CFX API endpoints
- <PERSON><PERSON><PERSON> vụ các file từ thư mục build local
- Tự động scan và tạo danh sách file
- Tính toán hash cho từng file
- API tương thích với client GangHaiCity

## Cài đặt

1. Cài đặt Node.js (phiên bản 16 trở lên)
2. Chạy lệnh cài đặt dependencies:

```bash
cd server
npm install
```

## Chạy server

### Development mode (với auto-reload):
```bash
npm run dev
```

### Production mode:
```bash
npm start
```

Server sẽ chạy trên `http://localhost:3001`

## API Endpoints

### 1. Health Check
```
GET /health
```
<PERSON><PERSON><PERSON> tra trạng thái server và số lượng file có sẵn.

### 2. <PERSON>ache Heads (Version Info)
```
GET /updates/heads/:cacheName/:channel
```
Trả về thông tin phiên bản cho cache được yêu cầu.

Ví dụ: `GET /updates/heads/fivereborn/production`

### 3. Cache Content (File List)
```
GET /updates/caches/:cacheName/:channel
```
Trả về danh sách file XML cho cache được yêu cầu.

Ví dụ: `GET /updates/caches/fivereborn/production`

### 4. File Download
```
GET /updates/:hash1/:hash2/:fullHash
```
Tải xuống file dựa trên hash SHA256.

Ví dụ: `GET /updates/ab/cd/abcd1234...`

## Cấu trúc thư mục

```
server/
├── package.json          # Dependencies và scripts
├── server.js             # Main server file
├── README.md             # Tài liệu này
└── content/              # Thư mục cache (tự động tạo)
```

## Cách hoạt động

1. Server tự động scan thư mục `code/bin/five/release/`
2. Tính toán hash SHA256 cho mỗi file
3. Tạo danh sách file tương thích với format CFX
4. Phục vụ file khi client yêu cầu

## Lưu ý

- Server cần được chạy trước khi khởi động GangHaiCity
- Đảm bảo port 3001 không bị sử dụng bởi ứng dụng khác
- File sẽ được phục vụ trực tiếp từ thư mục build, không có compression

## Troubleshooting

### Server không khởi động được
- Kiểm tra port 3001 có bị chiếm dụng không
- Đảm bảo Node.js đã được cài đặt đúng

### Client không tải được file
- Kiểm tra server có đang chạy không (`GET /health`)
- Kiểm tra đường dẫn đến thư mục release có đúng không
- Xem log server để debug

### File không được tìm thấy
- Đảm bảo file tồn tại trong thư mục `code/bin/five/release/`
- Kiểm tra quyền đọc file
- Restart server để rescan file list
