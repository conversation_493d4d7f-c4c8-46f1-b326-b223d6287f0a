// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=faa11d38d989eb250f28646485cd2f0d38438807$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_RESOURCE_BUNDLE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_RESOURCE_BUNDLE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_resource_bundle_capi.h"
#include "include/cef_resource_bundle.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefResourceBundleCToCpp
    : public CefCToCppRefCounted<CefResourceBundleCToCpp,
                                 CefResourceBundle,
                                 cef_resource_bundle_t> {
 public:
  CefResourceBundleCToCpp();
  virtual ~CefResourceBundleCToCpp();

  // CefResourceBundle methods.
  CefString GetLocalizedString(int string_id) override;
  CefRefPtr<CefBinaryValue> GetDataResource(int resource_id) override;
  CefRefPtr<CefBinaryValue> GetDataResourceForScale(
      int resource_id,
      ScaleFactor scale_factor) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_RESOURCE_BUNDLE_CTOCPP_H_
