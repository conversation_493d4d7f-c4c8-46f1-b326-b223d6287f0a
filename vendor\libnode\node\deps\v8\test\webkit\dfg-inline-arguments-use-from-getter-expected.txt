# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves basic function.arguments functionality when said functionality is used from a getter.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b0, c0"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b1, c1"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b2, c2"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b3, c3"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b4, c4"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b5, c5"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b6, c6"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b7, c7"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b8, c8"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b9, c9"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b10, c10"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b11, c11"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b12, c12"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b13, c13"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b14, c14"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b15, c15"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b16, c16"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b17, c17"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b18, c18"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b19, c19"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b20, c20"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b21, c21"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b22, c22"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b23, c23"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b24, c24"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b25, c25"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b26, c26"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b27, c27"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b28, c28"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b29, c29"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b30, c30"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b31, c31"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b32, c32"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b33, c33"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b34, c34"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b35, c35"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b36, c36"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b37, c37"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b38, c38"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b39, c39"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b40, c40"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b41, c41"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b42, c42"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b43, c43"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b44, c44"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b45, c45"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b46, c46"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b47, c47"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b48, c48"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b49, c49"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b50, c50"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b51, c51"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b52, c52"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b53, c53"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b54, c54"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b55, c55"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b56, c56"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b57, c57"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b58, c58"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b59, c59"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b60, c60"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b61, c61"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b62, c62"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b63, c63"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b64, c64"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b65, c65"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b66, c66"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b67, c67"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b68, c68"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b69, c69"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b70, c70"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b71, c71"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b72, c72"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b73, c73"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b74, c74"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b75, c75"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b76, c76"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b77, c77"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b78, c78"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b79, c79"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b80, c80"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b81, c81"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b82, c82"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b83, c83"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b84, c84"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b85, c85"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b86, c86"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b87, c87"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b88, c88"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b89, c89"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b90, c90"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b91, c91"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b92, c92"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b93, c93"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b94, c94"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b95, c95"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b96, c96"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b97, c97"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b98, c98"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b99, c99"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b100, c100"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b101, c101"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b102, c102"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b103, c103"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b104, c104"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b105, c105"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b106, c106"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b107, c107"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b108, c108"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b109, c109"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b110, c110"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b111, c111"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b112, c112"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b113, c113"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b114, c114"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b115, c115"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b116, c116"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b117, c117"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b118, c118"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b119, c119"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b120, c120"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b121, c121"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b122, c122"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b123, c123"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b124, c124"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b125, c125"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b126, c126"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b127, c127"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b128, c128"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b129, c129"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b130, c130"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b131, c131"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b132, c132"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b133, c133"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b134, c134"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b135, c135"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b136, c136"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b137, c137"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b138, c138"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b139, c139"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b140, c140"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b141, c141"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b142, c142"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b143, c143"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b144, c144"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b145, c145"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b146, c146"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b147, c147"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b148, c148"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b149, c149"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b150, c150"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b151, c151"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b152, c152"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b153, c153"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b154, c154"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b155, c155"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b156, c156"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b157, c157"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b158, c158"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b159, c159"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b160, c160"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b161, c161"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b162, c162"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b163, c163"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b164, c164"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b165, c165"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b166, c166"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b167, c167"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b168, c168"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b169, c169"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b170, c170"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b171, c171"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b172, c172"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b173, c173"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b174, c174"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b175, c175"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b176, c176"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b177, c177"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b178, c178"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b179, c179"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b180, c180"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b181, c181"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b182, c182"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b183, c183"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b184, c184"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b185, c185"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b186, c186"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b187, c187"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b188, c188"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b189, c189"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b190, c190"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b191, c191"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b192, c192"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b193, c193"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b194, c194"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b195, c195"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b196, c196"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b197, c197"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b198, c198"
PASS argsToStr(bar(o, "b" + __i, "c" + __i)) is "[object Arguments]: [object Object], b199, c199"
PASS successfullyParsed is true

TEST COMPLETE

