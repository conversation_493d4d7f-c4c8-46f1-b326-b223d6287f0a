// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=9fb143d4df823ed6a7dfca295ce4ca2b5756df9c$
//

#include "libcef_dll/ctocpp/file_dialog_callback_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"
#include "libcef_dll/transfer_util.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
void CefFileDialogCallbackCToCpp::Continue(
    const std::vector<CefString>& file_paths) {
  shutdown_checker::AssertNotShutdown();

  cef_file_dialog_callback_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, cont))
    return;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: file_paths

  // Translate param: file_paths; type: string_vec_byref_const
  cef_string_list_t file_pathsList = cef_string_list_alloc();
  DCHECK(file_pathsList);
  if (file_pathsList)
    transfer_string_list_contents(file_paths, file_pathsList);

  // Execute
  _struct->cont(_struct, file_pathsList);

  // Restore param:file_paths; type: string_vec_byref_const
  if (file_pathsList)
    cef_string_list_free(file_pathsList);
}

NO_SANITIZE("cfi-icall") void CefFileDialogCallbackCToCpp::Cancel() {
  shutdown_checker::AssertNotShutdown();

  cef_file_dialog_callback_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, cancel))
    return;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->cancel(_struct);
}

// CONSTRUCTOR - Do not edit by hand.

CefFileDialogCallbackCToCpp::CefFileDialogCallbackCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefFileDialogCallbackCToCpp::~CefFileDialogCallbackCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_file_dialog_callback_t* CefCToCppRefCounted<
    CefFileDialogCallbackCToCpp,
    CefFileDialogCallback,
    cef_file_dialog_callback_t>::UnwrapDerived(CefWrapperType type,
                                               CefFileDialogCallback* c) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefFileDialogCallbackCToCpp,
                                   CefFileDialogCallback,
                                   cef_file_dialog_callback_t>::kWrapperType =
    WT_FILE_DIALOG_CALLBACK;
