#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      #a() { return 1; }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 45
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaConstant), U8(2),
                B(Star3),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(3), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
                B(LdaUndefined),
  /*   77 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["A"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class D {
      #d() { return 1; }
    }
    class E extends D {
      #e() { return 2; }
    }
  }
"
frame size: 7
parameter count: 1
bytecode array length: 89
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaConstant), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(5),
                B(PushContext), R(2),
                B(LdaConstant), U8(7),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(CreateClosure), U8(8), U8(2), U8(2),
                B(StaCurrentContextSlot), U8(2),
  /*   93 E> */ B(CreateClosure), U8(9), U8(3), U8(2),
                B(Star3),
                B(LdaConstant), U8(6),
                B(Star4),
                B(Mov), R(3), R(5),
                B(Mov), R(0), R(6),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(PopContext), R(2),
                B(Mov), R(5), R(1),
                B(LdaUndefined),
  /*  126 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["D"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["E"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A { foo() {} }
    class C extends A {
      #m() { return super.foo; }
    }
  }
"
frame size: 8
parameter count: 1
bytecode array length: 82
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star7),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(4),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(4),
                B(PushContext), R(2),
                B(LdaConstant), U8(6),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(CreateClosure), U8(7), U8(2), U8(2),
                B(StaCurrentContextSlot), U8(2),
  /*   77 E> */ B(CreateClosure), U8(8), U8(3), U8(2),
                B(Star3),
                B(LdaConstant), U8(5),
                B(Star4),
                B(Mov), R(3), R(5),
                B(Mov), R(0), R(6),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(StaCurrentContextSlot), U8(4),
                B(Ldar), R(5),
                B(StaCurrentContextSlot), U8(5),
                B(PopContext), R(2),
                B(Star1),
                B(LdaUndefined),
  /*  118 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["C"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

