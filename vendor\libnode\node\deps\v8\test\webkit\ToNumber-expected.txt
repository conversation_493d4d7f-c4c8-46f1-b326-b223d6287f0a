# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Test the JavaScript ToNumber operation.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS +undefined is NaN
PASS +null is 0
PASS +false is 0
PASS +true is 1
PASS +2 is 2
PASS +'' is 0
PASS +' ' is 0
PASS +' 1' is 1
PASS +'1 ' is 1
PASS +'x1' is NaN
PASS +'1x' is NaN
PASS +'0x1' is 1
PASS +'1x0' is NaN
PASS +(nullCharacter + '1') is NaN
PASS +('1' + nullCharacter) is NaN
PASS +('1' + nullCharacter + '1') is NaN
PASS +(nonASCIICharacter + '1') is NaN
PASS +('1' + nonASCIICharacter) is NaN
PASS +('1' + nonASCIICharacter + '1') is NaN
PASS +('1' + nonASCIINonSpaceCharacter) is NaN
PASS +(nonASCIINonSpaceCharacter + '1') is NaN
PASS +('1' + nonASCIINonSpaceCharacter + '1') is NaN
PASS +(illegalUTF16Sequence + '1') is NaN
PASS +('1' + illegalUTF16Sequence) is NaN
PASS +('1' + illegalUTF16Sequence + '1') is NaN
PASS +'inf' is NaN
PASS +'infinity' is NaN
PASS +'Inf' is NaN
PASS +'+inf' is NaN
PASS +'+infinity' is NaN
PASS +'+Inf' is NaN
PASS +'-inf' is NaN
PASS +'-infinity' is NaN
PASS +'-Inf' is NaN
PASS +'Infinity' is Infinity
PASS +'+Infinity' is Infinity
PASS +'-Infinity' is -Infinity
PASS +'++1' is NaN
PASS +'AB' is NaN
PASS +'0xAB' is 171
PASS +'1e1' is 10
PASS +'1E1' is 10
PASS +tab is 0
PASS +nbsp is 0
PASS +ff is 0
PASS +vt is 0
PASS +cr is 0
PASS +lf is 0
PASS +ls is 0
PASS +ps is 0
PASS +oghamSpaceMark is 0
PASS +mongolianVowelSeparator is NaN
PASS +enQuad is 0
PASS +emQuad is 0
PASS +enSpace is 0
PASS +emSpace is 0
PASS +threePerEmSpace is 0
PASS +fourPerEmSpace is 0
PASS +sixPerEmSpace is 0
PASS +figureSpace is 0
PASS +punctuationSpace is 0
PASS +thinSpace is 0
PASS +hairSpace is 0
PASS +narrowNoBreakSpace is 0
PASS +mediumMathematicalSpace is 0
PASS +ideographicSpace is 0
PASS +(tab + '1') is 1
PASS +(nbsp + '1') is 1
PASS +(ff + '1') is 1
PASS +(vt + '1') is 1
PASS +(cr + '1') is 1
PASS +(lf + '1') is 1
PASS +(ls + '1') is 1
PASS +(ps + '1') is 1
PASS +(oghamSpaceMark + '1') is 1
PASS +(mongolianVowelSeparator + '1') is NaN
PASS +(enQuad + '1') is 1
PASS +(emQuad + '1') is 1
PASS +(enSpace + '1') is 1
PASS +(emSpace + '1') is 1
PASS +(threePerEmSpace + '1') is 1
PASS +(fourPerEmSpace + '1') is 1
PASS +(sixPerEmSpace + '1') is 1
PASS +(figureSpace + '1') is 1
PASS +(punctuationSpace + '1') is 1
PASS +(thinSpace + '1') is 1
PASS +(hairSpace + '1') is 1
PASS +(narrowNoBreakSpace + '1') is 1
PASS +(mediumMathematicalSpace + '1') is 1
PASS +(ideographicSpace + '1') is 1
PASS +('1' + tab) is 1
PASS +('1' + nbsp) is 1
PASS +('1' + ff) is 1
PASS +('1' + vt) is 1
PASS +('1' + cr) is 1
PASS +('1' + lf) is 1
PASS +('1' + ls) is 1
PASS +('1' + ps) is 1
PASS +('1' + oghamSpaceMark) is 1
PASS +('1' + mongolianVowelSeparator) is NaN
PASS +('1' + enQuad) is 1
PASS +('1' + emQuad) is 1
PASS +('1' + enSpace) is 1
PASS +('1' + emSpace) is 1
PASS +('1' + threePerEmSpace) is 1
PASS +('1' + fourPerEmSpace) is 1
PASS +('1' + sixPerEmSpace) is 1
PASS +('1' + figureSpace) is 1
PASS +('1' + punctuationSpace) is 1
PASS +('1' + thinSpace) is 1
PASS +('1' + hairSpace) is 1
PASS +('1' + narrowNoBreakSpace) is 1
PASS +('1' + mediumMathematicalSpace) is 1
PASS +('1' + ideographicSpace) is 1
PASS +('1' + tab + '1') is NaN
PASS +('1' + nbsp + '1') is NaN
PASS +('1' + ff + '1') is NaN
PASS +('1' + vt + '1') is NaN
PASS +('1' + cr + '1') is NaN
PASS +('1' + lf + '1') is NaN
PASS +('1' + ls + '1') is NaN
PASS +('1' + ps + '1') is NaN
PASS +('1' + oghamSpaceMark + '1') is NaN
PASS +('1' + mongolianVowelSeparator + '1') is NaN
PASS +('1' + enQuad + '1') is NaN
PASS +('1' + emQuad + '1') is NaN
PASS +('1' + enSpace + '1') is NaN
PASS +('1' + emSpace + '1') is NaN
PASS +('1' + threePerEmSpace + '1') is NaN
PASS +('1' + fourPerEmSpace + '1') is NaN
PASS +('1' + sixPerEmSpace + '1') is NaN
PASS +('1' + figureSpace + '1') is NaN
PASS +('1' + punctuationSpace + '1') is NaN
PASS +('1' + thinSpace + '1') is NaN
PASS +('1' + hairSpace + '1') is NaN
PASS +('1' + narrowNoBreakSpace + '1') is NaN
PASS +('1' + mediumMathematicalSpace + '1') is NaN
PASS +('1' + ideographicSpace + '1') is NaN
PASS successfullyParsed is true

TEST COMPLETE

