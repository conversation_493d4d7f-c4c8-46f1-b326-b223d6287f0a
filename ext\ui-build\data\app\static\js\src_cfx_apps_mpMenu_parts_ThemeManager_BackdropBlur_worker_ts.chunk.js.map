{"version": 3, "file": "static/js/src_cfx_apps_mpMenu_parts_ThemeManager_BackdropBlur_worker_ts.chunk.js", "mappings": "mBAAA,MAAMA,EAAkB,CACtB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAGWC,EAAYC,IACvB,IAAIC,EAAQ,EACZ,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAIG,OAAQD,IAAK,CACnC,MAAME,EAAIJ,EAAIE,GAEdD,EAAgB,GAARA,EADMH,EAAgBO,QAAQD,E,CAGxC,OAAOH,CAAK,EAGDK,EAAW,CAACC,EAAWJ,KAClC,IAAIK,EAAS,GACb,IAAK,IAAIN,EAAI,EAAGA,GAAKC,EAAQD,IAAK,CAChC,IAAIO,EAASC,KAAKC,MAAMJ,GAAKG,KAAKE,IAAI,GAAIT,EAASD,GAAM,GACzDM,GAAUV,EAAgBY,KAAKC,MAAMF,G,CAEvC,OAAOD,CAAM,ECtGFK,EAAgBZ,IAC3B,IAAIa,EAAIb,EAAQ,IAChB,OAAIa,GAAK,OACAA,EAAI,MAEJJ,KAAKE,KAAKE,EAAI,MAAS,MAAO,I,EAI5BC,EAAgBd,IAC3B,IAAIa,EAAIJ,KAAKM,IAAI,EAAGN,KAAKO,IAAI,EAAGhB,IAChC,OAAIa,GAAK,SACAJ,KAAKQ,MAAU,MAAJJ,EAAY,IAAM,IAE7BJ,KAAKQ,MAA+C,KAAxC,MAAQR,KAAKE,IAAIE,EAAG,EAAI,KAAO,MAAe,G,EAMxDK,EAAU,CAACC,EAAaC,KAC9BD,EAHiC,GAAK,EAAI,GAGnCV,KAAKE,IAAIF,KAAKY,IAAIF,GAAMC,GCrB/B,MAAME,UAAwBC,MACnC,WAAAC,CAAYC,GACVC,MAAMD,GACNE,KAAKC,KAAO,kBACZD,KAAKF,QAAUA,CACjB,ECGF,MAgCMI,EAAY7B,IAChB,MACM8B,EAAQ9B,GAAS,EAAK,IACtB+B,EAAe,IAAR/B,EACb,MAAO,CAACY,EAHKZ,GAAS,IAGMY,EAAakB,GAAOlB,EAAamB,GAAM,EAG/DC,EAAW,CAAChC,EAAeiC,KAC/B,MAAMC,EAASzB,KAAKC,MAAMV,EAAQ,KAC5BmC,EAAS1B,KAAKC,MAAMV,EAAQ,IAAM,GAClCoC,EAASpC,EAAQ,GAQvB,MANY,CACVkB,GAASgB,EAAS,GAAK,EAAG,GAAOD,EACjCf,GAASiB,EAAS,GAAK,EAAG,GAAOF,EACjCf,GAASkB,EAAS,GAAK,EAAG,GAAOH,EAGzB,EAkEZ,EA/De,CACbI,EACAC,EACAC,EACAC,KAzDuB,CAACH,IACxB,IAAKA,GAAYA,EAASnC,OAAS,EACjC,MAAM,IAAIoB,EACR,qDAIJ,MAAMmB,EAAW3C,EAASuC,EAAS,IAC7BK,EAAOjC,KAAKC,MAAM+B,EAAW,GAAK,EAClCE,EAAQF,EAAW,EAAK,EAE9B,GAAIJ,EAASnC,SAAW,EAAI,EAAIyC,EAAOD,EACrC,MAAM,IAAIpB,EACR,uCACEe,EAASnC,2BACU,EAAI,EAAIyC,EAAOD,I,EA4CxCE,CAAiBP,GAEjBG,GAAgB,EAEhB,MAAMC,EAAW3C,EAASuC,EAAS,IAC7BK,EAAOjC,KAAKC,MAAM+B,EAAW,GAAK,EAClCE,EAAQF,EAAW,EAAK,EAGxBR,GADwBnC,EAASuC,EAAS,IACF,GAAK,IAE7CQ,EAAS,IAAIC,MAAMH,EAAOD,GAEhC,IAAK,IAAIzC,EAAI,EAAGA,EAAI4C,EAAO3C,OAAQD,IACjC,GAAU,IAANA,EAAS,CACX,MAAMD,EAAQF,EAASuC,EAASU,UAAU,EAAG,IAC7CF,EAAO5C,GAAK4B,EAAS7B,E,KAChB,CACL,MAAMA,EAAQF,EAASuC,EAASU,UAAU,EAAQ,EAAJ9C,EAAO,EAAQ,EAAJA,IACzD4C,EAAO5C,GAAK+B,EAAShC,EAAOiC,EAAeO,E,CAI/C,MAAMQ,EAAsB,EAARV,EACdW,EAAS,IAAIC,kBAAkBF,EAAcT,GAEnD,IAAK,IAAIY,EAAI,EAAGA,EAAIZ,EAAQY,IAC1B,IAAK,IAAIC,EAAI,EAAGA,EAAId,EAAOc,IAAK,CAC9B,IAAIC,EAAI,EACJC,EAAI,EACJC,EAAI,EAER,IAAK,IAAIC,EAAI,EAAGA,EAAId,EAAMc,IACxB,IAAK,IAAIvD,EAAI,EAAGA,EAAI0C,EAAM1C,IAAK,CAC7B,MAAMwD,EACJhD,KAAKiD,IAAKjD,KAAKkD,GAAKP,EAAInD,EAAKqC,GAC7B7B,KAAKiD,IAAKjD,KAAKkD,GAAKR,EAAIK,EAAKjB,GAC/B,IAAIqB,EAAQf,EAAO5C,EAAIuD,EAAIb,GAC3BU,GAAKO,EAAM,GAAKH,EAChBH,GAAKM,EAAM,GAAKH,EAChBF,GAAKK,EAAM,GAAKH,C,CAIpB,IAAII,EAAO/C,EAAauC,GACpBvB,EAAOhB,EAAawC,GACpBvB,EAAOjB,EAAayC,GAExBN,EAAO,EAAIG,EAAI,EAAID,EAAIH,GAAea,EACtCZ,EAAO,EAAIG,EAAI,EAAID,EAAIH,GAAelB,EACtCmB,EAAO,EAAIG,EAAI,EAAID,EAAIH,GAAejB,EACtCkB,EAAO,EAAIG,EAAI,EAAID,EAAIH,GAAe,G,CAG1C,OAAOC,CAAM,ECjHTa,EAAwB,CAC5Bb,EACAX,EACAC,EACAwB,KAEA,IAAIV,EAAI,EACJC,EAAI,EACJC,EAAI,EACR,MAAMP,EAXc,EAWAV,EAEpB,IAAK,IAAIc,EAAI,EAAGA,EAAId,EAAOc,IACzB,IAAK,IAAID,EAAI,EAAGA,EAAIZ,EAAQY,IAAK,CAC/B,MAAMM,EAAQM,EAAcX,EAAGD,GAC/BE,GACEI,EAAQ7C,EAAaqC,EAjBP,EAiB8BG,EAAI,EAAID,EAAIH,IAC1DM,GACEG,EAAQ7C,EAAaqC,EAnBP,EAmB8BG,EAAI,EAAID,EAAIH,IAC1DO,GACEE,EAAQ7C,EAAaqC,EArBP,EAqB8BG,EAAI,EAAID,EAAIH,G,CAI9D,IAAIgB,EAAQ,GAAK1B,EAAQC,GAEzB,MAAO,CAACc,EAAIW,EAAOV,EAAIU,EAAOT,EAAIS,EAAM,EC1B1CC,UAAaC,GAEbC,eAA6BC,GAC3BC,QAAQC,KAAK,wBAEb,MAAOrB,EAAQX,EAAOC,GAmCxB,SAAsBgC,GACpB,MAEMC,EAAI,IAEJC,EAAID,GAJCD,EAAMjC,MAAQiC,EAAMhC,QAIZ,EAIbmC,EAFS,IAAIC,gBAAgBH,EAAGC,GAEfG,WAAW,MAGlC,OAFAF,EAASG,UAAUN,EAAO,EAAG,EAAGC,EAAGC,GAE5B,CAACC,EAASI,aAAa,EAAG,EAAGN,EAAGC,GAAGM,KAAMP,EAAGC,EACrD,CAhDkCK,OAyBlCX,eAAyBa,GACvB,MAAMC,cACEC,MAAMF,EAAK,CACfG,KAAM,aAERC,OAEF,OAAOC,kBAAkBJ,EAC3B,CAjCqDK,CAAUlB,IAEvD/B,EAAW,EDoDJ,EACbY,EACAX,EACAC,EACAgD,EACAC,KAEA,GAAID,EAAa,GAAKA,EAAa,GAAKC,EAAa,GAAKA,EAAa,EACrE,MAAM,IAAIlE,EAAgB,iDAE5B,GAAIgB,EAAQC,EAAS,IAAMU,EAAO/C,OAChC,MAAM,IAAIoB,EAAgB,gDAG5B,IAAImE,EAA2C,GAC/C,IAAK,IAAItC,EAAI,EAAGA,EAAIqC,EAAYrC,IAC9B,IAAK,IAAIC,EAAI,EAAGA,EAAImC,EAAYnC,IAAK,CACnC,MAAMsC,EAAqB,GAALtC,GAAe,GAALD,EAAS,EAAI,EACvCwC,EAAS7B,EACbb,EACAX,EACAC,GACA,CAACtC,EAAWuD,IACVkC,EACAjF,KAAKiD,IAAKjD,KAAKkD,GAAKP,EAAInD,EAAKqC,GAC7B7B,KAAKiD,IAAKjD,KAAKkD,GAAKR,EAAIK,EAAKjB,KAEjCkD,EAAQG,KAAKD,E,CAIjB,MAAME,EAAKJ,EAAQ,GACbK,EAAKL,EAAQM,MAAM,GAEzB,IAKI9D,EALA+D,EAAO,GAMX,GAHAA,GAAQ3F,EADOkF,EAAa,EAAuB,GAAlBC,EAAa,GACnB,GAGvBM,EAAG5F,OAAS,EAAG,CACjB,IAAI+F,EAAqBxF,KAAKM,OAAO+E,EAAGI,KAAI/E,GAAOV,KAAKM,OAAOI,MAC3DgF,EAAwB1F,KAAKC,MAC/BD,KAAKM,IAAI,EAAGN,KAAKO,IAAI,GAAIP,KAAKC,MAA2B,IAArBuF,EAA2B,OAEjEhE,GAAgBkE,EAAwB,GAAK,IAC7CH,GAAQ3F,EAAS8F,EAAuB,E,MAExClE,EAAe,EACf+D,GAAQ3F,EAAS,EAAG,GA/EP,IAACL,EAwFhB,OANAgG,GAAQ3F,GAjFSS,GADDd,EAkFU6F,GAjFU,KAGhB,KAFH/E,EAAad,EAAM,KAEG,GADtBc,EAAad,EAAM,IA+EL,GAE/B8F,EAAGM,SAAQT,IACTK,GAAQ3F,EA9EK,EAACL,EAAsBiC,IAoBtB,GAnBHxB,KAAKC,MAChBD,KAAKM,IACH,EACAN,KAAKO,IAAI,GAAIP,KAAKC,MAA8C,EAAxCQ,EAAQlB,EAAM,GAAKiC,EAAc,IAAW,QAgBnD,GAAc,GAbtBxB,KAAKC,MAChBD,KAAKM,IACH,EACAN,KAAKO,IAAI,GAAIP,KAAKC,MAA8C,EAAxCQ,EAAQlB,EAAM,GAAKiC,EAAc,IAAW,QAG3DxB,KAAKC,MAChBD,KAAKM,IACH,EACAN,KAAKO,IAAI,GAAIP,KAAKC,MAA8C,EAAxCQ,EAAQlB,EAAM,GAAKiC,EAAc,IAAW,QA8DrDoE,CAASV,EAAQ1D,GAAe,EAAE,IAG9C+D,CAAI,EC9Ga,CAAO/C,EAAQX,EAAOC,EAVjC,EACA,GAHD,GACC,IAYP+D,EAAM,IAAI3B,gBAbJ,GACC,IAaP4B,EAAMD,EAAI1B,WAAW,MAErB4B,EAAcD,EAAKE,gBAhBb,GACC,IAgBbD,EAAYzB,KAAK2B,IAAIrE,GAErBkE,EAAKI,aAAaH,EAAa,EAAG,GAElC,IAAII,EAAqB,KAEzB,IACE,MAAMxB,QAAakB,EAAIO,gBACvBD,EAAME,IAAIC,gBAAgB3B,EAC5B,CAAE,MAAO4B,GACP3C,QAAQ4C,MAAMD,EAChB,CAEA3C,QAAQ6C,QAAQ,wBAEhBC,YAAYP,EACd,CA5BqCQ,CAAclD,EAAMa,K", "sources": ["webpack://cfxuirnw/./node_modules/blurhash/src/base83.ts", "webpack://cfxuirnw/./node_modules/blurhash/src/utils.ts", "webpack://cfxuirnw/./node_modules/blurhash/src/error.ts", "webpack://cfxuirnw/./node_modules/blurhash/src/decode.ts", "webpack://cfxuirnw/./node_modules/blurhash/src/encode.ts", "webpack://cfxuirnw/./src/cfx/apps/mpMenu/parts/ThemeManager/BackdropBlur.worker.ts"], "sourcesContent": ["const digitCharacters = [\n  \"0\",\n  \"1\",\n  \"2\",\n  \"3\",\n  \"4\",\n  \"5\",\n  \"6\",\n  \"7\",\n  \"8\",\n  \"9\",\n  \"A\",\n  \"B\",\n  \"C\",\n  \"D\",\n  \"E\",\n  \"F\",\n  \"G\",\n  \"H\",\n  \"I\",\n  \"J\",\n  \"K\",\n  \"L\",\n  \"M\",\n  \"N\",\n  \"O\",\n  \"P\",\n  \"Q\",\n  \"R\",\n  \"S\",\n  \"T\",\n  \"U\",\n  \"V\",\n  \"W\",\n  \"X\",\n  \"Y\",\n  \"Z\",\n  \"a\",\n  \"b\",\n  \"c\",\n  \"d\",\n  \"e\",\n  \"f\",\n  \"g\",\n  \"h\",\n  \"i\",\n  \"j\",\n  \"k\",\n  \"l\",\n  \"m\",\n  \"n\",\n  \"o\",\n  \"p\",\n  \"q\",\n  \"r\",\n  \"s\",\n  \"t\",\n  \"u\",\n  \"v\",\n  \"w\",\n  \"x\",\n  \"y\",\n  \"z\",\n  \"#\",\n  \"$\",\n  \"%\",\n  \"*\",\n  \"+\",\n  \",\",\n  \"-\",\n  \".\",\n  \":\",\n  \";\",\n  \"=\",\n  \"?\",\n  \"@\",\n  \"[\",\n  \"]\",\n  \"^\",\n  \"_\",\n  \"{\",\n  \"|\",\n  \"}\",\n  \"~\"\n];\n\nexport const decode83 = (str: String) => {\n  let value = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str[i];\n    const digit = digitCharacters.indexOf(c);\n    value = value * 83 + digit;\n  }\n  return value;\n};\n\nexport const encode83 = (n: number, length: number): string => {\n  var result = \"\";\n  for (let i = 1; i <= length; i++) {\n    let digit = (Math.floor(n) / Math.pow(83, length - i)) % 83;\n    result += digitCharacters[Math.floor(digit)];\n  }\n  return result;\n};\n", "export const sRGBToLinear = (value: number) => {\n  let v = value / 255;\n  if (v <= 0.04045) {\n    return v / 12.92;\n  } else {\n    return Math.pow((v + 0.055) / 1.055, 2.4);\n  }\n};\n\nexport const linearTosRGB = (value: number) => {\n  let v = Math.max(0, Math.min(1, value));\n  if (v <= 0.0031308) {\n    return Math.round(v * 12.92 * 255 + 0.5);\n  } else {\n    return Math.round((1.055 * Math.pow(v, 1 / 2.4) - 0.055) * 255 + 0.5);\n  }\n};\n\nexport const sign = (n: number) => (n < 0 ? -1 : 1);\n\nexport const signPow = (val: number, exp: number) =>\n  sign(val) * Math.pow(Math.abs(val), exp);\n", "export class ValidationError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"ValidationError\";\n    this.message = message;\n  }\n}\n", "import { decode83 } from \"./base83\";\nimport { sRGBToLinear, signPow, linearTosRGB } from \"./utils\";\nimport { ValidationError } from \"./error\";\n\n/**\n * Returns an error message if invalid or undefined if valid\n * @param blurhash\n */\nconst validateBlurhash = (blurhash: string) => {\n  if (!blurhash || blurhash.length < 6) {\n    throw new ValidationError(\n      \"The blurhash string must be at least 6 characters\"\n    );\n  }\n\n  const sizeFlag = decode83(blurhash[0]);\n  const numY = Math.floor(sizeFlag / 9) + 1;\n  const numX = (sizeFlag % 9) + 1;\n\n  if (blurhash.length !== 4 + 2 * numX * numY) {\n    throw new ValidationError(\n      `blurhash length mismatch: length is ${\n        blurhash.length\n      } but it should be ${4 + 2 * numX * numY}`\n    );\n  }\n};\n\nexport const isBlurhashValid = (\n  blurhash: string\n): { result: boolean; errorReason?: string } => {\n  try {\n    validateBlurhash(blurhash);\n  } catch (error) {\n    return { result: false, errorReason: error.message };\n  }\n\n  return { result: true };\n};\n\nconst decodeDC = (value: number) => {\n  const intR = value >> 16;\n  const intG = (value >> 8) & 255;\n  const intB = value & 255;\n  return [sRGBToLinear(intR), sRGBToLinear(intG), sRGBToLinear(intB)];\n};\n\nconst decodeAC = (value: number, maximumValue: number) => {\n  const quantR = Math.floor(value / (19 * 19));\n  const quantG = Math.floor(value / 19) % 19;\n  const quantB = value % 19;\n\n  const rgb = [\n    signPow((quantR - 9) / 9, 2.0) * maximumValue,\n    signPow((quantG - 9) / 9, 2.0) * maximumValue,\n    signPow((quantB - 9) / 9, 2.0) * maximumValue\n  ];\n\n  return rgb;\n};\n\nconst decode = (\n  blurhash: string,\n  width: number,\n  height: number,\n  punch?: number\n) => {\n  validateBlurhash(blurhash);\n\n  punch = punch | 1;\n\n  const sizeFlag = decode83(blurhash[0]);\n  const numY = Math.floor(sizeFlag / 9) + 1;\n  const numX = (sizeFlag % 9) + 1;\n\n  const quantisedMaximumValue = decode83(blurhash[1]);\n  const maximumValue = (quantisedMaximumValue + 1) / 166;\n\n  const colors = new Array(numX * numY);\n\n  for (let i = 0; i < colors.length; i++) {\n    if (i === 0) {\n      const value = decode83(blurhash.substring(2, 6));\n      colors[i] = decodeDC(value);\n    } else {\n      const value = decode83(blurhash.substring(4 + i * 2, 6 + i * 2));\n      colors[i] = decodeAC(value, maximumValue * punch);\n    }\n  }\n\n  const bytesPerRow = width * 4;\n  const pixels = new Uint8ClampedArray(bytesPerRow * height);\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let r = 0;\n      let g = 0;\n      let b = 0;\n\n      for (let j = 0; j < numY; j++) {\n        for (let i = 0; i < numX; i++) {\n          const basis =\n            Math.cos((Math.PI * x * i) / width) *\n            Math.cos((Math.PI * y * j) / height);\n          let color = colors[i + j * numX];\n          r += color[0] * basis;\n          g += color[1] * basis;\n          b += color[2] * basis;\n        }\n      }\n\n      let intR = linearTosRGB(r);\n      let intG = linearTosRGB(g);\n      let intB = linearTosRGB(b);\n\n      pixels[4 * x + 0 + y * bytesPerRow] = intR;\n      pixels[4 * x + 1 + y * bytesPerRow] = intG;\n      pixels[4 * x + 2 + y * bytesPerRow] = intB;\n      pixels[4 * x + 3 + y * bytesPerRow] = 255; // alpha\n    }\n  }\n  return pixels;\n};\n\nexport default decode;\n", "import { encode83 } from \"./base83\";\nimport { sRGBToLinear, signPow, linearTosRGB } from \"./utils\";\nimport { ValidationError } from \"./error\";\n\ntype NumberTriplet = [number, number, number];\n\nconst bytesPerPixel = 4;\n\nconst multiplyBasisFunction = (\n  pixels: Uint8ClampedArray,\n  width: number,\n  height: number,\n  basisFunction: (i: number, j: number) => number\n): NumberTriplet => {\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  const bytesPerRow = width * bytesPerPixel;\n\n  for (let x = 0; x < width; x++) {\n    for (let y = 0; y < height; y++) {\n      const basis = basisFunction(x, y);\n      r +=\n        basis * sRGBToLinear(pixels[bytesPerPixel * x + 0 + y * bytesPerRow]);\n      g +=\n        basis * sRGBToLinear(pixels[bytesPerPixel * x + 1 + y * bytesPerRow]);\n      b +=\n        basis * sRGBToLinear(pixels[bytesPerPixel * x + 2 + y * bytesPerRow]);\n    }\n  }\n\n  let scale = 1 / (width * height);\n\n  return [r * scale, g * scale, b * scale];\n};\n\nconst encodeDC = (value: NumberTriplet): number => {\n  const roundedR = linearTosRGB(value[0]);\n  const roundedG = linearTosRGB(value[1]);\n  const roundedB = linearTosRGB(value[2]);\n  return (roundedR << 16) + (roundedG << 8) + roundedB;\n};\n\nconst encodeAC = (value: NumberTriplet, maximumValue: number): number => {\n  let quantR = Math.floor(\n    Math.max(\n      0,\n      Math.min(18, Math.floor(signPow(value[0] / maximumValue, 0.5) * 9 + 9.5))\n    )\n  );\n  let quantG = Math.floor(\n    Math.max(\n      0,\n      Math.min(18, Math.floor(signPow(value[1] / maximumValue, 0.5) * 9 + 9.5))\n    )\n  );\n  let quantB = Math.floor(\n    Math.max(\n      0,\n      Math.min(18, Math.floor(signPow(value[2] / maximumValue, 0.5) * 9 + 9.5))\n    )\n  );\n\n  return quantR * 19 * 19 + quantG * 19 + quantB;\n};\n\nconst encode = (\n  pixels: Uint8ClampedArray,\n  width: number,\n  height: number,\n  componentX: number,\n  componentY: number\n): string => {\n  if (componentX < 1 || componentX > 9 || componentY < 1 || componentY > 9) {\n    throw new ValidationError(\"BlurHash must have between 1 and 9 components\");\n  }\n  if (width * height * 4 !== pixels.length) {\n    throw new ValidationError(\"Width and height must match the pixels array\");\n  }\n\n  let factors: Array<[number, number, number]> = [];\n  for (let y = 0; y < componentY; y++) {\n    for (let x = 0; x < componentX; x++) {\n      const normalisation = x == 0 && y == 0 ? 1 : 2;\n      const factor = multiplyBasisFunction(\n        pixels,\n        width,\n        height,\n        (i: number, j: number) =>\n          normalisation *\n          Math.cos((Math.PI * x * i) / width) *\n          Math.cos((Math.PI * y * j) / height)\n      );\n      factors.push(factor);\n    }\n  }\n\n  const dc = factors[0];\n  const ac = factors.slice(1);\n\n  let hash = \"\";\n\n  let sizeFlag = componentX - 1 + (componentY - 1) * 9;\n  hash += encode83(sizeFlag, 1);\n\n  let maximumValue: number;\n  if (ac.length > 0) {\n    let actualMaximumValue = Math.max(...ac.map(val => Math.max(...val)));\n    let quantisedMaximumValue = Math.floor(\n      Math.max(0, Math.min(82, Math.floor(actualMaximumValue * 166 - 0.5)))\n    );\n    maximumValue = (quantisedMaximumValue + 1) / 166;\n    hash += encode83(quantisedMaximumValue, 1);\n  } else {\n    maximumValue = 1;\n    hash += encode83(0, 1);\n  }\n\n  hash += encode83(encodeDC(dc), 4);\n\n  ac.forEach(factor => {\n    hash += encode83(encodeAC(factor, maximumValue), 2);\n  });\n\n  return hash;\n};\n\nexport default encode;\n", "import { encode, decode } from 'blurhash';\n\nconst WIDTH = 64;\nconst HEIGHT = 64;\nconst COMP_X = 9;\nconst COMP_Y = 9;\n\nonmessage = (event: MessageEvent) => createBlurred(event.data);\n\nasync function createBlurred(imageUrl: string) {\n  console.time('BackdropBlur process');\n\n  const [pixels, width, height] = getImageData(await loadImage(imageUrl));\n\n  const blurhash = decode(encode(pixels, width, height, COMP_X, COMP_Y), WIDTH, HEIGHT);\n  const cvs = new OffscreenCanvas(WIDTH, HEIGHT);\n  const ctx = cvs.getContext('2d');\n\n  const bhImageData = ctx!.createImageData(WIDTH, HEIGHT);\n  bhImageData.data.set(blurhash);\n\n  ctx!.putImageData(bhImageData, 0, 0);\n\n  let url: string | null = null;\n\n  try {\n    const blob = await cvs.convertToBlob();\n    url = URL.createObjectURL(blob);\n  } catch (e) {\n    console.error(e);\n  }\n\n  console.timeEnd('BackdropBlur process');\n\n  postMessage(url);\n}\n\nasync function loadImage(src: string) {\n  const img = await (\n    await fetch(src, {\n      mode: 'no-cors',\n    })\n  ).blob();\n\n  return createImageBitmap(img);\n}\n\nfunction getImageData(image: HTMLImageElement | ImageBitmap): [Uint8ClampedArray, number, number] {\n  const ar = image.width / image.height;\n\n  const w = 128;\n  // eslint-disable-next-line no-bitwise\n  const h = w * ar | 0;\n\n  const canvas = new OffscreenCanvas(w, h);\n\n  const context = canvas.getContext('2d');\n  context!.drawImage(image, 0, 0, w, h);\n\n  return [context!.getImageData(0, 0, w, h).data, w, h];\n}\n"], "names": ["digitCharacters", "decode83", "str", "value", "i", "length", "c", "indexOf", "encode83", "n", "result", "digit", "Math", "floor", "pow", "sRGBToLinear", "v", "linearTosRGB", "max", "min", "round", "signPow", "val", "exp", "abs", "ValidationError", "Error", "constructor", "message", "super", "this", "name", "decodeDC", "intG", "intB", "decodeAC", "maximumValue", "quantR", "quantG", "quantB", "<PERSON><PERSON><PERSON>", "width", "height", "punch", "sizeFlag", "numY", "numX", "validate<PERSON><PERSON><PERSON><PERSON><PERSON>", "colors", "Array", "substring", "bytesPerRow", "pixels", "Uint8ClampedArray", "y", "x", "r", "g", "b", "j", "basis", "cos", "PI", "color", "intR", "multiplyBasisFunction", "basisFunction", "scale", "onmessage", "event", "async", "imageUrl", "console", "time", "image", "w", "h", "context", "OffscreenCanvas", "getContext", "drawImage", "getImageData", "data", "src", "img", "fetch", "mode", "blob", "createImageBitmap", "loadImage", "componentX", "componentY", "factors", "normalisation", "factor", "push", "dc", "ac", "slice", "hash", "actualMaximumValue", "map", "quantisedMaximumValue", "for<PERSON>ach", "encodeAC", "cvs", "ctx", "bhImageData", "createImageData", "set", "putImageData", "url", "convertToBlob", "URL", "createObjectURL", "e", "error", "timeEnd", "postMessage", "createBlurred"], "sourceRoot": ""}