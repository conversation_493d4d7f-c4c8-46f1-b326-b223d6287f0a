# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This test checks some cases that might be affected by constant folding.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS "abc" + "2.1" is "abc2.1"
PASS "123" + "2.1" is "1232.1"
PASS "123" + "=" is "123="
PASS "*" + "123" is "*123"
PASS !"abc" is false
PASS !"" is true
PASS 10.3 + 2.1 is 12.4
PASS 10.3 + "2.1" is "10.32.1"
PASS "10.3" + 2.1  is "10.32.1"
PASS "10.3" + "2.1" is "10.32.1"
PASS 10.3 + true is 11.3
PASS "10.3" + true is "10.3true"
PASS 10.3 + false is 10.3
PASS "10.3" + false is "10.3false"
PASS true + 2.1 is 3.1
PASS true + "2.1" is "true2.1"
PASS false + 2.1 is 2.1
PASS false + "2.1" is "false2.1"
PASS 10.3 - 2.1 is 8.200000000000001
PASS 10.3 - "2.1" is 8.200000000000001
PASS "10.3" - 2.1  is 8.200000000000001
PASS "10.3" - "2.1" is 8.200000000000001
PASS 10.3 - true is 9.3
PASS "10.3" - true is 9.3
PASS 10.3 - false is 10.3
PASS "10.3" - false is 10.3
PASS true - 2.1 is -1.1
PASS true - "2.1" is -1.1
PASS false - 2.1 is -2.1
PASS false - "2.1" is -2.1
PASS 10.3 * 2.1 is 21.630000000000003
PASS 10.3 * "2.1" is 21.630000000000003
PASS "10.3" * 2.1 is 21.630000000000003
PASS "10.3" * "2.1" is 21.630000000000003
PASS 10.3 * true is 10.3
PASS "10.3" * true is 10.3
PASS 10.3 * false is 0
PASS "10.3" * false is 0
PASS true * 10.3 is 10.3
PASS true * "10.3" is 10.3
PASS false * 10.3 is 0
PASS false * "10.3" is 0
PASS 10.3 / 2 is 5.15
PASS "10.3" / 2 is 5.15
PASS 10.3 / "2" is 5.15
PASS "10.3" / "2" is 5.15
PASS 10.3 / true is 10.3
PASS "10.3" / true is 10.3
PASS true / 2 is 0.5
PASS true / "2" is 0.5
PASS false / 2 is 0
PASS false / "2" is 0
PASS 10.3 % 2.1 is 1.9000000000000004
PASS "10.3" % 2.1 is 1.9000000000000004
PASS 10.3 % "2.1" is 1.9000000000000004
PASS "10.3" % "2.1" is 1.9000000000000004
PASS 10.3 % true is 0.3000000000000007
PASS "10.3" % true is 0.3000000000000007
PASS true % 2 is 1
PASS true % "2" is 1
PASS false % 2 is 0
PASS false % "2" is 0
PASS 10.3 << 2.1 is 40
PASS "10.3" << 2.1 is 40
PASS 10.3 << "2.1" is 40
PASS "10.3" << "2.1" is 40
PASS 10.3 << true is 20
PASS "10.3" << true is 20
PASS 10.3 << false is 10
PASS "10.3" << false is 10
PASS true << 2.1 is 4
PASS true << "2.1" is 4
PASS false << 2.1 is 0
PASS false << "2.1" is 0
PASS 10.3 >> 2.1 is 2
PASS "10.3" >> 2.1 is 2
PASS 10.3 >> "2.1" is 2
PASS "10.3" >> "2.1" is 2
PASS 10.3 >> true is 5
PASS "10.3" >> true is 5
PASS 10.3 >> false is 10
PASS "10.3" >> false is 10
PASS true >> 2.1 is 0
PASS true >> "2.1" is 0
PASS false >> 2.1 is 0
PASS false >> "2.1" is 0
PASS -10.3 >>> 2.1 is 1073741821
PASS "-10.3">>> 2.1 is 1073741821
PASS -10.3 >>> "2.1" is 1073741821
PASS "-10.3">>> "2.1" is 1073741821
PASS -10.3 >>> true is 2147483643
PASS "-10.3">>> true is 2147483643
PASS -10.3 >>> false is 4294967286
PASS "-10.3" >>> false is 4294967286
PASS true >>> 2.1 is 0
PASS true >>> "2.1" is 0
PASS false >>> 2.1 is 0
PASS false >>> "2.1" is 0
PASS 10.3 & 3.1 is 2
PASS "10.3" & 3.1 is 2
PASS 10.3 & "3.1" is 2
PASS "10.3" & "3.1" is 2
PASS 10.3 & true is 0
PASS "10.3" & true is 0
PASS 11.3 & true is 1
PASS "11.3" & true is 1
PASS 10.3 & false is 0
PASS "10.3" & false is 0
PASS 11.3 & false is 0
PASS "11.3" & false is 0
PASS true & 3.1 is 1
PASS true & "3.1" is 1
PASS true & 2.1 is 0
PASS true & "2.1" is 0
PASS false & 3.1 is 0
PASS false & "3.1" is 0
PASS false & 2.1 is 0
PASS false & "2.1" is 0
PASS 10.3 | 3.1 is 11
PASS "10.3" | 3.1 is 11
PASS 10.3 | "3.1" is 11
PASS "10.3" | "3.1" is 11
PASS 10.3 | true is 11
PASS "10.3" | true is 11
PASS 11.3 | true is 11
PASS "11.3" | true is 11
PASS 10.3 | false is 10
PASS "10.3" | false is 10
PASS 11.3 | false is 11
PASS "11.3" | false is 11
PASS true | 3.1 is 3
PASS true | "3.1" is 3
PASS true | 2.1 is 3
PASS true | "2.1" is 3
PASS false | 3.1 is 3
PASS false | "3.1" is 3
PASS false | 2.1 is 2
PASS false | "2.1" is 2
PASS 10.3 ^ 3.1 is 9
PASS "10.3" ^ 3.1 is 9
PASS 10.3 ^ "3.1" is 9
PASS "10.3" ^ "3.1" is 9
PASS 10.3 ^ true is 11
PASS "10.3" ^ true is 11
PASS 11.3 ^ true is 10
PASS "11.3" ^ true is 10
PASS 10.3 ^ false is 10
PASS "10.3" ^ false is 10
PASS 11.3 ^ false is 11
PASS "11.3" ^ false is 11
PASS true ^ 3.1 is 2
PASS true ^ "3.1" is 2
PASS true ^ 2.1 is 3
PASS true ^ "2.1" is 3
PASS false ^ 3.1 is 3
PASS false ^ "3.1" is 3
PASS false ^ 2.1 is 2
PASS false ^ "2.1" is 2
PASS 10.3 == 3.1 is false
PASS 3.1 == 3.1 is true
PASS "10.3" == 3.1 is false
PASS "3.1" == 3.1 is true
PASS 10.3 == "3.1" is false
PASS 3.1 == "3.1" is true
PASS "10.3" == "3.1" is false
PASS "3.1" == "3.1" is true
PASS 10.3 == true is false
PASS 1 == true is true
PASS "10.3" == true is false
PASS "1" == true is true
PASS 10.3 == false is false
PASS 0 == false is true
PASS "10.3" == false is false
PASS "0" == false is true
PASS true == 3.1 is false
PASS true == 1 is true
PASS true == "3.1" is false
PASS true == "1"  is true
PASS false == 3.1 is false
PASS false == 0 is true
PASS false == "3.1" is false
PASS false == "0" is true
PASS true == true is true
PASS false == true is false
PASS true == false is false
PASS false == false is true
PASS 10.3 != 3.1 is true
PASS 3.1 != 3.1 is false
PASS "10.3" != 3.1 is true
PASS "3.1" != 3.1 is false
PASS 10.3 != "3.1" is true
PASS 3.1 != "3.1" is false
PASS "10.3" != "3.1" is true
PASS "3.1" != "3.1" is false
PASS 10.3 != true is true
PASS 1 != true is false
PASS "10.3" != true is true
PASS "1" != true is false
PASS 10.3 != false is true
PASS 0 != false is false
PASS "10.3" != false is true
PASS "0" != false is false
PASS true != 3.1 is true
PASS true != 1 is false
PASS true != "3.1" is true
PASS true != "1"  is false
PASS false != 3.1 is true
PASS false != 0 is false
PASS false != "3.1" is true
PASS false != "0" is false
PASS true != true is false
PASS false != true is true
PASS true != false is true
PASS false != false is false
PASS 10.3 > 3.1 is true
PASS 3.1 > 3.1 is false
PASS "10.3" > 3.1 is true
PASS "3.1" > 3.1 is false
PASS 10.3 > "3.1" is true
PASS 3.1 > "3.1" is false
PASS "10.3" > "3.1" is false
PASS "3.1" > "3.1" is false
PASS 10.3 > true is true
PASS 0 > true is false
PASS "10.3" > true is true
PASS "0" > true is false
PASS 10.3 > false is true
PASS -1 > false is false
PASS "10.3" > false is true
PASS "-1" > false is false
PASS true > 0.1 is true
PASS true > 1.1 is false
PASS true > "0.1" is true
PASS true > "1.1" is false
PASS false > -3.1 is true
PASS false > 0 is false
PASS false > "-3.1" is true
PASS false > "0" is false
PASS true > true is false
PASS false > true is false
PASS true > false is true
PASS false > false is false
PASS 10.3 < 3.1 is false
PASS 2.1 < 3.1 is true
PASS "10.3" < 3.1 is false
PASS "2.1" < 3.1 is true
PASS 10.3 < "3.1" is false
PASS 2.1 < "3.1" is true
PASS "10.3" < "3.1" is true
PASS "2.1" < "3.1" is true
PASS 10.3 < true is false
PASS 0 < true is true
PASS "10.3" < true is false
PASS "0" < true is true
PASS 10.3 < false is false
PASS -1 < false is true
PASS "10.3" < false is false
PASS "-1" < false is true
PASS true < 0.1 is false
PASS true < 1.1 is true
PASS true < "0.1" is false
PASS true < "1.1" is true
PASS false < -3.1 is false
PASS false < 0.1 is true
PASS false < "-3.1" is false
PASS false < "0.1" is true
PASS true < true is false
PASS false < true is true
PASS true < false is false
PASS false < false is false
PASS 10.3 >= 3.1 is true
PASS 2.1 >= 3.1 is false
PASS "10.3" >= 3.1 is true
PASS "2.1" >= 3.1 is false
PASS 10.3 >= "3.1" is true
PASS 2.1 >= "3.1" is false
PASS "10.3" >= "3.1" is false
PASS "2.1" >= "3.1" is false
PASS 10.3 >= true is true
PASS 0 >= true is false
PASS "10.3" >= true is true
PASS "0" >= true is false
PASS 10.3 >= false is true
PASS -1 >= false is false
PASS "10.3" >= false is true
PASS "-1" >= false is false
PASS true >= 0.1 is true
PASS true >= 1.1 is false
PASS true >= "0.1" is true
PASS true >= "1.1" is false
PASS false >= -3.1 is true
PASS false >= 0 is true
PASS false >= "-3.1" is true
PASS false >= "0" is true
PASS true >= true is true
PASS false >= true is false
PASS true >= false is true
PASS false >= false is true
PASS 10.3 <= 3.1 is false
PASS 2.1 <= 3.1 is true
PASS "10.3" <= 3.1 is false
PASS "2.1" <= 3.1 is true
PASS 10.3 <= "3.1" is false
PASS 2.1 <= "3.1" is true
PASS "10.3" <= "3.1" is true
PASS "2.1" <= "3.1" is true
PASS 10.3 <= true is false
PASS 0 <= true is true
PASS "10.3" <= true is false
PASS "0" <= true is true
PASS 10.3 <= false is false
PASS -1 <= false is true
PASS "10.3" <= false is false
PASS "-1" <= false is true
PASS true <= 0.1 is false
PASS true <= 1.1 is true
PASS true <= "0.1" is false
PASS true <= "1.1" is true
PASS false <= -3.1 is false
PASS false <= 0.1 is true
PASS false <= "-3.1" is false
PASS false <= "0.1" is true
PASS true <= true is true
PASS false <= true is true
PASS true <= false is false
PASS false <= false is true
PASS true && true is true
PASS true && false is false
PASS false && true is false
PASS false && false is false
PASS 1.1 && true is true
PASS 1.1 && false is false
PASS 0 && true is 0
PASS 0 && false is 0
PASS "1.1" && true is true
PASS "1.1" && false is false
PASS "0" && true is true
PASS "0" && false is false
PASS true && 1.1 is 1.1
PASS true && 0 is 0
PASS false && 1.1 is false
PASS false && 0 is false
PASS true && "1.1" is "1.1"
PASS true && "0" is "0"
PASS false && "1.1" is false
PASS false && "0" is false
PASS 1.1 && 1.1 is 1.1
PASS 1.1 && 0 is 0
PASS 0 && 1.1 is 0
PASS 0 && 0 is 0
PASS "1.1" && 1.1 is 1.1
PASS "1.1" && 0 is 0
PASS "0" && 1.1 is 1.1
PASS "0" && 0 is 0
PASS 1.1 && "1.1" is "1.1"
PASS 1.1 && "0" is "0"
PASS 0 && "1.1" is 0
PASS 0 && "0" is 0
PASS "1.1" && "1.1" is "1.1"
PASS "1.1" && "0" is "0"
PASS "0" && "1.1" is "1.1"
PASS "0" && "0" is "0"
PASS true || true is true
PASS true || false is true
PASS false || true is true
PASS false || false is false
PASS 1.1 || true is 1.1
PASS 1.1 || false is 1.1
PASS 0 || true is true
PASS 0 || false is false
PASS "1.1" || true is "1.1"
PASS "1.1" || false is "1.1"
PASS "0" || true is "0"
PASS "0" || false is "0"
PASS true || 1.1 is true
PASS true || 0 is true
PASS false || 1.1 is 1.1
PASS false || 0 is 0
PASS true || "1.1" is true
PASS true || "0" is true
PASS false || "1.1" is "1.1"
PASS false || "0" is "0"
PASS 1.1 || 1.1 is 1.1
PASS 1.1 || 0 is 1.1
PASS 0 || 1.1 is 1.1
PASS 0 || 0 is 0
PASS "1.1" || 1.1 is "1.1"
PASS "1.1" || 0 is "1.1"
PASS "0" || 1.1 is "0"
PASS "0" || 0 is "0"
PASS 1.1 || "1.1" is 1.1
PASS 1.1 || "0" is 1.1
PASS 0 || "1.1" is "1.1"
PASS 0 || "0" is "0"
PASS "1.1" || "1.1" is "1.1"
PASS "1.1" || "0" is "1.1"
PASS "0" || "1.1" is "0"
PASS "0" || "0" is "0"
PASS +3.1 is 3.1
PASS + +3.1 is 3.1
PASS +"3.1" is 3.1
PASS +true is 1
PASS +false is 0
PASS -3.1 is -3.1
PASS - -3.1 is 3.1
PASS -"3.1" is -3.1
PASS -true is -1
PASS -false is -0
PASS ~3 is -4
PASS ~ ~3 is 3
PASS ~"3" is -4
PASS ~true is -2
PASS ~false is -1
PASS !true is false
PASS !false is true
PASS !3 is false
PASS !0 is true
PASS 10.3 / 0 is Infinity
PASS "10.3" / 0 is Infinity
PASS -10.3 / 0 is -Infinity
PASS "-10.3" / 0 is -Infinity
PASS true / 0 is Infinity
PASS false / 0 is NaN
PASS 0 / 0 is NaN
PASS 10.3 / -0 is -Infinity
PASS "10.3" / -0 is -Infinity
PASS -10.3 / -0 is Infinity
PASS "-10.3" / -0 is Infinity
PASS true / -0 is -Infinity
PASS false / -0 is NaN
PASS 0 / -0 is NaN
PASS 1 / -0 is -Infinity
PASS 1 / - 0 is -Infinity
PASS 1 / - -0 is Infinity
PASS 1 / - - -0 is -Infinity
PASS successfullyParsed is true

TEST COMPLETE

