# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests for Array.prototype.filter

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS [undefined].filter(passUndefined) is [undefined]
PASS (new Array(20)).filter(passUndefined) is []
PASS [0,1,2,3,4,5,6,7,8,9].filter(passEven) is [0,2,4,6,8]
PASS [0,1,2,3,4,5,6,7,8,9].filter(passAfter5) is [5,6,7,8,9]
PASS mixPartialAndFast.filter(passAfter5) is [5,6,7,8,9,sparseArrayLength-1]
PASS toObject([undefined]).filter(passUndefined) is [undefined]
PASS toObject(new Array(20)).filter(passUndefined) is []
PASS toObject([0,1,2,3,4,5,6,7,8,9]).filter(passEven) is [0,2,4,6,8]
PASS toObject([0,1,2,3,4,5,6,7,8,9]).filter(passAfter5) is [5,6,7,8,9]
PASS toObject(mixPartialAndFast).filter(passAfter5) is [5,6,7,8,9,sparseArrayLength-1]
PASS reverseInsertionOrder([undefined]).filter(passUndefined) is [undefined]
PASS reverseInsertionOrder(new Array(20)).filter(passUndefined) is []
PASS reverseInsertionOrder([0,1,2,3,4,5,6,7,8,9]).filter(passEven) is [0,2,4,6,8]
PASS reverseInsertionOrder([0,1,2,3,4,5,6,7,8,9]).filter(passAfter5) is [5,6,7,8,9]
PASS reverseInsertionOrder(mixPartialAndFast).filter(passAfter5) is [5,6,7,8,9,sparseArrayLength-1]
,0,[object Object]
PASS reverseInsertionOrder([undefined]).filter(filterLog(passUndefined)) is [undefined]
PASS reverseInsertionOrder(new Array(20)).filter(filterLog(passUndefined)) is []
0,0,[object Object]
1,1,[object Object]
2,2,[object Object]
3,3,[object Object]
4,4,[object Object]
PASS reverseInsertionOrder([0,1,2,3,4]).filter(filterLog(passEven)) is [0,2,4]
0,0,[object Object]
1,1,[object Object]
2,2,[object Object]
3,3,[object Object]
4,4,[object Object]
5,5,[object Object]
6,6,[object Object]
7,7,[object Object]
8,8,[object Object]
9,9,[object Object]
10099,10099,[object Object]
PASS reverseInsertionOrder(mixPartialAndFast).filter(filterLog(passAfter5)) is [5,6,7,8,9,sparseArrayLength-1]
,0,
PASS ([undefined]).filter(filterLog(passUndefined)) is [undefined]
PASS (new Array(20)).filter(filterLog(passUndefined)) is []
0,0,0,1,2,3,4
1,1,0,1,2,3,4
2,2,0,1,2,3,4
3,3,0,1,2,3,4
4,4,0,1,2,3,4
PASS ([0,1,2,3,4]).filter(filterLog(passEven)) is [0,2,4]
0,0,0,1,2,3,4,5,6,7,8,9,
1,1,0,1,2,3,4,5,6,7,8,9,
2,2,0,1,2,3,4,5,6,7,8,9,
3,3,0,1,2,3,4,5,6,7,8,9,
4,4,0,1,2,3,4,5,6,7,8,9,
5,5,0,1,2,3,4,5,6,7,8,9,
6,6,0,1,2,3,4,5,6,7,8,9,
7,7,0,1,2,3,4,5,6,7,8,9,
8,8,0,1,2,3,4,5,6,7,8,9,
9,9,0,1,2,3,4,5,6,7,8,9,
10099,10099,0,1,2,3,4,5,6,7,8,9,
PASS (mixPartialAndFast).filter(filterLog(passAfter5)) is [5,6,7,8,9,sparseArrayLength-1]
PASS [1,2,3].filter(function(i,j,k,l,m){ return m=!m; }) is [1,2,3]
PASS successfullyParsed is true

TEST COMPLETE

