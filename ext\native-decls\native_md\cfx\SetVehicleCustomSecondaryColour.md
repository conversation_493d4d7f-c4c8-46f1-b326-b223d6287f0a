---
ns: CFX
apiset: server
---
## SET_VEHICLE_CUSTOM_SECONDARY_COLOUR

```c
void SET_VEHICLE_CUSTOM_SECONDARY_COLOUR(Vehicle vehicle, int r, int g, int b);
```

```
p1, p2, p3 are RGB values for color (255,0,0 for Red, ect)
```

**This is the server-side RPC native equivalent of the client native [SET\_VEHICLE\_CUSTOM\_SECONDARY\_COLOUR](?_0x36CED73BFED89754).**

## Parameters
* **vehicle**: 
* **r**: 
* **g**: 
* **b**: 

