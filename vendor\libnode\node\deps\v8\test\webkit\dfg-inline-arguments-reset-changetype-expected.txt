# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves function.arguments functionality if the arguments are reassigned with a different type.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a0, a0, c0"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a1, a1, c1"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a2, a2, c2"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a3, a3, c3"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a4, a4, c4"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a5, a5, c5"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a6, a6, c6"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a7, a7, c7"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a8, a8, c8"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a9, a9, c9"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a10, a10, c10"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a11, a11, c11"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a12, a12, c12"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a13, a13, c13"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a14, a14, c14"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a15, a15, c15"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a16, a16, c16"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a17, a17, c17"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a18, a18, c18"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a19, a19, c19"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a20, a20, c20"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a21, a21, c21"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a22, a22, c22"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a23, a23, c23"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a24, a24, c24"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a25, a25, c25"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a26, a26, c26"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a27, a27, c27"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a28, a28, c28"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a29, a29, c29"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a30, a30, c30"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a31, a31, c31"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a32, a32, c32"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a33, a33, c33"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a34, a34, c34"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a35, a35, c35"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a36, a36, c36"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a37, a37, c37"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a38, a38, c38"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a39, a39, c39"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a40, a40, c40"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a41, a41, c41"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a42, a42, c42"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a43, a43, c43"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a44, a44, c44"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a45, a45, c45"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a46, a46, c46"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a47, a47, c47"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a48, a48, c48"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a49, a49, c49"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a50, a50, c50"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a51, a51, c51"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a52, a52, c52"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a53, a53, c53"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a54, a54, c54"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a55, a55, c55"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a56, a56, c56"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a57, a57, c57"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a58, a58, c58"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a59, a59, c59"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a60, a60, c60"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a61, a61, c61"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a62, a62, c62"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a63, a63, c63"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a64, a64, c64"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a65, a65, c65"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a66, a66, c66"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a67, a67, c67"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a68, a68, c68"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a69, a69, c69"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a70, a70, c70"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a71, a71, c71"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a72, a72, c72"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a73, a73, c73"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a74, a74, c74"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a75, a75, c75"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a76, a76, c76"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a77, a77, c77"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a78, a78, c78"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a79, a79, c79"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a80, a80, c80"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a81, a81, c81"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a82, a82, c82"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a83, a83, c83"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a84, a84, c84"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a85, a85, c85"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a86, a86, c86"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a87, a87, c87"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a88, a88, c88"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a89, a89, c89"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a90, a90, c90"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a91, a91, c91"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a92, a92, c92"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a93, a93, c93"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a94, a94, c94"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a95, a95, c95"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a96, a96, c96"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a97, a97, c97"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a98, a98, c98"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a99, a99, c99"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a100, a100, c100"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a101, a101, c101"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a102, a102, c102"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a103, a103, c103"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a104, a104, c104"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a105, a105, c105"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a106, a106, c106"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a107, a107, c107"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a108, a108, c108"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a109, a109, c109"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a110, a110, c110"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a111, a111, c111"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a112, a112, c112"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a113, a113, c113"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a114, a114, c114"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a115, a115, c115"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a116, a116, c116"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a117, a117, c117"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a118, a118, c118"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a119, a119, c119"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a120, a120, c120"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a121, a121, c121"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a122, a122, c122"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a123, a123, c123"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a124, a124, c124"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a125, a125, c125"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a126, a126, c126"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a127, a127, c127"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a128, a128, c128"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a129, a129, c129"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a130, a130, c130"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a131, a131, c131"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a132, a132, c132"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a133, a133, c133"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a134, a134, c134"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a135, a135, c135"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a136, a136, c136"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a137, a137, c137"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a138, a138, c138"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a139, a139, c139"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a140, a140, c140"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a141, a141, c141"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a142, a142, c142"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a143, a143, c143"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a144, a144, c144"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a145, a145, c145"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a146, a146, c146"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a147, a147, c147"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a148, a148, c148"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a149, a149, c149"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a150, a150, c150"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a151, a151, c151"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a152, a152, c152"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a153, a153, c153"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a154, a154, c154"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a155, a155, c155"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a156, a156, c156"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a157, a157, c157"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a158, a158, c158"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a159, a159, c159"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a160, a160, c160"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a161, a161, c161"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a162, a162, c162"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a163, a163, c163"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a164, a164, c164"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a165, a165, c165"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a166, a166, c166"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a167, a167, c167"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a168, a168, c168"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a169, a169, c169"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a170, a170, c170"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a171, a171, c171"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a172, a172, c172"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a173, a173, c173"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a174, a174, c174"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a175, a175, c175"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a176, a176, c176"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a177, a177, c177"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a178, a178, c178"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a179, a179, c179"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a180, a180, c180"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a181, a181, c181"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a182, a182, c182"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a183, a183, c183"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a184, a184, c184"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a185, a185, c185"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a186, a186, c186"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a187, a187, c187"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a188, a188, c188"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a189, a189, c189"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a190, a190, c190"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a191, a191, c191"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a192, a192, c192"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a193, a193, c193"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a194, a194, c194"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a195, a195, c195"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a196, a196, c196"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a197, a197, c197"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a198, a198, c198"
PASS argsToStr(baz("a" + __i, __i + 2, "c" + __i)) is "[object Arguments]: a199, a199, c199"
PASS successfullyParsed is true

TEST COMPLETE

