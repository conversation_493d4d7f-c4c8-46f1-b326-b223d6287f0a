#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var ld_a = 1;
  while(true) {
    ld_a = ld_a + ld_a;
    if (ld_a > 10) break;
  }
  return ld_a;
"
frame size: 1
parameter count: 1
bytecode array length: 25
bytecodes: [
  /*   45 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   64 S> */ B(Ldar), R(0),
  /*   76 E> */ B(Add), R(0), U8(0),
                B(Star0),
  /*   86 S> */ B(LdaSmi), I8(10),
  /*   95 E> */ B(TestGreaterThan), R(0), U8(1),
                B(JumpIfFalse), U8(4),
  /*  101 S> */ B(Jump), U8(6),
  /*   48 E> */ B(JumpLoop), U8(15), I8(0), U8(2),
  /*  110 S> */ B(Ldar), R(0),
  /*  122 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var ld_a = 1;
  do {
    ld_a = ld_a + ld_a;
    if (ld_a > 10) continue;
  } while(false);
  return ld_a;
"
frame size: 1
parameter count: 1
bytecode array length: 19
bytecodes: [
  /*   45 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   67 S> */ B(Add), R(0), U8(0),
                B(Star0),
  /*   77 S> */ B(LdaSmi), I8(10),
  /*   86 E> */ B(TestGreaterThan), R(0), U8(1),
                B(JumpIfFalse), U8(4),
  /*   92 S> */ B(Jump), U8(2),
  /*  118 S> */ B(Ldar), R(0),
  /*  130 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var ld_a = 1;
    ld_a = ld_a + ld_a;
    return ld_a;
"
frame size: 1
parameter count: 1
bytecode array length: 8
bytecodes: [
  /*   45 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   62 S> */ B(Add), R(0), U8(0),
                B(Star0),
  /*   84 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

