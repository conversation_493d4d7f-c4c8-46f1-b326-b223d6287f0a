// Copyright 2017 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Generated by tools/bigint-tester.py.

var data = [{
  a: -0x46505bec40d461c595b5e4be178b7d00n,
  b: -0x9170e5437d4e3ec7c0971e2c6d3bbbd2929ff108ea4ee64f7a91aa367fn,
  r: 0x9170e5437d4e3ec7c0971e2c6d7deb897edf25692fdb53abc486214a81n
}, {
  a: -0x49171f24aa9660f7f854148018a8b03256200508n,
  b: 0x75c2103e4e8e52d0311n,
  r: -0x49171f24aa9660f7f85413dc39ab54dab30d0617n
}, {
  a: -0x3cde31210d299e4f6734f76c4f2df3056fn,
  b: -0x402b7fe66d16877867f43n,
  r: 0x3cde31210d299a4dd0ca91bd275a757a2cn
}, {
  a: 0x727n,
  b: 0xe1c82371da63bdb801273077095be8977ff9f14aa619829bf4b418n,
  r: 0xe1c82371da63bdb801273077095be8977ff9f14aa619829bf4b33fn
}, {
  a: 0x7c2b1e0918a85bf5faea9077b7dn,
  b: -0xed714ba58fd54b19n,
  r: -0x7c2b1e0918a68ce140b26d23066n
}, {
  a: 0x1dded5fd695f4babcan,
  b: 0x7e1cb4346c68e84f8fbdd3501daead2ce99a90e56038n,
  r: 0x7e1cb4346c68e84f8fbdd3501db373f914f3cfaecbf2n
}, {
  a: 0xec7d9595de759652a3bb96c80edca63790c32ce7f6cf0ef0n,
  b: -0x67f0feef424f56d6ae6856a20901199de53ebn,
  r: -0xec7d9595de73e95d4d4fb23d63b640b2fae3bcf66f115d1bn
}, {
  a: 0x69ab204291f752866c3d49fdf1d656298f43e896cea3ef31a04n,
  b: -0x5f5b2fd130e33ed76fa22de5ac70bdf96dee80a09e3107e1c93a135ea80b7e3640cdn,
  r: -0x5f5b2fd130e33ed769389fe1856fc8d10b2d543f412c628351ce2dd7c4e140c55ac9n
}, {
  a: -0xd396d40076en,
  b: 0x3158623c80393n,
  r: -0x318bf4e8804ffn
}, {
  a: 0x8c1dbfbd68007d171986777446303896a1ee46n,
  b: -0x75fd69710f5bea1cece9d59ec4bca29712b49dcf5ee5cc3299d7fb4fb024f10ae955fn,
  r: -0x75fd69710f5bea1cece9d59ec4bca29fd36f6619dee21d4301b08c0bd3277860f7b19n
}, {
  a: -0x4c6e3ccbabdd6f58450ec3ec8adfb10831b70893cb996f0ac97a0ae2f3a943185d1n,
  b: 0xeb136731a19867949d46886e62050c4b446767a076c73dn,
  r: -0x4c6e3ccbabdd6f58450ecd5dbcacab11b7ce4147a31f892a99bebea485d344742een
}, {
  a: 0xeaff60e10ebb4b8f2da6a517n,
  b: -0x17af62a3e808af1be3d864132dfd7363bc95872580585d7a9904n,
  r: -0x17af62a3e808af1be3d864132dfd999cdc74899ecbd770dc3c15n
}, {
  a: -0xd4424b4f09210108076d63bd621180f280df8f4480n,
  b: -0x20953185dd2c534b3cb8da73ce55ab386d62fe8a793a1e74cdf3ad95f3cc2573b3n,
  r: 0x20953185dd2c534b3cb8da731a17e0776443ff827e577dc9afe22d677313aa37cdn
}, {
  a: 0x84317d7ec6df6dbfe9413cee812ff95c587f61c7b8de5828d445a69555cff26fba9n,
  b: -0x853c667aed62685df5993748e5668802b7bf918f8c1222a5267c33f013ff1e10f1b909n,
  r: -0x8534256d3a8e05ab2e67a35b2b8e9afd227a16799069af40a4f177aa7aaa42efd742a2n
}, {
  a: -0xe3b4bf724b172b23c5834ed6f70f984ab3b146070770cbc3b86779db7n,
  b: -0x68166de3a03d9efce30cb36e242db000c850c0d4f454594e23a1a7cn,
  r: 0xe3dca91fa8b716bd39604265992bb5fab37916c7d3849f9af644d87cdn
}, {
  a: -0x5358b8efb260b40e37cb5b45eb4e7864n,
  b: -0x3e617e3967a5b3554ebf24f1e51a253dfc20a76ef01f02442fn,
  r: 0x3e617e3967a5b3554eec7c490aa84589f2176c35b5f44c3c4dn
}, {
  a: -0x702359917a8aceedc381n,
  b: -0x714f08d9c29e9fc0044982eb2469707896265n,
  r: 0x714f08d9c29e9fc0034bb77233c1dc964a1e4n
}, {
  a: -0x455ac38dn,
  b: -0x6152562bf5b6f785abec41e8625bccd3bf3067225733dan,
  r: 0x6152562bf5b6f785abec41e8625bccd3bf3067670df055n
}, {
  a: 0x47n,
  b: 0xa3d30490286ddf5d4f4256n,
  r: 0xa3d30490286ddf5d4f4211n
}, {
  a: -0x530cc599859ccdbbb3c1dcb46248a4474c63323cc58a7891da79e0322b91c795ac57n,
  b: 0x2d986083244cd488657c947a952ae15b23d90ebbc34daan,
  r: -0x530cc599859ccdbbb3c1dc99fa28276300b7ba59b91e0204f098bb11f29f7c56e1fdn
}];

var error_count = 0;
for (var i = 0; i < data.length; i++) {
  var d = data[i];
  var r = d.a ^ d.b;
  if (d.r !== r) {
    print("Input A:  " + d.a.toString(16));
    print("Input B:  " + d.b.toString(16));
    print("Result:   " + r.toString(16));
    print("Expected: " + d.r);
    print("Op: ^");
    error_count++;
  }
}
if (error_count !== 0) {
  print("Finished with " + error_count + " errors.")
  quit(1);
}
