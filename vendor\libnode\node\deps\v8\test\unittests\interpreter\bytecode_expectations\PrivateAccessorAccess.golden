#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: test

---
snippet: "
  class A {
    get #a() { return 1; }
    set #a(val) { }
  
    constructor() {
      this.#a++;
      this.#a = 1;
      return this.#a;
    }
  }
  var test = A;
  new test;
"
frame size: 5
parameter count: 1
bytecode array length: 83
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   67 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   76 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star2),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   81 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(CallRuntime), U16(Runtime::kLoadPrivateGetter), R(2), U8(1),
                B(Star3),
                B(CallProperty0), R(3), R(this), U8(4),
                B(Inc), U8(6),
                B(Star3),
  /*   83 E> */ B(CallRuntime), U16(Runtime::kLoadPrivateSetter), R(2), U8(1),
                B(Star4),
                B(CallProperty1), R(4), R(this), R(3), U8(7),
  /*   91 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star2),
                B(LdaSmi), I8(1),
                B(Star3),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   96 E> */ B(GetKeyedProperty), R(this), U8(9),
                B(CallRuntime), U16(Runtime::kLoadPrivateSetter), R(2), U8(1),
                B(Star4),
                B(CallProperty1), R(4), R(this), R(3), U8(11),
  /*  108 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star2),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*  120 E> */ B(GetKeyedProperty), R(this), U8(13),
                B(CallRuntime), U16(Runtime::kLoadPrivateGetter), R(2), U8(1),
                B(Star3),
                B(CallProperty0), R(3), R(this), U8(15),
  /*  123 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  class B {
    get #b() { return 1; }
    constructor() { this.#b++; }
  }
  var test = B;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 29
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   48 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   53 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   58 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(320),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#b"],
]
handlers: [
]

---
snippet: "
  class C {
    set #c(val) { }
    constructor() { this.#c++; }
  }
  var test = C;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 29
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   41 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   46 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   51 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(319),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#c"],
]
handlers: [
]

---
snippet: "
  class D {
    get #d() { return 1; }
    constructor() { this.#d = 1; }
  }
  var test = D;
  new test;
"
frame size: 5
parameter count: 1
bytecode array length: 32
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   48 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   53 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
                B(Star2),
                B(LdaImmutableCurrentContextSlot), U8(3),
  /*   58 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(320),
                B(Star3),
                B(LdaConstant), U8(0),
                B(Star4),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(3), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#d"],
]
handlers: [
]

---
snippet: "
  class E {
    set #e(val) { }
    constructor() { this.#e; }
  }
  var test = E;
  new test;
"
frame size: 4
parameter count: 1
bytecode array length: 29
bytecodes: [
                B(LdaImmutableCurrentContextSlot), U8(3),
                B(Star0),
                B(Ldar), R(context),
  /*   41 E> */ B(DefineKeyedOwnProperty), R(this), R(0), U8(0), U8(0),
  /*   46 S> */ B(LdaImmutableCurrentContextSlot), U8(3),
  /*   51 E> */ B(GetKeyedProperty), R(this), U8(2),
                B(Wide), B(LdaSmi), I16(319),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(CallRuntime), U16(Runtime::kNewTypeError), R(2), U8(2),
                B(Throw),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["#e"],
]
handlers: [
]

