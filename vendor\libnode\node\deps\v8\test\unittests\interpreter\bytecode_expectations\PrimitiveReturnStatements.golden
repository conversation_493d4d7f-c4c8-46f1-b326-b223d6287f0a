#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
                B(LdaUndefined),
  /*   34 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaUndefined),
  /*   41 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return null;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaNull),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return true;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaTrue),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return false;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaFalse),
  /*   47 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return 0;
"
frame size: 0
parameter count: 1
bytecode array length: 2
bytecodes: [
  /*   34 S> */ B(LdaZero),
  /*   43 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return +1;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(1),
  /*   44 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return -1;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(-1),
  /*   44 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return +127;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(127),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return -128;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(-128),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  return 2.0;
"
frame size: 0
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   34 S> */ B(LdaSmi), I8(2),
  /*   45 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

