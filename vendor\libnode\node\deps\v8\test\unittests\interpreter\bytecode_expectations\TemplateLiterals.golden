#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var a = 1;
  var b = 2;
  return `${a}${b}string`;
"
frame size: 3
parameter count: 1
bytecode array length: 23
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(Ldar), R(0),
                B(ToString),
                B(Star2),
                B(Ldar), R(1),
  /*   70 E> */ B(ToString),
                B(Add), R(2), U8(0),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Add), R(2), U8(0),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return `string${a}${b}`;
"
frame size: 3
parameter count: 1
bytecode array length: 23
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
                B(Star2),
                B(Ldar), R(0),
  /*   72 E> */ B(ToString),
                B(Add), R(2), U8(0),
                B(Star2),
                B(Ldar), R(1),
  /*   76 E> */ B(ToString),
                B(Add), R(2), U8(0),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return `${a}string${b}`;
"
frame size: 3
parameter count: 1
bytecode array length: 23
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(Ldar), R(0),
                B(ToString),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Add), R(2), U8(0),
                B(Star2),
                B(Ldar), R(1),
  /*   76 E> */ B(ToString),
                B(Add), R(2), U8(0),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return `foo${a}bar${b}baz${1}`;
"
frame size: 3
parameter count: 1
bytecode array length: 42
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
                B(Star2),
                B(Ldar), R(0),
  /*   69 E> */ B(ToString),
                B(Add), R(2), U8(0),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Add), R(2), U8(0),
                B(Star2),
                B(Ldar), R(1),
  /*   76 E> */ B(ToString),
                B(Add), R(2), U8(0),
                B(Star2),
                B(LdaConstant), U8(2),
                B(Add), R(2), U8(0),
                B(Star2),
                B(LdaSmi), I8(1),
                B(ToString),
                B(Add), R(2), U8(0),
  /*   87 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["foo"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["bar"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["baz"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return `${a}string` + `string${b}`;
"
frame size: 4
parameter count: 1
bytecode array length: 29
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(Ldar), R(0),
                B(ToString),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Add), R(2), U8(1),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(Ldar), R(1),
  /*   87 E> */ B(ToString),
                B(Add), R(3), U8(2),
  /*   76 E> */ B(Add), R(2), U8(0),
  /*   91 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  function foo(a, b) { };
  return `string${foo(a, b)}${a}${b}`;
"
frame size: 4
parameter count: 1
bytecode array length: 38
bytecodes: [
  /*   30 E> */ B(CreateClosure), U8(0), U8(0), U8(2),
                B(Star2),
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   80 S> */ B(LdaConstant), U8(1),
                B(Star3),
  /*   96 E> */ B(CallUndefinedReceiver2), R(2), R(0), R(1), U8(1),
                B(ToString),
                B(Add), R(3), U8(0),
                B(Star3),
                B(Ldar), R(0),
  /*  108 E> */ B(ToString),
                B(Add), R(3), U8(0),
                B(Star3),
                B(Ldar), R(1),
  /*  112 E> */ B(ToString),
                B(Add), R(3), U8(0),
  /*  116 S> */ B(Return),
]
constant pool: [
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

