#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return /ab+d/;
"
frame size: 0
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   34 S> */ B(CreateRegExpLiteral), U8(0), U8(0), U16(0),
  /*   48 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["ab+d"],
]
handlers: [
]

---
snippet: "
  return /(\\w+)\\s(\\w+)/i;
"
frame size: 0
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   34 S> */ B(CreateRegExpLiteral), U8(0), U8(0), U16(2),
  /*   57 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["(\u005cw+)\u005cs(\u005cw+)"],
]
handlers: [
]

---
snippet: "
  return /ab+d/.exec('abdd');
"
frame size: 3
parameter count: 1
bytecode array length: 20
bytecodes: [
  /*   34 S> */ B(CreateRegExpLiteral), U8(0), U8(0), U16(0),
                B(Star1),
  /*   48 E> */ B(GetNamedProperty), R(1), U8(1), U8(1),
                B(Star0),
                B(LdaConstant), U8(2),
                B(Star2),
  /*   48 E> */ B(CallProperty1), R(0), R(1), R(2), U8(3),
  /*   61 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["ab+d"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["exec"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["abdd"],
]
handlers: [
]

