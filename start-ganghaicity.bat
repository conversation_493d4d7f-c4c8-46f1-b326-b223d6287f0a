@echo off
title GangHaiCity Launcher
echo ========================================
echo          GangHaiCity Launcher
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/3] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo Node.js is installed.
echo.

REM Start the content server
echo [2/3] Starting GangHaiCity Content Server...
cd server

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing server dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install server dependencies!
        pause
        exit /b 1
    )
)

REM Start server in background
echo Starting content server on http://localhost:3001
start /min "GangHaiCity Content Server" cmd /c "npm start"

REM Wait a moment for server to start
timeout /t 3 /nobreak >nul

REM Check if server is running
echo Checking server status...
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Content server may not be running properly
    echo Please check the server window for errors
    echo.
)

cd ..

REM Start GangHaiCity
echo [3/3] Starting GangHaiCity...
echo.
echo Starting GangHaiCity.exe...
echo If this is the first run, it will download necessary files.
echo.

cd code\bin\five\release
if exist "GangHaiCity.exe" (
    start "" "GangHaiCity.exe"
    echo GangHaiCity has been started!
) else (
    echo ERROR: GangHaiCity.exe not found!
    echo Please make sure you have built the project first.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Both Content Server and GangHaiCity are now running.
echo.
echo To stop the content server, close the "GangHaiCity Content Server" window.
echo To stop GangHaiCity, close the game normally.
echo ========================================
echo.
pause
