#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f() {
   var x = 13;
   return typeof(x);
  };
  f();
"
frame size: 1
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   24 S> */ B(LdaSmi), I8(13),
                B(Star0),
  /*   29 S> */ B(TypeOf),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 13;
  function f() {
   return typeof(x);
  };
  f();
"
frame size: 0
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   28 S> */ B(LdaGlobalInsideTypeof), U8(0), U8(0),
                B(TypeOf),
  /*   45 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

