#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var x = 0; return x;
"
frame size: 1
parameter count: 1
bytecode array length: 3
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   54 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return x + 3;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   54 S> */ B(AddSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return 3 + x;
"
frame size: 2
parameter count: 1
bytecode array length: 11
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   54 E> */ B(Add), R(1), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return x - 3;
"
frame size: 1
parameter count: 1
bytecode array length: 6
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   54 S> */ B(SubSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return 3 - x;
"
frame size: 2
parameter count: 1
bytecode array length: 11
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   54 E> */ B(Sub), R(1), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return x * 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   54 S> */ B(MulSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return 3 * x;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   54 S> */ B(MulSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return x / 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   54 S> */ B(DivSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return 3 / x;
"
frame size: 2
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   54 E> */ B(Div), R(1), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return x % 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   54 S> */ B(ModSmi), I8(3), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 4; return 3 % x;
"
frame size: 2
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(4),
                B(Star0),
  /*   45 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   54 E> */ B(Mod), R(1), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return x | 2;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseOrSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return 2 | x;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseOrSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return x ^ 2;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseXorSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return 2 ^ x;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseXorSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return x & 2;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseAndSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 1; return 2 & x;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   54 S> */ B(BitwiseAndSmi), I8(2), U8(0),
  /*   58 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return x << 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   55 S> */ B(ShiftLeftSmi), I8(3), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return 3 << x;
"
frame size: 2
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   46 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   55 E> */ B(ShiftLeft), R(1), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return x >> 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   55 S> */ B(ShiftRightSmi), I8(3), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return 3 >> x;
"
frame size: 2
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   46 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   55 E> */ B(ShiftRight), R(1), U8(0),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return x >>> 3;
"
frame size: 1
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   55 S> */ B(ShiftRightLogicalSmi), I8(3), U8(0),
  /*   61 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 10; return 3 >>> x;
"
frame size: 2
parameter count: 1
bytecode array length: 12
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   46 S> */ B(LdaSmi), I8(3),
                B(Star1),
                B(Ldar), R(0),
  /*   55 E> */ B(ShiftRightLogical), R(1), U8(0),
  /*   61 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var x = 0; return (x, 3);
"
frame size: 1
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   42 S> */ B(LdaZero),
                B(Star0),
  /*   56 S> */ B(LdaSmi), I8(3),
  /*   59 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

