#define HAVE_NATIVE_TYPES 1
static const uint32_t nt_38[] = { 0x00000004, 0x00000000, 0x80000004, 0x80000004, 0x80000004 };
static const uint32_t nt_62[] = { 0x40000105, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_50[] = { 0x4000000B, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_61[] = { 0x00000003, 0xC0000000, 0xA0000000, 0x00000000 };
static const uint32_t nt_60[] = { 0x00000004, 0xC0000000, 0xC0000000, 0xA0000000, 0x00000000 };
static const uint32_t nt_59[] = { 0x20000010, 0x00000000, 0xC0000000, 0xC0000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_10[] = { 0x40000001, 0x00000000 };
static const uint32_t nt_58[] = { 0x0000000B, 0x00000000, 0xC0000000, 0xC0000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_17[] = { 0x00000002, 0xC0000000, 0xC0000000 };
static const uint32_t nt_32[] = { 0x40000102, 0x00000000, 0x00000000 };
static const uint32_t nt_56[] = { 0x20000007, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_4[] = { 0x40000003, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_55[] = { 0x4000000A, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_54[] = { 0x00000005, 0xC0000000, 0xC0000000, 0xA0000000, 0x00000000, 0x00000000 };
static const uint32_t nt_37[] = { 0x00000003, 0x00000000, 0x80000004, 0x80000004 };
static const uint32_t nt_13[] = { 0x40000108, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_53[] = { 0x00000002, 0x00000000, 0xC0000000 };
static const uint32_t nt_52[] = { 0x00000003, 0xC0000000, 0x00000000, 0x00000000 };
static const uint32_t nt_20[] = { 0x00000502, 0xA0000000, 0x00000000 };
static const uint32_t nt_51[] = { 0x40000007, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_49[] = { 0x40000004, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_3[] = { 0x00000102, 0xC0000000, 0xC0000000 };
static const uint32_t nt_48[] = { 0x40000006, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_47[] = { 0x40000008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_46[] = { 0x00000104, 0xC0000000, 0xC0000000, 0xC0000000, 0x00000000 };
static const uint32_t nt_6[] = { 0x4000000E, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_12[] = { 0x40000107, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_45[] = { 0x00000002, 0xC0000000, 0x00000000 };
static const uint32_t nt_39[] = { 0x00000002, 0x00000000, 0x80000004 };
static const uint32_t nt_23[] = { 0x40000500 };
static const uint32_t nt_0[] = { 0x40000103, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_43[] = { 0x00000003, 0xC0000000, 0xC0000000, 0x00000000 };
static const uint32_t nt_27[] = { 0x40000401, 0x00000000 };
static const uint32_t nt_42[] = { 0x00000102, 0xA0000000, 0x00000000 };
static const uint32_t nt_34[] = { 0x00000502, 0xC0000000, 0x00000000 };
static const uint32_t nt_30[] = { 0x00000701, 0xC0000000 };
static const uint32_t nt_36[] = { 0x00000702, 0xC0000000, 0xC0000000 };
static const uint32_t nt_44[] = { 0x00000001, 0x80000004 };
static const uint32_t nt_29[] = { 0x40000100 };
static const uint32_t nt_40[] = { 0x00000103, 0x00000000, 0x80000004, 0x80000004 };
static const uint32_t nt_35[] = { 0x00000503, 0xC0000000, 0xC0000000, 0x00000000 };
static const uint32_t nt_31[] = { 0x40000300 };
static const uint32_t nt_41[] = { 0x40000005, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_25[] = { 0x00000202, 0xC0000000, 0x00000000 };
static const uint32_t nt_57[] = { 0x40000009, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_5[] = { 0x00000103, 0xC0000000, 0xC0000000, 0xC0000000 };
static const uint32_t nt_18[] = { 0x00000501, 0xC0000000 };
static const uint32_t nt_15[] = { 0x00000106, 0x00000000, 0xC0000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_11[] = { 0x00000001, 0xC0000000 };
static const uint32_t nt_26[] = { 0x00000708, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0xA0000000, 0x00000000 };
static const uint32_t nt_33[] = { 0x00000401, 0xC0000000 };
static const uint32_t nt_2[] = { 0x40000104, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_24[] = { 0x00000502, 0xC0000000, 0xC0000000 };
static const uint32_t nt_9[] = { 0x40000002, 0x00000000, 0x00000000 };
static const uint32_t nt_1[] = { 0x40000101, 0x00000000 };
static const uint32_t nt_28[] = { 0x40000201, 0x00000000 };
static const uint32_t nt_8[] = { 0x00000101, 0xC0000000 };
static const uint32_t nt_22[] = { 0x40000700 };
static const uint32_t nt_21[] = { 0x00000201, 0xC0000000 };
static const uint32_t nt_7[] = { 0x40000000 };
static const uint32_t nt_19[] = { 0x40000501, 0x00000000 };
static const uint32_t nt_14[] = { 0x40000106, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 };
static const uint32_t nt_16[] = { 0x00000102, 0xC0000000, 0x00000000 };
static const NativeTypeInfo native_types[] = {
{ 0xc6f43d0e        , nt_0   }, // UNKNOWN/ADD_BLIP_FOR_COORD (trivial)
{ 0x30822554        , nt_1   }, // UNKNOWN/ADD_BLIP_FOR_ENTITY (trivial)
{ 0x4626756c        , nt_2   }, // UNKNOWN/ADD_BLIP_FOR_RADIUS (trivial)
{ 0xab7f7241        , nt_3   }, // CFX/ADD_CONVAR_CHANGE_LISTENER
{ 0x70559ac7        , nt_4   }, // UNKNOWN/ADD_PED_DECORATION_FROM_HASHES (trivial)
{ 0x5ba35aaf        , nt_5   }, // CFX/ADD_STATE_BAG_CHANGE_HANDLER
{ 0xc1c0855a        , nt_6   }, // UNKNOWN/APPLY_FORCE_TO_ENTITY (trivial)
{ 0xfa29d35d        , nt_7   }, // CFX/CANCEL_EVENT (trivial)
{ 0x429461c3        , nt_8   }, // CFX/CAN_PLAYER_START_COMMERCE_SESSION
{ 0x2d23d743        , nt_9   }, // UNKNOWN/CLEAR_PED_PROP (trivial)
{ 0xa635f451        , nt_10  }, // UNKNOWN/CLEAR_PED_SECONDARY_TASK (trivial)
{ 0xde3316ab        , nt_10  }, // UNKNOWN/CLEAR_PED_TASKS (trivial)
{ 0xbc045625        , nt_10  }, // UNKNOWN/CLEAR_PED_TASKS_IMMEDIATELY (trivial)
{ 0x54ea5bcc        , nt_11  }, // UNKNOWN/CLEAR_PLAYER_WANTED_LEVEL
{ 0x2f7aa05c        , nt_12  }, // UNKNOWN/CREATE_OBJECT (trivial)
{ 0x58040420        , nt_12  }, // UNKNOWN/CREATE_OBJECT_NO_OFFSET (trivial)
{ 0x389ef71         , nt_13  }, // UNKNOWN/CREATE_PED (trivial)
{ 0x3000f092        , nt_14  }, // UNKNOWN/CREATE_PED_INSIDE_VEHICLE (trivial)
{ 0xdd75460a        , nt_12  }, // UNKNOWN/CREATE_VEHICLE (trivial)
{ 0x6ae51d4b        , nt_15  }, // CFX/CREATE_VEHICLE_SERVER_SETTER
{ 0xfaa3d236        , nt_10  }, // CFX/DELETE_ENTITY (trivial)
{ 0x1e86f206        , nt_11  }, // CFX/DELETE_FUNCTION_REFERENCE
{ 0x7389b5df        , nt_11  }, // CFX/DELETE_RESOURCE_KVP
{ 0x4152c90         , nt_11  }, // CFX/DELETE_RESOURCE_KVP_NO_SYNC
{ 0x523ba3da        , nt_10  }, // CFX/DELETE_TRAIN (trivial)
{ 0x43f15989        , nt_1   }, // CFX/DOES_BOAT_SINK_WHEN_WRECKED (trivial)
{ 0x3ac90869        , nt_1   }, // CFX/DOES_ENTITY_EXIST (trivial)
{ 0x12038599        , nt_8   }, // CFX/DOES_PLAYER_EXIST
{ 0x167aba27        , nt_16  }, // CFX/DOES_PLAYER_OWN_SKU
{ 0xdef0480b        , nt_16  }, // CFX/DOES_PLAYER_OWN_SKU_EXT
{ 0x77cc80dc        , nt_1   }, // CFX/DOES_TRAIN_STOP_AT_STATIONS (trivial)
{ 0xba0613e1        , nt_17  }, // CFX/DROP_PLAYER
{ 0xf4e2079d        , nt_18  }, // CFX/DUPLICATE_FUNCTION_REFERENCE
{ 0xf97b1c93        , nt_10  }, // CFX/ENABLE_ENHANCED_HOST_SUPPORT (trivial)
{ 0xb3210203        , nt_10  }, // CFX/END_FIND_KVP (trivial)
{ 0x3bb78f05        , nt_10  }, // CFX/ENSURE_ENTITY_STATE_BAG (trivial)
{ 0x561c060b        , nt_11  }, // CFX/EXECUTE_COMMAND
{ 0xbd7bebc5        , nt_19  }, // CFX/FIND_KVP (trivial)
{ 0x13b6855d        , nt_10  }, // CFX/FLAG_SERVER_AS_PRIVATE (trivial)
{ 0xe27c97a0        , nt_7   }, // CFX/FLUSH_RESOURCE_KVP (trivial)
{ 0xd70c3bca        , nt_20  }, // CFX/FORMAT_STACK_TRACE
{ 0x65c16d57        , nt_9   }, // UNKNOWN/FREEZE_ENTITY_POSITION (trivial)
{ 0x62fc38d0        , nt_21  }, // CFX/GET_AIR_DRAG_MULTIPLIER_FOR_PLAYERS_VEHICLE
{ 0x6886c3fe        , nt_22  }, // CFX/GET_ALL_OBJECTS (trivial)
{ 0xb8584fef        , nt_22  }, // CFX/GET_ALL_PEDS (trivial)
{ 0x332169f5        , nt_22  }, // CFX/GET_ALL_VEHICLES (trivial)
{ 0x72ff2e73        , nt_1   }, // UNKNOWN/GET_BLIP_SPRITE (trivial)
{ 0xe57429fa        , nt_23  }, // CFX/GET_CONSOLE_BUFFER (trivial)
{ 0x6ccd2564        , nt_24  }, // CFX/GET_CONVAR
{ 0x7e8ebfe5        , nt_16  }, // CFX/GET_CONVAR_BOOL
{ 0x9e666d          , nt_25  }, // CFX/GET_CONVAR_FLOAT
{ 0x935c0ab2        , nt_16  }, // CFX/GET_CONVAR_INT
{ 0xb0237302        , nt_1   }, // CFX/GET_CURRENT_PED_WEAPON (trivial)
{ 0xe5e9ebbb        , nt_23  }, // CFX/GET_CURRENT_RESOURCE_NAME (trivial)
{ 0xdffba12f        , nt_26  }, // CFX/GET_ENTITIES_IN_RADIUS
{ 0xfe1589f9        , nt_1   }, // CFX/GET_ENTITY_ATTACHED_TO (trivial)
{ 0xe8c0c629        , nt_1   }, // CFX/GET_ENTITY_COLLISION_DISABLED (trivial)
{ 0x1647f1cb        , nt_27  }, // CFX/GET_ENTITY_COORDS (trivial)
{ 0x4bdf1867        , nt_8   }, // CFX/GET_ENTITY_FROM_STATE_BAG_NAME
{ 0x972cc383        , nt_28  }, // CFX/GET_ENTITY_HEADING (trivial)
{ 0x8e3222b7        , nt_1   }, // CFX/GET_ENTITY_HEALTH (trivial)
{ 0xc7ae6aa1        , nt_1   }, // CFX/GET_ENTITY_MAX_HEALTH (trivial)
{ 0xdafcb3ec        , nt_1   }, // CFX/GET_ENTITY_MODEL (trivial)
{ 0xd16ea02f        , nt_1   }, // CFX/GET_ENTITY_ORPHAN_MODE (trivial)
{ 0xfc30ddff        , nt_1   }, // CFX/GET_ENTITY_POPULATION_TYPE (trivial)
{ 0x91b38fb6        , nt_1   }, // CFX/GET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED (trivial)
{ 0x8ff45b04        , nt_27  }, // CFX/GET_ENTITY_ROTATION (trivial)
{ 0x9bf8a73f        , nt_27  }, // CFX/GET_ENTITY_ROTATION_VELOCITY (trivial)
{ 0xed4b0486        , nt_1   }, // CFX/GET_ENTITY_ROUTING_BUCKET (trivial)
{ 0xb7f70784        , nt_19  }, // CFX/GET_ENTITY_SCRIPT (trivial)
{ 0x9e1e4798        , nt_28  }, // CFX/GET_ENTITY_SPEED (trivial)
{ 0xb1bd08d         , nt_1   }, // CFX/GET_ENTITY_TYPE (trivial)
{ 0xc14c9b6b        , nt_27  }, // CFX/GET_ENTITY_VELOCITY (trivial)
{ 0x804b9f7b        , nt_29  }, // CFX/GET_GAME_BUILD_NUMBER (trivial)
{ 0xe8eaa18b        , nt_23  }, // CFX/GET_GAME_NAME (trivial)
{ 0x2b9d4f50        , nt_30  }, // CFX/GET_GAME_POOL
{ 0xa4ea0691        , nt_31  }, // CFX/GET_GAME_TIMER (trivial)
{ 0x98eff6f1        , nt_8   }, // CFX/GET_HASH_KEY
{ 0xa886495d        , nt_1   }, // CFX/GET_HELI_BODY_HEALTH (trivial)
{ 0x82afc0a3        , nt_1   }, // CFX/GET_HELI_DISABLE_EXPLODE_FROM_BODY_DAMAGE (trivial)
{ 0xa0fa0354        , nt_1   }, // CFX/GET_HELI_ENGINE_HEALTH (trivial)
{ 0xd4ec7858        , nt_1   }, // CFX/GET_HELI_GAS_TANK_HEALTH (trivial)
{ 0xc37d668         , nt_28  }, // CFX/GET_HELI_MAIN_ROTOR_DAMAGE_SCALE (trivial)
{ 0xf01e2aab        , nt_28  }, // CFX/GET_HELI_MAIN_ROTOR_HEALTH (trivial)
{ 0x1944ac95        , nt_28  }, // CFX/GET_HELI_PITCH_CONTROL (trivial)
{ 0xc40161e2        , nt_28  }, // CFX/GET_HELI_REAR_ROTOR_DAMAGE_SCALE (trivial)
{ 0x33ee6e2b        , nt_28  }, // CFX/GET_HELI_REAR_ROTOR_HEALTH (trivial)
{ 0x12948de9        , nt_28  }, // CFX/GET_HELI_ROLL_CONTROL (trivial)
{ 0x22239130        , nt_28  }, // CFX/GET_HELI_TAIL_ROTOR_DAMAGE_SCALE (trivial)
{ 0xa41bc13d        , nt_28  }, // CFX/GET_HELI_TAIL_ROTOR_HEALTH (trivial)
{ 0x8e86238d        , nt_28  }, // CFX/GET_HELI_THROTTLE_CONTROL (trivial)
{ 0x8fdc0768        , nt_28  }, // CFX/GET_HELI_YAW_CONTROL (trivial)
{ 0x5f70f5a3        , nt_23  }, // CFX/GET_HOST_ID (trivial)
{ 0x9f1c4383        , nt_29  }, // CFX/GET_INSTANCE_ID (trivial)
{ 0x4d52fe5b        , nt_23  }, // CFX/GET_INVOKING_RESOURCE (trivial)
{ 0x3efe38d1        , nt_1   }, // CFX/GET_IS_HELI_ENGINE_RUNNING (trivial)
{ 0x7dc6d022        , nt_1   }, // CFX/GET_IS_VEHICLE_ENGINE_RUNNING (trivial)
{ 0xd7ec8760        , nt_1   }, // CFX/GET_IS_VEHICLE_PRIMARY_COLOUR_CUSTOM (trivial)
{ 0x288ad228        , nt_1   }, // CFX/GET_IS_VEHICLE_SECONDARY_COLOUR_CUSTOM (trivial)
{ 0xa6f02670        , nt_1   }, // CFX/GET_LANDING_GEAR_STATE (trivial)
{ 0xf7c6792d        , nt_32  }, // CFX/GET_LAST_PED_IN_VEHICLE_SEAT (trivial)
{ 0x23b2a641        , nt_1   }, // CFX/GET_NET_TYPE_FROM_ENTITY (trivial)
{ 0xff7f66ab        , nt_8   }, // CFX/GET_NUM_PLAYER_IDENTIFIERS
{ 0x63d13184        , nt_29  }, // CFX/GET_NUM_PLAYER_INDICES (trivial)
{ 0x619e4a3d        , nt_8   }, // CFX/GET_NUM_PLAYER_TOKENS
{ 0x863f27b         , nt_29  }, // CFX/GET_NUM_RESOURCES (trivial)
{ 0x776e864         , nt_3   }, // CFX/GET_NUM_RESOURCE_METADATA
{ 0x23473ea4        , nt_18  }, // CFX/GET_PASSWORD_HASH
{ 0x2ce311a7        , nt_1   }, // CFX/GET_PED_ARMOUR (trivial)
{ 0x63458c27        , nt_1   }, // CFX/GET_PED_CAUSE_OF_DEATH (trivial)
{ 0xc182f76e        , nt_28  }, // CFX/GET_PED_DESIRED_HEADING (trivial)
{ 0x388fde9a        , nt_32  }, // CFX/GET_PED_IN_VEHICLE_SEAT (trivial)
{ 0xa45b6c8d        , nt_1   }, // CFX/GET_PED_MAX_HEALTH (trivial)
{ 0x354f283c        , nt_1   }, // CFX/GET_PED_RELATIONSHIP_GROUP_HASH (trivial)
{ 0x84fe084         , nt_1   }, // CFX/GET_PED_SCRIPT_TASK_COMMAND (trivial)
{ 0x44b0e5e2        , nt_1   }, // CFX/GET_PED_SCRIPT_TASK_STAGE (trivial)
{ 0x535db43f        , nt_1   }, // CFX/GET_PED_SOURCE_OF_DAMAGE (trivial)
{ 0x84adf9eb        , nt_1   }, // CFX/GET_PED_SOURCE_OF_DEATH (trivial)
{ 0x7f4563d3        , nt_32  }, // CFX/GET_PED_SPECIFIC_TASK_TYPE (trivial)
{ 0x40321b83        , nt_1   }, // CFX/GET_PED_STEALTH_MOVEMENT (trivial)
{ 0x433c765d        , nt_33  }, // CFX/GET_PLAYER_CAMERA_ROTATION
{ 0xfee404f9        , nt_18  }, // CFX/GET_PLAYER_ENDPOINT
{ 0x98d244          , nt_8   }, // CFX/GET_PLAYER_FAKE_WANTED_LEVEL
{ 0x586f80ff        , nt_33  }, // CFX/GET_PLAYER_FOCUS_POS
{ 0xc8a9ce08        , nt_19  }, // CFX/GET_PLAYER_FROM_INDEX (trivial)
{ 0xa56135e0        , nt_8   }, // CFX/GET_PLAYER_FROM_STATE_BAG_NAME
{ 0xe52d9680        , nt_18  }, // CFX/GET_PLAYER_GUID
{ 0x7302dbcf        , nt_34  }, // CFX/GET_PLAYER_IDENTIFIER
{ 0xa61c8fc6        , nt_24  }, // CFX/GET_PLAYER_IDENTIFIER_BY_TYPE
{ 0x680c90ee        , nt_8   }, // CFX/GET_PLAYER_INVINCIBLE
{ 0x427e8e6a        , nt_8   }, // CFX/GET_PLAYER_LAST_MSG
{ 0x2a50657         , nt_8   }, // CFX/GET_PLAYER_MAX_ARMOUR
{ 0x8154e470        , nt_8   }, // CFX/GET_PLAYER_MAX_HEALTH
{ 0x8689a825        , nt_21  }, // CFX/GET_PLAYER_MELEE_WEAPON_DAMAGE_MODIFIER
{ 0x406b4b20        , nt_18  }, // CFX/GET_PLAYER_NAME
{ 0x6e31e993        , nt_8   }, // CFX/GET_PLAYER_PED
{ 0x9a928294        , nt_16  }, // CFX/GET_PLAYER_PEER_STATISTICS
{ 0xff1290d4        , nt_8   }, // CFX/GET_PLAYER_PING
{ 0x52441c34        , nt_8   }, // CFX/GET_PLAYER_ROUTING_BUCKET
{ 0x9873e404        , nt_8   }, // CFX/GET_PLAYER_TEAM
{ 0x7ade63e1        , nt_16  }, // CFX/GET_PLAYER_TIME_IN_PURSUIT
{ 0x67d2e605        , nt_8   }, // CFX/GET_PLAYER_TIME_ONLINE
{ 0x54c06897        , nt_34  }, // CFX/GET_PLAYER_TOKEN
{ 0x821f2d2c        , nt_33  }, // CFX/GET_PLAYER_WANTED_CENTRE_POSITION
{ 0xbdcdd163        , nt_8   }, // CFX/GET_PLAYER_WANTED_LEVEL
{ 0x2a3d7cda        , nt_21  }, // CFX/GET_PLAYER_WEAPON_DAMAGE_MODIFIER
{ 0xf1543251        , nt_21  }, // CFX/GET_PLAYER_WEAPON_DEFENSE_MODIFIER
{ 0x986b65ff        , nt_21  }, // CFX/GET_PLAYER_WEAPON_DEFENSE_MODIFIER_2
{ 0xd4bef069        , nt_22  }, // CFX/GET_REGISTERED_COMMANDS (trivial)
{ 0x387246b7        , nt_19  }, // CFX/GET_RESOURCE_BY_FIND_INDEX (trivial)
{ 0x97628584        , nt_30  }, // CFX/GET_RESOURCE_COMMANDS
{ 0x35bdceea        , nt_21  }, // CFX/GET_RESOURCE_KVP_FLOAT
{ 0x557b586a        , nt_8   }, // CFX/GET_RESOURCE_KVP_INT
{ 0x5240da5a        , nt_18  }, // CFX/GET_RESOURCE_KVP_STRING
{ 0x964bab1d        , nt_35  }, // CFX/GET_RESOURCE_METADATA
{ 0x61dcf017        , nt_18  }, // CFX/GET_RESOURCE_PATH
{ 0x4039b485        , nt_18  }, // CFX/GET_RESOURCE_STATE
{ 0xd240123e        , nt_1   }, // CFX/GET_SELECTED_PED_WEAPON (trivial)
{ 0x78d864c7        , nt_30  }, // CFX/GET_STATE_BAG_KEYS
{ 0x637f4c75        , nt_36  }, // CFX/GET_STATE_BAG_VALUE
{ 0x1c939e87        , nt_28  }, // CFX/GET_THRUSTER_SIDE_RCS_THROTTLE (trivial)
{ 0x94e24c96        , nt_28  }, // CFX/GET_THRUSTER_THROTTLE (trivial)
{ 0x456e34a         , nt_1   }, // CFX/GET_TRAIN_BACKWARD_CARRIAGE (trivial)
{ 0x95070fa         , nt_1   }, // CFX/GET_TRAIN_CARRIAGE_ENGINE (trivial)
{ 0x4b8285cf        , nt_1   }, // CFX/GET_TRAIN_CARRIAGE_INDEX (trivial)
{ 0xa4921ef5        , nt_28  }, // CFX/GET_TRAIN_CRUISE_SPEED (trivial)
{ 0x8daf79b6        , nt_1   }, // CFX/GET_TRAIN_DIRECTION (trivial)
{ 0x24dc88d9        , nt_1   }, // CFX/GET_TRAIN_FORWARD_CARRIAGE (trivial)
{ 0x81b50033        , nt_1   }, // CFX/GET_TRAIN_STATE (trivial)
{ 0x9aa339d         , nt_1   }, // CFX/GET_TRAIN_TRACK_INDEX (trivial)
{ 0x2b2fcc28        , nt_28  }, // CFX/GET_VEHICLE_BODY_HEALTH (trivial)
{ 0x40d82d88        , nt_37  }, // CFX/GET_VEHICLE_COLOURS
{ 0x1c2b9fef        , nt_38  }, // CFX/GET_VEHICLE_CUSTOM_PRIMARY_COLOUR
{ 0x3ff247a2        , nt_38  }, // CFX/GET_VEHICLE_CUSTOM_SECONDARY_COLOUR
{ 0xa0dbd08d        , nt_39  }, // CFX/GET_VEHICLE_DASHBOARD_COLOUR
{ 0xfd15c065        , nt_28  }, // CFX/GET_VEHICLE_DIRT_LEVEL (trivial)
{ 0x1dc50247        , nt_1   }, // CFX/GET_VEHICLE_DOORS_LOCKED_FOR_PLAYER (trivial)
{ 0xd72cef2         , nt_1   }, // CFX/GET_VEHICLE_DOOR_LOCK_STATUS (trivial)
{ 0x6e35c49c        , nt_32  }, // CFX/GET_VEHICLE_DOOR_STATUS (trivial)
{ 0x8880038a        , nt_28  }, // CFX/GET_VEHICLE_ENGINE_HEALTH (trivial)
{ 0x80e4659b        , nt_37  }, // CFX/GET_VEHICLE_EXTRA_COLOURS
{ 0xad40ad55        , nt_28  }, // CFX/GET_VEHICLE_FLIGHT_NOZZLE_POSITION (trivial)
{ 0x483b013c        , nt_1   }, // CFX/GET_VEHICLE_HANDBRAKE (trivial)
{ 0xd7147656        , nt_1   }, // CFX/GET_VEHICLE_HEADLIGHTS_COLOUR (trivial)
{ 0xfbde9fd8        , nt_1   }, // CFX/GET_VEHICLE_HOMING_LOCKON_STATE (trivial)
{ 0xdea49773        , nt_1   }, // CFX/GET_VEHICLE_HORN_TYPE (trivial)
{ 0xccff3b6e        , nt_39  }, // CFX/GET_VEHICLE_INTERIOR_COLOUR
{ 0x7c278621        , nt_40  }, // CFX/GET_VEHICLE_LIGHTS_STATE
{ 0xec82a51d        , nt_1   }, // CFX/GET_VEHICLE_LIVERY (trivial)
{ 0x4a557117        , nt_1   }, // CFX/GET_VEHICLE_LOCK_ON_TARGET (trivial)
{ 0xd9319dcb        , nt_38  }, // CFX/GET_VEHICLE_NEON_COLOUR
{ 0x684bdbf2        , nt_32  }, // CFX/GET_VEHICLE_NEON_ENABLED (trivial)
{ 0xe8522d58        , nt_19  }, // CFX/GET_VEHICLE_NUMBER_PLATE_TEXT (trivial)
{ 0x499747b6        , nt_1   }, // CFX/GET_VEHICLE_NUMBER_PLATE_TEXT_INDEX (trivial)
{ 0xafe92319        , nt_32  }, // CFX/GET_VEHICLE_PED_IS_IN (trivial)
{ 0xe41595ce        , nt_28  }, // CFX/GET_VEHICLE_PETROL_TANK_HEALTH (trivial)
{ 0x57037960        , nt_1   }, // CFX/GET_VEHICLE_RADIO_STATION_INDEX (trivial)
{ 0x872cf42         , nt_1   }, // CFX/GET_VEHICLE_ROOF_LIVERY (trivial)
{ 0x1382fcea        , nt_28  }, // CFX/GET_VEHICLE_STEERING_ANGLE (trivial)
{ 0x9963d5f9        , nt_1   }, // CFX/GET_VEHICLE_TOTAL_REPAIRS (trivial)
{ 0xa273060e        , nt_19  }, // CFX/GET_VEHICLE_TYPE (trivial)
{ 0x75280015        , nt_38  }, // CFX/GET_VEHICLE_TYRE_SMOKE_COLOR
{ 0xda58d7ae        , nt_1   }, // CFX/GET_VEHICLE_WHEEL_TYPE (trivial)
{ 0x13d53892        , nt_1   }, // CFX/GET_VEHICLE_WINDOW_TINT (trivial)
{ 0x3e1e286d        , nt_4   }, // UNKNOWN/GIVE_WEAPON_COMPONENT_TO_PED (trivial)
{ 0xc4d88a85        , nt_41  }, // UNKNOWN/GIVE_WEAPON_TO_PED (trivial)
{ 0x9c9a3be0        , nt_1   }, // CFX/HAS_ENTITY_BEEN_MARKED_AS_NO_LONGER_NEEDED (trivial)
{ 0xb8af3137        , nt_1   }, // CFX/HAS_VEHICLE_BEEN_DAMAGED_BY_BULLETS (trivial)
{ 0xe4e83a5b        , nt_1   }, // CFX/HAS_VEHICLE_BEEN_OWNED_BY_PLAYER (trivial)
{ 0x7ebb9929        , nt_8   }, // CFX/IS_ACE_ALLOWED
{ 0xd5c39ee6        , nt_1   }, // CFX/IS_BOAT_ANCHORED_AND_FROZEN (trivial)
{ 0x9049db44        , nt_1   }, // CFX/IS_BOAT_WRECKED (trivial)
{ 0xcf24c52e        , nt_29  }, // CFX/IS_DUPLICITY_VERSION (trivial)
{ 0xedbe6add        , nt_1   }, // CFX/IS_ENTITY_POSITION_FROZEN (trivial)
{ 0x120b4ed5        , nt_1   }, // CFX/IS_ENTITY_VISIBLE (trivial)
{ 0x76876154        , nt_1   }, // CFX/IS_FLASH_LIGHT_ON (trivial)
{ 0x23e46bd7        , nt_1   }, // CFX/IS_HELI_TAIL_BOOM_BREAKABLE (trivial)
{ 0x2c59f987        , nt_1   }, // CFX/IS_HELI_TAIL_BOOM_BROKEN (trivial)
{ 0x404794ca        , nt_1   }, // CFX/IS_PED_A_PLAYER (trivial)
{ 0x25865633        , nt_1   }, // CFX/IS_PED_HANDCUFFED (trivial)
{ 0xc833bbe1        , nt_1   }, // CFX/IS_PED_RAGDOLL (trivial)
{ 0xefeed13c        , nt_1   }, // CFX/IS_PED_STRAFING (trivial)
{ 0x5ae7eda2        , nt_1   }, // CFX/IS_PED_USING_ACTION_MODE (trivial)
{ 0xdedae23d        , nt_3   }, // CFX/IS_PLAYER_ACE_ALLOWED
{ 0xbefe93f4        , nt_8   }, // CFX/IS_PLAYER_COMMERCE_INFO_LOADED
{ 0x1d14f4fe        , nt_8   }, // CFX/IS_PLAYER_COMMERCE_INFO_LOADED_EXT
{ 0x89a3881a        , nt_8   }, // CFX/IS_PLAYER_EVADING_WANTED_LEVEL
{ 0x1f14f2ac        , nt_8   }, // CFX/IS_PLAYER_IN_FREE_CAM_MODE
{ 0xc7d2c20c        , nt_8   }, // CFX/IS_PLAYER_USING_SUPER_JUMP
{ 0x37cf52ce        , nt_3   }, // CFX/IS_PRINCIPAL_ACE_ALLOWED
{ 0xfa9336e5        , nt_1   }, // CFX/IS_TRAIN_CABOOSE (trivial)
{ 0xbb340d04        , nt_1   }, // CFX/IS_VEHICLE_ENGINE_STARTING (trivial)
{ 0x42098b5         , nt_32  }, // CFX/IS_VEHICLE_EXTRA_TURNED_ON (trivial)
{ 0x25eb5873        , nt_1   }, // CFX/IS_VEHICLE_SIREN_ON (trivial)
{ 0x48c80210        , nt_0   }, // CFX/IS_VEHICLE_TYRE_BURST (trivial)
{ 0xac4ef23d        , nt_32  }, // CFX/IS_VEHICLE_WINDOW_INTACT (trivial)
{ 0xa8f63eab        , nt_11  }, // CFX/LOAD_PLAYER_COMMERCE_DATA
{ 0x7995539e        , nt_11  }, // CFX/LOAD_PLAYER_COMMERCE_DATA_EXT
{ 0x76a9ee1f        , nt_24  }, // CFX/LOAD_RESOURCE_FILE
{ 0x262663c5        , nt_10  }, // CFX/MUMBLE_CREATE_CHANNEL (trivial)
{ 0x1d5d50c2        , nt_1   }, // CFX/MUMBLE_IS_PLAYER_MUTED (trivial)
{ 0xcc6c2eb1        , nt_9   }, // CFX/MUMBLE_SET_PLAYER_MUTED (trivial)
{ 0x5b912c3f        , nt_1   }, // CFX/NETWORK_GET_ENTITY_FROM_NETWORK_ID (trivial)
{ 0x526fee31        , nt_1   }, // CFX/NETWORK_GET_ENTITY_OWNER (trivial)
{ 0x1e546224        , nt_1   }, // CFX/NETWORK_GET_FIRST_ENTITY_OWNER (trivial)
{ 0x9e35dab6        , nt_1   }, // CFX/NETWORK_GET_NETWORK_ID_FROM_ENTITY (trivial)
{ 0xffeef513        , nt_33  }, // CFX/NETWORK_GET_VOICE_PROXIMITY_OVERRIDE_FOR_PLAYER
{ 0x8e8cc653        , nt_42  }, // CFX/PERFORM_HTTP_REQUEST_INTERNAL
{ 0x6b171e87        , nt_42  }, // CFX/PERFORM_HTTP_REQUEST_INTERNAL_EX
{ 0x90892ded        , nt_11  }, // CFX/PRINT_STRUCTURED_TRACE
{ 0xc795a4a9        , nt_11  }, // CFX/PROFILER_ENTER_SCOPE
{ 0xb39ca35c        , nt_7   }, // CFX/PROFILER_EXIT_SCOPE (trivial)
{ 0xf8b7d7bb        , nt_29  }, // CFX/PROFILER_IS_RECORDING (trivial)
{ 0x5fa79b0f        , nt_43  }, // CFX/REGISTER_COMMAND
{ 0x281b5448        , nt_11  }, // CFX/REGISTER_CONSOLE_LISTENER
{ 0x9862b266        , nt_24  }, // CFX/REGISTER_RESOURCE_ASSET
{ 0xd233a168        , nt_11  }, // CFX/REGISTER_RESOURCE_AS_EVENT_HANDLER
{ 0x285b43ca        , nt_17  }, // CFX/REGISTER_RESOURCE_BUILD_TASK_FACTORY
{ 0xa44ce817        , nt_9   }, // UNKNOWN/REMOVE_ALL_PED_WEAPONS (trivial)
{ 0xd8c3c1cd        , nt_44  }, // UNKNOWN/REMOVE_BLIP
{ 0xeac49841        , nt_10  }, // CFX/REMOVE_CONVAR_CHANGE_LISTENER (trivial)
{ 0xd36be661        , nt_10  }, // CFX/REMOVE_STATE_BAG_CHANGE_HANDLER (trivial)
{ 0x412aa00d        , nt_4   }, // UNKNOWN/REMOVE_WEAPON_COMPONENT_FROM_PED (trivial)
{ 0x9c37f220        , nt_9   }, // UNKNOWN/REMOVE_WEAPON_FROM_PED (trivial)
{ 0x96f93cce        , nt_45  }, // CFX/REQUEST_PLAYER_COMMERCE_SESSION
{ 0xa09e7e7b        , nt_46  }, // CFX/SAVE_RESOURCE_FILE
{ 0x636f097f        , nt_17  }, // CFX/SCAN_RESOURCE_ROOT
{ 0xb88a73ad        , nt_11  }, // CFX/SCHEDULE_RESOURCE_TICK
{ 0x8dbbb0b9        , nt_9   }, // UNKNOWN/SET_BLIP_SPRITE (trivial)
{ 0x341b16d2        , nt_17  }, // CFX/SET_CONVAR
{ 0xf292858c        , nt_17  }, // CFX/SET_CONVAR_REPLICATED
{ 0x9338d547        , nt_17  }, // CFX/SET_CONVAR_SERVER_INFO
{ 0xb8278882        , nt_4   }, // UNKNOWN/SET_CURRENT_PED_WEAPON (trivial)
{ 0xdf70b41b        , nt_47  }, // UNKNOWN/SET_ENTITY_COORDS (trivial)
{ 0xd3a183a3        , nt_9   }, // CFX/SET_ENTITY_DISTANCE_CULLING_RADIUS (trivial)
{ 0xe0ff064d        , nt_9   }, // UNKNOWN/SET_ENTITY_HEADING (trivial)
{ 0x9f7f8d36        , nt_9   }, // CFX/SET_ENTITY_IGNORE_REQUEST_CONTROL_FILTER (trivial)
{ 0x489e9162        , nt_9   }, // CFX/SET_ENTITY_ORPHAN_MODE (trivial)
{ 0xd3fc9d88        , nt_9   }, // CFX/SET_ENTITY_REMOTE_SYNCED_SCENES_ALLOWED (trivial)
{ 0xa345efe         , nt_48  }, // UNKNOWN/SET_ENTITY_ROTATION (trivial)
{ 0x635e5289        , nt_9   }, // CFX/SET_ENTITY_ROUTING_BUCKET (trivial)
{ 0xff5a1988        , nt_49  }, // UNKNOWN/SET_ENTITY_VELOCITY (trivial)
{ 0xf90b7469        , nt_11  }, // CFX/SET_GAME_TYPE
{ 0xf5c6330c        , nt_11  }, // CFX/SET_HTTP_HANDLER
{ 0xb7ba82dc        , nt_11  }, // CFX/SET_MAP_NAME
{ 0xbf90df1a        , nt_4   }, // UNKNOWN/SET_PED_AMMO (trivial)
{ 0x4e3a0cc4        , nt_9   }, // UNKNOWN/SET_PED_ARMOUR (trivial)
{ 0xcf1384c4        , nt_9   }, // UNKNOWN/SET_PED_CAN_RAGDOLL (trivial)
{ 0xd4f7b05c        , nt_41  }, // UNKNOWN/SET_PED_COMPONENT_VARIATION (trivial)
{ 0x9cfbe10d        , nt_4   }, // UNKNOWN/SET_PED_CONFIG_FLAG (trivial)
{ 0xc866a984        , nt_10  }, // UNKNOWN/SET_PED_DEFAULT_COMPONENT_VARIATION (trivial)
{ 0xa23fe32c        , nt_4   }, // UNKNOWN/SET_PED_HAIR_TINT (trivial)
{ 0x60746b88        , nt_50  }, // UNKNOWN/SET_PED_HEAD_BLEND_DATA (trivial)
{ 0xd28dba90        , nt_49  }, // UNKNOWN/SET_PED_HEAD_OVERLAY (trivial)
{ 0x7500c79         , nt_4   }, // UNKNOWN/SET_PED_INTO_VEHICLE (trivial)
{ 0x829f2e2         , nt_41  }, // UNKNOWN/SET_PED_PROP_INDEX (trivial)
{ 0x4111ba46        , nt_9   }, // UNKNOWN/SET_PED_RANDOM_COMPONENT_VARIATION (trivial)
{ 0xe3318e0e        , nt_10  }, // UNKNOWN/SET_PED_RANDOM_PROPS (trivial)
{ 0xcff6ff66        , nt_4   }, // UNKNOWN/SET_PED_RESET_FLAG (trivial)
{ 0x83cb5052        , nt_51  }, // UNKNOWN/SET_PED_TO_RAGDOLL (trivial)
{ 0xfa12e286        , nt_6   }, // UNKNOWN/SET_PED_TO_RAGDOLL_WITH_FALL (trivial)
{ 0xd17afcd8        , nt_52  }, // UNKNOWN/SET_PLAYER_CONTROL
{ 0x8a2fbad4        , nt_45  }, // CFX/SET_PLAYER_CULLING_RADIUS
{ 0xdfb9a2a2        , nt_45  }, // UNKNOWN/SET_PLAYER_INVINCIBLE
{ 0x774a4c54        , nt_45  }, // UNKNOWN/SET_PLAYER_MODEL
{ 0x6504eb38        , nt_45  }, // CFX/SET_PLAYER_ROUTING_BUCKET
{ 0xb7a0914b        , nt_52  }, // UNKNOWN/SET_PLAYER_WANTED_LEVEL
{ 0x21c7a35b        , nt_17  }, // CFX/SET_RESOURCE_KVP
{ 0x9add2938        , nt_45  }, // CFX/SET_RESOURCE_KVP_FLOAT
{ 0x3517bfbe        , nt_45  }, // CFX/SET_RESOURCE_KVP_FLOAT_NO_SYNC
{ 0x6a2b1e8         , nt_45  }, // CFX/SET_RESOURCE_KVP_INT
{ 0x26aeb707        , nt_45  }, // CFX/SET_RESOURCE_KVP_INT_NO_SYNC
{ 0xcf9a2ff         , nt_17  }, // CFX/SET_RESOURCE_KVP_NO_SYNC
{ 0xa0f2201f        , nt_53  }, // CFX/SET_ROUTING_BUCKET_ENTITY_LOCKDOWN_MODE
{ 0xce51ac2c        , nt_9   }, // CFX/SET_ROUTING_BUCKET_POPULATION_ENABLED (trivial)
{ 0x8d50e33a        , nt_54  }, // CFX/SET_STATE_BAG_VALUE
{ 0x24877d84        , nt_9   }, // UNKNOWN/SET_VEHICLE_ALARM (trivial)
{ 0x920c2517        , nt_9   }, // UNKNOWN/SET_VEHICLE_BODY_HEALTH (trivial)
{ 0x57f24253        , nt_4   }, // UNKNOWN/SET_VEHICLE_COLOURS (trivial)
{ 0xa557aead        , nt_9   }, // UNKNOWN/SET_VEHICLE_COLOUR_COMBINATION (trivial)
{ 0x8df9f9bc        , nt_49  }, // UNKNOWN/SET_VEHICLE_CUSTOM_PRIMARY_COLOUR (trivial)
{ 0x9d77259e        , nt_49  }, // UNKNOWN/SET_VEHICLE_CUSTOM_SECONDARY_COLOUR (trivial)
{ 0x2b39128b        , nt_9   }, // UNKNOWN/SET_VEHICLE_DIRT_LEVEL (trivial)
{ 0x4cdd35d0        , nt_9   }, // UNKNOWN/SET_VEHICLE_DOORS_LOCKED (trivial)
{ 0x8147fea7        , nt_4   }, // UNKNOWN/SET_VEHICLE_DOOR_BROKEN (trivial)
{ 0x400f9556        , nt_53  }, // UNKNOWN/SET_VEHICLE_NUMBER_PLATE_TEXT
{ 0xdd379006        , nt_8   }, // CFX/START_FIND_KVP
{ 0x29b440dc        , nt_8   }, // CFX/START_RESOURCE
{ 0x12a330          , nt_3   }, // CFX/STATE_BAG_HAS_KEY
{ 0x21783161        , nt_8   }, // CFX/STOP_RESOURCE
{ 0xcb0d8932        , nt_49  }, // UNKNOWN/TASK_COMBAT_PED (trivial)
{ 0x2b84d1c4        , nt_55  }, // UNKNOWN/TASK_DRIVE_BY (trivial)
{ 0xb8689b4e        , nt_56  }, // UNKNOWN/TASK_ENTER_VEHICLE (unsafe) (trivial)
{ 0xc1971f30        , nt_10  }, // UNKNOWN/TASK_EVERYONE_LEAVE_VEHICLE (trivial)
{ 0x80a9e7a7        , nt_47  }, // UNKNOWN/TASK_GO_STRAIGHT_TO_COORD (trivial)
{ 0xf91df93b        , nt_57  }, // UNKNOWN/TASK_GO_TO_COORD_ANY_MEANS (trivial)
{ 0x374827c2        , nt_51  }, // UNKNOWN/TASK_GO_TO_ENTITY (trivial)
{ 0x8dcc19c5        , nt_41  }, // UNKNOWN/TASK_HANDS_UP (trivial)
{ 0xdbdd79fa        , nt_4   }, // UNKNOWN/TASK_LEAVE_ANY_VEHICLE (trivial)
{ 0x7b1141c6        , nt_4   }, // UNKNOWN/TASK_LEAVE_VEHICLE (trivial)
{ 0x5ab552c6        , nt_58  }, // UNKNOWN/TASK_PLAY_ANIM
{ 0x3ddeb0e6        , nt_59  }, // UNKNOWN/TASK_PLAY_ANIM_ADVANCED (unsafe)
{ 0x8a632bd8        , nt_9   }, // UNKNOWN/TASK_REACT_AND_FLEE_PED (trivial)
{ 0x601c22e3        , nt_48  }, // UNKNOWN/TASK_SHOOT_AT_COORD (trivial)
{ 0xac0631c9        , nt_49  }, // UNKNOWN/TASK_SHOOT_AT_ENTITY (trivial)
{ 0x65d4a35d        , nt_4   }, // UNKNOWN/TASK_WARP_PED_INTO_VEHICLE (trivial)
{ 0x1e35dbba        , nt_17  }, // CFX/TEMP_BAN_PLAYER
{ 0x2f7a49e6        , nt_60  }, // CFX/TRIGGER_CLIENT_EVENT_INTERNAL
{ 0x91310870        , nt_61  }, // CFX/TRIGGER_EVENT_INTERNAL
{ 0x70b35890        , nt_54  }, // CFX/TRIGGER_LATENT_CLIENT_EVENT_INTERNAL
{ 0x2e310acd        , nt_3   }, // CFX/VERIFY_PASSWORD_HASH
{ 0x58382a19        , nt_29  }, // CFX/WAS_EVENT_CANCELED (trivial)
{ 0x6228f159        , nt_62  }, // UNKNOWN/_ADD_BLIP_FOR_AREA (trivial)
{ 0xec09db1b        , nt_9   }, // UNKNOWN/_SET_PED_EYE_COLOR (trivial)
{ 0x6c8d4458        , nt_4   }, // UNKNOWN/_SET_PED_FACE_FEATURE (trivial)
{ 0x78935a27        , nt_41  }, // UNKNOWN/_SET_PED_HEAD_OVERLAY_COLOR (trivial)
};
