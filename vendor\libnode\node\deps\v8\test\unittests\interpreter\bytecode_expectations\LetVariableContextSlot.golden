#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  let x = 10; function f1() {return x;}
"
frame size: 1
parameter count: 1
bytecode array length: 14
bytecodes: [
  /*   30 E> */ B(CreateFunctionContext), U8(0), U8(1),
                B(PushContext), R(0),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*   42 S> */ B(LdaSmi), I8(10),
  /*   42 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaUndefined),
  /*   72 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  let x = 10; function f1() {return x;} return x;
"
frame size: 1
parameter count: 1
bytecode array length: 15
bytecodes: [
  /*   30 E> */ B(CreateFunctionContext), U8(0), U8(1),
                B(PushContext), R(0),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*   42 S> */ B(LdaSmi), I8(10),
  /*   42 E> */ B(StaCurrentContextSlot), U8(2),
  /*   72 S> */ B(LdaImmutableCurrentContextSlot), U8(2),
  /*   81 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  let x = (x = 20); function f1() {return x;}
"
frame size: 2
parameter count: 1
bytecode array length: 23
bytecodes: [
  /*   30 E> */ B(CreateFunctionContext), U8(0), U8(1),
                B(PushContext), R(0),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*   42 S> */ B(LdaSmi), I8(20),
                B(Star1),
                B(LdaCurrentContextSlot), U8(2),
  /*   45 E> */ B(ThrowReferenceErrorIfHole), U8(1),
                B(Ldar), R(1),
                B(StaCurrentContextSlot), U8(2),
  /*   42 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaUndefined),
  /*   78 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  let x = 10; x = 20; function f1() {return x;}
"
frame size: 1
parameter count: 1
bytecode array length: 18
bytecodes: [
  /*   30 E> */ B(CreateFunctionContext), U8(0), U8(1),
                B(PushContext), R(0),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*   42 S> */ B(LdaSmi), I8(10),
  /*   42 E> */ B(StaCurrentContextSlot), U8(2),
  /*   46 S> */ B(LdaSmi), I8(20),
  /*   48 E> */ B(StaCurrentContextSlot), U8(2),
                B(LdaUndefined),
  /*   80 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
]
handlers: [
]

