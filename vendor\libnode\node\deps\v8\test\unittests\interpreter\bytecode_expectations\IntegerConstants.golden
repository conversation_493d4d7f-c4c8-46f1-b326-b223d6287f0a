#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  return 12345678;
"
frame size: 0
parameter count: 1
bytecode array length: 7
bytecodes: [
  /*   34 S> */ B(ExtraWide), B(LdaSmi), I32(12345678),
  /*   50 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1234; return 5678;
"
frame size: 1
parameter count: 1
bytecode array length: 10
bytecodes: [
  /*   42 S> */ B(Wide), B(LdaSmi), I16(1234),
                B(Star0),
  /*   48 S> */ B(Wide), B(LdaSmi), I16(5678),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  var a = 1234; return 1234;
"
frame size: 1
parameter count: 1
bytecode array length: 10
bytecodes: [
  /*   42 S> */ B(Wide), B(LdaSmi), I16(1234),
                B(Star0),
  /*   48 S> */ B(Wide), B(LdaSmi), I16(1234),
  /*   60 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

