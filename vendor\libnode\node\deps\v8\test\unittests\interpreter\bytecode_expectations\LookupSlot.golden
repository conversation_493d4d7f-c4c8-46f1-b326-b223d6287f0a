#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes
test function name: f

---
snippet: "
  eval('var x = 10;'); return x;
"
frame size: 10
parameter count: 1
bytecode array length: 56
bytecodes: [
  /*   10 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(1),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(4),
  /*   14 S> */ B(LdaLookupGlobalSlot), U8(1), U8(0), U8(1),
                B(Star2),
                B(LdaConstant), U8(2),
                B(Star3),
                B(LdaZero),
                B(Star7),
                B(LdaSmi), I8(10),
                B(Star8),
                B(LdaSmi), I8(14),
                B(Star9),
                B(Mov), R(2), R(4),
                B(Mov), R(3), R(5),
                B(Mov), R(closure), R(6),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(4), U8(6),
                B(Star2),
  /*   14 E> */ B(CallUndefinedReceiver1), R(2), R(3), U8(2),
  /*   35 S> */ B(LdaLookupGlobalSlot), U8(3), U8(4), U8(1),
  /*   44 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["var x = 10;"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  eval('var x = 10;'); return typeof x;
"
frame size: 10
parameter count: 1
bytecode array length: 57
bytecodes: [
  /*   10 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(1),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(4),
  /*   14 S> */ B(LdaLookupGlobalSlot), U8(1), U8(0), U8(1),
                B(Star2),
                B(LdaConstant), U8(2),
                B(Star3),
                B(LdaZero),
                B(Star7),
                B(LdaSmi), I8(10),
                B(Star8),
                B(LdaSmi), I8(14),
                B(Star9),
                B(Mov), R(2), R(4),
                B(Mov), R(3), R(5),
                B(Mov), R(closure), R(6),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(4), U8(6),
                B(Star2),
  /*   14 E> */ B(CallUndefinedReceiver1), R(2), R(3), U8(2),
  /*   35 S> */ B(LdaLookupGlobalSlotInsideTypeof), U8(3), U8(4), U8(1),
                B(TypeOf),
  /*   51 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["var x = 10;"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  x = 20; return eval('');
"
frame size: 10
parameter count: 1
bytecode array length: 57
bytecodes: [
  /*   10 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(1),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(4),
  /*   14 S> */ B(LdaSmi), I8(20),
  /*   16 E> */ B(StaLookupSlot), U8(1), U8(0),
  /*   22 S> */ B(LdaLookupGlobalSlot), U8(2), U8(0), U8(1),
                B(Star2),
                B(LdaConstant), U8(3),
                B(Star3),
                B(LdaZero),
                B(Star7),
                B(LdaSmi), I8(10),
                B(Star8),
                B(LdaSmi), I8(29),
                B(Star9),
                B(Mov), R(2), R(4),
                B(Mov), R(3), R(5),
                B(Mov), R(closure), R(6),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(4), U8(6),
                B(Star2),
  /*   29 E> */ B(CallUndefinedReceiver1), R(2), R(3), U8(2),
  /*   38 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE [""],
]
handlers: [
]

---
snippet: "
  var x = 20;
  f = function(){
    eval('var x = 10');
    return x;
  }
  f();
"
frame size: 10
parameter count: 1
bytecode array length: 56
bytecodes: [
  /*   38 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(1),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(4),
  /*   44 S> */ B(LdaLookupGlobalSlot), U8(1), U8(0), U8(1),
                B(Star2),
                B(LdaConstant), U8(2),
                B(Star3),
                B(LdaZero),
                B(Star7),
                B(LdaSmi), I8(38),
                B(Star8),
                B(LdaSmi), I8(44),
                B(Star9),
                B(Mov), R(2), R(4),
                B(Mov), R(3), R(5),
                B(Mov), R(closure), R(6),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(4), U8(6),
                B(Star2),
  /*   44 E> */ B(CallUndefinedReceiver1), R(2), R(3), U8(2),
  /*   66 S> */ B(LdaLookupContextSlot), U8(3), U8(4), U8(1),
  /*   75 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["var x = 10"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  x = 20;
  f = function(){
    eval('var x = 10');
    return x;
  }
  f();
"
frame size: 10
parameter count: 1
bytecode array length: 56
bytecodes: [
  /*   34 E> */ B(CreateFunctionContext), U8(0), U8(4),
                B(PushContext), R(1),
                B(Ldar), R(this),
                B(StaCurrentContextSlot), U8(3),
                B(CreateMappedArguments),
                B(StaCurrentContextSlot), U8(5),
                B(Ldar), R(0),
                B(StaCurrentContextSlot), U8(4),
  /*   40 S> */ B(LdaLookupGlobalSlot), U8(1), U8(0), U8(1),
                B(Star2),
                B(LdaConstant), U8(2),
                B(Star3),
                B(LdaZero),
                B(Star7),
                B(LdaSmi), I8(34),
                B(Star8),
                B(LdaSmi), I8(40),
                B(Star9),
                B(Mov), R(2), R(4),
                B(Mov), R(3), R(5),
                B(Mov), R(closure), R(6),
                B(CallRuntime), U16(Runtime::kResolvePossiblyDirectEval), R(4), U8(6),
                B(Star2),
  /*   40 E> */ B(CallUndefinedReceiver1), R(2), R(3), U8(2),
  /*   62 S> */ B(LdaLookupGlobalSlot), U8(3), U8(4), U8(1),
  /*   71 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["eval"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["var x = 10"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

