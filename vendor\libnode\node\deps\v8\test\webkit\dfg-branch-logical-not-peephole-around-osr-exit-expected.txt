# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests that our attempts to do peephole Branch(LogicalNot) optimization doesn't break OSR exit's ability to recover the value of LogicalNot.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 6
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 7
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 8
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 9
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 10
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 9
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 12
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 13
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 14
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 15
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 16
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 17
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 18
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 19
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 20
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 21
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 22
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 23
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 24
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 25
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 26
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 27
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 28
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 29
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 30
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 31
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 32
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 33
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 34
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 35
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 36
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 37
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 38
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 39
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 40
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 41
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 42
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 43
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 44
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 45
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 46
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 47
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 48
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 49
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 50
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 51
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 52
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 53
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 54
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 55
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 56
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 57
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 58
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 59
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 60
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 61
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 62
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 63
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 64
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 65
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 66
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 67
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 68
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 69
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 70
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 71
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 72
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 73
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 74
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 75
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 76
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 77
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 78
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 79
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 80
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 81
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 82
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 83
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 84
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 85
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 86
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 87
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 88
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 89
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 90
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 91
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 92
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 93
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 94
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 95
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 96
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 97
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 98
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 99
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 100
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 101
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 102
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 103
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 104
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 105
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 106
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 107
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 108
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 109
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 110
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 111
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 112
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 113
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 114
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 115
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 116
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 117
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 118
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 119
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 120
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 121
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 122
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 123
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 124
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 125
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 126
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 127
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 128
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 129
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 130
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 131
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 132
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 133
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 134
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 135
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 136
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 137
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 138
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 139
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 140
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 141
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 142
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 143
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 144
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 145
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 146
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 147
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 148
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 149
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 150
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 151
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 152
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 153
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 154
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 155
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 156
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 157
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 158
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 159
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 160
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 161
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 162
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 163
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 164
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 165
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 166
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 167
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 168
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 169
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 170
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 171
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 172
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 173
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 174
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 175
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 176
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 177
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 178
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 179
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 180
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 181
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 182
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 183
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 184
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 185
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 186
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 187
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 188
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 189
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 190
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 191
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 192
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 193
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 194
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 195
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 196
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 197
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 198
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 199
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 200
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 201
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 202
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 203
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 204
PASS foo(i, 5, i == 199 ? 2147483647 : 5) is 2147483847
PASS successfullyParsed is true

TEST COMPLETE

