(()=>{"use strict";var __webpack_modules__={5597:e=>{e.exports=function(e,t){for(var r=new Array(arguments.length-1),n=0,a=2,u=!0;a<arguments.length;)r[n++]=arguments[a++];return new Promise((function(a,s){r[n]=function(e){if(u)if(u=!1,e)s(e);else{for(var t=new Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];a.apply(null,t)}};try{e.apply(t||null,r)}catch(e){u&&(u=!1,s(e))}}))}},8679:(e,t)=>{var r=t;r.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};for(var n=new Array(64),a=new Array(123),u=0;u<64;)a[n[u]=u<26?u+65:u<52?u+71:u<62?u-4:u-59|43]=u++;r.encode=function(e,t,r){for(var a,u=null,s=[],o=0,i=0;t<r;){var c=e[t++];switch(i){case 0:s[o++]=n[c>>2],a=(3&c)<<4,i=1;break;case 1:s[o++]=n[a|c>>4],a=(15&c)<<2,i=2;break;case 2:s[o++]=n[a|c>>6],s[o++]=n[63&c],i=0}o>8191&&((u||(u=[])).push(String.fromCharCode.apply(String,s)),o=0)}return i&&(s[o++]=n[a],s[o++]=61,1===i&&(s[o++]=61)),u?(o&&u.push(String.fromCharCode.apply(String,s.slice(0,o))),u.join("")):String.fromCharCode.apply(String,s.slice(0,o))};var s="invalid encoding";r.decode=function(e,t,r){for(var n,u=r,o=0,i=0;i<e.length;){var c=e.charCodeAt(i++);if(61===c&&o>1)break;if(void 0===(c=a[c]))throw Error(s);switch(o){case 0:n=c,o=1;break;case 1:t[r++]=n<<2|(48&c)>>4,n=c,o=2;break;case 2:t[r++]=(15&n)<<4|(60&c)>>2,n=c,o=3;break;case 3:t[r++]=(3&n)<<6|c,o=0}}if(1===o)throw Error(s);return r-u},r.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}},5318:e=>{function t(){this._listeners={}}e.exports=t,t.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},t.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],n=0;n<r.length;)r[n].fn===t?r.splice(n,1):++n;return this},t.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<t.length;)t[n].fn.apply(t[n++].ctx,r)}return this}},5826:e=>{function t(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),n=128===r[3];function a(e,n,a){t[0]=e,n[a]=r[0],n[a+1]=r[1],n[a+2]=r[2],n[a+3]=r[3]}function u(e,n,a){t[0]=e,n[a]=r[3],n[a+1]=r[2],n[a+2]=r[1],n[a+3]=r[0]}function s(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],t[0]}function o(e,n){return r[3]=e[n],r[2]=e[n+1],r[1]=e[n+2],r[0]=e[n+3],t[0]}e.writeFloatLE=n?a:u,e.writeFloatBE=n?u:a,e.readFloatLE=n?s:o,e.readFloatBE=n?o:s}():function(){function t(e,t,r,n){var a=t<0?1:0;if(a&&(t=-t),0===t)e(1/t>0?0:2147483648,r,n);else if(isNaN(t))e(2143289344,r,n);else if(t>34028234663852886e22)e((a<<31|2139095040)>>>0,r,n);else if(t<11754943508222875e-54)e((a<<31|Math.round(t/1401298464324817e-60))>>>0,r,n);else{var u=Math.floor(Math.log(t)/Math.LN2);e((a<<31|u+127<<23|8388607&Math.round(t*Math.pow(2,-u)*8388608))>>>0,r,n)}}function s(e,t,r){var n=e(t,r),a=2*(n>>31)+1,u=n>>>23&255,s=8388607&n;return 255===u?s?NaN:a*(1/0):0===u?1401298464324817e-60*a*s:a*Math.pow(2,u-150)*(s+8388608)}e.writeFloatLE=t.bind(null,r),e.writeFloatBE=t.bind(null,n),e.readFloatLE=s.bind(null,a),e.readFloatBE=s.bind(null,u)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),n=128===r[7];function a(e,n,a){t[0]=e,n[a]=r[0],n[a+1]=r[1],n[a+2]=r[2],n[a+3]=r[3],n[a+4]=r[4],n[a+5]=r[5],n[a+6]=r[6],n[a+7]=r[7]}function u(e,n,a){t[0]=e,n[a]=r[7],n[a+1]=r[6],n[a+2]=r[5],n[a+3]=r[4],n[a+4]=r[3],n[a+5]=r[2],n[a+6]=r[1],n[a+7]=r[0]}function s(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],r[4]=e[n+4],r[5]=e[n+5],r[6]=e[n+6],r[7]=e[n+7],t[0]}function o(e,n){return r[7]=e[n],r[6]=e[n+1],r[5]=e[n+2],r[4]=e[n+3],r[3]=e[n+4],r[2]=e[n+5],r[1]=e[n+6],r[0]=e[n+7],t[0]}e.writeDoubleLE=n?a:u,e.writeDoubleBE=n?u:a,e.readDoubleLE=n?s:o,e.readDoubleBE=n?o:s}():function(){function t(e,t,r,n,a,u){var s=n<0?1:0;if(s&&(n=-n),0===n)e(0,a,u+t),e(1/n>0?0:2147483648,a,u+r);else if(isNaN(n))e(0,a,u+t),e(2146959360,a,u+r);else if(n>17976931348623157e292)e(0,a,u+t),e((s<<31|2146435072)>>>0,a,u+r);else{var o;if(n<22250738585072014e-324)e((o=n/5e-324)>>>0,a,u+t),e((s<<31|o/4294967296)>>>0,a,u+r);else{var i=Math.floor(Math.log(n)/Math.LN2);1024===i&&(i=1023),e(4503599627370496*(o=n*Math.pow(2,-i))>>>0,a,u+t),e((s<<31|i+1023<<20|1048576*o&1048575)>>>0,a,u+r)}}}function s(e,t,r,n,a){var u=e(n,a+t),s=e(n,a+r),o=2*(s>>31)+1,i=s>>>20&2047,c=4294967296*(1048575&s)+u;return 2047===i?c?NaN:o*(1/0):0===i?5e-324*o*c:o*Math.pow(2,i-1075)*(c+4503599627370496)}e.writeDoubleLE=t.bind(null,r,0,4),e.writeDoubleBE=t.bind(null,n,4,0),e.readDoubleLE=s.bind(null,a,0,4),e.readDoubleBE=s.bind(null,u,4,0)}(),e}function r(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function n(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function a(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function u(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}e.exports=t(t)},7353:module=>{function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}module.exports=inquire},7454:e=>{e.exports=function(e,t,r){var n=r||8192,a=n>>>1,u=null,s=n;return function(r){if(r<1||r>a)return e(r);s+r>n&&(u=e(n),s=0);var o=t.call(u,s,s+=r);return 7&s&&(s=1+(7|s)),o}}},6247:(e,t)=>{var r=t;r.length=function(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:55296==(64512&r)&&56320==(64512&e.charCodeAt(n+1))?(++n,t+=4):t+=3;return t},r.read=function(e,t,r){if(r-t<1)return"";for(var n,a=null,u=[],s=0;t<r;)(n=e[t++])<128?u[s++]=n:n>191&&n<224?u[s++]=(31&n)<<6|63&e[t++]:n>239&&n<365?(n=((7&n)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,u[s++]=55296+(n>>10),u[s++]=56320+(1023&n)):u[s++]=(15&n)<<12|(63&e[t++])<<6|63&e[t++],s>8191&&((a||(a=[])).push(String.fromCharCode.apply(String,u)),s=0);return a?(s&&a.push(String.fromCharCode.apply(String,u.slice(0,s))),a.join("")):String.fromCharCode.apply(String,u.slice(0,s))},r.write=function(e,t,r){for(var n,a,u=r,s=0;s<e.length;++s)(n=e.charCodeAt(s))<128?t[r++]=n:n<2048?(t[r++]=n>>6|192,t[r++]=63&n|128):55296==(64512&n)&&56320==(64512&(a=e.charCodeAt(s+1)))?(n=65536+((1023&n)<<10)+(1023&a),++s,t[r++]=n>>18|240,t[r++]=n>>12&63|128,t[r++]=n>>6&63|128,t[r++]=63&n|128):(t[r++]=n>>12|224,t[r++]=n>>6&63|128,t[r++]=63&n|128);return r-u}},5874:(e,t,r)=>{e.exports=r(3242)},3242:(e,t,r)=>{var n=t;function a(){n.util._configure(),n.Writer._configure(n.BufferWriter),n.Reader._configure(n.BufferReader)}n.build="minimal",n.Writer=r(9913),n.BufferWriter=r(2130),n.Reader=r(1037),n.BufferReader=r(5926),n.util=r(3162),n.rpc=r(8583),n.roots=r(6609),n.configure=a,a()},1037:(e,t,r)=>{e.exports=i;var n,a=r(3162),u=a.LongBits,s=a.utf8;function o(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function i(e){this.buf=e,this.pos=0,this.len=e.length}var c,l="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new i(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new i(e);throw Error("illegal buffer")},D=function(){return a.Buffer?function(e){return(i.create=function(e){return a.Buffer.isBuffer(e)?new n(e):l(e)})(e)}:l};function d(){var e=new u(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw o(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw o(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function h(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function p(){if(this.pos+8>this.len)throw o(this,8);return new u(h(this.buf,this.pos+=4),h(this.buf,this.pos+=4))}i.create=D(),i.prototype._slice=a.Array.prototype.subarray||a.Array.prototype.slice,i.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,o(this,10);return c}),i.prototype.int32=function(){return 0|this.uint32()},i.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)},i.prototype.bool=function(){return 0!==this.uint32()},i.prototype.fixed32=function(){if(this.pos+4>this.len)throw o(this,4);return h(this.buf,this.pos+=4)},i.prototype.sfixed32=function(){if(this.pos+4>this.len)throw o(this,4);return 0|h(this.buf,this.pos+=4)},i.prototype.float=function(){if(this.pos+4>this.len)throw o(this,4);var e=a.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},i.prototype.double=function(){if(this.pos+8>this.len)throw o(this,4);var e=a.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},i.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw o(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,r):t===r?new this.buf.constructor(0):this._slice.call(this.buf,t,r)},i.prototype.string=function(){var e=this.bytes();return s.read(e,0,e.length)},i.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw o(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw o(this)}while(128&this.buf[this.pos++]);return this},i.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},i._configure=function(e){n=e,i.create=D(),n._configure();var t=a.Long?"toLong":"toNumber";a.merge(i.prototype,{int64:function(){return d.call(this)[t](!1)},uint64:function(){return d.call(this)[t](!0)},sint64:function(){return d.call(this).zzDecode()[t](!1)},fixed64:function(){return p.call(this)[t](!0)},sfixed64:function(){return p.call(this)[t](!1)}})}},5926:(e,t,r)=>{e.exports=u;var n=r(1037);(u.prototype=Object.create(n.prototype)).constructor=u;var a=r(3162);function u(e){n.call(this,e)}u._configure=function(){a.Buffer&&(u.prototype._slice=a.Buffer.prototype.slice)},u.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},u._configure()},6609:e=>{e.exports={}},8583:(e,t,r)=>{t.Service=r(1579)},1579:(e,t,r)=>{e.exports=a;var n=r(3162);function a(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");n.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(a.prototype=Object.create(n.EventEmitter.prototype)).constructor=a,a.prototype.rpcCall=function e(t,r,a,u,s){if(!u)throw TypeError("request must be specified");var o=this;if(!s)return n.asPromise(e,o,t,r,a,u);if(o.rpcImpl)try{return o.rpcImpl(t,r[o.requestDelimited?"encodeDelimited":"encode"](u).finish(),(function(e,r){if(e)return o.emit("error",e,t),s(e);if(null!==r){if(!(r instanceof a))try{r=a[o.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return o.emit("error",e,t),s(e)}return o.emit("data",r,t),s(null,r)}o.end(!0)}))}catch(e){return o.emit("error",e,t),void setTimeout((function(){s(e)}),0)}else setTimeout((function(){s(Error("already ended"))}),0)},a.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},7247:(e,t,r)=>{e.exports=a;var n=r(3162);function a(e,t){this.lo=e>>>0,this.hi=t>>>0}var u=a.zero=new a(0,0);u.toNumber=function(){return 0},u.zzEncode=u.zzDecode=function(){return this},u.length=function(){return 1};var s=a.zeroHash="\0\0\0\0\0\0\0\0";a.fromNumber=function(e){if(0===e)return u;var t=e<0;t&&(e=-e);var r=e>>>0,n=(e-r)/4294967296>>>0;return t&&(n=~n>>>0,r=~r>>>0,++r>4294967295&&(r=0,++n>4294967295&&(n=0))),new a(r,n)},a.from=function(e){if("number"==typeof e)return a.fromNumber(e);if(n.isString(e)){if(!n.Long)return a.fromNumber(parseInt(e,10));e=n.Long.fromString(e)}return e.low||e.high?new a(e.low>>>0,e.high>>>0):u},a.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+4294967296*r)}return this.lo+4294967296*this.hi},a.prototype.toLong=function(e){return n.Long?new n.Long(0|this.lo,0|this.hi,Boolean(e)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(e)}};var o=String.prototype.charCodeAt;a.fromHash=function(e){return e===s?u:new a((o.call(e,0)|o.call(e,1)<<8|o.call(e,2)<<16|o.call(e,3)<<24)>>>0,(o.call(e,4)|o.call(e,5)<<8|o.call(e,6)<<16|o.call(e,7)<<24)>>>0)},a.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},a.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},a.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},a.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10}},3162:function(e,t,r){var n=t;function a(e,t,r){for(var n=Object.keys(t),a=0;a<n.length;++a)void 0!==e[n[a]]&&r||(e[n[a]]=t[n[a]]);return e}function u(e){function t(e,r){if(!(this instanceof t))return new t(e,r);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),r&&a(this,r)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t}n.asPromise=r(5597),n.base64=r(8679),n.EventEmitter=r(5318),n.float=r(5826),n.inquire=r(7353),n.utf8=r(6247),n.pool=r(7454),n.LongBits=r(7247),n.isNode=Boolean(void 0!==r.g&&r.g&&r.g.process&&r.g.process.versions&&r.g.process.versions.node),n.global=n.isNode&&r.g||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,n.emptyArray=Object.freeze?Object.freeze([]):[],n.emptyObject=Object.freeze?Object.freeze({}):{},n.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},n.isString=function(e){return"string"==typeof e||e instanceof String},n.isObject=function(e){return e&&"object"==typeof e},n.isset=n.isSet=function(e,t){var r=e[t];return!(null==r||!e.hasOwnProperty(t))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},n.Buffer=function(){try{var e=n.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),n._Buffer_from=null,n._Buffer_allocUnsafe=null,n.newBuffer=function(e){return"number"==typeof e?n.Buffer?n._Buffer_allocUnsafe(e):new n.Array(e):n.Buffer?n._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},n.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,n.Long=n.global.dcodeIO&&n.global.dcodeIO.Long||n.global.Long||n.inquire("long"),n.key2Re=/^true|false|0|1$/,n.key32Re=/^-?(?:0|[1-9][0-9]*)$/,n.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,n.longToHash=function(e){return e?n.LongBits.from(e).toHash():n.LongBits.zeroHash},n.longFromHash=function(e,t){var r=n.LongBits.fromHash(e);return n.Long?n.Long.fromBits(r.lo,r.hi,t):r.toNumber(Boolean(t))},n.merge=a,n.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},n.newError=u,n.ProtocolError=u("ProtocolError"),n.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},n.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},n.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},n._configure=function(){var e=n.Buffer;e?(n._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},n._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):n._Buffer_from=n._Buffer_allocUnsafe=null}},9913:(e,t,r)=>{e.exports=D;var n,a=r(3162),u=a.LongBits,s=a.base64,o=a.utf8;function i(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function c(){}function l(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function D(){this.len=0,this.head=new i(c,0,0),this.tail=this.head,this.states=null}var d=function(){return a.Buffer?function(){return(D.create=function(){return new n})()}:function(){return new D}};function h(e,t,r){t[r]=255&e}function p(e,t){this.len=e,this.next=void 0,this.val=t}function y(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function f(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}D.create=d(),D.alloc=function(e){return new a.Array(e)},a.Array!==Array&&(D.alloc=a.pool(D.alloc,a.Array.prototype.subarray)),D.prototype._push=function(e,t,r){return this.tail=this.tail.next=new i(e,t,r),this.len+=t,this},p.prototype=Object.create(i.prototype),p.prototype.fn=function(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e},D.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new p((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},D.prototype.int32=function(e){return e<0?this._push(y,10,u.fromNumber(e)):this.uint32(e)},D.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},D.prototype.uint64=function(e){var t=u.from(e);return this._push(y,t.length(),t)},D.prototype.int64=D.prototype.uint64,D.prototype.sint64=function(e){var t=u.from(e).zzEncode();return this._push(y,t.length(),t)},D.prototype.bool=function(e){return this._push(h,1,e?1:0)},D.prototype.fixed32=function(e){return this._push(f,4,e>>>0)},D.prototype.sfixed32=D.prototype.fixed32,D.prototype.fixed64=function(e){var t=u.from(e);return this._push(f,4,t.lo)._push(f,4,t.hi)},D.prototype.sfixed64=D.prototype.fixed64,D.prototype.float=function(e){return this._push(a.float.writeFloatLE,4,e)},D.prototype.double=function(e){return this._push(a.float.writeDoubleLE,8,e)};var m=a.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var n=0;n<e.length;++n)t[r+n]=e[n]};D.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(h,1,0);if(a.isString(e)){var r=D.alloc(t=s.length(e));s.decode(e,r,0),e=r}return this.uint32(t)._push(m,t,e)},D.prototype.string=function(e){var t=o.length(e);return t?this.uint32(t)._push(o.write,t,e):this._push(h,1,0)},D.prototype.fork=function(){return this.states=new l(this),this.head=this.tail=new i(c,0,0),this.len=0,this},D.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new i(c,0,0),this.len=0),this},D.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},D.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},D._configure=function(e){n=e,D.create=d(),n._configure()}},2130:(e,t,r)=>{e.exports=u;var n=r(9913);(u.prototype=Object.create(n.prototype)).constructor=u;var a=r(3162);function u(){n.call(this)}function s(e,t,r){e.length<40?a.utf8.write(e,t,r):t.utf8Write?t.utf8Write(e,r):t.write(e,r)}u._configure=function(){u.alloc=a._Buffer_allocUnsafe,u.writeBytesBuffer=a.Buffer&&a.Buffer.prototype instanceof Uint8Array&&"set"===a.Buffer.prototype.set.name?function(e,t,r){t.set(e,r)}:function(e,t,r){if(e.copy)e.copy(t,r,0,e.length);else for(var n=0;n<e.length;)t[r++]=e[n++]}},u.prototype.bytes=function(e){a.isString(e)&&(e=a._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(u.writeBytesBuffer,t,e),this},u.prototype.string=function(e){var t=a.Buffer.byteLength(e);return this.uint32(t),t&&this._push(s,t,e),this},u._configure()},8883:(e,t,r)=>{var n=r(5874),a=n.Reader,u=n.Writer,s=n.util,o=n.roots.default||(n.roots.default={});o.master=function(){var e={};return e.Player=function(){function e(e){if(this.identifiers=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.name="",e.prototype.identifiers=s.emptyArray,e.prototype.endpoint="",e.prototype.ping=0,e.prototype.id=0,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=u.create()),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(10).string(e.name),null!=e.identifiers&&e.identifiers.length)for(var r=0;r<e.identifiers.length;++r)t.uint32(18).string(e.identifiers[r]);return null!=e.endpoint&&Object.hasOwnProperty.call(e,"endpoint")&&t.uint32(26).string(e.endpoint),null!=e.ping&&Object.hasOwnProperty.call(e,"ping")&&t.uint32(32).int32(e.ping),null!=e.id&&Object.hasOwnProperty.call(e,"id")&&t.uint32(40).int32(e.id),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new o.master.Player;e.pos<r;){var u=e.uint32();switch(u>>>3){case 1:n.name=e.string();break;case 2:n.identifiers&&n.identifiers.length||(n.identifiers=[]),n.identifiers.push(e.string());break;case 3:n.endpoint=e.string();break;case 4:n.ping=e.int32();break;case 5:n.id=e.int32();break;default:e.skipType(7&u)}}return n},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.name&&e.hasOwnProperty("name")&&!s.isString(e.name))return"name: string expected";if(null!=e.identifiers&&e.hasOwnProperty("identifiers")){if(!Array.isArray(e.identifiers))return"identifiers: array expected";for(var t=0;t<e.identifiers.length;++t)if(!s.isString(e.identifiers[t]))return"identifiers: string[] expected"}return null!=e.endpoint&&e.hasOwnProperty("endpoint")&&!s.isString(e.endpoint)?"endpoint: string expected":null!=e.ping&&e.hasOwnProperty("ping")&&!s.isInteger(e.ping)?"ping: integer expected":null!=e.id&&e.hasOwnProperty("id")&&!s.isInteger(e.id)?"id: integer expected":null},e.fromObject=function(e){if(e instanceof o.master.Player)return e;var t=new o.master.Player;if(null!=e.name&&(t.name=String(e.name)),e.identifiers){if(!Array.isArray(e.identifiers))throw TypeError(".master.Player.identifiers: array expected");t.identifiers=[];for(var r=0;r<e.identifiers.length;++r)t.identifiers[r]=String(e.identifiers[r])}return null!=e.endpoint&&(t.endpoint=String(e.endpoint)),null!=e.ping&&(t.ping=0|e.ping),null!=e.id&&(t.id=0|e.id),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.identifiers=[]),t.defaults&&(r.name="",r.endpoint="",r.ping=0,r.id=0),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),e.identifiers&&e.identifiers.length){r.identifiers=[];for(var n=0;n<e.identifiers.length;++n)r.identifiers[n]=e.identifiers[n]}return null!=e.endpoint&&e.hasOwnProperty("endpoint")&&(r.endpoint=e.endpoint),null!=e.ping&&e.hasOwnProperty("ping")&&(r.ping=e.ping),null!=e.id&&e.hasOwnProperty("id")&&(r.id=e.id),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e.ServerData=function(){function e(e){if(this.resources=[],this.players=[],this.vars={},this.connectEndPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.svMaxclients=0,e.prototype.clients=0,e.prototype.protocol=0,e.prototype.hostname="",e.prototype.gametype="",e.prototype.mapname="",e.prototype.resources=s.emptyArray,e.prototype.server="",e.prototype.players=s.emptyArray,e.prototype.iconVersion=0,e.prototype.vars=s.emptyObject,e.prototype.enhancedHostSupport=!1,e.prototype.upvotePower=0,e.prototype.burstPower=0,e.prototype.connectEndPoints=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=u.create()),null!=e.svMaxclients&&Object.hasOwnProperty.call(e,"svMaxclients")&&t.uint32(8).int32(e.svMaxclients),null!=e.clients&&Object.hasOwnProperty.call(e,"clients")&&t.uint32(16).int32(e.clients),null!=e.protocol&&Object.hasOwnProperty.call(e,"protocol")&&t.uint32(24).int32(e.protocol),null!=e.hostname&&Object.hasOwnProperty.call(e,"hostname")&&t.uint32(34).string(e.hostname),null!=e.gametype&&Object.hasOwnProperty.call(e,"gametype")&&t.uint32(42).string(e.gametype),null!=e.mapname&&Object.hasOwnProperty.call(e,"mapname")&&t.uint32(50).string(e.mapname),null!=e.resources&&e.resources.length)for(var r=0;r<e.resources.length;++r)t.uint32(66).string(e.resources[r]);if(null!=e.server&&Object.hasOwnProperty.call(e,"server")&&t.uint32(74).string(e.server),null!=e.players&&e.players.length)for(r=0;r<e.players.length;++r)o.master.Player.encode(e.players[r],t.uint32(82).fork()).ldelim();if(null!=e.iconVersion&&Object.hasOwnProperty.call(e,"iconVersion")&&t.uint32(88).int32(e.iconVersion),null!=e.vars&&Object.hasOwnProperty.call(e,"vars")){var n=Object.keys(e.vars);for(r=0;r<n.length;++r)t.uint32(98).fork().uint32(10).string(n[r]).uint32(18).string(e.vars[n[r]]).ldelim()}if(null!=e.enhancedHostSupport&&Object.hasOwnProperty.call(e,"enhancedHostSupport")&&t.uint32(128).bool(e.enhancedHostSupport),null!=e.upvotePower&&Object.hasOwnProperty.call(e,"upvotePower")&&t.uint32(136).int32(e.upvotePower),null!=e.connectEndPoints&&e.connectEndPoints.length)for(r=0;r<e.connectEndPoints.length;++r)t.uint32(146).string(e.connectEndPoints[r]);return null!=e.burstPower&&Object.hasOwnProperty.call(e,"burstPower")&&t.uint32(152).int32(e.burstPower),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r,n,u=void 0===t?e.len:e.pos+t,i=new o.master.ServerData;e.pos<u;){var c=e.uint32();switch(c>>>3){case 1:i.svMaxclients=e.int32();break;case 2:i.clients=e.int32();break;case 3:i.protocol=e.int32();break;case 4:i.hostname=e.string();break;case 5:i.gametype=e.string();break;case 6:i.mapname=e.string();break;case 8:i.resources&&i.resources.length||(i.resources=[]),i.resources.push(e.string());break;case 9:i.server=e.string();break;case 10:i.players&&i.players.length||(i.players=[]),i.players.push(o.master.Player.decode(e,e.uint32()));break;case 11:i.iconVersion=e.int32();break;case 12:i.vars===s.emptyObject&&(i.vars={});var l=e.uint32()+e.pos;for(r="",n="";e.pos<l;){var D=e.uint32();switch(D>>>3){case 1:r=e.string();break;case 2:n=e.string();break;default:e.skipType(7&D)}}i.vars[r]=n;break;case 16:i.enhancedHostSupport=e.bool();break;case 17:i.upvotePower=e.int32();break;case 19:i.burstPower=e.int32();break;case 18:i.connectEndPoints&&i.connectEndPoints.length||(i.connectEndPoints=[]),i.connectEndPoints.push(e.string());break;default:e.skipType(7&c)}}return i},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.svMaxclients&&e.hasOwnProperty("svMaxclients")&&!s.isInteger(e.svMaxclients))return"svMaxclients: integer expected";if(null!=e.clients&&e.hasOwnProperty("clients")&&!s.isInteger(e.clients))return"clients: integer expected";if(null!=e.protocol&&e.hasOwnProperty("protocol")&&!s.isInteger(e.protocol))return"protocol: integer expected";if(null!=e.hostname&&e.hasOwnProperty("hostname")&&!s.isString(e.hostname))return"hostname: string expected";if(null!=e.gametype&&e.hasOwnProperty("gametype")&&!s.isString(e.gametype))return"gametype: string expected";if(null!=e.mapname&&e.hasOwnProperty("mapname")&&!s.isString(e.mapname))return"mapname: string expected";if(null!=e.resources&&e.hasOwnProperty("resources")){if(!Array.isArray(e.resources))return"resources: array expected";for(var t=0;t<e.resources.length;++t)if(!s.isString(e.resources[t]))return"resources: string[] expected"}if(null!=e.server&&e.hasOwnProperty("server")&&!s.isString(e.server))return"server: string expected";if(null!=e.players&&e.hasOwnProperty("players")){if(!Array.isArray(e.players))return"players: array expected";for(t=0;t<e.players.length;++t){var r=o.master.Player.verify(e.players[t]);if(r)return"players."+r}}if(null!=e.iconVersion&&e.hasOwnProperty("iconVersion")&&!s.isInteger(e.iconVersion))return"iconVersion: integer expected";if(null!=e.vars&&e.hasOwnProperty("vars")){if(!s.isObject(e.vars))return"vars: object expected";var n=Object.keys(e.vars);for(t=0;t<n.length;++t)if(!s.isString(e.vars[n[t]]))return"vars: string{k:string} expected"}if(null!=e.enhancedHostSupport&&e.hasOwnProperty("enhancedHostSupport")&&"boolean"!=typeof e.enhancedHostSupport)return"enhancedHostSupport: boolean expected";if(null!=e.upvotePower&&e.hasOwnProperty("upvotePower")&&!s.isInteger(e.upvotePower))return"upvotePower: integer expected";if(null!=e.burstPower&&e.hasOwnProperty("burstPower")&&!s.isInteger(e.burstPower))return"burstPower: integer expected";if(null!=e.connectEndPoints&&e.hasOwnProperty("connectEndPoints")){if(!Array.isArray(e.connectEndPoints))return"connectEndPoints: array expected";for(t=0;t<e.connectEndPoints.length;++t)if(!s.isString(e.connectEndPoints[t]))return"connectEndPoints: string[] expected"}return null},e.fromObject=function(e){if(e instanceof o.master.ServerData)return e;var t=new o.master.ServerData;if(null!=e.svMaxclients&&(t.svMaxclients=0|e.svMaxclients),null!=e.clients&&(t.clients=0|e.clients),null!=e.protocol&&(t.protocol=0|e.protocol),null!=e.hostname&&(t.hostname=String(e.hostname)),null!=e.gametype&&(t.gametype=String(e.gametype)),null!=e.mapname&&(t.mapname=String(e.mapname)),e.resources){if(!Array.isArray(e.resources))throw TypeError(".master.ServerData.resources: array expected");t.resources=[];for(var r=0;r<e.resources.length;++r)t.resources[r]=String(e.resources[r])}if(null!=e.server&&(t.server=String(e.server)),e.players){if(!Array.isArray(e.players))throw TypeError(".master.ServerData.players: array expected");for(t.players=[],r=0;r<e.players.length;++r){if("object"!=typeof e.players[r])throw TypeError(".master.ServerData.players: object expected");t.players[r]=o.master.Player.fromObject(e.players[r])}}if(null!=e.iconVersion&&(t.iconVersion=0|e.iconVersion),e.vars){if("object"!=typeof e.vars)throw TypeError(".master.ServerData.vars: object expected");t.vars={};var n=Object.keys(e.vars);for(r=0;r<n.length;++r)t.vars[n[r]]=String(e.vars[n[r]])}if(null!=e.enhancedHostSupport&&(t.enhancedHostSupport=Boolean(e.enhancedHostSupport)),null!=e.upvotePower&&(t.upvotePower=0|e.upvotePower),null!=e.burstPower&&(t.burstPower=0|e.burstPower),e.connectEndPoints){if(!Array.isArray(e.connectEndPoints))throw TypeError(".master.ServerData.connectEndPoints: array expected");for(t.connectEndPoints=[],r=0;r<e.connectEndPoints.length;++r)t.connectEndPoints[r]=String(e.connectEndPoints[r])}return t},e.toObject=function(e,t){t||(t={});var r,n={};if((t.arrays||t.defaults)&&(n.resources=[],n.players=[],n.connectEndPoints=[]),(t.objects||t.defaults)&&(n.vars={}),t.defaults&&(n.svMaxclients=0,n.clients=0,n.protocol=0,n.hostname="",n.gametype="",n.mapname="",n.server="",n.iconVersion=0,n.enhancedHostSupport=!1,n.upvotePower=0,n.burstPower=0),null!=e.svMaxclients&&e.hasOwnProperty("svMaxclients")&&(n.svMaxclients=e.svMaxclients),null!=e.clients&&e.hasOwnProperty("clients")&&(n.clients=e.clients),null!=e.protocol&&e.hasOwnProperty("protocol")&&(n.protocol=e.protocol),null!=e.hostname&&e.hasOwnProperty("hostname")&&(n.hostname=e.hostname),null!=e.gametype&&e.hasOwnProperty("gametype")&&(n.gametype=e.gametype),null!=e.mapname&&e.hasOwnProperty("mapname")&&(n.mapname=e.mapname),e.resources&&e.resources.length){n.resources=[];for(var a=0;a<e.resources.length;++a)n.resources[a]=e.resources[a]}if(null!=e.server&&e.hasOwnProperty("server")&&(n.server=e.server),e.players&&e.players.length)for(n.players=[],a=0;a<e.players.length;++a)n.players[a]=o.master.Player.toObject(e.players[a],t);if(null!=e.iconVersion&&e.hasOwnProperty("iconVersion")&&(n.iconVersion=e.iconVersion),e.vars&&(r=Object.keys(e.vars)).length)for(n.vars={},a=0;a<r.length;++a)n.vars[r[a]]=e.vars[r[a]];if(null!=e.enhancedHostSupport&&e.hasOwnProperty("enhancedHostSupport")&&(n.enhancedHostSupport=e.enhancedHostSupport),null!=e.upvotePower&&e.hasOwnProperty("upvotePower")&&(n.upvotePower=e.upvotePower),e.connectEndPoints&&e.connectEndPoints.length)for(n.connectEndPoints=[],a=0;a<e.connectEndPoints.length;++a)n.connectEndPoints[a]=e.connectEndPoints[a];return null!=e.burstPower&&e.hasOwnProperty("burstPower")&&(n.burstPower=e.burstPower),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e.Server=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.EndPoint="",e.prototype.Data=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=u.create()),null!=e.EndPoint&&Object.hasOwnProperty.call(e,"EndPoint")&&t.uint32(10).string(e.EndPoint),null!=e.Data&&Object.hasOwnProperty.call(e,"Data")&&o.master.ServerData.encode(e.Data,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new o.master.Server;e.pos<r;){var u=e.uint32();switch(u>>>3){case 1:n.EndPoint=e.string();break;case 2:n.Data=o.master.ServerData.decode(e,e.uint32());break;default:e.skipType(7&u)}}return n},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.EndPoint&&e.hasOwnProperty("EndPoint")&&!s.isString(e.EndPoint))return"EndPoint: string expected";if(null!=e.Data&&e.hasOwnProperty("Data")){var t=o.master.ServerData.verify(e.Data);if(t)return"Data."+t}return null},e.fromObject=function(e){if(e instanceof o.master.Server)return e;var t=new o.master.Server;if(null!=e.EndPoint&&(t.EndPoint=String(e.EndPoint)),null!=e.Data){if("object"!=typeof e.Data)throw TypeError(".master.Server.Data: object expected");t.Data=o.master.ServerData.fromObject(e.Data)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.EndPoint="",r.Data=null),null!=e.EndPoint&&e.hasOwnProperty("EndPoint")&&(r.EndPoint=e.EndPoint),null!=e.Data&&e.hasOwnProperty("Data")&&(r.Data=o.master.ServerData.toObject(e.Data,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e.Servers=function(){function e(e){if(this.servers=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.servers=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=u.create()),null!=e.servers&&e.servers.length)for(var r=0;r<e.servers.length;++r)o.master.Server.encode(e.servers[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new o.master.Servers;e.pos<r;){var u=e.uint32();u>>>3==1?(n.servers&&n.servers.length||(n.servers=[]),n.servers.push(o.master.Server.decode(e,e.uint32()))):e.skipType(7&u)}return n},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.servers&&e.hasOwnProperty("servers")){if(!Array.isArray(e.servers))return"servers: array expected";for(var t=0;t<e.servers.length;++t){var r=o.master.Server.verify(e.servers[t]);if(r)return"servers."+r}}return null},e.fromObject=function(e){if(e instanceof o.master.Servers)return e;var t=new o.master.Servers;if(e.servers){if(!Array.isArray(e.servers))throw TypeError(".master.Servers.servers: array expected");t.servers=[];for(var r=0;r<e.servers.length;++r){if("object"!=typeof e.servers[r])throw TypeError(".master.Servers.servers: object expected");t.servers[r]=o.master.Server.fromObject(e.servers[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.servers=[]),e.servers&&e.servers.length){r.servers=[];for(var n=0;n<e.servers.length;++n)r.servers[n]=o.master.Server.toObject(e.servers[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e.ServerIcon=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.endPoint="",e.prototype.icon=s.newBuffer([]),e.prototype.iconVersion=0,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=u.create()),null!=e.endPoint&&Object.hasOwnProperty.call(e,"endPoint")&&t.uint32(10).string(e.endPoint),null!=e.icon&&Object.hasOwnProperty.call(e,"icon")&&t.uint32(18).bytes(e.icon),null!=e.iconVersion&&Object.hasOwnProperty.call(e,"iconVersion")&&t.uint32(24).int32(e.iconVersion),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new o.master.ServerIcon;e.pos<r;){var u=e.uint32();switch(u>>>3){case 1:n.endPoint=e.string();break;case 2:n.icon=e.bytes();break;case 3:n.iconVersion=e.int32();break;default:e.skipType(7&u)}}return n},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.endPoint&&e.hasOwnProperty("endPoint")&&!s.isString(e.endPoint)?"endPoint: string expected":null!=e.icon&&e.hasOwnProperty("icon")&&!(e.icon&&"number"==typeof e.icon.length||s.isString(e.icon))?"icon: buffer expected":null!=e.iconVersion&&e.hasOwnProperty("iconVersion")&&!s.isInteger(e.iconVersion)?"iconVersion: integer expected":null},e.fromObject=function(e){if(e instanceof o.master.ServerIcon)return e;var t=new o.master.ServerIcon;return null!=e.endPoint&&(t.endPoint=String(e.endPoint)),null!=e.icon&&("string"==typeof e.icon?s.base64.decode(e.icon,t.icon=s.newBuffer(s.base64.length(e.icon)),0):e.icon.length&&(t.icon=e.icon)),null!=e.iconVersion&&(t.iconVersion=0|e.iconVersion),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.endPoint="",t.bytes===String?r.icon="":(r.icon=[],t.bytes!==Array&&(r.icon=s.newBuffer(r.icon))),r.iconVersion=0),null!=e.endPoint&&e.hasOwnProperty("endPoint")&&(r.endPoint=e.endPoint),null!=e.icon&&e.hasOwnProperty("icon")&&(r.icon=t.bytes===String?s.base64.encode(e.icon,0,e.icon.length):t.bytes===Array?Array.prototype.slice.call(e.icon):e.icon),null!=e.iconVersion&&e.hasOwnProperty("iconVersion")&&(r.iconVersion=e.iconVersion),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e.ServerIcons=function(){function e(e){if(this.icons=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.icons=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=u.create()),null!=e.icons&&e.icons.length)for(var r=0;r<e.icons.length;++r)o.master.ServerIcon.encode(e.icons[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof a||(e=a.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new o.master.ServerIcons;e.pos<r;){var u=e.uint32();u>>>3==1?(n.icons&&n.icons.length||(n.icons=[]),n.icons.push(o.master.ServerIcon.decode(e,e.uint32()))):e.skipType(7&u)}return n},e.decodeDelimited=function(e){return e instanceof a||(e=new a(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.icons&&e.hasOwnProperty("icons")){if(!Array.isArray(e.icons))return"icons: array expected";for(var t=0;t<e.icons.length;++t){var r=o.master.ServerIcon.verify(e.icons[t]);if(r)return"icons."+r}}return null},e.fromObject=function(e){if(e instanceof o.master.ServerIcons)return e;var t=new o.master.ServerIcons;if(e.icons){if(!Array.isArray(e.icons))throw TypeError(".master.ServerIcons.icons: array expected");t.icons=[];for(var r=0;r<e.icons.length;++r){if("object"!=typeof e.icons[r])throw TypeError(".master.ServerIcons.icons: object expected");t.icons[r]=o.master.ServerIcon.fromObject(e.icons[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.icons=[]),e.icons&&e.icons.length){r.icons=[];for(var n=0;n<e.icons.length;++n)r.icons[n]=o.master.ServerIcon.toObject(e.icons[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,n.util.toJSONOptions)},e}(),e}(),e.exports=o}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var __webpack_exports__={},game_GameName,GameUpdateChannel;function getGameBuildDLCName(e){switch(e){case"2060":return"Los Santos Summer Special";case"2189":case"2215":case"2245":return"Cayo Perico Heist";case"2372":return"Los Santos Tuners";case"2545":case"2612":return"The Contract";case"2699":return"The Criminal Enterprises";case"2802":return"Los Santos Drug Wars";case"2944":return"San Andreas Mercenaries";case"3095":return"The Chop Shop";case"3258":case"3323":return"Bottom Dollar Bounties";case"3407":return"Agents of Sabotage"}return""}!function(e){e.FiveM="gta5",e.RedM="rdr3",e.LibertyM="ny",e.Launcher="launcher"}(game_GameName||(game_GameName={})),function(e){e.Production="production",e.Beta="beta",e.Canary="canary"}(GameUpdateChannel||(GameUpdateChannel={}));const emoji_regex=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;var ServersListSortBy,ServersListType,ServerListSortDir;!function(e){e.Boosts="upvotePower",e.Name="name",e.Players="players"}(ServersListSortBy||(ServersListSortBy={})),function(e){e.All="browse",e.Supporters="premium",e.Favorites="favorites",e.History="history",e.RegionalTop="regionalTop"}(ServersListType||(ServersListType={})),function(e){e[e.Asc=1]="Asc",e[e.Desc=-1]="Desc"}(ServerListSortDir||(ServerListSortDir={}));const _cldr={validLocales:new Set(["ac","ad","ae","af","ag","ai","al","am","ao","aq","ar","as","at","au","aw","ax","az","ba","bb","bd","be","bf","bg","bh","bi","bj","bl","bm","bn","bo","bq","br","bs","bt","bv","bw","by","bz","ca","cc","cd","cf","cg","ch","ci","ck","cl","cm","cn","co","cp","cr","cu","cv","cw","cx","cy","cz","de","dg","dj","dk","dm","do","dz","ea","ec","ee","eg","eh","er","es","et","fi","fj","fk","fm","fo","fr","ga","gb","gd","ge","gf","gg","gh","gi","gl","gm","gn","gp","gq","gr","gs","gt","gu","gw","gy","hk","hm","hn","hr","ht","hu","ic","id","ie","il","im","in","io","iq","ir","is","it","je","jm","jo","jp","ke","kg","kh","ki","km","kn","kp","kr","kw","ky","kz","la","lb","lc","li","lk","lr","ls","lt","lu","lv","ly","ma","mc","md","me","mf","mg","mh","mk","ml","mm","mn","mo","mp","mq","mr","ms","mt","mu","mv","mw","mx","my","mz","na","nc","ne","nf","ng","ni","nl","no","np","nr","nu","nz","om","pa","pe","pf","pg","ph","pk","pl","pm","pn","pr","ps","pt","pw","py","qa","re","ro","rs","ru","rw","sa","sb","sc","sd","se","sg","sh","si","sj","sk","sl","sm","sn","so","sr","ss","st","sv","sx","sy","sz","ta","tc","td","tf","tg","th","tj","tk","tl","tm","tn","to","tr","tt","tv","tw","tz","ua","ug","um","us","uy","uz","va","vc","ve","vg","vi","vn","vu","wf","ws","xk","ye","yt","za","zm","zw","zz","aa","aa-alt-secondary","ab","ab-alt-secondary","abq","abr","abr-alt-secondary","ace","ace-alt-secondary","ach","ach-alt-secondary","ada","ady","ady-alt-secondary","ae-alt-secondary","aeb","aeb-alt-secondary","af-alt-secondary","agq","aii","aii-alt-secondary","ain-alt-secondary","ak","ak-alt-secondary","akk-alt-secondary","akz","ale","aln","aln-alt-secondary","alt","amo","an","ang-alt-secondary","anp","aoz","ar-alt-secondary","arc-alt-secondary","arn","aro","arp","arq","arq-alt-secondary","ars","ars-alt-secondary","arw-alt-secondary","ary","ary-alt-secondary","arz","arz-alt-secondary","as-alt-secondary","asa","ast","ast-alt-secondary","atj","av","av-alt-secondary","avk-alt-secondary","awa","awa-alt-secondary","ay","az-alt-secondary","ba-alt-secondary","bal","bal-alt-secondary","ban","ban-alt-secondary","bap","bar","bar-alt-secondary","bas","bax","bbc","bbc-alt-secondary","bbj","bci","bci-alt-secondary","bej","bej-alt-secondary","bem","bem-alt-secondary","bew","bew-alt-secondary","bez","bfd","bfq","bft","bft-alt-secondary","bfy","bgc","bgc-alt-secondary","bgn","bgn-alt-secondary","bgx","bhb","bhb-alt-secondary","bhi","bhi-alt-secondary","bho","bho-alt-secondary","bik","bik-alt-secondary","bin","bin-alt-secondary","bjj","bjj-alt-secondary","bjn","bjn-alt-secondary","bjt-alt-secondary","bkm","bku","bku-alt-secondary","bla","blt","bm-alt-secondary","bmq","bn-alt-secondary","bo-alt-secondary","bpy","bqi","bqi-alt-secondary","bqv","bra","brh","brh-alt-secondary","brx","brx-alt-secondary","bsc-alt-secondary","bss","bto","btv","bua","buc","buc-alt-secondary","bug","bug-alt-secondary","bum","bum-alt-secondary","bvb","byn","byv","bze","bzx","ca-alt-secondary","cad","car","cay","cch","ccp","ce","ce-alt-secondary","ceb","ceb-alt-secondary","cgg","cgg-alt-secondary","chk","chk-alt-secondary","chm","chn-alt-secondary","cho","chp","chp-alt-secondary","chr","chy","cic","cja","cja-alt-secondary","cjm","cjm-alt-secondary","cjs","ckb","ckt","cop-alt-secondary","cps","crh","crj","crj-alt-secondary","crk","crl","crl-alt-secondary","crm","crs","crs-alt-secondary","cs","cs-alt-secondary","csb-alt-secondary","csw","ctd","cu-alt-secondary","cv-alt-secondary","cy-alt-secondary","da","da-alt-secondary","dak","dar","dav","dcc","dcc-alt-secondary","de-alt-secondary","del","den","den-alt-secondary","dgr","din","dje","dje-alt-secondary","dng","dnj","dnj-alt-secondary","doi","doi-alt-secondary","dsb","dtm","dtp","dty","dua","dum-alt-secondary","dv","dyo","dyo-alt-secondary","dyu","dyu-alt-secondary","ebu","ee-alt-secondary","efi","efi-alt-secondary","egl","egy-alt-secondary","eka","eky","el","en","en-alt-secondary","enm-alt-secondary","eo","es-alt-secondary","esu","ett-alt-secondary","eu","eu-alt-secondary","evn","ewo","ext","fa","fa-alt-secondary","fan","fan-alt-secondary","fbl-alt-secondary","ff","ff-alt-secondary","ffm","ffm-alt-secondary","fi-alt-secondary","fia","fil","fil-alt-secondary","fit","fon","fon-alt-secondary","fr-alt-secondary","frc","frm-alt-secondary","fro-alt-secondary","frp","frr","frs","fud","fud-alt-secondary","fuq","fuq-alt-secondary","fur","fuv","fuv-alt-secondary","fvr","fvr-alt-secondary","fy","fy-alt-secondary","ga-alt-secondary","gaa","gaa-alt-secondary","gag","gag-alt-secondary","gan","gan-alt-secondary","gay","gba","gbm","gbm-alt-secondary","gbz","gcr","gcr-alt-secondary","gd-alt-secondary","gez-alt-secondary","gil","gjk","gju","gl-alt-secondary","gld","glk","glk-alt-secondary","gmh-alt-secondary","goh-alt-secondary","gom","gom-alt-secondary","gon","gon-alt-secondary","gor","gor-alt-secondary","gos","got-alt-secondary","grb","grc-alt-secondary","grt","gsw","gsw-alt-secondary","gu-alt-secondary","gub","guc","gur","gur-alt-secondary","guz","guz-alt-secondary","gv","gvr","gwi","ha","ha-alt-secondary","hai","hak","hak-alt-secondary","haw","haw-alt-secondary","haz","haz-alt-secondary","he","hi","hi-alt-secondary","hif","hil","hil-alt-secondary","hit-alt-secondary","hmd","hmn","hmn-alt-secondary","hnd","hne","hne-alt-secondary","hnj","hnj-alt-secondary","hnn","hnn-alt-secondary","hno","hno-alt-secondary","ho","hoc","hoc-alt-secondary","hoj","hoj-alt-secondary","hop","hr-alt-secondary","hsb","hsn","hsn-alt-secondary","hu-alt-secondary","hup","hy","hy-alt-secondary","hz","ia-alt-secondary","iba","ibb","ibb-alt-secondary","id-alt-secondary","ife","ig","ig-alt-secondary","ii","ii-alt-secondary","ik","ikt","ikt-alt-secondary","ilo","ilo-alt-secondary","inh","inh-alt-secondary","it-alt-secondary","iu","iu-alt-secondary","izh","ja","jam","jam-alt-secondary","jgo","jmc","jml","jpr","jrb","jut-alt-secondary","jv","jv-alt-secondary","ka","kaa","kab","kab-alt-secondary","kac","kaj","kam","kam-alt-secondary","kao","kbd","kbd-alt-secondary","kca","kcg","kck","kde","kde-alt-secondary","kdt","kea","kea-alt-secondary","kfo","kfr","kfy","kfy-alt-secondary","kg-alt-secondary","kge","kgp","kha","kha-alt-secondary","khb","khn","khn-alt-secondary","khq","kht","khw","ki-alt-secondary","kiu","kj","kj-alt-secondary","kjg","kjg-alt-secondary","kjh","kk","kk-alt-secondary","kkj","kl","kl-alt-secondary","kln","kln-alt-secondary","kmb","kmb-alt-secondary","kn-alt-secondary","knf-alt-secondary","ko","ko-alt-secondary","koi","koi-alt-secondary","kok","kok-alt-secondary","kos","kpe","kpy","krc","krc-alt-secondary","kri","kri-alt-secondary","krj","krl","kru","kru-alt-secondary","ks","ks-alt-secondary","ksb","ksf","ksh","ku","ku-alt-secondary","kum","kum-alt-secondary","kut","kv","kv-alt-secondary","kvr","kvx","kxm","kxm-alt-secondary","kxp","kyu","la-alt-secondary","lab-alt-secondary","lad","lag","lah","lah-alt-secondary","laj","laj-alt-secondary","lam","lbe","lbe-alt-secondary","lbw","lcp","lep","lez","lez-alt-secondary","lfn-alt-secondary","lg","lg-alt-secondary","lif","lij","lis","liv-alt-secondary","ljp","ljp-alt-secondary","lki","lkt","lmn","lmn-alt-secondary","lmo","ln","ln-alt-secondary","lo","lol","loz","loz-alt-secondary","lrc","lrc-alt-secondary","lt-alt-secondary","ltg","lu-alt-secondary","lua","lua-alt-secondary","lui-alt-secondary","lun","luo","luo-alt-secondary","lus","lut-alt-secondary","luy","luy-alt-secondary","luz","luz-alt-secondary","lwl","lzh-alt-secondary","lzz","mad","mad-alt-secondary","maf","mag","mag-alt-secondary","mai","mai-alt-secondary","mak","mak-alt-secondary","man","man-alt-secondary","mas","maz","mdf","mdf-alt-secondary","mdh","mdh-alt-secondary","mdr","mdr-alt-secondary","mdt","men","men-alt-secondary","mer","mer-alt-secondary","mey-alt-secondary","mfa","mfa-alt-secondary","mfe","mfe-alt-secondary","mfv-alt-secondary","mgh","mgh-alt-secondary","mgo","mgp","mgy","mi","mic","min","min-alt-secondary","ml-alt-secondary","mls","mn-alt-secondary","mnc-alt-secondary","mni","mni-alt-secondary","mns","mnw","moe","moh","mos","mos-alt-secondary","mr-alt-secondary","mrd","mrj","mro","mro-alt-secondary","ms-alt-secondary","mtr","mtr-alt-secondary","mua","mus","mvy","mwk","mwl","mwr","mwr-alt-secondary","mwv","mxc","myv","myv-alt-secondary","myx","myx-alt-secondary","myz-alt-secondary","mzn","mzn-alt-secondary","nan","nan-alt-secondary","nap","naq","nb","nch","nd","ndc","ndc-alt-secondary","nds","nds-alt-secondary","ne-alt-secondary","new","new-alt-secondary","ng-alt-secondary","ngl","ngl-alt-secondary","nhe","nhw","nia","nij","niu","njo","nl-alt-secondary","nmg","nn","nnh","nod","nod-alt-secondary","noe","noe-alt-secondary","nog","non-alt-secondary","nov-alt-secondary","nqo","nr-alt-secondary","nsk","nsk-alt-secondary","nso","nso-alt-secondary","nus","nv","nxq","ny","ny-alt-secondary","nym","nym-alt-secondary","nyn","nyn-alt-secondary","nyo","nzi","oc","oc-alt-secondary","oj","oj-alt-secondary","om-alt-secondary","or","or-alt-secondary","os","os-alt-secondary","osa","osa-alt-secondary","osc-alt-secondary","otk-alt-secondary","pa-alt-secondary","pag","pag-alt-secondary","pal-alt-secondary","pam","pam-alt-secondary","pap","pap-alt-secondary","pau","pcd","pcm","pcm-alt-secondary","pdc","pdt","peo-alt-secondary","pfl","phn-alt-secondary","pi-alt-secondary","pko","pl-alt-secondary","pms","pnt","pon","pon-alt-secondary","prd","prg-alt-secondary","pro-alt-secondary","ps-alt-secondary","puu","qu","quc","quc-alt-secondary","qug","raj","raj-alt-secondary","rap","rar","rcf","rcf-alt-secondary","rej","rej-alt-secondary","rgn","rhg","rhg-alt-secondary","ria","rif","rif-alt-secondary","rjs","rkt","rkt-alt-secondary","rm","rm-alt-secondary","rmf","rmo","rmt","rmt-alt-secondary","rmu","rn","rng","rng-alt-secondary","ro-alt-secondary","rob","rof","rom","rom-alt-secondary","rtm","ru-alt-secondary","rue","rug","rup","rwk","ryu","sa-alt-secondary","sad","saf","sah","sah-alt-secondary","sam-alt-secondary","saq","sas","sas-alt-secondary","sat","sat-alt-secondary","sav-alt-secondary","saz","sbp","sc-alt-secondary","sck","sck-alt-secondary","scn","sco","sco-alt-secondary","scs","sd-alt-secondary","sdc","sdh","sdh-alt-secondary","se-alt-secondary","see","sef","sef-alt-secondary","seh","seh-alt-secondary","sei","sel-alt-secondary","ses","sga-alt-secondary","sgs","shi","shi-alt-secondary","shn","shn-alt-secondary","sid","sid-alt-secondary","sk-alt-secondary","skr","skr-alt-secondary","sl-alt-secondary","sli","sly","sma","smj","smn","smp-alt-secondary","sms","snf-alt-secondary","snk","snk-alt-secondary","so-alt-secondary","sou","sou-alt-secondary","sq","sq-alt-secondary","srb","srb-alt-secondary","srn","srn-alt-secondary","srr","srr-alt-secondary","srx","ss-alt-secondary","ssy","st-alt-secondary","stq","su","su-alt-secondary","suk","suk-alt-secondary","sus","sus-alt-secondary","sw","sw-alt-secondary","swb","swb-alt-secondary","swg","swv","swv-alt-secondary","sxn","syi","syl","syl-alt-secondary","syr-alt-secondary","szl","ta-alt-secondary","tab","taj","taj-alt-secondary","tbw","tbw-alt-secondary","tcy","tcy-alt-secondary","tdd","tdg","tdg-alt-secondary","tdh","te","te-alt-secondary","tem","tem-alt-secondary","teo","teo-alt-secondary","ter","tet","thl","thq","thr","ti","ti-alt-secondary","tig","tig-alt-secondary","tiv","tiv-alt-secondary","tk-alt-secondary","tkl","tkr","tkt","tli","tly","tly-alt-secondary","tmh","tmh-alt-secondary","tn-alt-secondary","tnr-alt-secondary","tog","tpi","tr-alt-secondary","tru","tru-alt-secondary","trv","trw","ts","ts-alt-secondary","tsd","tsg","tsg-alt-secondary","tsi","tsj","tt-alt-secondary","ttj","tts","tts-alt-secondary","ttt","ttt-alt-secondary","tum","tum-alt-secondary","tvl","twq","ty","tyv","tyv-alt-secondary","tzm","ude","udm","udm-alt-secondary","ug-alt-secondary","uga-alt-secondary","uk","uk-alt-secondary","uli","umb","umb-alt-secondary","und-alt-secondary","unr","unr-alt-secondary","unx","ur","ur-alt-secondary","uz-alt-secondary","vai","ve-alt-secondary","vec","vep","vi-alt-secondary","vic","vls","vls-alt-secondary","vmf","vmf-alt-secondary","vmw","vmw-alt-secondary","vo-alt-secondary","vot-alt-secondary","vro","vun","wa","wae","wal","wal-alt-secondary","war","war-alt-secondary","was","wbp","wbq","wbq-alt-secondary","wbr","wbr-alt-secondary","wls","wls-alt-secondary","wni","wo","wo-alt-secondary","wtm","wtm-alt-secondary","wuu","wuu-alt-secondary","xal","xav","xcr-alt-secondary","xh","xh-alt-secondary","xlc-alt-secondary","xld-alt-secondary","xmf","xmn-alt-secondary","xmr-alt-secondary","xna-alt-secondary","xnr","xnr-alt-secondary","xog","xog-alt-secondary","xpr-alt-secondary","xsa-alt-secondary","xsr","xum-alt-secondary","yao","yap","yav","ybb","yi","yo","yrk","yrl","yua","yue","yue-alt-secondary","za-alt-secondary","zag","zap","zdj","zea","zen-alt-secondary","zgh","zgh-alt-secondary","zh","zh-alt-secondary","zmi","zu","zu-alt-secondary","zun","zza","zza-alt-secondary"]),languageMap:{afar:"aa",abkhazian:"ab",achinese:"ace",acoli:"ach",adangme:"ada",adyghe:"ady",avestan:"ae","tunisian-arabic":"aeb",afrikaans:"af",afrihili:"afh",aghem:"agq",ainu:"ain",akan:"ak",akkadian:"akk",alabama:"akz",aleut:"ale","gheg-albanian":"aln","southern-altai":"alt",amharic:"am",aragonese:"an","old-english":"ang",angika:"anp",arabic:"ar","modern-standard-arabic":"ar",aramaic:"arc",mapuche:"arn",araona:"aro",arapaho:"arp","algerian-arabic":"arq","najdi-arabic":"ars","arabic,-najdi":"ars",arawak:"arw","moroccan-arabic":"ary","egyptian-arabic":"arz",assamese:"as",asu:"asa","american-sign-language":"ase",asturian:"ast",avaric:"av",kotava:"avk",awadhi:"awa",aymara:"ay",azerbaijani:"az",azeri:"az",bashkir:"ba",baluchi:"bal",balinese:"ban",bavarian:"bar",basaa:"bas",bamun:"bax","batak-toba":"bbc",ghomala:"bbj",belarusian:"be",beja:"bej",bemba:"bem",betawi:"bew",bena:"bez",bafut:"bfd",badaga:"bfq",bulgarian:"bg","western-balochi":"bgn",bhojpuri:"bho",bislama:"bi",bikol:"bik",bini:"bin",banjar:"bjn",kom:"bkm",siksika:"bla",bambara:"bm",bangla:"bn",tibetan:"bo",bishnupriya:"bpy",bakhtiari:"bqi",breton:"br",braj:"bra",brahui:"brh",bodo:"brx",bosnian:"bs",akoose:"bss",buriat:"bua",buginese:"bug",bulu:"bum",blin:"byn",medumba:"byv",catalan:"ca",caddo:"cad",carib:"car",cayuga:"cay",atsam:"cch",chakma:"ccp",chechen:"ce",cebuano:"ceb",chiga:"cgg",chamorro:"ch",chibcha:"chb",chagatai:"chg",chuukese:"chk",mari:"chm","chinook-jargon":"chn",choctaw:"cho",chipewyan:"chp",cherokee:"chr",cheyenne:"chy",chickasaw:"cic","central-kurdish":"ckb","kurdish,-central":"ckb","kurdish,-sorani":"ckb",corsican:"co",coptic:"cop",capiznon:"cps",cree:"cr","crimean-turkish":"crh","seselwa-creole-french":"crs",czech:"cs",kashubian:"csb","church-slavic":"cu",chuvash:"cv",welsh:"cy",danish:"da",dakota:"dak",dargwa:"dar",taita:"dav",german:"de","austrian-german":"de","swiss-high-german":"de",delaware:"del",slave:"den",dogrib:"dgr",dinka:"din",zarma:"dje",dogri:"doi","lower-sorbian":"dsb","central-dusun":"dtp",duala:"dua","middle-dutch":"dum",divehi:"dv","jola-fonyi":"dyo",dyula:"dyu",dzongkha:"dz",dazaga:"dzg",embu:"ebu",ewe:"ee",efik:"efi",emilian:"egl","ancient-egyptian":"egy",ekajuk:"eka",greek:"el",elamite:"elx",english:"en","australian-english":"en","canadian-english":"en","british-english":"en","uk-english":"en","american-english":"en","us-english":"en","middle-english":"enm",esperanto:"eo",spanish:"es","latin-american-spanish":"es","european-spanish":"es","mexican-spanish":"es","central-yupik":"esu",estonian:"et",basque:"eu",ewondo:"ewo",extremaduran:"ext",persian:"fa",dari:"fa",fang:"fan",fanti:"fat",fulah:"ff",finnish:"fi",filipino:"fil","tornedalen-finnish":"fit",fijian:"fj",faroese:"fo",fon:"fon",french:"fr","canadian-french":"fr","swiss-french":"fr","cajun-french":"frc","middle-french":"frm","old-french":"fro",arpitan:"frp","northern-frisian":"frr","eastern-frisian":"frs",friulian:"fur","western-frisian":"fy",irish:"ga",ga:"gaa",gagauz:"gag","gan-chinese":"gan",gayo:"gay",gbaya:"gba","zoroastrian-dari":"gbz","scottish-gaelic":"gd",geez:"gez",gilbertese:"gil",galician:"gl",gilaki:"glk","middle-high-german":"gmh",guarani:"gn","old-high-german":"goh","goan-konkani":"gom",gondi:"gon",gorontalo:"gor",gothic:"got",grebo:"grb","ancient-greek":"grc","swiss-german":"gsw",gujarati:"gu",wayuu:"guc",frafra:"gur",gusii:"guz",manx:"gv",gwichʼin:"gwi",hausa:"ha",haida:"hai","hakka-chinese":"hak",hawaiian:"haw",hebrew:"he",hindi:"hi","fiji-hindi":"hif",hiligaynon:"hil",hittite:"hit",hmong:"hmn","hiri-motu":"ho",croatian:"hr","upper-sorbian":"hsb","xiang-chinese":"hsn","haitian-creole":"ht",hungarian:"hu",hupa:"hup",armenian:"hy",herero:"hz",interlingua:"ia",iban:"iba",ibibio:"ibb",indonesian:"id",interlingue:"ie",igbo:"ig","sichuan-yi":"ii",inupiaq:"ik",iloko:"ilo",ingush:"inh",ido:"io",icelandic:"is",italian:"it",inuktitut:"iu",ingrian:"izh",japanese:"ja","jamaican-creole-english":"jam",lojban:"jbo",ngomba:"jgo",machame:"jmc","judeo-persian":"jpr","judeo-arabic":"jrb",jutish:"jut",javanese:"jv",georgian:"ka","kara-kalpak":"kaa",kabyle:"kab",kachin:"kac",jju:"kaj",kamba:"kam",kawi:"kaw",kabardian:"kbd",kanembu:"kbl",tyap:"kcg",makonde:"kde",kabuverdianu:"kea",kenyang:"ken",koro:"kfo",kongo:"kg",kaingang:"kgp",khasi:"kha",khotanese:"kho","koyra-chiini":"khq",khowar:"khw",kikuyu:"ki",kirmanjki:"kiu",kuanyama:"kj",kazakh:"kk",kako:"kkj",kalaallisut:"kl",kalenjin:"kln",khmer:"km",kimbundu:"kmb",kannada:"kn",korean:"ko","komi-permyak":"koi",konkani:"kok",kosraean:"kos",kpelle:"kpe",kanuri:"kr","karachay-balkar":"krc",krio:"kri","kinaray-a":"krj",karelian:"krl",kurukh:"kru",kashmiri:"ks",shambala:"ksb",bafia:"ksf",colognian:"ksh",kurdish:"ku",kumyk:"kum",kutenai:"kut",komi:"kv",cornish:"kw",kyrgyz:"ky",kirghiz:"ky",latin:"la",ladino:"lad",langi:"lag",lahnda:"lah",lamba:"lam",luxembourgish:"lb",lezghian:"lez","lingua-franca-nova":"lfn",ganda:"lg",limburgish:"li",ligurian:"lij",livonian:"liv",lakota:"lkt",lombard:"lmo",lingala:"ln",lao:"lo",mongo:"lol","louisiana-creole":"lou",lozi:"loz","northern-luri":"lrc",lithuanian:"lt",latgalian:"ltg","luba-katanga":"lu","luba-lulua":"lua",luiseno:"lui",lunda:"lun",luo:"luo",mizo:"lus",luyia:"luy",latvian:"lv","literary-chinese":"lzh",laz:"lzz",madurese:"mad",mafa:"maf",magahi:"mag",maithili:"mai",makasar:"mak",mandingo:"man",masai:"mas",maba:"mde",moksha:"mdf",mandar:"mdr",mende:"men",meru:"mer",morisyen:"mfe",malagasy:"mg","middle-irish":"mga","makhuwa-meetto":"mgh",metaʼ:"mgo",marshallese:"mh",māori:"mi","mi'kmaq":"mic",minangkabau:"min",macedonian:"mk",malayalam:"ml",mongolian:"mn",manchu:"mnc",manipuri:"mni",mohawk:"moh",mossi:"mos",marathi:"mr","western-mari":"mrj",malay:"ms",maltese:"mt",mundang:"mua","multiple-languages":"mul",muscogee:"mus",mirandese:"mwl",marwari:"mwr",mentawai:"mwv",burmese:"my","myanmar-language":"my",myene:"mye",erzya:"myv",mazanderani:"mzn",nauru:"na","min-nan-chinese":"nan",neapolitan:"nap",nama:"naq","norwegian-bokmål":"nb","north-ndebele":"nd","low-german":"nds","low-saxon":"nds",nepali:"ne",newari:"new",ndonga:"ng",nias:"nia",niuean:"niu","ao-naga":"njo",dutch:"nl",flemish:"nl",kwasio:"nmg","norwegian-nynorsk":"nn",ngiemboon:"nnh",norwegian:"no",nogai:"nog","old-norse":"non",novial:"nov","n’ko":"nqo","south-ndebele":"nr","northern-sotho":"nso",nuer:"nus",navajo:"nv","classical-newari":"nwc",nyanja:"ny",nyamwezi:"nym",nyankole:"nyn",nyoro:"nyo",nzima:"nzi",occitan:"oc",ojibwa:"oj",oromo:"om",odia:"or",ossetic:"os",osage:"osa","ottoman-turkish":"ota",punjabi:"pa",pangasinan:"pag",pahlavi:"pal",pampanga:"pam",papiamento:"pap",palauan:"pau",picard:"pcd","nigerian-pidgin":"pcm","pennsylvania-german":"pdc",plautdietsch:"pdt","old-persian":"peo","palatine-german":"pfl",phoenician:"phn",pali:"pi",polish:"pl",piedmontese:"pms",pontic:"pnt",pohnpeian:"pon",prussian:"prg","old-provençal":"pro",pashto:"ps",pushto:"ps",portuguese:"pt","brazilian-portuguese":"pt","european-portuguese":"pt",quechua:"qu",kʼicheʼ:"quc","chimborazo-highland-quichua":"qug",rajasthani:"raj",rapanui:"rap",rarotongan:"rar",romagnol:"rgn",rohingya:"rhg",riffian:"rif",romansh:"rm",rundi:"rn",romanian:"ro",moldavian:"ro",rombo:"rof",romany:"rom",rotuman:"rtm",russian:"ru",rusyn:"rue",roviana:"rug",aromanian:"rup",kinyarwanda:"rw",rwa:"rwk",sanskrit:"sa",sandawe:"sad",sakha:"sah","samaritan-aramaic":"sam",samburu:"saq",sasak:"sas",santali:"sat",saurashtra:"saz",ngambay:"sba",sangu:"sbp",sardinian:"sc",sicilian:"scn",scots:"sco",sindhi:"sd","sassarese-sardinian":"sdc","southern-kurdish":"sdh","northern-sami":"se","sami,-northern":"se",seneca:"see",sena:"seh",seri:"sei",selkup:"sel","koyraboro-senni":"ses",sango:"sg","old-irish":"sga",samogitian:"sgs","serbo-croatian":"sh",tachelhit:"shi",shan:"shn","chadian-arabic":"shu",sinhala:"si",sidamo:"sid",slovak:"sk",slovenian:"sl","lower-silesian":"sli",selayar:"sly",samoan:"sm","southern-sami":"sma","sami,-southern":"sma","lule-sami":"smj","sami,-lule":"smj","inari-sami":"smn","sami,-inari":"smn","skolt-sami":"sms","sami,-skolt":"sms",shona:"sn",soninke:"snk",somali:"so",sogdien:"sog",albanian:"sq",serbian:"sr",montenegrin:"sr","sranan-tongo":"srn",serer:"srr",swati:"ss",saho:"ssy","southern-sotho":"st","saterland-frisian":"stq",sundanese:"su",sukuma:"suk",susu:"sus",sumerian:"sux",swedish:"sv",swahili:"sw","congo-swahili":"sw",comorian:"swb","classical-syriac":"syc",syriac:"syr",silesian:"szl",tamil:"ta",tulu:"tcy",telugu:"te",timne:"tem",teso:"teo",tereno:"ter",tetum:"tet",tajik:"tg",thai:"th",tigrinya:"ti",tigre:"tig",tiv:"tiv",turkmen:"tk",tokelau:"tkl",tsakhur:"tkr",tagalog:"tl",klingon:"tlh",tlingit:"tli",talysh:"tly",tamashek:"tmh",tswana:"tn",tongan:"to","nyasa-tonga":"tog","tok-pisin":"tpi",turkish:"tr",turoyo:"tru",taroko:"trv",tsonga:"ts",tsakonian:"tsd",tsimshian:"tsi",tatar:"tt","muslim-tat":"ttt",tumbuka:"tum",tuvalu:"tvl",twi:"tw",tasawaq:"twq",tahitian:"ty",tuvinian:"tyv","central-atlas-tamazight":"tzm",udmurt:"udm",uyghur:"ug",uighur:"ug",ugaritic:"uga",ukrainian:"uk",umbundu:"umb","unknown-language":"und",urdu:"ur",uzbek:"uz",vai:"vai",venda:"ve",venetian:"vec",veps:"vep",vietnamese:"vi","west-flemish":"vls","main-franconian":"vmf",volapük:"vo",votic:"vot",võro:"vro",vunjo:"vun",walloon:"wa",walser:"wae",wolaytta:"wal",waray:"war",washo:"was",warlpiri:"wbp",wolof:"wo","wu-chinese":"wuu",kalmyk:"xal",xhosa:"xh",mingrelian:"xmf",soga:"xog",yao:"yao",yapese:"yap",yangben:"yav",yemba:"ybb",yiddish:"yi",yoruba:"yo",nheengatu:"yrl",cantonese:"yue","chinese,-cantonese":"yue",zhuang:"za",zapotec:"zap",blissymbols:"zbl",zeelandic:"zea",zenaga:"zen","standard-moroccan-tamazight":"zgh",chinese:"zh","mandarin-chinese":"zh","chinese,-mandarin":"zh","simplified-chinese":"zh","simplified-mandarin-chinese":"zh","traditional-chinese":"zh","traditional-mandarin-chinese":"zh",zulu:"zu",zuni:"zun","no-linguistic-content":"zxx",zaza:"zza"},countryMap:{asia:"142","central-asia":"143","western-asia":"145",europe:"150","eastern-europe":"151","northern-europe":"154","western-europe":"155","sub-saharan-africa":"202","latin-america":"419",world:"001",africa:"002","north-america":"003","south-america":"005",oceania:"009","western-africa":"011","central-america":"013","eastern-africa":"014","northern-africa":"015","middle-africa":"017","southern-africa":"018",americas:"019","northern-america":"021",caribbean:"029","eastern-asia":"030","southern-asia":"034","southeast-asia":"035","southern-europe":"039",australasia:"053",melanesia:"054","micronesian-region":"057",polynesia:"061","ascension-island":"ac",andorra:"ad","united-arab-emirates":"ae",afghanistan:"af","antigua-&-barbuda":"ag",anguilla:"ai",albania:"al",armenia:"am",angola:"ao",antarctica:"aq",argentina:"ar","american-samoa":"as",austria:"at",australia:"au",aruba:"aw","åland-islands":"ax",azerbaijan:"az","bosnia-&-herzegovina":"ba",bosnia:"ba",barbados:"bb",bangladesh:"bd",belgium:"be","burkina-faso":"bf",bulgaria:"bg",bahrain:"bh",burundi:"bi",benin:"bj","st.-barthélemy":"bl",bermuda:"bm",brunei:"bn",bolivia:"bo","caribbean-netherlands":"bq",brazil:"br",bahamas:"bs",bhutan:"bt","bouvet-island":"bv",botswana:"bw",belarus:"by",belize:"bz",canada:"ca","cocos-(keeling)-islands":"cc","congo---kinshasa":"cd","congo-(drc)":"cd","central-african-republic":"cf","congo---brazzaville":"cg","congo-(republic)":"cg",switzerland:"ch","côte-d’ivoire":"ci","ivory-coast":"ci","cook-islands":"ck",chile:"cl",cameroon:"cm",china:"cn",colombia:"co","clipperton-island":"cp","costa-rica":"cr",cuba:"cu","cape-verde":"cv","cabo-verde":"cv",curaçao:"cw","christmas-island":"cx",cyprus:"cy",czechia:"cz","czech-republic":"cz",germany:"de","diego-garcia":"dg",djibouti:"dj",denmark:"dk",dominica:"dm","dominican-republic":"do",algeria:"dz","ceuta-&-melilla":"ea",ecuador:"ec",estonia:"ee",egypt:"eg","western-sahara":"eh",eritrea:"er",spain:"es",ethiopia:"et","european-union":"eu",eurozone:"ez",finland:"fi",fiji:"fj","falkland-islands":"fk","falkland-islands-(islas-malvinas)":"fk",micronesia:"fm","faroe-islands":"fo",france:"fr",gabon:"ga","united-kingdom":"gb",uk:"gb",grenada:"gd",georgia:"ge","french-guiana":"gf",guernsey:"gg",ghana:"gh",gibraltar:"gi",greenland:"gl",gambia:"gm",guinea:"gn",guadeloupe:"gp","equatorial-guinea":"gq",greece:"gr","south-georgia-&-south-sandwich-islands":"gs",guatemala:"gt",guam:"gu","guinea-bissau":"gw",guyana:"gy","hong-kong-sar-china":"hk","hong-kong":"hk","heard-&-mcdonald-islands":"hm",honduras:"hn",croatia:"hr",haiti:"ht",hungary:"hu","canary-islands":"ic",indonesia:"id",ireland:"ie",israel:"il","isle-of-man":"im",india:"in","british-indian-ocean-territory":"io",iraq:"iq",iran:"ir",iceland:"is",italy:"it",jersey:"je",jamaica:"jm",jordan:"jo",japan:"jp",kenya:"ke",kyrgyzstan:"kg",cambodia:"kh",kiribati:"ki",comoros:"km","st.-kitts-&-nevis":"kn","north-korea":"kp","south-korea":"kr",kuwait:"kw","cayman-islands":"ky",kazakhstan:"kz",laos:"la",lebanon:"lb","st.-lucia":"lc",liechtenstein:"li","sri-lanka":"lk",liberia:"lr",lesotho:"ls",lithuania:"lt",luxembourg:"lu",latvia:"lv",libya:"ly",morocco:"ma",monaco:"mc",moldova:"md",montenegro:"me","st.-martin":"mf",madagascar:"mg","marshall-islands":"mh","north-macedonia":"mk",mali:"ml","myanmar-(burma)":"mm",myanmar:"mm",mongolia:"mn","macao-sar-china":"mo",macao:"mo","northern-mariana-islands":"mp",martinique:"mq",mauritania:"mr",montserrat:"ms",malta:"mt",mauritius:"mu",maldives:"mv",malawi:"mw",mexico:"mx",malaysia:"my",mozambique:"mz",namibia:"na","new-caledonia":"nc",niger:"ne","norfolk-island":"nf",nigeria:"ng",nicaragua:"ni",netherlands:"nl",norway:"no",nepal:"np",nauru:"nr",niue:"nu","new-zealand":"nz",oman:"om",panama:"pa",peru:"pe","french-polynesia":"pf","papua-new-guinea":"pg",philippines:"ph",pakistan:"pk",poland:"pl","st.-pierre-&-miquelon":"pm","pitcairn-islands":"pn","puerto-rico":"pr","palestinian-territories":"ps",palestine:"ps",portugal:"pt",palau:"pw",paraguay:"py",qatar:"qa","outlying-oceania":"qo",réunion:"re",romania:"ro",serbia:"rs",russia:"ru",rwanda:"rw","saudi-arabia":"sa","solomon-islands":"sb",seychelles:"sc",sudan:"sd",sweden:"se",singapore:"sg","st.-helena":"sh",slovenia:"si","svalbard-&-jan-mayen":"sj",slovakia:"sk","sierra-leone":"sl","san-marino":"sm",senegal:"sn",somalia:"so",suriname:"sr","south-sudan":"ss","são-tomé-&-príncipe":"st","el-salvador":"sv","sint-maarten":"sx",syria:"sy",eswatini:"sz",swaziland:"sz","tristan-da-cunha":"ta","turks-&-caicos-islands":"tc",chad:"td","french-southern-territories":"tf",togo:"tg",thailand:"th",tajikistan:"tj",tokelau:"tk","timor-leste":"tl","east-timor":"tl",turkmenistan:"tm",tunisia:"tn",tonga:"to",turkey:"tr","trinidad-&-tobago":"tt",tuvalu:"tv",taiwan:"tw",tanzania:"tz",ukraine:"ua",uganda:"ug","u.s.-outlying-islands":"um","united-nations":"un",un:"un","united-states":"us",us:"us",uruguay:"uy",uzbekistan:"uz","vatican-city":"va","st.-vincent-&-grenadines":"vc",venezuela:"ve","british-virgin-islands":"vg","u.s.-virgin-islands":"vi",vietnam:"vn",vanuatu:"vu","wallis-&-futuna":"wf",samoa:"ws","pseudo-accents":"xa","pseudo-bidi":"xb",kosovo:"xk",yemen:"ye",mayotte:"yt","south-africa":"za",zambia:"zm",zimbabwe:"zw","unknown-region":"zz"},localeDisplayNames:{}},cldr=_cldr,{countryMap}=cldr,{languageMap}=cldr,{validLocales}=cldr;function encodeTermBit(e){const t=e.replaceAll(/["'`~]/g,(e=>`\\${e}`));return t.includes(" ")?`"${t}"`:t}function searchTermToString(e){let t="";return e.invert&&(t+="~"),"category"===e.type&&(t+=`${encodeTermBit(e.category)}:`),e.regexp?t+=`/${e.value}/`:t+=encodeTermBit(e.value),t}function isAddressSearchTerm(e){return"string"==typeof e?">"===e[0]:"address"===e[0]?.type}function parseSearchTerms2(e){if(isAddressSearchTerm(e))return[{type:"address",source:e,value:e.substring(1).trim(),offset:0,invert:!1,regexp:!1}];if(1===e.length)return[];const t=[];let r=0,n=!1,a="",u=!1,s=emptyTerm(r);function o(e=r){if(s.source.trim()){if(s.regexp)try{new RegExp(s.value,"i")}catch(e){s.regexp=!1,s.value=quoteRe(s.value)}if("name"===s.type){if(1===s.source.length)return void(s=emptyTerm(e));if(2===s.value.length&&validLocales.has(s.value))s.type="locale";else{const e=s.value.toLowerCase();countryMap[e]?s.matchLocale={at:"end",with:`-${countryMap[e]}`}:languageMap[e]&&(s.matchLocale={at:"start",with:`${languageMap[e]}-`})}}t.push(s),s=emptyTerm(e)}else s=emptyTerm(e)}for(;r<e.length;){const t=e[r],i=s.source.length,c=s.value.length;if(r++,u)u=!1,s.value+=t,s.source+=t;else if("\\"!==t)if(n){if(s.source+=t,t===a){n=!1,a="",(s.regexp||"category"===s.type)&&o();continue}s.value+=t}else isEmptyChar(t)?o():isQuotationChar(t)?(c>0&&o(r-1),s.source+=t,"/"===t&&(s.regexp=!0),n=!0,a=t):(s.source+=t,"~"!==t||0!==i?":"===t&&c>=2?(s.type="category",s.category=s.value,s.value=""):s.value+=t:s.invert=!0);else u=!0,s.source+=t,s.regexp&&(s.value+=t)}return o(),t}globalThis.__countryMap=countryMap,globalThis.__languageMap=languageMap,globalThis.__validLocales=validLocales;const quotationChar={'"':!0,"'":!0,"`":!0,"/":!0};function isQuotationChar(e){return quotationChar[e]||!1}function isEmptyChar(e){return!e.trim()}function emptyTerm(e){return{type:"name",invert:!1,regexp:!1,offset:e,source:"",value:""}}function quoteRe(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}try{window.__parseSearchTerms=parseSearchTerms2}catch(e){}const EOL_LINK="aka.cfx.re/eol",EOS_LINK="aka.cfx.re/eos",DEFAULT_SERVER_PORT_INT=30120,DEFAULT_SERVER_PORT=DEFAULT_SERVER_PORT_INT.toString(10),serverUtils_DEFAULT_SERVER_LOCALE="root-AQ",serverUtils_DEFAULT_SERVER_LOCALE_COUNTRY="AQ",ere=`(?:${emoji_regex().source})`,emojiPreRe=new RegExp(`^${ere}`,""),SPLIT_RE=new RegExp(`((?<!\\.(?:[a-zA-Z]{2,6}))\\s?\\/+\\s?|\\||\\s[-~:x×☆ᆞ]+\\s|\\s[Il]\\s|(?:[\\s⠀ㅤ¦[]|${ere})+(?![#0-9])\\p{Emoji}|(?<=(?!^)(?![#0-9])\\p{Emoji}).+|[・·•│]|(?<=(?:\\]|\\}))[-\\s]|ㅤ|kush|(?<=[】⏌」』]).)`,"u"),COMMA_SPLIT_RE=/(?:(?<!(?:\d+|Q))\+|,\s*|\.\s+)/u;function filterSplit(e){const t=e.split(SPLIT_RE).map((e=>e.trim())).filter((e=>""!==e));return t.length>0?t[0]:""}function filterCommas(e){const t=e.split(COMMA_SPLIT_RE).map((e=>e.trim())).filter((e=>""!==e));return t.slice(0,3).join(", ")}function equalReplace(e,...t){let r,n=e;do{r=n;for(const e of t)n=n.replace(e[0],e[1])}while(n!==r);return n}const COUNTRY_PREFIX_RE=/^[[{(][a-zA-Z]{2,}(?:\/...?)*(?:\s.+?)?[\]})]/,projectNameReplaces=[[/^[\sㅤ]+/,""],[/(?<=(?!(\d|#))\p{Emoji})(?!(\d|#))\p{Emoji}/u,""],[/^\p{So}/u,""],[/(\s|\u2800)+/gu," "],[/(?:[0-9]+\+|\+[0-9]+)\s*FPS/g,"+"],[/\^[0-9]/,""],[/[\])]\s*[[(].*$/,"]"],[/,.*$/,""],[COUNTRY_PREFIX_RE,""],[emojiPreRe,""]],projectNamesReplacesExtra=[[/[\p{Pe}】]/gu,""],[/(?<!\d)[\p{Ps}【]/gu,""]];function filterServerProjectName(e){let t=e;if(!t)return"";t.length>=50&&(t=t.substring(0,50));let r="";const n=filterSplit(equalReplace(equalReplace(t,[/^\^[0-9]/,e=>(r=e,"")],...projectNameReplaces),...projectNamesReplacesExtra));return r+n.normalize("NFKD")}function filterServerProjectDesc(e){let t=e;return t?(t.length>=125&&(t=t.substring(0,125)),filterCommas(filterSplit(equalReplace(t,[/\^[0-9]/g,""],[/^[\sㅤ]+/,""],[COUNTRY_PREFIX_RE,""],[emojiPreRe,""]))).replace(/(\s|\u2800)+/gu," ").normalize("NFKD")):""}function normalizeSearchString(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function filterServerTag(e){return!!e&&"default"!==e}function shouldPrioritizePinnedServers(e){return!!e.prioritizePinned||(e.type===ServersListType.All?!isAddressSearchTerm(e.searchTextParsed)&&Boolean(e.searchText):e.type===ServersListType.Supporters)}function getListServerTags(e,t){if(!e.tags)return[];if(!t)return[];const r=t.tag.items,n=[];for(const t of e.tags){const e=r[t];e&&(e.count<8||n.push(t))}return n.sort(((e,t)=>r[t].count-r[e].count)).slice(0,4)}function getPinnedServersList(e,t){return e?e.pinnedServers.filter((e=>t(e))).sort(((e,r)=>(t(r)?.playersCurrent||0)-(t(e)?.playersCurrent||0))):[]}function isServerEOL(e){return!((new Date).getTime()<16224984e5||"unknown"!==e.supportStatus&&"end_of_life"!==e.supportStatus)}function isServerEOS(e){return"end_of_support"===e.supportStatus||!isServerEOL(e)&&"end_of_life"===e.supportStatus}const NON_DISPLAY_SERVER_RESOURCE_NAMES=new Set(["_cfx_internal","hardcap","sessionmanager"]);function shouldDisplayServerResource(e){return!NON_DISPLAY_SERVER_RESOURCE_NAMES.has(e)}const SERVER_PRIVATE_CONNECT_ENDPOINT="https://private-placeholder.cfx.re/";function hasPrivateConnectEndpoint(e){return!!e&&!notPrivateConnectEndpoint(e[0])}function notPrivateConnectEndpoint(e){return e!==SERVER_PRIVATE_CONNECT_ENDPOINT}function getConnectEndpoits(e){const t={};if(e.historicalAddress&&(t.manual=e.historicalAddress),e.connectEndPoints){const r=e.connectEndPoints.filter(notPrivateConnectEndpoint);r.length&&(t.provided=r)}return t}function hasConnectEndpoints(e){const t=getConnectEndpoits(e);return Boolean(t.manual||t.provided)}class AutocompleteIndexer{constructor(){this.tags={},this.locales={}}add(e){if(e.locale!==serverUtils_DEFAULT_SERVER_LOCALE&&(this.locales[e.locale]=(this.locales[e.locale]||0)+1),e.tags)for(const t of e.tags){const e=t.toLowerCase();this.tags[e]=(this.tags[e]||0)+1}}reset(){this.tags={},this.locales={}}getIndex(){const e=enlist(this.tags).slice(0,50),t=enlist(this.locales);return{tag:{sequence:e,items:e.reduce(((e,t)=>(e[t]={count:this.tags[t]||0},e)),{})},locale:{sequence:t,items:t.reduce(((e,t)=>(e[t]={count:this.locales[t]||0,locale:t,country:t.split("-")[1]||t},e)),{})}}}}function enlist(e){return Object.entries(e).filter((([,e])=>e>1)).sort((([,e],[,t])=>t-e)).map((([e])=>e))}function t(){}function r(){return!0}function e(){return!1}function u(e){return e}function o(e){e()}function arrayAt(e,t){return t<0?e[e.length+t]:e[t]}function uniqueArray(e){return[...new Set(e)]}function reverseArray(e){return e.slice().reverse()}function arrayAll(e,t){return e.reduce(((e,r)=>e&&t(r)),!0)}function arraySome(e,t){return e.some(t)}function randomArrayItem(e){return e[Math.floor(Math.random()*e.length)]}function filterList(e,t,r){const n=[];return compileTagsFilters(n,r),compileLocaleFilters(n,r),compileEmptyFullFilters(n,r),compileOnlyPremiumFilters(n,r),compileSearchTermsFilters(n,r),t.filter((t=>{const r=e[t];for(const e of n)if(!e(r))return!1;return!0}))}function compileLocaleFilters(e,t){const n=Object.entries(t.locales),a=n.filter((([,e])=>e)),u=n.filter((([,e])=>!e)),s=a.length?e=>arraySome(a,(([t])=>e.locale===t)):r,o=u.length?e=>arrayAll(u,(([t])=>e.locale!==t)):r;n.length&&e.push((e=>s(e)&&o(e)))}function compileTagsFilters(e,t){const r=Object.entries(t.tags);r.length&&e.push((e=>arrayAll(r,(([t,r])=>r===Boolean(e.tagsMap[t])))))}function compileOnlyPremiumFilters(e,t){t.onlyPremium&&e.push((e=>Boolean(e.premium)))}function compileEmptyFullFilters(e,t){const{hideFull:r,hideEmpty:n}=t;r&&e.push((e=>!e.isFull)),n&&e.push((e=>!e.isEmpty))}function compileTermValueMatcher(e){if(!e.regexp){const t=normalizeSearchString(e.value.toLowerCase());return e=>e.toLowerCase().includes(t)}const t=new RegExp(e.value,"i");return e=>t.test(e)}function compileSearchTermsFilters(e,t){const{searchTextParsed:n}=t;if(n.length)for(const t of n){const{type:n,value:a,invert:u}=t,s=compileTermValueMatcher(t);let o;switch(n){case"name":{let e=r;if(t.matchLocale){const r=t.matchLocale.with;switch(t.matchLocale.at){case"start":e=e=>e.locale.startsWith(r);break;case"end":e=e=>e.locale.endsWith(r)}}o=t=>e(t)&&s(t.searchableName);break}case"locale":o=e=>{const t=e.locale.indexOf(a),r=0===t,n=t===e.locale.length-2;return r||n};break;case"category":{const{category:e}=t;o=t=>{const r=t.categories[e]||t.categories[`${e}s`];if(!r)return!1;switch(r.type){case"string":return s(r.against);case"array":return r.against.some((e=>s(e)));default:return!1}};break}}o&&e.push(u?e=>!o(e):o)}}function sortList(e,t,r){const{sortBy:n,sortDir:a}=r,u=n===ServersListSortBy.Name?sortByProperty.bind(null,e,"sortableName",a):sortByProperty.bind(null,e,"sortableName",ServerListSortDir.Asc),s=n===ServersListSortBy.Boosts?sortByProperty.bind(null,e,"upvotePower",a):sortByProperty.bind(null,e,"upvotePower",ServerListSortDir.Desc),o=n===ServersListSortBy.Players?sortByProperty.bind(null,e,"players",a):sortByProperty.bind(null,e,"players",ServerListSortDir.Desc),i=[];switch(t&&shouldPrioritizePinnedServers(r)&&i.push(sortByPinConfig.bind(null,t)),n){case ServersListSortBy.Name:i.push(u,s,o);break;case ServersListSortBy.Players:i.push(o,s,u);break;case ServersListSortBy.Boosts:default:i.push(s,o,u)}return Object.keys(e).sort(((e,t)=>{for(const r of i){const n=r(e,t);if(0!==n)return n}return e<t?-1:e>t?1:0}))}function sortByPinConfig(e,t,r){const n=e.pinnedServers.includes(t),a=e.pinnedServers.includes(r);return n===a?0:n&&!a?-1:1}function sortByProperty(e,t,r,n,a){const u=e[n][t]||0,s=e[a][t]||0;return u===s?0:u>s?r:-r}const queueMicrotask=(globalThis||window).queueMicrotask||(e=>Promise.resolve().then(e));function timeout(e){return new Promise((t=>{setTimeout(t,e)}))}function idleCallback(e){return new Promise((t=>{requestIdleCallback(t,{timeout:e})}))}function animationFrame(){return new Promise((e=>{requestAnimationFrame(e)}))}function resolveOrTimeout(e,t,r){return Promise.race([timeout(e).then((()=>{throw new Error(t)})),r])}class Deferred{constructor(){this.promise=new Promise(((e,t)=>{this.resolve=e,this.reject=t}))}}class OnlyLatest{get nextRunningIndex(){return++this.runningIndex}constructor(e,t,r){this.runner=e,this.callback=t,this.delay=r,this.runningIndex=0,this.args=null,this.disposed=!1,this.delayTimeout=null,this.delayedRunPending=!1,this.delayedRunner=(...e)=>{this.args=e,this.delayedRunPending||(this.delayedRunPending=!0,this.delayTimeout=setTimeout((()=>{this.disposed||(this.delayedRunPending=!1,this.delayTimeout=null,this.doRun())}),this.delay))},this.normalRunner=(...e)=>{this.args=e,this.doRun()},this.doRun=async()=>{const e=this.nextRunningIndex,t=this.args;this.args=null;const r=await this.runner(...t);this.disposed||e===this.runningIndex&&this.callback(r)},this.delay?this.run=this.delayedRunner:this.run=this.normalRunner}dispose(){this.disposed=!0,null!==this.delayTimeout&&clearTimeout(this.delayTimeout)}}async function retry(e,t){let r=0;for(;r++<=e;){const n=r===e;try{return await t()}catch(e){if(n)throw e}}}const originalFetch=fetch;var fetcher_fetcher,types_ServerViewDetailsLevel,ServerPureLevel;!function(e){class t extends Error{static is(e){return e instanceof t}constructor(e){super(`Request to ${e.url} failed with status code ${e.status}`),this.response=e,this.cfRay=null,this.status=e.status,this.statusText=e.statusText,this.cfRay=e.headers.get("cf-ray")}async readJsonBody(){if(this.response.bodyUsed)return null;try{return await this.response.json()}catch(e){return null}}}e.HttpError=t;class r extends Error{static is(e){return e instanceof r}constructor(e,t){super(`Invalid json "${e}", ${t.message}`),this.originalString=e,this.stack=t.stack}}async function n(...e){const r=await originalFetch(...e);if(!r.ok)throw new t(r);return r}async function a(...e){return(await n(...e)).arrayBuffer()}e.JsonParseError=r,e.fetch=n,e.json=async function(...e){const t=await n(...e);try{return await t.json()}catch(e){throw new r(t.bodyUsed?"BODY UNAVAILABLE":await t.text(),e)}},e.text=async function(...e){return(await n(...e)).text()},e.arrayBuffer=a,e.typedArray=async function(e,...t){return new e(await a(...t))}}(fetcher_fetcher||(fetcher_fetcher={}));class FrameReader{constructor(e,t,r){this.stream=e,this.onFrame=t,this.onEnd=r,this.reader=this.stream.getReader(),this.lastArray=null,this.frameLength=-1,this.framePos=0}read(){this.doRead()}async doRead(){const{done:e,value:t}=await this.reader.read();if(e||!t)return void this.onEnd();let r=t;for(;r.length>0;){const e=4;if(this.lastArray){const e=new Uint8Array(r.length+this.lastArray.length);e.set(this.lastArray),e.set(r,this.lastArray.length),this.lastArray=null,r=e}if(this.frameLength<0){if(r.length<4)return this.lastArray=r,void this.doRead();if(this.frameLength=r[0]|r[1]<<8|r[2]<<16|r[3]<<24,this.frameLength>65535)throw new Error("A too large frame was passed.")}const t=4+this.frameLength-this.framePos;if(r.length<t)return this.lastArray=r,void this.doRead();const n=softSlice(r,e,t);if(this.framePos+=t-e,this.framePos===this.frameLength&&(this.frameLength=-1,this.framePos=0),this.onFrame(n),!(r.length>t))return void this.doRead();r=softSlice(r,t)}}}function softSlice(e,t,r){return new Uint8Array(e.buffer,e.byteOffset+t,r&&r-t)}function p(e,t){const r=e.charCodeAt(t);let n;return r>=55296&&r<=56319&&e.length>t+1&&(n=e.charCodeAt(t+1),n>=56320&&n<=57343)?e.substring(t,t+2):e[t]}function A(e,t){return e[t]}function b(e,t){return e-t}function C(e,t,r=!1){const n=r?p:A,a=t.filter(((t,r,n)=>n.lastIndexOf(t)===r&&t<e.length&&t>0)).sort(b);if(0===a.length)return new Map([[0,e]]);const u=new Map;let s="",o=0,i=0,c=0,l=0;for(;i<e.length;){const t=n(e,i);s+=t,i+=t.length,o+=1,a[c]===o&&(u.set(l,s),s="",l=a[c],c+=1)}return s&&u.set(l,s),u}function I(e,t,r,n){return`${e.substring(0,r)}${t}${e.substring(n)}`}function S(e){return e.replace(/\\/g,"/")}function d(e){return!!e||"true"===e||"1"===e}function w(e){return!e||"false"===e||"0"===e}!function(e){e[e.Address=0]="Address",e[e.Historical=50]="Historical",e[e.Live=100]="Live",e[e.DynamicDataJson=101]="DynamicDataJson",e[e.InfoAndDynamicDataJson=199]="InfoAndDynamicDataJson",e[e.MasterList=201]="MasterList",e[e.MasterListFull=299]="MasterListFull"}(types_ServerViewDetailsLevel||(types_ServerViewDetailsLevel={})),function(e){e.None="0",e.AudioAndGraphicsAllowed="1",e.NoModsAllowed="2"}(ServerPureLevel||(ServerPureLevel={}));const convarsToHide=new Set(["mapname","onesync","gametype"]);function serverAddress2ServerView(e){const t=`⚠️ Server is loading or failed to load (${e}) ⚠️`;return{id:e,detailsLevel:types_ServerViewDetailsLevel.Address,hostname:t,locale:serverUtils_DEFAULT_SERVER_LOCALE,localeCountry:serverUtils_DEFAULT_SERVER_LOCALE_COUNTRY,projectName:t,rawVariables:{}}}function masterListServerData2ServerView(e,t){const r=Object.assign(serverAddress2ServerView(e),{joinId:e,detailsLevel:types_ServerViewDetailsLevel.MasterList,enforceGameBuild:t.vars?.sv_enforceGameBuild,gametype:t.gametype,mapname:t.mapname,server:t.server,hostname:t.hostname||"",playersMax:t.svMaxclients||0,playersCurrent:t.clients||0,burstPower:t.burstPower||0,upvotePower:t.upvotePower||0,connectEndPoints:t.connectEndPoints,private:hasPrivateConnectEndpoint(t.connectEndPoints),rawVariables:t.vars||{}},processServerDataVariables(t.vars));return Object.prototype.hasOwnProperty.call(t,"iconVersion")&&(r.iconVersion=t.iconVersion),r.projectName||(r.upvotePower=0),r}function transformers_masterListFullServerData2ServerView(e,t){const r=Object.assign(serverAddress2ServerView(e),{joinId:e,detailsLevel:types_ServerViewDetailsLevel.MasterListFull,enforceGameBuild:t.vars?.sv_enforceGameBuild,gametype:t.gametype,mapname:t.mapname,server:t.server,hostname:t.hostname||"",playersMax:t.svMaxclients||0,playersCurrent:t.clients||0,burstPower:t.burstPower||0,upvotePower:t.upvotePower||0,connectEndPoints:t.connectEndPoints,private:t.private||hasPrivateConnectEndpoint(t.connectEndPoints),ownerID:t.ownerID,ownerName:t.ownerName,ownerAvatar:t.ownerAvatar,ownerProfile:t.ownerProfile,supportStatus:t.support_status||"supported",resources:t.resources,players:t.players,rawVariables:t.vars||{}},processServerDataVariables(t.vars));return Object.prototype.hasOwnProperty.call(t,"iconVersion")&&(r.iconVersion=t.iconVersion),r.projectName||(r.upvotePower=0),t.fallback&&(r.offline=!0),r}function historyServer2ServerView(e){const t={id:e.address,detailsLevel:ServerViewDetailsLevel.Historical,locale:DEFAULT_SERVER_LOCALE,localeCountry:DEFAULT_SERVER_LOCALE_COUNTRY,hostname:e.hostname,projectName:e.hostname,rawVariables:e.vars,historicalIconURL:e.rawIcon};return Object.assign(t,processServerDataVariables(e.vars))}function serverView2ListableServerView(e){const t=e.playersCurrent||0,r=e.playersMax||0,n=getSearchableName(e),a=getSortableName(n);return{id:e.id,ping:0,players:t,isFull:t>=r,isEmpty:0===t,locale:e.locale,tags:e.tags||[],tagsMap:e.tags?e.tags.reduce(((e,t)=>(e[t]=!0,e)),{}):{},variables:e.variables||{},searchableName:n,sortableName:a,premium:e.premium||"",upvotePower:e.upvotePower||0,categories:createCategoryMatchers(e)}}function getSearchableName(e){return normalizeSearchString((e.projectDescription?`${e.projectName} ${e.projectDescription}`:e.projectName).replace(/\^[0-9]/g,""))}function getSortableName(e){return e.replace(/[^a-zA-Z0-9]/g,"").replace(/^[0-9]+/g,"").toLowerCase()}function shouldVarBeShown(e){return!convarsToHide.has(e)&&!e.startsWith("sv_")}function processServerDataVariables(e){const t={projectName:""};if(!e)return t;t.variables={};for(const[r,n]of Object.entries(e)){const e=r.toLowerCase();switch(!0){case"sv_projectName"===r:t.projectName=filterServerProjectName(n);continue;case"sv_projectDesc"===r:t.projectDescription=filterServerProjectDesc(n);continue;case"sv_licenseKeyToken"===r:t.licenseKeyToken=n;continue;case"sv_scriptHookAllowed"===r:t.scriptHookAllowed="true"===n;continue;case"gamename"===r:t.gamename=n;continue;case"activitypubFeed"===r:t.activitypubFeed=n;continue;case"premium"===r:t.premium=n;continue;case"locale"===r:t.locale=getCanonicalLocale(n),t.localeCountry=arrayAt(t.locale.split("-"),-1)||"??";continue;case"tags"===r:t.tags=[...new Set(n.split(",").map((e=>e.trim().toLowerCase())).filter(filterServerTag))];continue;case"banner_connecting"===r:t.bannerConnecting=n;continue;case"banner_detail"===r:t.bannerDetail=n;continue;case"can_review"===r:t.canReview=Boolean(n);continue;case"onesync_enabled"===r:t.onesyncEnabled="true"===n;continue;case"sv_enforceGameBuild"===r:n&&(t.enforceGameBuild=n);continue;case"sv_pureLevel"===r:t.pureLevel=n;continue;case!shouldVarBeShown(r):case e.includes("banner_"):case e.includes("sv_project"):case e.includes("version"):case e.includes("uuid"):continue}t.variables[r]=n}return t}function createCategoryMatchers(e){const{id:t,tags:r,locale:n,gamename:a,gametype:u,mapname:s,hostname:o,enforceGameBuild:i,pureLevel:c,rawVariables:l}=e,D={address:createStringMatcher(t)};if(n&&(D.locale=createStringMatcher(n)),o&&(D.hostname=createStringMatcher(o)),a&&(D.gamename=createStringMatcher(a)),u&&(D.gametype=createStringMatcher(u)),s&&(D.mapname=createStringMatcher(s)),i&&(D.gamebuild=createStringMatcher(i)),c&&(D.purelevel=createStringMatcher(c)),r&&r.length&&(D.tag=createArrayMatcher(r)),l){const e=Object.entries(l).filter((([,e])=>!w(e))).map((([e])=>e));e.length&&(D.var=createArrayMatcher(e));for(const[e,t]of Object.entries(l))D[e]||(D[e]=createStringMatcher(t))}return D}function createStringMatcher(e){return{type:"string",against:e}}function createArrayMatcher(e){return{type:"array",against:e}}function getCanonicalLocale(e){try{return Intl.getCanonicalLocales(e.replace(/_/g,"-"))[0]}catch{return serverUtils_DEFAULT_SERVER_LOCALE}}var master=__webpack_require__(8883);function decodeServer(e){return master.master.Server.decode(e)}const BASE_URL="https://servers-frontend.fivem.net/api/servers",ALL_SERVERS_URL=`${BASE_URL}/streamRedir/`,SINGLE_SERVER_URL=`${BASE_URL}/single/`,TOP_SERVER_URL=null;async function readBodyToServers(e,t,r){const n=new Deferred;let a=0,u=0,s=0;new FrameReader(r,(r=>{let n=performance.now();const o=decodeServer(r);if(a+=performance.now()-n,o.EndPoint&&o.Data){const r=o.Data?.vars?.gamename||game_GameName.FiveM;if(e===r){n=performance.now();const e=masterListServerData2ServerView(o.EndPoint,o.Data);u+=performance.now()-n,n=performance.now(),t(e),s+=performance.now()-n}}a+=performance.now()-n}),n.resolve).read(),await n.promise,console.log("Times: decode",a,"ms, transform",u,"ms, onServer",s,"ms")}async function getAllMasterListServers(e,t){console.time("Total getAllServers");const{body:r}=await fetcher_fetcher.fetch(new Request(ALL_SERVERS_URL));if(!r)throw console.timeEnd("Total getAllServers"),new Error("Empty body of all servers stream");await readBodyToServers(e,t,r),console.timeEnd("Total getAllServers")}async function getMasterListServer(e,t){try{const r=await fetcher_fetcher.json(SINGLE_SERVER_URL+t);return r.EndPoint&&r.Data&&e===(r.Data?.vars?.gamename||game_GameName.FiveM)?transformers_masterListFullServerData2ServerView(r.EndPoint,r.Data):null}catch(e){return null}}async function getTopServer(e){try{const t=(await fetcher.json(TOP_SERVER_URL+e.language)).Data;if(t.EndPoint&&t.Data){const r=t.Data?.vars?.gamename||GameName.FiveM;return e.gameName&&r!==e.gameName?null:masterListFullServerData2ServerView(t.EndPoint,t.Data)}return null}catch(e){return console.error(e),null}}try{window.__getSingleServer=getMasterListServer}catch(e){}function postMessageToMainThread(e,t){try{postMessage({type:e,data:t})}catch(r){console.warn(r,e,t)}}const ServerResponses=defineEvents(postMessageToMainThread).add().add().add().add().add().add();class ServersWorker{constructor(){this.fetching=!0,this.serversIndex=new AutocompleteIndexer,this.listableServersMap={},this.sortedLists={},this.listsRegistry={},this.gameName=game_GameName.FiveM,this.serversChunkSize=500,this.pinnedServersConfig=null,onmessage=e=>{const{type:t,data:r}=e.data;"function"==typeof this[t]&&this[t](...r)}}async init(e){this.gameName=e.gameName,this.serversChunkSize=e.serversChunkSize,this.refresh()}setPinnedServersConfig(e){this.pinnedServersConfig=e}applyListConfig(e){const t=e.type;this.listsRegistry[t]?(this.listsRegistry[t].config=e,this.listsRegistry[t].id++):this.listsRegistry[t]={id:0,config:e},this.fetching||this.filterAndSendList(e.type)}async refresh(){this.fetching=!0,ServerResponses.send("allServersBegin");let e=[];const t=()=>{0!==e.length&&(ServerResponses.send("allServersChunk",e),e=[],this.reapplyListConfigs())},r=setInterval(t,500);try{await getAllMasterListServers(this.gameName,(r=>{this.serversIndex.add(r),this.listableServersMap[r.id]=serverView2ListableServerView(r),e.push(r),e.length===this.serversChunkSize&&t()})),clearInterval(r),ServerResponses.send("allServersEnd",e),ServerResponses.send("index",this.serversIndex.getIndex()),this.reapplyListConfigs()}catch(e){console.error(e),ServerResponses.send("allServersError",e.message)}finally{this.fetching=!1}}reapplyListConfigs(){for(const{config:e}of Object.values(this.listsRegistry))e&&(this.createdSortedList(e),this.filterAndSendList(e.type))}filterAndSendList(e){const t=this.listsRegistry[e];t&&t.config&&this.sendList(t.config,t.id,filterList(this.listableServersMap,this.getSortedList(t.config),t.config))}getSortedList(e){const t=getSortedListHash(e);let r=this.sortedLists[t];return r||(r=this.createdSortedList(e)),r}createdSortedList(e){const t=getSortedListHash(e);return this.sortedLists[t]=sortList(this.listableServersMap,this.pinnedServersConfig,e),this.sortedLists[t]}async sendList(e,t,r){await Promise.resolve(),this.listsRegistry[e.type].id===t?ServerResponses.send("list",[e.type,r]):console.log("Dropping list as not most recent",e.type,t,this.listsRegistry[e.type])}}function defineEvents(e){const t={send:e,add:()=>t};return t}function getSortedListHash(e){let t=`${e.sortBy}:${e.sortDir}`;return shouldPrioritizePinnedServers(e)&&(t+=":pinsOnTop"),t}new ServersWorker})();
//# sourceMappingURL=src_cfx_common_services_servers_source_WorkerSource_worker_ts.chunk.js.map