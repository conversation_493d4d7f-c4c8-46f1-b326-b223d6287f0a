const _i = Citizen.pointerValueInt();
const _f = Citizen.pointerValueFloat();
const _v = Citizen.pointerValueVector();
const _r = Citizen.returnResultAnyway();
const _ri = Citizen.resultAsInteger();
const _rf = Citizen.resultAsFloat();
const _rl = Citizen.resultAsLong();
const _s = Citizen.resultAsString();
const _rv = Citizen.resultAsVector();
const _ro = Citizen.resultAsObject2();
const _in = Citizen.invokeNativeByHash;
const _ii = Citizen.pointerValueIntInitialized;
const _fi = Citizen.pointerValueFloatInitialized;
function _ch(hash) {
	if (typeof hash === 'string') {
		return global.GetHashKey(hash);
	}

	return hash;
}

function _obj(obj) {
	const s = msgpack_pack(obj);
	return [s, s.length];
}

function _ts(num) {
	if (num === 0 || num === null || num === undefined || num === false) { // workaround for users calling string parameters with '0', also nil being translated
		return null;
	}
	if (ArrayBuffer.isView(num) || num instanceof ArrayBuffer) { // these are handled as strings internally
		return num;
	}
	return num.toString();
}
function _fv(flt) {
	return (flt === 0.0) ? flt : (flt + 0.0000001);
}

function _mfr(fn) {
	return Citizen.getRefFunction(fn);
}

const _ENV = null;

global.AbortAllGarageActivity = function () {
	return _in(0x00000000, 0x5db95843);
};

global.AbortScriptedConversation = function (abort) {
	return _in(0x00000000, 0x57db70ce, abort, _r, _ri);
};

global.Absf = function (value) {
	return _in(0x00000000, 0x067640f3, _fv(value), _r, _rf);
};

global.Absi = function (value) {
	return _in(0x00000000, 0x75a648b7, _fv(value), _r, _rf);
};

global.Acos = function (Unk496) {
	return _in(0x00000000, 0x2e746e53, _fv(Unk496), _r, _rf);
};

global.ActivateCheat = function (cheat) {
	return _in(0x00000000, 0x69e742fc, cheat);
};

global.ActivateDamageTrackerOnNetworkId = function (Unk897, Unk898) {
	return _in(0x00000000, 0x01181ca3, Unk897, Unk898);
};

global.ActivateFrontend = function () {
	return _in(0x00000000, 0x3c64626f);
};

global.ActivateHeliSpeedCheat = function (heli, cheat) {
	return _in(0x00000000, 0x033b52ca, heli, cheat);
};

global.ActivateInterior = function (interior, unknownTrue) {
	return _in(0x00000000, 0x66dd4f67, interior, unknownTrue);
};

global.ActivateMenuItem = function (menuid, item, activate) {
	return _in(0x00000000, 0x608237a4, menuid, item, activate);
};

global.ActivateNetworkSettingsMenu = function () {
	return _in(0x00000000, 0x609d0203);
};

global.ActivateReplayMenu = function () {
	return _in(0x00000000, 0x61040b08);
};

global.ActivateSaveMenu = function () {
	return _in(0x00000000, 0x78ac735f);
};

global.ActivateScriptPopulationZone = function () {
	return _in(0x00000000, 0x03f90052);
};

global.ActivateScriptedCams = function (Unk538, Unk539) {
	return _in(0x00000000, 0x3ebe11b9, Unk538, Unk539);
};

/**
 * Activates built-in timecycle editing tool.
 */
global.ActivateTimecycleEditor = function () {
	return _in(0x00000000, 0xeeb9b76a);
};

global.ActivateViewport = function (viewportid, activate) {
	return _in(0x00000000, 0x4d7d105a, viewportid, activate);
};

global.AddAdditionalPopulationModel = function (model) {
	return _in(0x00000000, 0x7ede120f, model);
};

global.AddAmmoToChar = function (ped, weapon, amount) {
	return _in(0x00000000, 0x1ada0c3a, ped, weapon, amount);
};

global.AddArmourToChar = function (ped, amount) {
	return _in(0x00000000, 0x1c623537, ped, amount);
};

/**
 * Adds an output for the specified audio submix.
 * @param submixId The input submix.
 * @param outputSubmixId The output submix. Use `0` for the master game submix.
 */
global.AddAudioSubmixOutput = function (submixId, outputSubmixId) {
	return _in(0x00000000, 0xac6e290d, submixId, outputSubmixId);
};

global.AddBlipForCar = function (vehicle, pBlip) {
	return _in(0x00000000, 0x6d21564d, vehicle, _ii(pBlip) /* may be optional */);
};

global.AddBlipForChar = function (ped, pBlip) {
	return _in(0x00000000, 0x19a64c5d, ped, _ii(pBlip) /* may be optional */);
};

global.AddBlipForContact = function (x, y, z, pBlip) {
	return _in(0x00000000, 0x7c671162, _fv(x), _fv(y), _fv(z), _ii(pBlip) /* may be optional */);
};

global.AddBlipForCoord = function (x, y, z, pBlip) {
	return _in(0x00000000, 0x3e7d3074, _fv(x), _fv(y), _fv(z), _ii(pBlip) /* may be optional */);
};

global.AddBlipForGangTerritory = function (x0, y0, x1, y1, colour, blip) {
	return _in(0x00000000, 0x2c1b52ce, _fv(x0), _fv(y0), _fv(x1), _fv(y1), colour, _ii(blip) /* may be optional */);
};

global.AddBlipForObject = function (obj, pBlip) {
	return _in(0x00000000, 0x70cc1487, obj, _ii(pBlip) /* may be optional */);
};

global.AddBlipForPickup = function (pickup, pBlip) {
	return _in(0x00000000, 0x04f567fb, pickup, _ii(pBlip) /* may be optional */);
};

global.AddBlipForRadius = function (x, y, z, type, blip) {
	return _in(0x00000000, 0x21804d1a, _fv(x), _fv(y), _fv(z), type, _ii(blip) /* may be optional */);
};

global.AddBlipForWeapon = function (x, y, z, blip) {
	return _in(0x00000000, 0x4ca708b9, _fv(x), _fv(y), _fv(z), _ii(blip) /* may be optional */);
};

global.AddCamSplineNode = function (cam, camnode) {
	return _in(0x00000000, 0x3b4f1eba, cam, camnode);
};

global.AddCarToMissionDeletionList = function (car) {
	return _in(0x00000000, 0x45e80bf7, car);
};

global.AddCharDecisionMakerEventResponse = function (dm, eventid, responseid, param1, param2, param3, param4, unknown0_1, unknown1_1) {
	return _in(0x00000000, 0x65536ecf, dm, eventid, responseid, _fv(param1), _fv(param2), _fv(param3), _fv(param4), unknown0_1, unknown1_1);
};

/**
 * Adds a listener for Console Variable changes.
 * The function called expects to match the following signature:
 * ```ts
 * function ConVarChangeListener(conVarName: string, reserved: any);
 * ```
 * *   **conVarName**: The ConVar that changed.
 * *   **reserved**: Currently unused.
 * @param conVarFilter The Console Variable to listen for, this can be a pattern like "test:\*", or null for any
 * @param handler The handler function.
 * @return A cookie to remove the change handler.
 */
global.AddConvarChangeListener = function (conVarFilter, handler) {
	return _in(0x00000000, 0xab7f7241, _ts(conVarFilter), _mfr(handler), _r, _ri);
};

global.AddCoverBlockingArea = function (Unk110, Unk111, Unk112, Unk113, Unk114, Unk115, Unk116, Unk117, Unk118) {
	return _in(0x00000000, 0x6e856548, Unk110, Unk111, Unk112, Unk113, Unk114, Unk115, Unk116, Unk117, Unk118);
};

global.AddCoverPoint = function (Unk119, Unk120, Unk121, Unk122, Unk123, Unk124, Unk125, Unk126) {
	return _in(0x00000000, 0x18d5264d, Unk119, Unk120, Unk121, Unk122, Unk123, Unk124, Unk125, Unk126);
};

global.AddExplosion = function (x, y, z, exptype, radius, playsound, novisual, camshake) {
	return _in(0x00000000, 0x32da5e3a, _fv(x), _fv(y), _fv(z), exptype, _fv(radius), playsound, novisual, _fv(camshake));
};

global.AddFirstNCharactersOfStringToHtmlScriptObject = function (htmlobj, str, n) {
	return _in(0x00000000, 0x75fc34ef, htmlobj, _ts(str), n);
};

global.AddFollowNavmeshToPhoneTask = function (ped, Unk127, Unk128, Unk129) {
	return _in(0x00000000, 0x7f5d69c4, ped, Unk127, Unk128, Unk129);
};

global.AddGroupDecisionMakerEventResponse = function (dm, eventid, responseid, param1, param2, param3, param4, unknown0_1, unknown1_1) {
	return _in(0x00000000, 0x40cf3953, dm, eventid, responseid, _fv(param1), _fv(param2), _fv(param3), _fv(param4), unknown0_1, unknown1_1);
};

global.AddGroupToNetworkRestartNodeGroupList = function (Unk899) {
	return _in(0x00000000, 0x592e0e0f, Unk899);
};

global.AddHospitalRestart = function (x, y, z, radius, islandnum) {
	return _in(0x00000000, 0x2ab06643, _fv(x), _fv(y), _fv(z), _fv(radius), islandnum);
};

global.AddLineToConversation = function (Unk522, Unk523, Unk524, Unk525, Unk526) {
	return _in(0x00000000, 0x75080482, Unk522, Unk523, Unk524, Unk525, Unk526);
};

global.AddLineToMobilePhoneCall = function (id, name, text) {
	return _in(0x00000000, 0x0bed1dde, id, _ts(name), _ts(text));
};

global.AddLineToScriptedConversation = function (conversation, Unk527, Unk528) {
	return _in(0x00000000, 0x416413f6, conversation, Unk527, Unk528);
};

global.AddNavmeshRequiredRegion = function (x, y, z) {
	return _in(0x00000000, 0x6cb64bdb, _fv(x), _fv(y), _fv(z), _r, _ri);
};

global.AddNeededAtPosn = function (x, y, z) {
	return _in(0x00000000, 0x2e831921, _fv(x), _fv(y), _fv(z));
};

global.AddNewConversationSpeaker = function (id, Unk529, Unk530) {
	return _in(0x00000000, 0x542d499e, id, Unk529, _ii(Unk530) /* may be optional */);
};

global.AddNewFrontendConversationSpeaker = function (Unk531, Unk532) {
	return _in(0x00000000, 0x13d44996, Unk531, Unk532);
};

global.AddNextMessageToPreviousBriefs = function (add) {
	return _in(0x00000000, 0x1b086d33, add);
};

global.AddObjectToInteriorRoomByKey = function (obj, roomKey) {
	return _in(0x00000000, 0x67d83807, obj, roomKey);
};

global.AddObjectToInteriorRoomByName = function (obj, room_name) {
	return _in(0x00000000, 0x076863c9, obj, _ts(room_name));
};

global.AddPedToCinematographyAi = function (Unk28, ped) {
	return _in(0x00000000, 0x62687944, Unk28, ped);
};

global.AddPedToMissionDeletionList = function (ped, Unk29) {
	return _in(0x00000000, 0x10f64fbf, ped, Unk29);
};

global.AddPickupToInteriorRoomByKey = function (pickup, room_hash) {
	return _in(0x00000000, 0x198b786f, pickup, room_hash);
};

global.AddPickupToInteriorRoomByName = function (pickup, roomName) {
	return _in(0x00000000, 0x0365042f, pickup, _ts(roomName));
};

global.AddPointToGpsRaceTrack = function (point) {
	return _in(0x00000000, 0x5be115fd, _v);
};

global.AddPoliceRestart = function (x, y, z, radius, islandnum) {
	return _in(0x00000000, 0x42492860, _fv(x), _fv(y), _fv(z), _fv(radius), islandnum);
};

global.AddScenarioBlockingArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x4c1e3a64, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.AddScore = function (playerIndex, score) {
	return _in(0x00000000, 0x537379a8, playerIndex, score);
};

global.AddSimpleBlipForPickup = function (pickup) {
	return _in(0x00000000, 0x44b30452, pickup);
};

global.AddSpawnBlockingArea = function (Unk900, Unk901, Unk902, Unk903) {
	return _in(0x00000000, 0x36df37db, Unk900, Unk901, Unk902, Unk903);
};

global.AddSpawnBlockingDisc = function (Unk904, Unk905, Unk906, Unk907, Unk908) {
	return _in(0x00000000, 0x2b4e2a8c, Unk904, Unk905, Unk906, Unk907, Unk908);
};

/**
 * Adds a handler for changes to a state bag.
 * The function called expects to match the following signature:
 * ```ts
 * function StateBagChangeHandler(bagName: string, key: string, value: any, reserved: number, replicated: boolean);
 * ```
 * *   **bagName**: The internal bag ID for the state bag which changed. This is usually `player:Source`, `entity:NetID`
 * or `localEntity:Handle`.
 * *   **key**: The changed key.
 * *   **value**: The new value stored at key. The old value is still stored in the state bag at the time this callback executes.
 * *   **reserved**: Currently unused.
 * *   **replicated**: Whether the set is meant to be replicated.
 * At this time, the change handler can't opt to reject changes.
 * If bagName refers to an entity, use [GET_ENTITY_FROM_STATE_BAG_NAME](#\_0x4BDF1867) to get the entity handle
 * If bagName refers to a player, use [GET_PLAYER_FROM_STATE_BAG_NAME](#\_0xA56135E0) to get the player handle
 * @param keyFilter The key to check for, or null for no filter.
 * @param bagFilter The bag ID to check for such as `entity:65535`, or null for no filter.
 * @param handler The handler function.
 * @return A cookie to remove the change handler.
 */
global.AddStateBagChangeHandler = function (keyFilter, bagFilter, handler) {
	return _in(0x00000000, 0x5ba35aaf, _ts(keyFilter), _ts(bagFilter), _mfr(handler), _r, _ri);
};

global.AddStringToHtmlScriptObject = function (htmlobj, str) {
	return _in(0x00000000, 0x7eb70379, htmlobj, _ts(str));
};

global.AddStringToNewsScrollbar = function (str) {
	return _in(0x00000000, 0x192e5726, _ts(str));
};

global.AddStringWithThisTextLabelToPreviousBrief = function (gxtname) {
	return _in(0x00000000, 0x76860554, _ts(gxtname));
};

global.AddStuckCarCheck = function (car, stuckdif, timeout) {
	return _in(0x00000000, 0x03a01b12, car, _fv(stuckdif), timeout);
};

global.AddStuckCarCheckWithWarp = function (car, stuckdif, time, flag0, flag1, flag2, flag3) {
	return _in(0x00000000, 0x3bca4aca, car, _fv(stuckdif), time, flag0, flag1, flag2, flag3);
};

global.AddStuntJump = function (x, y, z, x0, y0, z0, x1, y1, z1, x2, y2, z2, x3, y3, z3, reward) {
	return _in(0x00000000, 0x422e7ac3, _fv(x), _fv(y), _fv(z), _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), _fv(x3), _fv(y3), _fv(z3), reward);
};

/**
 * ADD_TEXT_ENTRY
 */
global.AddTextEntry = function (entryKey, entryText) {
	return _in(0x00000000, 0x32ca01c3, _ts(entryKey), _ts(entryText));
};

/**
 * ADD_TEXT_ENTRY_BY_HASH
 */
global.AddTextEntryByHash = function (entryKey, entryText) {
	return _in(0x00000000, 0x289da860, _ch(entryKey), _ts(entryText));
};

global.AddTextWidget = function (Unk1089) {
	return _in(0x00000000, 0x7537050d, Unk1089, _r, _s);
};

global.AddTickerToPreviousBriefWithUnderscore = function (Unk625, Unk626, Unk627, Unk628, Unk629, Unk630, Unk631) {
	return _in(0x00000000, 0x020e0318, Unk625, Unk626, Unk627, Unk628, Unk629, Unk630, Unk631);
};

global.AddToHtmlScriptObject = function (htmlobj, htmlcode) {
	return _in(0x00000000, 0x3ecc0086, htmlobj, _ts(htmlcode));
};

global.AddToPreviousBrief = function (gxtentry) {
	return _in(0x00000000, 0x446e6515, _ts(gxtentry));
};

global.AddToPreviousBriefWithUnderscore = function (gxtentry) {
	return _in(0x00000000, 0x3d0a71a2, _ts(gxtentry));
};

global.AddToWidgetCombo = function (Unk1091) {
	return _in(0x00000000, 0x4f0d4ac7, Unk1091);
};

global.AddUpsidedownCarCheck = function (vehicle) {
	return _in(0x00000000, 0x557c076c, vehicle);
};

global.AddWidgetFloatReadOnly = function (Unk1092, Unk1093) {
	return _in(0x00000000, 0x4c8a7614, Unk1092, Unk1093);
};

global.AddWidgetFloatSlider = function (Unk1094, Unk1095, Unk1096, Unk1097, Unk1098) {
	return _in(0x00000000, 0x6f9256df, Unk1094, Unk1095, Unk1096, Unk1097, Unk1098);
};

global.AddWidgetReadOnly = function (Unk1099, Unk1100) {
	return _in(0x00000000, 0x4a2e3bca, Unk1099, Unk1100);
};

global.AddWidgetSlider = function (Unk1101, Unk1102, Unk1103, Unk1104, Unk1105) {
	return _in(0x00000000, 0x4a904476, Unk1101, Unk1102, Unk1103, Unk1104, Unk1105);
};

global.AddWidgetString = function (Unk1106) {
	return _in(0x00000000, 0x27d20f21, Unk1106);
};

global.AddWidgetToggle = function (Unk1107, Unk1108) {
	return _in(0x00000000, 0x66f47727, Unk1107, Unk1108);
};

global.AllocateScriptToObject = function (ScriptName, model, Unk602, radius, UnkTime) {
	return _in(0x00000000, 0x71c30148, _ts(ScriptName), model, Unk602, _fv(radius), UnkTime);
};

global.AllocateScriptToRandomPed = function (ScriptName, model, Unk603, flag) {
	return _in(0x00000000, 0x19db19d8, _ts(ScriptName), model, Unk603, flag);
};

global.AllowAutoConversationLookats = function (ped, allow) {
	return _in(0x00000000, 0x736d423e, ped, allow);
};

global.AllowEmergencyServices = function (allow) {
	return _in(0x00000000, 0x69a72c50, allow);
};

global.AllowGameToPauseForStreaming = function (allow) {
	return _in(0x00000000, 0x085e559e, allow);
};

global.AllowGangRelationshipsToBeChangedByNextCommand = function (value) {
	return _in(0x00000000, 0x585157fe, value);
};

global.AllowLockonToFriendlyPlayers = function (player, allow) {
	return _in(0x00000000, 0x362b5d1b, player, allow);
};

global.AllowLockonToRandomPeds = function (player, allow) {
	return _in(0x00000000, 0x6fe455d8, player, allow);
};

global.AllowMultipleDrivebyPickups = function (allow) {
	return _in(0x00000000, 0x7fc02528, allow);
};

global.AllowOneTimeOnlyCommandsToRun = function () {
	return _in(0x00000000, 0x3b2e3198, _r);
};

global.AllowPlayerToCarryNonMissionObjects = function (playerIndex, allow) {
	return _in(0x00000000, 0x6a0a724c, playerIndex, allow);
};

global.AllowReactionAnims = function (ped, allow) {
	return _in(0x00000000, 0x0fea6230, ped, allow);
};

global.AllowScenarioPedsToBeReturnedByNextCommand = function (value) {
	return _in(0x00000000, 0x6eee7e6c, value);
};

global.AllowStuntJumpsToTrigger = function (allow) {
	return _in(0x00000000, 0x5e8d7e3f, allow);
};

global.AllowTargetWhenInjured = function (ped, allow) {
	return _in(0x00000000, 0x33f8250b, ped, allow);
};

global.AllowThisScriptToBePaused = function (allows) {
	return _in(0x00000000, 0x3514533b, allows);
};

global.AlterWantedLevel = function (playerIndex, level) {
	return _in(0x00000000, 0x60c80ec9, playerIndex, level);
};

global.AlterWantedLevelNoDrop = function (playerIndex, level) {
	return _in(0x00000000, 0x5f3b6079, playerIndex, level);
};

global.AlwaysUseHeadOnHornAnimWhenDeadInCar = function (ped, use) {
	return _in(0x00000000, 0x7c156670, ped, use);
};

global.AmbientAudioBankNoLongerNeeded = function () {
	return _in(0x00000000, 0x292349c7);
};

global.AnchorBoat = function (boat, anchor) {
	return _in(0x00000000, 0x2e12687b, boat, anchor);
};

global.AnchorObject = function (obj, anchor, flags) {
	return _in(0x00000000, 0x5785181b, obj, anchor, flags);
};

global.ApplyForceToCar = function (vehicle, unknown0_3, x, y, z, spinX, spinY, spinZ, unknown4_0, unknown5_1, unknown6_1, unknown7_1) {
	return _in(0x00000000, 0x434611a3, vehicle, unknown0_3, _fv(x), _fv(y), _fv(z), _fv(spinX), _fv(spinY), _fv(spinZ), unknown4_0, unknown5_1, unknown6_1, unknown7_1);
};

global.ApplyForceToObject = function (obj, uk0_3, pX, pY, pZ, spinX, spinY, spinZ, uk4_0, uk5_1, uk6_1, uk7_1) {
	return _in(0x00000000, 0x438f6ecb, obj, uk0_3, _fv(pX), _fv(pY), _fv(pZ), _fv(spinX), _fv(spinY), _fv(spinZ), uk4_0, uk5_1, uk6_1, uk7_1);
};

global.ApplyForceToPed = function (ped, unknown0_3, x, y, z, spinX, spinY, spinZ, unknown4_0, unknown5_1, unknown6_1, unknown7_1) {
	return _in(0x00000000, 0x7305301d, ped, unknown0_3, _fv(x), _fv(y), _fv(z), _fv(spinX), _fv(spinY), _fv(spinZ), unknown4_0, unknown5_1, unknown6_1, unknown7_1);
};

global.ApplyWantedLevelChangeNow = function (playerIndex) {
	return _in(0x00000000, 0x705a6ed9, playerIndex);
};

/**
 * APPLY_WEATHER_CYCLES
 * @param numEntries The number of cycle entries. Must be between 1 and 256
 * @param msPerCycle The duration in milliseconds of each cycle. Must be between 1000 and 86400000 (24 hours)
 * @return Returns true if all parameters were valid, otherwise false.
 */
global.ApplyWeatherCycles = function (numEntries, msPerCycle) {
	return _in(0x00000000, 0x3422291c, numEntries, msPerCycle, _r);
};

global.AreAllNavmeshRegionsLoaded = function () {
	return _in(0x00000000, 0x73737990, _r);
};

global.AreAnyCharsNearChar = function (ped, radius) {
	return _in(0x00000000, 0x0f4a4fb2, ped, _fv(radius), _r);
};

global.AreCreditsFinished = function () {
	return _in(0x00000000, 0x63a669b6, _r);
};

global.AreEnemyPedsInArea = function (ped, x, y, z, radius) {
	return _in(0x00000000, 0x5c081186, ped, _fv(x), _fv(y), _fv(z), _fv(radius), _r);
};

global.AreTaxiLightsOn = function (vehicle) {
	return _in(0x00000000, 0x5f4b0b22, vehicle, _r);
};

global.AreWidescreenBordersActive = function () {
	return _in(0x00000000, 0x4fe17259, _r);
};

global.AsciiIntToString = function (ascii) {
	return _in(0x00000000, 0x7f4c0e47, ascii, _r, _s);
};

global.Asin = function (value) {
	return _in(0x00000000, 0x590a6f04, _fv(value), _r, _rf);
};

global.Atan = function (value) {
	return _in(0x00000000, 0x7ffe0a12, _fv(value), _r, _rf);
};

global.Atan2 = function (Unk497, Unk498) {
	return _in(0x00000000, 0x10a1449c, _fv(Unk497), _fv(Unk498), _r, _rf);
};

global.AttachAnimsToModel = function (model, anims) {
	return _in(0x00000000, 0x0b5704e0, model, _ts(anims));
};

global.AttachCamToObject = function (cam, obj) {
	return _in(0x00000000, 0x2966710d, cam, obj);
};

global.AttachCamToPed = function (cam, ped) {
	return _in(0x00000000, 0x78b00cb2, cam, ped);
};

global.AttachCamToVehicle = function (cam, veh) {
	return _in(0x00000000, 0x5e564cff, cam, veh);
};

global.AttachCamToViewport = function (cam, viewportid) {
	return _in(0x00000000, 0x21a3110a, cam, viewportid);
};

global.AttachCarToCar = function (car0, car1, Unk51, x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x64146142, car0, car1, Unk51, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.AttachCarToCarPhysically = function (vehid1, vehid2, Unk52, Unk53, xoffset, yoffset, zoffset, xbuffer, ybuffer, zbuffer, xrotateveh1, yrotateveh1, Unk54, Unk55, Unk56) {
	return _in(0x00000000, 0x778f46e3, vehid1, vehid2, Unk52, Unk53, _fv(xoffset), _fv(yoffset), _fv(zoffset), _fv(xbuffer), _fv(ybuffer), _fv(zbuffer), _fv(xrotateveh1), _fv(yrotateveh1), _fv(Unk54), _fv(Unk55), _fv(Unk56));
};

global.AttachCarToObject = function (car, obj, Unk57, Unk58, Unk59, Unk60, Unk61, Unk62, Unk63) {
	return _in(0x00000000, 0x61c81e88, car, obj, _fv(Unk57), _fv(Unk58), _fv(Unk59), _fv(Unk60), _fv(Unk61), _fv(Unk62), _fv(Unk63));
};

global.AttachObjectToCar = function (obj, v, unknown0_0, pX, pY, pZ, rX, rY, rZ) {
	return _in(0x00000000, 0x7e81412a, obj, v, unknown0_0, _fv(pX), _fv(pY), _fv(pZ), _fv(rX), _fv(rY), _fv(rZ));
};

global.AttachObjectToCarPhysically = function (obj, car, Unk79, Unk80, Unk81, Unk82, Unk83, Unk84, Unk85, Unk86, Unk87, Unk88, Unk89, Unk90, flag) {
	return _in(0x00000000, 0x161b05a9, obj, car, Unk79, Unk80, Unk81, Unk82, Unk83, Unk84, Unk85, Unk86, Unk87, Unk88, Unk89, Unk90, flag);
};

global.AttachObjectToObject = function (obj0, obj1_attach_to, Unk91, x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x089e42c1, obj0, obj1_attach_to, Unk91, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.AttachObjectToPed = function (obj, c, bone, pX, pY, pZ, rX, rY, rZ, unknown1_0) {
	return _in(0x00000000, 0x577a699e, obj, c, bone, _fv(pX), _fv(pY), _fv(pZ), _fv(rX), _fv(rY), _fv(rZ), unknown1_0);
};

global.AttachObjectToPedPhysically = function (obj, c, unknown, bone, pX, pY, pZ, rX, rY, rZ, unknown1_0, unknown2_0) {
	return _in(0x00000000, 0x1f760e1a, obj, c, unknown, bone, _fv(pX), _fv(pY), _fv(pZ), _fv(rX), _fv(rY), _fv(rZ), unknown1_0, unknown2_0);
};

global.AttachParachuteModelToPlayer = function (ped, obj) {
	return _in(0x00000000, 0x7edd58e1, ped, obj);
};

global.AttachPedToCar = function (ped, vehicle, unknown0_0, offsetX, offsetY, offsetZ, rotX, rotY, Unk64, Unk65) {
	return _in(0x00000000, 0x3efc1a7d, ped, vehicle, unknown0_0, _fv(offsetX), _fv(offsetY), _fv(offsetZ), _fv(rotX), _fv(rotY), Unk64, Unk65);
};

global.AttachPedToCarPhysically = function (ped, car, pedbone, x, y, z, angle, Unk30, Unk31, Unk32) {
	return _in(0x00000000, 0x7ff3248c, ped, car, pedbone, _fv(x), _fv(y), _fv(z), _fv(angle), _fv(Unk30), Unk31, Unk32);
};

global.AttachPedToObject = function (ped, obj, pedbone, x, y, z, angle, Unk33, Unk34, Unk35) {
	return _in(0x00000000, 0x376917ab, ped, obj, pedbone, _fv(x), _fv(y), _fv(z), _fv(angle), _fv(Unk33), Unk34, Unk35);
};

global.AttachPedToObjectPhysically = function (ped, obj, pedbone, x, y, z, angle, Unk36, Unk37, Unk38) {
	return _in(0x00000000, 0x782e78bf, ped, obj, pedbone, _fv(x), _fv(y), _fv(z), _fv(angle), _fv(Unk36), Unk37, Unk38);
};

global.AttachPedToShimmyEdge = function (ped, x, y, z, Unk39) {
	return _in(0x00000000, 0x0860560b, ped, _fv(x), _fv(y), _fv(z), _fv(Unk39));
};

global.AwardAchievement = function (achievement) {
	return _in(0x00000000, 0x5ed03255, achievement, _r, _ri);
};

global.AwardPlayerMissionRespect = function (respect) {
	return _in(0x00000000, 0x7783449d, _fv(respect));
};

global.BeginCamCommands = function (Unk540) {
	return _in(0x00000000, 0x351f4c86, _ii(Unk540) /* may be optional */);
};

global.BeginCharSearchCriteria = function () {
	return _in(0x00000000, 0x43f86230);
};

global.BlendFromNmWithAnim = function (ped, AnimName0, AnimName1, Unk1, x, y, z) {
	return _in(0x00000000, 0x6e405bd5, ped, _ts(AnimName0), _ts(AnimName1), Unk1, _fv(x), _fv(y), _fv(z));
};

global.BlendOutCharMoveAnims = function (ped) {
	return _in(0x00000000, 0x65a34b7a, ped);
};

global.BlockCharAmbientAnims = function (ped, block) {
	return _in(0x00000000, 0x1a2d7640, ped, block);
};

global.BlockCharGestureAnims = function (ped, value) {
	return _in(0x00000000, 0x1c144e4e, ped, value);
};

global.BlockCharHeadIk = function (ped, block) {
	return _in(0x00000000, 0x3efa66e8, ped, block);
};

global.BlockCharVisemeAnims = function (ped, block) {
	return _in(0x00000000, 0x44881d27, ped, block);
};

global.BlockCoweringInCover = function (ped, set) {
	return _in(0x00000000, 0x1866612d, ped, set);
};

global.BlockPedWeaponSwitching = function (ped, value) {
	return _in(0x00000000, 0x315238d5, ped, value);
};

global.BlockPeekingInCover = function (ped, set) {
	return _in(0x00000000, 0x15101503, ped, set);
};

global.BlockStatsMenuActions = function (player) {
	return _in(0x00000000, 0x734e3f62, player);
};

global.BreakCarDoor = function (vehicle, door, unknownFalse) {
	return _in(0x00000000, 0x18bd071b, vehicle, door, unknownFalse);
};

global.BurstCarTyre = function (vehicle, tyre) {
	return _in(0x00000000, 0x690d344f, vehicle, tyre);
};

global.CalculateChecksum = function (Unk1006, Unk1007) {
	return _in(0x00000000, 0x18a302cd, Unk1006, Unk1007, _r, _ri);
};

global.CalculateTravelDistanceBetweenNodes = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x09a558a5, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r, _rf);
};

global.CamIsSphereVisible = function (camera, pX, pY, pZ, radius) {
	return _in(0x00000000, 0x2d5611d4, camera, _fv(pX), _fv(pY), _fv(pZ), _fv(radius), _r);
};

global.CamProcess = function (cam) {
	return _in(0x00000000, 0x52411dda, cam);
};

global.CamRestore = function () {
	return _in(0x00000000, 0x348f612d);
};

global.CamRestoreJumpcut = function () {
	return _in(0x00000000, 0x538021cd);
};

global.CamSequenceClose = function () {
	return _in(0x00000000, 0x5d975a46);
};

global.CamSequenceGetProgress = function (Unk541, progress) {
	return _in(0x00000000, 0x7aad273f, Unk541, _ii(progress) /* may be optional */);
};

global.CamSequenceOpen = function (Unk542) {
	return _in(0x00000000, 0x5d867a02, Unk542);
};

global.CamSequenceRemove = function (Unk543) {
	return _in(0x00000000, 0x01473acb, Unk543);
};

global.CamSequenceStart = function (Unk544) {
	return _in(0x00000000, 0x26335ee7, Unk544);
};

global.CamSequenceStop = function (Unk545) {
	return _in(0x00000000, 0x282e4efb, Unk545);
};

global.CamSequenceWait = function (cam, time) {
	return _in(0x00000000, 0x0d970483, cam, time);
};

global.CamSetCinematic = function (veh, set) {
	return _in(0x00000000, 0x63a86d87, veh, set);
};

global.CamSetDollyZoomLock = function (cam, set) {
	return _in(0x00000000, 0x25071df3, cam, set);
};

global.CamSetInterpGraphPos = function (cam, Unk547) {
	return _in(0x00000000, 0x3c7c3e89, cam, Unk547);
};

global.CamSetInterpGraphRot = function (cam, val) {
	return _in(0x00000000, 0x1c5b7c51, cam, val);
};

global.CamSetInterpStateSrc = function (cam, Unk548) {
	return _in(0x00000000, 0x32c67124, cam, Unk548);
};

global.CamSetInterpStateSrc = function (Unk549, Unk550) {
	return _in(0x00000000, 0x32c67124, Unk549, Unk550);
};

global.CamSetInterpolationDetails = function (Unk546) {
	return _in(0x00000000, 0x5aac39c1, Unk546);
};

global.CanBeDescribedAsACar = function (veh) {
	return _in(0x00000000, 0x79103802, veh, _r);
};

global.CanCharSeeDeadChar = function (ped, pednext) {
	return _in(0x00000000, 0x7ed82ed9, ped, pednext, _r);
};

global.CanCreateRandomChar = function (flag0, flag1) {
	return _in(0x00000000, 0x5cd64d63, flag0, flag1, _r);
};

global.CanFontBeLoaded = function (fontid) {
	return _in(0x00000000, 0x1e2a5820, fontid, _r);
};

global.CanPedShimmyInDirection = function (ped, direction) {
	return _in(0x00000000, 0x6d1e5c25, ped, direction, _r);
};

global.CanPhoneBeSeenOnScreen = function () {
	return _in(0x00000000, 0x5c9863f6, _r);
};

global.CanPlayerStartMission = function (player) {
	return _in(0x00000000, 0x02a235d0, player, _r);
};

global.CanRegisterMissionObject = function () {
	return _in(0x00000000, 0x42f1557d, _r);
};

global.CanRegisterMissionPed = function () {
	return _in(0x00000000, 0x1dc730b8, _r);
};

global.CanRegisterMissionVehicle = function () {
	return _in(0x00000000, 0x200a510b, _r);
};

global.CanStartMissionPassedTune = function () {
	return _in(0x00000000, 0x22ab641d, _r);
};

global.CanTheStatHaveString = function (stat) {
	return _in(0x00000000, 0x0b651afb, stat, _r);
};

global.CancelCurrentlyPlayingAmbientSpeech = function (ped) {
	return _in(0x00000000, 0x495d445f, ped);
};

global.CancelCurrentlyPlayingAmbientSpeech = function (ped) {
	return _in(0x00000000, 0x495d445f, ped);
};

/**
 * Cancels the currently executing event.
 */
global.CancelEvent = function () {
	return _in(0x00000000, 0xfa29d35d);
};

global.CancelOverrideRestart = function () {
	return _in(0x00000000, 0x6ed83424);
};

global.Ceil = function (value) {
	return _in(0x00000000, 0x76181322, _fv(value), _r, _ri);
};

global.CellCamActivate = function (Unk551, Unk552) {
	return _in(0x00000000, 0x446f74e5, Unk551, Unk552);
};

global.CellCamIsCharVisible = function (ped) {
	return _in(0x00000000, 0x0d6c0836, ped, _r);
};

global.CellCamIsCharVisibleNoFaceCheck = function (ped) {
	return _in(0x00000000, 0x770600cf, ped, _r);
};

global.CellCamSetCentrePos = function (x, y) {
	return _in(0x00000000, 0x32c67003, _fv(x), _fv(y));
};

global.CellCamSetColourBrightness = function (Unk553, Unk554, Unk555, Unk556) {
	return _in(0x00000000, 0x4ecb189e, _fv(Unk553), _fv(Unk554), _fv(Unk555), _fv(Unk556));
};

global.CellCamSetZoom = function (zoom) {
	return _in(0x00000000, 0x087c5347, _fv(zoom));
};

global.ChangeBlipAlpha = function (blip, alpha) {
	return _in(0x00000000, 0x2fb14e41, blip, alpha);
};

global.ChangeBlipColour = function (blip, colour) {
	return _in(0x00000000, 0x1d8800e3, blip, colour);
};

global.ChangeBlipDisplay = function (blip, display) {
	return _in(0x00000000, 0x3acc1794, blip, display);
};

global.ChangeBlipNameFromAscii = function (blip, blipName) {
	return _in(0x00000000, 0x6c9f2330, blip, _ts(blipName));
};

global.ChangeBlipNameFromTextFile = function (blip, gxtName) {
	return _in(0x00000000, 0x0a9d695e, blip, _ts(gxtName));
};

global.ChangeBlipNameToPlayerName = function (blip, playerid) {
	return _in(0x00000000, 0x731b11a7, blip, playerid);
};

global.ChangeBlipPriority = function (blip, priority) {
	return _in(0x00000000, 0x69ec0e70, blip, priority);
};

global.ChangeBlipRotation = function (blip, rotation) {
	return _in(0x00000000, 0x3af307b1, blip, rotation);
};

global.ChangeBlipScale = function (blip, scale) {
	return _in(0x00000000, 0x44d349d9, blip, _fv(scale));
};

global.ChangeBlipSprite = function (blip, sprite) {
	return _in(0x00000000, 0x6a90123d, blip, sprite);
};

global.ChangeBlipTeamRelevance = function (blip, relevance) {
	return _in(0x00000000, 0x4b2625be, blip, relevance);
};

global.ChangeCarColour = function (vehicle, colour1, colour2) {
	return _in(0x00000000, 0x06441eaf, vehicle, colour1, colour2);
};

global.ChangeCharSitIdleAnim = function (ped, Unk2, Unk3, Unk4) {
	return _in(0x00000000, 0x7b2822f7, ped, Unk2, Unk3, Unk4);
};

global.ChangeGarageType = function (garage, type) {
	return _in(0x00000000, 0x6e0a438a, garage, type);
};

global.ChangePickupBlipColour = function (colour) {
	return _in(0x00000000, 0x65d949b7, colour);
};

global.ChangePickupBlipDisplay = function (display) {
	return _in(0x00000000, 0x3e5f2362, display);
};

global.ChangePickupBlipPriority = function (priority) {
	return _in(0x00000000, 0x31321d1a, priority);
};

global.ChangePickupBlipScale = function (scale) {
	return _in(0x00000000, 0x4f66544e, _fv(scale));
};

global.ChangePickupBlipSprite = function (sprite) {
	return _in(0x00000000, 0x05766dde, sprite);
};

global.ChangePlaybackToUseAi = function (car) {
	return _in(0x00000000, 0x76eb2878, car);
};

global.ChangePlayerModel = function (playerIndex, model) {
	return _in(0x00000000, 0x232f1a85, playerIndex, model);
};

global.ChangePlayerPhoneModel = function (player, model) {
	return _in(0x00000000, 0x7f2a71fd, player, model);
};

global.ChangePlayerPhoneModelOffsets = function (player, x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x481e2be7, player, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.ChangeTerritoryBlipScale = function (blip, Unk632, Unk633) {
	return _in(0x00000000, 0x35a250c2, blip, _fv(Unk632), _fv(Unk633));
};

global.CheatHappenedRecently = function (cheat, time) {
	return _in(0x00000000, 0x7488454d, cheat, time, _r);
};

global.CheckNmFeedback = function (ped, id, Unk13) {
	return _in(0x00000000, 0x7c4c63ef, ped, id, Unk13, _r);
};

global.CheckStuckTimer = function (car, timernum, timeout) {
	return _in(0x00000000, 0x15285933, car, timernum, timeout, _r);
};

global.ClearAdditionalText = function (textid, Unk634) {
	return _in(0x00000000, 0x0a1b465c, textid, Unk634);
};

global.ClearAllCharProps = function (ped) {
	return _in(0x00000000, 0x232a52fa, ped);
};

global.ClearAllCharRelationships = function (ped, relgroup) {
	return _in(0x00000000, 0x57297d58, ped, relgroup);
};

global.ClearAngledAreaOfCars = function (x0, y0, z0, x1, y1, z1, radius) {
	return _in(0x00000000, 0x7e2a7743, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _fv(radius));
};

global.ClearArea = function (x, y, z, radius, unknown) {
	return _in(0x00000000, 0x27722942, _fv(x), _fv(y), _fv(z), _fv(radius), unknown);
};

global.ClearAreaOfCars = function (x, y, z, radius) {
	return _in(0x00000000, 0x24367e48, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.ClearAreaOfChars = function (x, y, z, radius) {
	return _in(0x00000000, 0x0c2747b9, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.ClearAreaOfCops = function (x, y, z, radius) {
	return _in(0x00000000, 0x5f182e21, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.ClearAreaOfObjects = function (x, y, z, radius) {
	return _in(0x00000000, 0x118a67c9, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.ClearBit = function (bit) {
	return _in(0x00000000, 0x66d57cc4, _i, bit);
};

global.ClearBrief = function () {
	return _in(0x00000000, 0x16d762e5);
};

global.ClearCarLastDamageEntity = function (vehicle) {
	return _in(0x00000000, 0x4d6665f7, vehicle);
};

global.ClearCarLastWeaponDamage = function (vehicle) {
	return _in(0x00000000, 0x31102e20, vehicle);
};

global.ClearCharLastDamageBone = function (ped) {
	return _in(0x00000000, 0x1a013092, ped);
};

global.ClearCharLastDamageEntity = function (ped) {
	return _in(0x00000000, 0x0ab9317b, ped);
};

global.ClearCharLastWeaponDamage = function (ped) {
	return _in(0x00000000, 0x718508b4, ped);
};

global.ClearCharProp = function (ped, unknown) {
	return _in(0x00000000, 0x51546112, ped, unknown);
};

global.ClearCharRelationship = function (ped, reltype, relgroup) {
	return _in(0x00000000, 0x42db145f, ped, reltype, relgroup);
};

global.ClearCharSecondaryTask = function (ped) {
	return _in(0x00000000, 0x7fc96dd5, ped);
};

global.ClearCharTasks = function (ped) {
	return _in(0x00000000, 0x4ab470f3, ped);
};

global.ClearCharTasksImmediately = function (ped) {
	return _in(0x00000000, 0x3c116620, ped);
};

global.ClearCutscene = function () {
	return _in(0x00000000, 0x79611458);
};

global.ClearGroupDecisionMakerEventResponse = function (dm, eventid) {
	return _in(0x00000000, 0x3bf71d5f, dm, eventid);
};

global.ClearHelp = function () {
	return _in(0x00000000, 0x07244253);
};

global.ClearNamedCutscene = function (name) {
	return _in(0x00000000, 0x62ef058e, _ts(name));
};

global.ClearNetworkRestartNodeGroupList = function () {
	return _in(0x00000000, 0x1bda1f9a);
};

global.ClearNewsScrollbar = function () {
	return _in(0x00000000, 0x0d721eea);
};

global.ClearObjectLastDamageEntity = function (obj) {
	return _in(0x00000000, 0x64be2e39, obj);
};

global.ClearObjectLastWeaponDamage = function (obj) {
	return _in(0x00000000, 0x15f11bab, obj);
};

global.ClearOnscreenCounter = function (counterid) {
	return _in(0x00000000, 0x3f236954, counterid);
};

global.ClearOnscreenTimer = function (timerid) {
	return _in(0x00000000, 0x34c751a2, timerid);
};

global.ClearPedNonCreationArea = function () {
	return _in(0x00000000, 0x0c1c7919);
};

global.ClearPedNonRemovalArea = function () {
	return _in(0x00000000, 0x0a74017b);
};

global.ClearPlayerHasDamagedAtLeastOnePed = function (playerIndex) {
	return _in(0x00000000, 0x45ab718f, playerIndex);
};

global.ClearPlayerHasDamagedAtLeastOneVehicle = function (player) {
	return _in(0x00000000, 0x26aa20cf, player);
};

global.ClearPrints = function () {
	return _in(0x00000000, 0x1d8c324a);
};

global.ClearRelationship = function (p0, p1, p2) {
	return _in(0x00000000, 0x3ff16cbc, p0, p1, p2);
};

global.ClearRoomForCar = function (vehicle) {
	return _in(0x00000000, 0x5fd24fea, vehicle);
};

global.ClearRoomForChar = function (ped) {
	return _in(0x00000000, 0x405b16cf, ped);
};

global.ClearRoomForObject = function (obj) {
	return _in(0x00000000, 0x12ed69a6, obj);
};

global.ClearRoomForViewport = function (viewportid) {
	return _in(0x00000000, 0x7a583068, viewportid);
};

global.ClearScriptArrayFromScratchpad = function (Unk909) {
	return _in(0x00000000, 0x6e120246, Unk909);
};

global.ClearScriptedConversionCentre = function () {
	return _in(0x00000000, 0x2e4662b3);
};

global.ClearSequenceTask = function (taskSequence) {
	return _in(0x00000000, 0x7ed774fe, taskSequence);
};

global.ClearShakePlayerpadWhenControllerDisabled = function () {
	return _in(0x00000000, 0x3f1f51e0);
};

global.ClearSmallPrints = function () {
	return _in(0x00000000, 0x7c515b18);
};

global.ClearTextLabel = function (label) {
	return _in(0x00000000, 0x412e68d0, _ts(label));
};

global.ClearThisBigPrint = function (gxtentry) {
	return _in(0x00000000, 0x4a4f2699, _ts(gxtentry));
};

global.ClearThisPrint = function (gxtentry) {
	return _in(0x00000000, 0x08d85cbb, _ts(gxtentry));
};

global.ClearThisPrintBigNow = function (Unk635) {
	return _in(0x00000000, 0x1c8b73b6, Unk635);
};

global.ClearTimecycleModifier = function () {
	return _in(0x00000000, 0x60fb61a7);
};

global.ClearWantedLevel = function (playerIndex) {
	return _in(0x00000000, 0x205622ac, playerIndex);
};

global.CloneCam = function (cam, camcopy) {
	return _in(0x00000000, 0x483e5be8, cam, _ii(camcopy) /* may be optional */);
};

/**
 * CLONE_TIMECYCLE_MODIFIER
 * @param sourceModifierName The source timecycle name.
 * @param clonedModifierName The clone timecycle name, must be unique.
 * @return The cloned timecycle modifier index, or -1 if failed.
 */
global.CloneTimecycleModifier = function (sourceModifierName, clonedModifierName) {
	return _in(0x00000000, 0x54d636b3, _ts(sourceModifierName), _ts(clonedModifierName), _r, _ri);
};

global.CloseAllCarDoors = function (vehicle) {
	return _in(0x00000000, 0x56b8674f, vehicle);
};

global.CloseDebugFile = function () {
	return _in(0x00000000, 0x41286578);
};

global.CloseGarage = function (garageName) {
	return _in(0x00000000, 0x5c083072, _ts(garageName));
};

global.CloseMicPed = function (id, ped) {
	return _in(0x00000000, 0x14b06047, id, ped);
};

global.CloseSequenceTask = function (taskSequence) {
	return _in(0x00000000, 0x016c1b04, taskSequence);
};

global.CodeWantsMobilePhoneRemoved = function () {
	return _in(0x00000000, 0x63da2195, _r);
};

global.CodeWantsMobilePhoneRemovedForWeaponSwitching = function () {
	return _in(0x00000000, 0x736027e6, _r);
};

global.CompareString = function (str0, str1) {
	return _in(0x00000000, 0x080b4f21, _ts(str0), _ts(str1), _r, _ri);
};

global.CompareTwoDates = function (date0_0, date0_1, date1_0, date1_1) {
	return _in(0x00000000, 0x116d009a, date0_0, date0_1, date1_0, date1_1, _r, _ri);
};

global.ConnectLods = function (obj0, obj1) {
	return _in(0x00000000, 0x79eb2bc9, obj0, obj1);
};

global.ControlCarDoor = function (vehicle, door, unknown_maybe_open, angle) {
	return _in(0x00000000, 0x194f76d4, vehicle, door, unknown_maybe_open, _fv(angle));
};

global.ConvertIntToPlayerindex = function (playerId) {
	return _in(0x00000000, 0x5996315e, playerId, _r, _ri);
};

global.ConvertMetresToFeet = function (metres) {
	return _in(0x00000000, 0x4d2771ce, _fv(metres), _r, _rf);
};

global.ConvertMetresToFeetInt = function (metres) {
	return _in(0x00000000, 0x01a05add, metres, _r, _ri);
};

global.ConvertThenAddStringToHtmlScriptObject = function (htmlobj, strgxtkey) {
	return _in(0x00000000, 0x72ec0aa6, htmlobj, _ts(strgxtkey));
};

global.CopyAnimations = function (ped, pednext, speed) {
	return _in(0x00000000, 0x308d1778, ped, pednext, _fv(speed));
};

global.CopyCharDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x1bb41b75, type, _ii(pDM) /* may be optional */);
};

global.CopyCombatDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x062e0076, type, _ii(pDM) /* may be optional */);
};

global.CopyGroupCharDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x472e65d6, type, _ii(pDM) /* may be optional */);
};

global.CopyGroupCombatDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x17002e03, type, _ii(pDM) /* may be optional */);
};

global.CopySharedCharDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x189e32c9, type, _ii(pDM) /* may be optional */);
};

global.CopySharedCombatDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x13de5c59, type, _ii(pDM) /* may be optional */);
};

global.Cos = function (value) {
	return _in(0x00000000, 0x061d4b5f, _fv(value), _r, _rf);
};

global.CountPickupsOfType = function (type) {
	return _in(0x00000000, 0x2e921b0f, type, _r, _ri);
};

global.CountScriptCams = function () {
	return _in(0x00000000, 0x4806044a, _r, _ri);
};

global.CountScriptCamsByTypeAndOrState = function (type, Unk536, Unk537) {
	return _in(0x00000000, 0x009641ee, type, Unk536, Unk537, _r, _ri);
};

/**
 * Creates an audio submix with the specified name, or gets the existing audio submix by that name.
 * @param name The audio submix name.
 * @return A submix ID, or -1 if the submix could not be created.
 */
global.CreateAudioSubmix = function (name) {
	return _in(0x00000000, 0x658d2bc8, _ts(name), _r, _ri);
};

global.CreateCam = function (camtype_usually14, camera) {
	return _in(0x00000000, 0x694a0dc1, camtype_usually14, _ii(camera) /* may be optional */);
};

global.CreateCar = function (nameHash, x, y, z, unknownTrue) {
	return _in(0x00000000, 0x2f1d6843, nameHash, _fv(x), _fv(y), _fv(z), _i, unknownTrue);
};

global.CreateCarGenerator = function (x, y, z, yaw, pitch, roll, model, color1, color2, spec1, spec2, Unk66, alarm, doorlock, handle) {
	return _in(0x00000000, 0x0f132f7e, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), model, color1, color2, spec1, spec2, Unk66, alarm, doorlock, _ii(handle) /* may be optional */);
};

global.CreateCarsOnGeneratorsInArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x0d940af4, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.CreateChar = function (type, model, x, y, z, unknownTrue) {
	return _in(0x00000000, 0x4a673763, type, model, _fv(x), _fv(y), _fv(z), _i, unknownTrue);
};

global.CreateCharAsPassenger = function (vehicle, charType, model, passengerIndex, pPed) {
	return _in(0x00000000, 0x442b1c1d, vehicle, charType, model, passengerIndex, _ii(pPed) /* may be optional */);
};

global.CreateCharInsideCar = function (vehicle, charType, model, pPed) {
	return _in(0x00000000, 0x2702274d, vehicle, charType, model, _ii(pPed) /* may be optional */);
};

global.CreateCheckpoint = function (type, x, y, z, Unk709, Unk710) {
	return _in(0x00000000, 0x41f27499, type, _fv(x), _fv(y), _fv(z), _fv(Unk709), _fv(Unk710), _r, _ri);
};

/**
 * Creates a DUI browser. This can be used to draw on a runtime texture using CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.
 * @param url The initial URL to load in the browser.
 * @param width The width of the backing surface.
 * @param height The height of the backing surface.
 * @return A DUI object.
 */
global.CreateDui = function (url, width, height) {
	return _in(0x00000000, 0x23eaf899, _ts(url), width, height, _r, _rl);
};

global.CreateEmergencyServicesCar = function (model, x, y, z) {
	return _in(0x00000000, 0x768b3ac7, model, _fv(x), _fv(y), _fv(z), _r);
};

global.CreateEmergencyServicesCarReturnDriver = function (model, x, y, z) {
	return _in(0x00000000, 0x68251a95, model, _fv(x), _fv(y), _fv(z), _i, _i, _i, _r);
};

global.CreateEmergencyServicesCarThenWalk = function (model, x, y, z) {
	return _in(0x00000000, 0x4a3d6d97, model, _fv(x), _fv(y), _fv(z), _r);
};

global.CreateGroup = function (unknownFalse, unknownTrue) {
	return _in(0x00000000, 0x78300c0c, unknownFalse, _i, unknownTrue);
};

global.CreateHtmlScriptObject = function (objname) {
	return _in(0x00000000, 0x6aa63375, _ts(objname), _r, _ri);
};

global.CreateHtmlViewport = function (htmlviewport) {
	return _in(0x00000000, 0x2fae4c6e, _ii(htmlviewport) /* may be optional */);
};

global.CreateMenu = function (gxtentry, Unk859, Unk860, Unk861, Unk862, Unk863, Unk864, Unk865, menuid) {
	return _in(0x00000000, 0x7dca398f, _ts(gxtentry), Unk859, Unk860, Unk861, Unk862, Unk863, Unk864, Unk865, _ii(menuid) /* may be optional */);
};

global.CreateMissionTrain = function (unknown1, x, y, z, unknown2, pTrain) {
	return _in(0x00000000, 0x0ddd70ae, unknown1, _fv(x), _fv(y), _fv(z), unknown2, _ii(pTrain) /* may be optional */);
};

global.CreateMobilePhone = function (Unk799) {
	return _in(0x00000000, 0x2fee095b, Unk799);
};

global.CreateMoneyPickup = function (x, y, z, amount, unknownTrue, pPickup) {
	return _in(0x00000000, 0x019a0068, _fv(x), _fv(y), _fv(z), amount, unknownTrue, _ii(pPickup) /* may be optional */);
};

global.CreateNmMessage = function (Unk40, id) {
	return _in(0x00000000, 0x22aa010c, Unk40, id);
};

global.CreateObject = function (model, x, y, z, unknownTrue) {
	return _in(0x00000000, 0x4de152a0, model, _fv(x), _fv(y), _fv(z), _i, unknownTrue);
};

global.CreateObjectNoOffset = function (model, x, y, z, unknownTrue) {
	return _in(0x00000000, 0x75c51a26, model, _fv(x), _fv(y), _fv(z), _i, unknownTrue);
};

global.CreatePickup = function (model, pickupType, x, y, z, unknownFalse) {
	return _in(0x00000000, 0x7e2868d4, model, pickupType, _fv(x), _fv(y), _fv(z), _i, unknownFalse);
};

global.CreatePickupRotate = function (model, pickupType, unknown, x, y, z, rX, rY, rZ, pPickup) {
	return _in(0x00000000, 0x675e5940, model, pickupType, unknown, _fv(x), _fv(y), _fv(z), _fv(rX), _fv(rY), _fv(rZ), _ii(pPickup) /* may be optional */);
};

global.CreatePickupWithAmmo = function (model, pickupType, unknown, x, y, z, pPickup) {
	return _in(0x00000000, 0x1f736f00, model, pickupType, unknown, _fv(x), _fv(y), _fv(z), _ii(pPickup) /* may be optional */);
};

global.CreatePlayer = function (playerId, x, y, z, pPlayerIndex) {
	return _in(0x00000000, 0x335e3951, playerId, _fv(x), _fv(y), _fv(z), _ii(pPlayerIndex) /* may be optional */);
};

global.CreateRandomCarForCarPark = function (x, y, z, radius) {
	return _in(0x00000000, 0x36da42af, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.CreateRandomChar = function (x, y, z, pPed) {
	return _in(0x00000000, 0x375d6223, _fv(x), _fv(y), _fv(z), _ii(pPed) /* may be optional */);
};

global.CreateRandomCharAsDriver = function (vehicle, pPed) {
	return _in(0x00000000, 0x31cd5f18, vehicle, _ii(pPed) /* may be optional */);
};

global.CreateRandomCharAsPassenger = function (vehicle, seat, pPed) {
	return _in(0x00000000, 0x46d01849, vehicle, seat, _ii(pPed) /* may be optional */);
};

global.CreateRandomFemaleChar = function (x, y, z, pPed) {
	return _in(0x00000000, 0x1a920c02, _fv(x), _fv(y), _fv(z), _ii(pPed) /* may be optional */);
};

global.CreateRandomMaleChar = function (x, y, z, pPed) {
	return _in(0x00000000, 0x2fc728bb, _fv(x), _fv(y), _fv(z), _ii(pPed) /* may be optional */);
};

global.CreateTemporaryRadarBlipsForPickupsInArea = function (x, y, z, radius, bliptype) {
	return _in(0x00000000, 0x44ea47bb, _fv(x), _fv(y), _fv(z), _fv(radius), bliptype);
};

/**
 * Create a clean timecycle modifier. See [`SET_TIMECYCLE_MODIFIER_VAR`](#\_0x6E0A422B) to add variables.
 * @param modifierName The new timecycle name, must be unique.
 * @return The created timecycle modifier index, or -1 if failed.
 */
global.CreateTimecycleModifier = function (modifierName) {
	return _in(0x00000000, 0x70fa2afa, _ts(modifierName), _r, _ri);
};

global.CreateViewport = function (viewport) {
	return _in(0x00000000, 0x13134ccd, _ii(viewport) /* may be optional */);
};

global.CreateWidgetGroup = function (Unk1109) {
	return _in(0x00000000, 0x558c4259, Unk1109);
};

global.DamageCar = function (car, x, y, z, unkforce0, unkforce1, flag) {
	return _in(0x00000000, 0x2d2b208a, car, _fv(x), _fv(y), _fv(z), _fv(unkforce0), _fv(unkforce1), flag);
};

global.DamageChar = function (ped, hitPoints, unknown) {
	return _in(0x00000000, 0x6045426e, ped, hitPoints, unknown);
};

global.DamagePedBodyPart = function (ped, part, hitPoints) {
	return _in(0x00000000, 0x0744307b, ped, part, hitPoints);
};

global.DeactivateFrontend = function () {
	return _in(0x00000000, 0x72b16d0d);
};

global.DeactivateNetworkSettingsMenu = function () {
	return _in(0x00000000, 0x4ad22b80);
};

global.DeactivateScriptPopulationZone = function () {
	return _in(0x00000000, 0x66bb737d);
};

global.DebugOff = function () {
	return _in(0x00000000, 0x67177eec);
};

global.DecrementFloatStat = function (stat, val) {
	return _in(0x00000000, 0x0754000c, stat, _fv(val));
};

global.DecrementIntStat = function (stat, amount) {
	return _in(0x00000000, 0x7dd91295, stat, amount);
};

global.DefinePedGenerationConstraintArea = function (x, y, z, radius) {
	return _in(0x00000000, 0x0991172d, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.DeleteAllHtmlScriptObjects = function () {
	return _in(0x00000000, 0x31a77970);
};

global.DeleteAllTrains = function () {
	return _in(0x00000000, 0x552b2224);
};

global.DeleteCar = function (pVehicle) {
	return _in(0x00000000, 0x7f71342d, _ii(pVehicle) /* may be optional */);
};

global.DeleteCarGenerator = function (handle) {
	return _in(0x00000000, 0x76e738a3, handle);
};

global.DeleteChar = function (pPed) {
	return _in(0x00000000, 0x0e3b49bf, _ii(pPed) /* may be optional */);
};

global.DeleteCheckpoint = function (checkpoint) {
	return _in(0x00000000, 0x1293731d, checkpoint);
};

/**
 * DELETE_FUNCTION_REFERENCE
 */
global.DeleteFunctionReference = function (referenceIdentity) {
	return _in(0x00000000, 0x1e86f206, _ts(referenceIdentity));
};

global.DeleteHtmlScriptObject = function (htmlobj) {
	return _in(0x00000000, 0x53456730, htmlobj);
};

global.DeleteMenu = function (menuid) {
	return _in(0x00000000, 0x252138b3, menuid);
};

global.DeleteMissionTrain = function (pTrain) {
	return _in(0x00000000, 0x7da237bc, _ii(pTrain) /* may be optional */);
};

global.DeleteMissionTrains = function () {
	return _in(0x00000000, 0x7d635e2c);
};

global.DeleteObject = function (pObj) {
	return _in(0x00000000, 0x62fe6290, _ii(pObj) /* may be optional */);
};

global.DeletePlayer = function () {
	return _in(0x00000000, 0x627a3586);
};

/**
 * DELETE_RESOURCE_KVP
 * @param key The key to delete
 */
global.DeleteResourceKvp = function (key) {
	return _in(0x00000000, 0x7389b5df, _ts(key));
};

/**
 * Nonsynchronous [DELETE_RESOURCE_KVP](#\_0x7389B5DF) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to delete
 */
global.DeleteResourceKvpNoSync = function (key) {
	return _in(0x00000000, 0x04152c90, _ts(key));
};

global.DeleteWidget = function (Unk1110) {
	return _in(0x00000000, 0x267d5146, Unk1110);
};

global.DeleteWidgetGroup = function (Unk1111) {
	return _in(0x00000000, 0x17d72833, Unk1111);
};

global.DestroyAllCams = function () {
	return _in(0x00000000, 0x614a3353);
};

global.DestroyAllScriptViewports = function () {
	return _in(0x00000000, 0x5e4327d2);
};

global.DestroyCam = function (camera) {
	return _in(0x00000000, 0x14334eee, camera);
};

/**
 * Destroys a DUI browser.
 * @param duiObject The DUI browser handle.
 */
global.DestroyDui = function (duiObject) {
	return _in(0x00000000, 0xa085cb10, duiObject);
};

global.DestroyMobilePhone = function () {
	return _in(0x00000000, 0x38be5bf6);
};

global.DestroyPedGenerationConstraintArea = function () {
	return _in(0x00000000, 0x3cc5682f);
};

global.DestroyThread = function (ScriptHandle) {
	return _in(0x00000000, 0x47381e59, ScriptHandle);
};

global.DestroyViewport = function (viewportid) {
	return _in(0x00000000, 0x651e50ec, viewportid);
};

global.DetachCamFromViewport = function (Unk557) {
	return _in(0x00000000, 0x1dea65de, Unk557);
};

global.DetachCar = function (vehicle) {
	return _in(0x00000000, 0x34cc1f23, vehicle);
};

global.DetachObject = function (obj, unknown) {
	return _in(0x00000000, 0x05c87c26, obj, unknown);
};

global.DetachObjectNoCollide = function (obj, flag) {
	return _in(0x00000000, 0x6b2e49cd, obj, flag);
};

global.DetachPed = function (ped, unknown) {
	return _in(0x00000000, 0x2cd52c5c, ped, unknown);
};

global.DidSaveCompleteSuccessfully = function () {
	return _in(0x00000000, 0x5aa33e86, _r);
};

global.DimBlip = function (blip, unknownTrue) {
	return _in(0x00000000, 0x272d15fd, blip, unknownTrue);
};

global.DisableCarGenerators = function (flag0, flag1) {
	return _in(0x00000000, 0x581e2306, flag0, flag1);
};

global.DisableCarGeneratorsWithHeli = function (disable) {
	return _in(0x00000000, 0x018c4131, disable);
};

global.DisableEndCreditsFade = function () {
	return _in(0x00000000, 0x21b45ec1);
};

global.DisableFrontendRadio = function () {
	return _in(0x00000000, 0x6b2f3e97);
};

global.DisableGps = function (disable) {
	return _in(0x00000000, 0x32a81853, disable);
};

global.DisableHeliChaseCamBonnetNitroFix = function () {
	return _in(0x00000000, 0x19a73e70);
};

global.DisableHeliChaseCamThisUpdate = function () {
	return _in(0x00000000, 0x78d17492);
};

global.DisableIntermezzoCams = function () {
	return _in(0x00000000, 0x3da200cb);
};

global.DisableLocalPlayerPickups = function (disable) {
	return _in(0x00000000, 0x19211e9d, disable);
};

global.DisablePauseMenu = function (disabled) {
	return _in(0x00000000, 0x07ed1dbf, disabled);
};

global.DisablePlayerAutoVehicleExit = function (ped, disable) {
	return _in(0x00000000, 0x50e33e8f, ped, disable);
};

global.DisablePlayerLockon = function (playerIndex, disabled) {
	return _in(0x00000000, 0x711214f3, playerIndex, disabled);
};

global.DisablePlayerSprint = function (playerIndex, disabled) {
	return _in(0x00000000, 0x3a244927, playerIndex, disabled);
};

global.DisablePlayerVehicleEntry = function (player, disable) {
	return _in(0x00000000, 0x05d51783, player, disable);
};

global.DisablePoliceScanner = function () {
	return _in(0x00000000, 0x63af5057);
};

/**
 * Disables the specified `rawKeyIndex`, making it not trigger the regular `IS_RAW_KEY_*` natives.
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
global.DisableRawKeyThisFrame = function (rawKeyIndex) {
	return _in(0x00000000, 0x8bcf0014, rawKeyIndex, _r);
};

global.DisableStickyBombActiveSound = function (ped, disable) {
	return _in(0x00000000, 0x0c2d2cc5, ped, disable);
};

/**
 * Disables the game's world horizon lods rendering (see `farlods.#dd`).
 * Using the island hopper natives might also affect this state.
 * @param state On/Off
 */
global.DisableWorldhorizonRendering = function (state) {
	return _in(0x00000000, 0xa9c92cdc, state);
};

global.DisplayAltimeterThisFrame = function () {
	return _in(0x00000000, 0x50c13702);
};

global.DisplayAmmo = function (display) {
	return _in(0x00000000, 0x2e115b4b, display);
};

global.DisplayAreaName = function (display) {
	return _in(0x00000000, 0x1e87298a, display);
};

global.DisplayCash = function (display) {
	return _in(0x00000000, 0x62ed1551, display);
};

global.DisplayFrontendMapBlips = function (display) {
	return _in(0x00000000, 0x61b830bc, display);
};

global.DisplayGrimeThisFrame = function () {
	return _in(0x00000000, 0x56b95223);
};

global.DisplayHelpTextThisFrame = function (gxtkey, Unk636) {
	return _in(0x00000000, 0x071542eb, _ts(gxtkey), Unk636);
};

global.DisplayHud = function (display) {
	return _in(0x00000000, 0x52632919, display);
};

global.DisplayLoadingThisFrameWithScriptSprites = function () {
	return _in(0x00000000, 0x38a10933);
};

global.DisplayNonMinigameHelpMessages = function (Unk637) {
	return _in(0x00000000, 0x73f56ac5, Unk637);
};

global.DisplayNthOnscreenCounterWithString = function (Unk638, Unk639, Unk640, str) {
	return _in(0x00000000, 0x4d9c4195, Unk638, Unk639, Unk640, _ts(str));
};

global.DisplayOnscreenTimerWithString = function (timerid, Unk641, str) {
	return _in(0x00000000, 0x384f104f, timerid, Unk641, _ts(str));
};

global.DisplayPlayerNames = function (Unk910) {
	return _in(0x00000000, 0x0b177d76, Unk910);
};

global.DisplayRadar = function (display) {
	return _in(0x00000000, 0x17920fa7, display);
};

global.DisplaySniperScopeThisFrame = function () {
	return _in(0x00000000, 0x5bf23ad5);
};

global.DisplayText = function (x, y, gxtName) {
	return _in(0x00000000, 0x0f002557, _fv(x), _fv(y), _ts(gxtName));
};

global.DisplayTextSubstring = function (Unk642, Unk643, Unk644, Unk645, Unk646, Unk647, Unk648) {
	return _in(0x00000000, 0x0da61310, Unk642, Unk643, Unk644, Unk645, Unk646, Unk647, Unk648);
};

global.DisplayTextWithBlipName = function (x, y, str, blip) {
	return _in(0x00000000, 0x7e8d1dce, _fv(x), _fv(y), _ts(str), blip);
};

global.DisplayTextWithFloat = function (x, y, gxtName, value, unknown) {
	return _in(0x00000000, 0x311f4fe9, _fv(x), _fv(y), _ts(gxtName), _fv(value), unknown);
};

global.DisplayTextWithLiteralString = function (x, y, gxtName, literalStr) {
	return _in(0x00000000, 0x661b239a, _fv(x), _fv(y), _ts(gxtName), _ts(literalStr));
};

global.DisplayTextWithLiteralSubstring = function (Unk652, Unk653, Unk654, Unk655, Unk656, Unk657) {
	return _in(0x00000000, 0x1fcb5241, Unk652, Unk653, Unk654, Unk655, Unk656, Unk657);
};

global.DisplayTextWithNumber = function (x, y, gxtName, value) {
	return _in(0x00000000, 0x5a495abe, _fv(x), _fv(y), _ts(gxtName), value);
};

global.DisplayTextWithString = function (x, y, gxtName, gxtStringName) {
	return _in(0x00000000, 0x10a75905, _fv(x), _fv(y), _ts(gxtName), _ts(gxtStringName));
};

global.DisplayTextWithStringAndInt = function (x, y, gxtname, gxtnamenext, val) {
	return _in(0x00000000, 0x369a4540, _fv(x), _fv(y), _ts(gxtname), _ts(gxtnamenext), val);
};

global.DisplayTextWithSubstringGivenHashKey = function (x, y, gxtkey, gxtkey0) {
	return _in(0x00000000, 0x7ef6599d, _fv(x), _fv(y), _ts(gxtkey), gxtkey0);
};

global.DisplayTextWithTwoLiteralStrings = function (x, y, gxtName, literalStr1, literalStr2) {
	return _in(0x00000000, 0x4b7c3aec, _fv(x), _fv(y), _ts(gxtName), _ts(literalStr1), _ts(literalStr2));
};

global.DisplayTextWithTwoStrings = function (x, y, gxtName, gxtStringName1, gxtStringName2) {
	return _in(0x00000000, 0x66842574, _fv(x), _fv(y), _ts(gxtName), _ts(gxtStringName1), _ts(gxtStringName2));
};

global.DisplayTextWithTwoSubstringsGivenHashKeys = function (x, y, gxtkey, gxtkey0, gxtkey1) {
	return _in(0x00000000, 0x39e77f70, _fv(x), _fv(y), _ts(gxtkey), gxtkey0, gxtkey1);
};

global.DisplayTextWith_2Numbers = function (x, y, gxtName, number1, number2) {
	return _in(0x00000000, 0x337957af, _fv(x), _fv(y), _ts(gxtName), number1, number2);
};

global.DisplayTextWith_3Numbers = function (x, y, gxtentry, Unk649, Unk650, Unk651) {
	return _in(0x00000000, 0x746c06e8, _fv(x), _fv(y), _ts(gxtentry), Unk649, Unk650, Unk651);
};

global.DoAutoSave = function () {
	return _in(0x00000000, 0x09b85174);
};

global.DoScreenFadeIn = function (timeMS) {
	return _in(0x00000000, 0x04d72200, timeMS);
};

global.DoScreenFadeInUnhacked = function (timeMS) {
	return _in(0x00000000, 0x5f9218c3, timeMS);
};

global.DoScreenFadeOut = function (timeMS) {
	return _in(0x00000000, 0x65de621c, timeMS);
};

global.DoScreenFadeOutUnhacked = function (timeMS) {
	return _in(0x00000000, 0x42d250a7, timeMS);
};

global.DoesBlipExist = function (blip) {
	return _in(0x00000000, 0x590a6ff4, blip, _r);
};

global.DoesCamExist = function (camera) {
	return _in(0x00000000, 0x46953225, camera, _r);
};

global.DoesCarHaveHydraulics = function (car) {
	return _in(0x00000000, 0x0f0956ca, car, _r);
};

global.DoesCarHaveRoof = function (vehicle) {
	return _in(0x00000000, 0x7ae52512, vehicle, _r);
};

global.DoesCarHaveStuckCarCheck = function (vehicle) {
	return _in(0x00000000, 0x2b856faa, vehicle, _r);
};

global.DoesCharExist = function (ped) {
	return _in(0x00000000, 0x46531797, ped, _r);
};

global.DoesDecisionMakerExist = function (dm) {
	return _in(0x00000000, 0x66d53314, dm, _r);
};

global.DoesGameCodeWantToLeaveNetworkSession = function () {
	return _in(0x00000000, 0x7e412ac8, _r);
};

global.DoesGroupExist = function (group) {
	return _in(0x00000000, 0x3d385f6d, group, _r);
};

global.DoesObjectExist = function (obj) {
	return _in(0x00000000, 0x6dab78cd, obj, _r);
};

global.DoesObjectExistWithNetworkId = function (netid) {
	return _in(0x00000000, 0x5bbc62cb, netid, _r);
};

global.DoesObjectHavePhysics = function (obj) {
	return _in(0x00000000, 0x39587d51, obj, _r);
};

global.DoesObjectHaveThisModel = function (obj, model) {
	return _in(0x00000000, 0x7505765b, obj, model, _r);
};

global.DoesObjectOfTypeExistAtCoords = function (x, y, z, radius, model) {
	return _in(0x00000000, 0x1f881a88, _fv(x), _fv(y), _fv(z), _fv(radius), model, _r);
};

global.DoesPedExistWithNetworkId = function (netid) {
	return _in(0x00000000, 0x21641887, netid, _r);
};

global.DoesPickupExist = function (pickup) {
	return _in(0x00000000, 0x7b567f1a, pickup, _r);
};

global.DoesPlayerHaveControlOfNetworkId = function (player, id) {
	return _in(0x00000000, 0x3d0b5e56, player, id, _r);
};

global.DoesScenarioExistInArea = function (Unk104, Unk105, Unk106, Unk107, Unk108) {
	return _in(0x00000000, 0x48252e33, Unk104, Unk105, Unk106, Unk107, Unk108, _r);
};

global.DoesScriptExist = function (name) {
	return _in(0x00000000, 0x1d1b266b, _ts(name), _r);
};

global.DoesScriptFireExist = function (fire) {
	return _in(0x00000000, 0x637e1d42, fire, _r);
};

global.DoesTextLabelExist = function (gxtentry) {
	return _in(0x00000000, 0x2a611607, _ts(gxtentry), _r);
};

global.DoesThisMinigameScriptAllowNonMinigameHelpMessages = function () {
	return _in(0x00000000, 0x73a1443f, _r);
};

/**
 * DOES_TIMECYCLE_MODIFIER_HAS_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @return Whether or not variable by name was found on the specified timecycle modifier.
 */
global.DoesTimecycleModifierHasVar = function (modifierName, varName) {
	return _in(0x00000000, 0xc53bb6d3, _ts(modifierName), _ts(varName), _r);
};

global.DoesVehicleExist = function (vehicle) {
	return _in(0x00000000, 0x67a42263, vehicle, _r);
};

global.DoesVehicleExistWithNetworkId = function (nedid) {
	return _in(0x00000000, 0x69c033d8, nedid, _r);
};

global.DoesViewportExist = function (viewportid) {
	return _in(0x00000000, 0x0c5a551b, viewportid, _r);
};

global.DoesWebPageExist = function (webaddress) {
	return _in(0x00000000, 0x1de062fd, _ts(webaddress), _r);
};

global.DoesWidgetGroupExist = function (Unk1114) {
	return _in(0x00000000, 0x3aaf5be5, Unk1114, _r);
};

global.DontAbortCarConversations = function (flag0, flag1) {
	return _in(0x00000000, 0x0a432423, flag0, flag1);
};

global.DontDispatchCopsForPlayer = function (player, dont) {
	return _in(0x00000000, 0x63b87ebe, player, dont);
};

global.DontDisplayLoadingOnFadeThisFrame = function () {
	return _in(0x00000000, 0x2f58286c);
};

global.DontRemoveChar = function (ped) {
	return _in(0x00000000, 0x3659084a, ped);
};

global.DontRemoveObject = function (obj) {
	return _in(0x00000000, 0x74ff26f9, obj);
};

global.DontSuppressAnyCarModels = function () {
	return _in(0x00000000, 0x69f55dcc);
};

global.DontSuppressAnyPedModels = function () {
	return _in(0x00000000, 0x72ef466e);
};

global.DontSuppressCarModel = function (model) {
	return _in(0x00000000, 0x0348074b, model);
};

global.DontSuppressPedModel = function (model) {
	return _in(0x00000000, 0x7cf256d0, model);
};

/**
 * Returns a list of door system entries: a door system hash (see [ADD_DOOR_TO_SYSTEM](#\_0x6F8838D03D1DC226)) and its object handle.
 * The data returned adheres to the following layout:
 * ```
 * [{doorHash1, doorHandle1}, ..., {doorHashN, doorHandleN}]
 * ```
 * @return An object containing a list of door system entries.
 */
global.DoorSystemGetActive = function () {
	return _in(0x00000000, 0xf65bba4b, _r, _ro);
};

/**
 * DOOR_SYSTEM_GET_SIZE
 * @return The number of doors registered in the system
 */
global.DoorSystemGetSize = function () {
	return _in(0x00000000, 0x237613b3, _r, _ri);
};

global.DrawCheckpoint = function (x, y, z, radius, r, g, b) {
	return _in(0x00000000, 0x29fc3e19, _fv(x), _fv(y), _fv(z), _fv(radius), r, g, b);
};

global.DrawCheckpointWithAlpha = function (x, y, z, radius, r, g, b, a) {
	return _in(0x00000000, 0x26810be3, _fv(x), _fv(y), _fv(z), _fv(radius), r, g, b, a);
};

global.DrawColouredCylinder = function (x, y, z, Unk712, Unk713, r, g, b, a) {
	return _in(0x00000000, 0x309860c4, _fv(x), _fv(y), _fv(z), _fv(Unk712), _fv(Unk713), r, g, b, a);
};

global.DrawCorona = function (x, y, z, radius, Unk714, Unk715, Unk716, Unk717, Unk718) {
	return _in(0x00000000, 0x39ed0c43, _fv(x), _fv(y), _fv(z), _fv(radius), Unk714, Unk715, Unk716, Unk717, Unk718);
};

global.DrawCurvedWindow = function (Unk719, Unk720, Unk721, Unk722, alpha) {
	return _in(0x00000000, 0x4b684d0b, _fv(Unk719), _fv(Unk720), _fv(Unk721), _fv(Unk722), alpha);
};

global.DrawCurvedWindowNotext = function (Unk723, Unk724, Unk725, Unk726, Unk727) {
	return _in(0x00000000, 0x12b9197e, _fv(Unk723), _fv(Unk724), _fv(Unk725), _fv(Unk726), Unk727);
};

global.DrawCurvedWindowText = function (Unk728, Unk729, Unk730, Unk731, Unk732, str0, str1, Unk733) {
	return _in(0x00000000, 0x7dd67e15, _fv(Unk728), _fv(Unk729), _fv(Unk730), Unk731, Unk732, _ts(str0), _ts(str1), Unk733);
};

global.DrawDebugSphere = function (x, y, z, radius) {
	return _in(0x00000000, 0x539572f3, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.DrawFrontendHelperText = function (str0, str1, Unk734) {
	return _in(0x00000000, 0x44e14770, _ts(str0), _ts(str1), Unk734);
};

global.DrawLightWithRange = function (x, y, z, r, g, b, width, height) {
	return _in(0x00000000, 0x30d27eb1, _fv(x), _fv(y), _fv(z), r, g, b, _fv(width), _fv(height));
};

global.DrawMovie = function (Unk735, Unk736, Unk737, Unk738, Unk739, r, g, b, a) {
	return _in(0x00000000, 0x26274628, _fv(Unk735), _fv(Unk736), _fv(Unk737), _fv(Unk738), _fv(Unk739), r, g, b, a);
};

global.DrawRect = function (x1, y1, x2, y2, r, g, b, a) {
	return _in(0x00000000, 0x3b2526e3, _fv(x1), _fv(y1), _fv(x2), _fv(y2), r, g, b, a);
};

global.DrawSphere = function (x, y, z, radius) {
	return _in(0x00000000, 0x769f6e66, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.DrawSprite = function (texture, Unk740, Unk741, Unk742, Unk743, angle, r, g, b, a) {
	return _in(0x00000000, 0x6add40ec, texture, _fv(Unk740), _fv(Unk741), _fv(Unk742), _fv(Unk743), _fv(angle), r, g, b, a);
};

global.DrawSpriteFrontBuff = function (x0, y0, x1, y1, rotation, r, g, b, a) {
	return _in(0x00000000, 0x22417905, _fv(x0), _fv(y0), _fv(x1), _fv(y1), _fv(rotation), r, g, b, a);
};

global.DrawSpritePhoto = function (x0, y0, x1, y1, rotation, r, g, b, a) {
	return _in(0x00000000, 0x4bd4248e, _fv(x0), _fv(y0), _fv(x1), _fv(y1), _fv(rotation), r, g, b, a);
};

global.DrawSpriteWithFixedRotation = function (texture, Unk744, Unk745, Unk746, Unk747, angle, r, g, b, a) {
	return _in(0x00000000, 0x7cb404d4, texture, _fv(Unk744), _fv(Unk745), _fv(Unk746), _fv(Unk747), _fv(angle), r, g, b, a);
};

global.DrawSpriteWithUv = function (texture, Unk748, Unk749, Unk750, Unk751, angle, r, g, b, a) {
	return _in(0x00000000, 0x58c41e8f, texture, _fv(Unk748), _fv(Unk749), _fv(Unk750), _fv(Unk751), _fv(angle), r, g, b, a);
};

global.DrawSpriteWithUvCoords = function (texture, Unk752, Unk753, Unk754, Unk755, Unk756, Unk757, Unk758, Unk759, angle, r, g, b, a) {
	return _in(0x00000000, 0x2d1d17c9, texture, _fv(Unk752), _fv(Unk753), _fv(Unk754), _fv(Unk755), _fv(Unk756), _fv(Unk757), _fv(Unk758), _fv(Unk759), _fv(angle), r, g, b, a);
};

global.DrawToplevelSprite = function (texture, Unk760, Unk761, Unk762, Unk763, angle, r, g, b, a) {
	return _in(0x00000000, 0x1849408d, texture, _fv(Unk760), _fv(Unk761), _fv(Unk762), _fv(Unk763), _fv(angle), r, g, b, a);
};

global.DrawWindow = function (Unk764, Unk765, Unk766, Unk767, str, alpha) {
	return _in(0x00000000, 0x232642de, _fv(Unk764), _fv(Unk765), _fv(Unk766), _fv(Unk767), _ts(str), alpha);
};

global.DrawWindowText = function (Unk768, Unk769, Unk770, Unk771, str0, Unk772) {
	return _in(0x00000000, 0x3d0f5735, _fv(Unk768), _fv(Unk769), _fv(Unk770), Unk771, _ts(str0), Unk772);
};

global.DropObject = function (ped, unknownTrue) {
	return _in(0x00000000, 0x24c45d0d, ped, unknownTrue);
};

/**
 * DUPLICATE_FUNCTION_REFERENCE
 */
global.DuplicateFunctionReference = function (referenceIdentity) {
	return _in(0x00000000, 0xf4e2079d, _ts(referenceIdentity), _r, _s);
};

global.EnableAllPedHelmets = function (enable) {
	return _in(0x00000000, 0x6c305137, enable);
};

global.EnableCamCollision = function (cam, enable) {
	return _in(0x00000000, 0x71ae1bdc, cam, enable);
};

global.EnableChaseAudio = function (enable) {
	return _in(0x00000000, 0x68664078, enable);
};

global.EnableDebugCam = function (enable) {
	return _in(0x00000000, 0x296b09e8, enable);
};

global.EnableDeferredLighting = function (enable) {
	return _in(0x00000000, 0x6cfc30ad, enable);
};

global.EnableDisabledAttractorsOnObject = function (obj, enable) {
	return _in(0x00000000, 0x17f62193, obj, enable);
};

global.EnableEndCreditsFade = function () {
	return _in(0x00000000, 0x1ea85697);
};

global.EnableFancyWater = function (enable) {
	return _in(0x00000000, 0x74fc2325, enable);
};

global.EnableFovLodMultiplier = function (enable) {
	return _in(0x00000000, 0x556b0755, enable);
};

global.EnableFrontendRadio = function () {
	return _in(0x00000000, 0x5328068b);
};

global.EnableGpsInVehicle = function (veh, enable) {
	return _in(0x00000000, 0x144f3ce5, veh, enable);
};

global.EnableMaxAmmoCap = function (enable) {
	return _in(0x00000000, 0x7e657b56, enable);
};

global.EnablePedHelmet = function (ped, enable) {
	return _in(0x00000000, 0x0c704586, ped, enable);
};

global.EnablePoliceScanner = function () {
	return _in(0x00000000, 0x5b262142);
};

global.EnableSaveHouse = function (savehouse, enable) {
	return _in(0x00000000, 0x208c03c9, savehouse, enable);
};

global.EnableSceneStreaming = function (enable) {
	return _in(0x00000000, 0x362b7d1b, enable);
};

global.EnableScriptControlledMicrophone = function () {
	return _in(0x00000000, 0x3ea0648d, _r);
};

global.EnableShadows = function (enable) {
	return _in(0x00000000, 0x41596b09, enable);
};

global.EndCamCommands = function (Unk558) {
	return _in(0x00000000, 0x627f3275, _ii(Unk558) /* may be optional */);
};

global.EndCharSearchCriteria = function () {
	return _in(0x00000000, 0x5ecf404a);
};

/**
 * END_FIND_KVP
 * @param handle The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
 * @return None.
 */
global.EndFindKvp = function (handle) {
	return _in(0x00000000, 0xb3210203, handle);
};

/**
 * END_FIND_OBJECT
 */
global.EndFindObject = function (findHandle) {
	return _in(0x00000000, 0xdeda4e50, findHandle);
};

/**
 * END_FIND_PED
 */
global.EndFindPed = function (findHandle) {
	return _in(0x00000000, 0x9615c2ad, findHandle);
};

/**
 * END_FIND_PICKUP
 */
global.EndFindPickup = function (findHandle) {
	return _in(0x00000000, 0x3c407d53, findHandle);
};

/**
 * END_FIND_VEHICLE
 */
global.EndFindVehicle = function (findHandle) {
	return _in(0x00000000, 0x9227415a, findHandle);
};

global.EndWidgetGroup = function () {
	return _in(0x00000000, 0x6f760759);
};

/**
 * Internal function for ensuring an entity has a state bag.
 */
global.EnsureEntityStateBag = function (entity) {
	return _in(0x00000000, 0x3bb78f05, entity);
};

global.EvolvePtfx = function (ptfx, evolvetype, val) {
	return _in(0x00000000, 0x3ce05e7c, ptfx, _ts(evolvetype), _fv(val));
};

/**
 * Depending on your use case you may need to use `add_acl resource.<your_resource_name> command.<command_name> allow` to use this native in your resource.
 */
global.ExecuteCommand = function (commandString) {
	return _in(0x00000000, 0x561c060b, _ts(commandString));
};

global.Exp = function (Unk1084) {
	return _in(0x00000000, 0x1ba61e20, _fv(Unk1084), _r, _rf);
};

/**
 * This native is not implemented.
 */
global.ExperimentalLoadCloneCreate = function (data, objectId, tree) {
	return _in(0x00000000, 0xd2cb95a3, _ts(data), objectId, _ts(tree), _r, _ri);
};

/**
 * This native is not implemented.
 */
global.ExperimentalLoadCloneSync = function (entity, data) {
	return _in(0x00000000, 0x6bc189ac, entity, _ts(data));
};

/**
 * This native is not implemented.
 */
global.ExperimentalSaveCloneCreate = function (entity) {
	return _in(0x00000000, 0x9d65cad2, entity, _r, _s);
};

/**
 * This native is not implemented.
 */
global.ExperimentalSaveCloneSync = function (entity) {
	return _in(0x00000000, 0x38d19210, entity, _r, _s);
};

global.ExplodeCar = function (vehicle, unknownTrue, unknownFalse) {
	return _in(0x00000000, 0x505518a2, vehicle, unknownTrue, unknownFalse);
};

global.ExplodeCarInCutscene = function (car, explode) {
	return _in(0x00000000, 0x01820daa, car, explode);
};

global.ExplodeCarInCutsceneShakeAndBit = function (car, flag0, flag1, flag2) {
	return _in(0x00000000, 0x7cf61a81, car, flag0, flag1, flag2);
};

global.ExplodeCharHead = function (ped) {
	return _in(0x00000000, 0x4a802e89, ped);
};

global.ExtendPatrolRoute = function (Unk484, Unk485, Unk486, Unk487, Unk488) {
	return _in(0x00000000, 0x0f3402b8, Unk484, Unk485, Unk486, Unk487, Unk488);
};

global.ExtinguishCarFire = function (vehicle) {
	return _in(0x00000000, 0x63a40f58, vehicle);
};

global.ExtinguishCharFire = function (ped) {
	return _in(0x00000000, 0x5d786eee, ped);
};

global.ExtinguishFireAtPoint = function (x, y, z, radius) {
	return _in(0x00000000, 0x35a97b73, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.ExtinguishObjectFire = function (obj) {
	return _in(0x00000000, 0x5fbc5fff, obj);
};

global.FailKillFrenzy = function () {
	return _in(0x00000000, 0x5ea253a5);
};

global.FakeDeatharrest = function () {
	return _in(0x00000000, 0x30d17655);
};

/**
 * FIND_FIRST_OBJECT
 */
global.FindFirstObject = function (outEntity) {
	return _in(0x00000000, 0xfaa6cb5d, _ii(outEntity) /* may be optional */, _r, _ri);
};

/**
 * FIND_FIRST_PED
 */
global.FindFirstPed = function (outEntity) {
	return _in(0x00000000, 0xfb012961, _ii(outEntity) /* may be optional */, _r, _ri);
};

/**
 * FIND_FIRST_PICKUP
 */
global.FindFirstPickup = function (outEntity) {
	return _in(0x00000000, 0x3ff9d340, _ii(outEntity) /* may be optional */, _r, _ri);
};

/**
 * FIND_FIRST_VEHICLE
 */
global.FindFirstVehicle = function (outEntity) {
	return _in(0x00000000, 0x15e55694, _ii(outEntity) /* may be optional */, _r, _ri);
};

/**
 * FIND_KVP
 * @param handle The KVP find handle returned from [START_FIND_KVP](#\_0xDD379006)
 * @return None.
 */
global.FindKvp = function (handle) {
	return _in(0x00000000, 0xbd7bebc5, handle, _r, _s);
};

global.FindMaxNumberOfGroupMembers = function () {
	return _in(0x00000000, 0x7e154274, _r, _ri);
};

global.FindNearestCollectableBinBags = function (x, y, z) {
	return _in(0x00000000, 0x056314a9, _fv(x), _fv(y), _fv(z));
};

global.FindNearestEntitiesWithSpecialAttribute = function (x, y, z) {
	return _in(0x00000000, 0x035261c6, _fv(x), _fv(y), _fv(z));
};

global.FindNetworkKillerOfPlayer = function (playerIndex) {
	return _in(0x00000000, 0x766e78a3, playerIndex, _r, _ri);
};

global.FindNetworkRestartPoint = function (Unk911, Unk912, Unk913) {
	return _in(0x00000000, 0x66f445bb, Unk911, Unk912, Unk913);
};

/**
 * FIND_NEXT_OBJECT
 */
global.FindNextObject = function (findHandle, outEntity) {
	return _in(0x00000000, 0x4e129dbf, findHandle, _ii(outEntity) /* may be optional */, _r);
};

/**
 * FIND_NEXT_PED
 */
global.FindNextPed = function (findHandle, outEntity) {
	return _in(0x00000000, 0xab09b548, findHandle, _ii(outEntity) /* may be optional */, _r);
};

/**
 * FIND_NEXT_PICKUP
 */
global.FindNextPickup = function (findHandle, outEntity) {
	return _in(0x00000000, 0x4107ef0f, findHandle, _ii(outEntity) /* may be optional */, _r);
};

/**
 * FIND_NEXT_VEHICLE
 */
global.FindNextVehicle = function (findHandle, outEntity) {
	return _in(0x00000000, 0x8839120d, findHandle, _ii(outEntity) /* may be optional */, _r);
};

global.FindPositionInRecording = function (car) {
	return _in(0x00000000, 0x22087f31, car, _r, _rf);
};

global.FindPrimaryPopulationZoneGroup = function () {
	return _in(0x00000000, 0x36601178, _i, _i);
};

global.FindStaticEmitterIndex = function (StaticEmitterName) {
	return _in(0x00000000, 0x64793a54, _ts(StaticEmitterName), _r, _ri);
};

global.FindStreetNameAtPosition = function (pX, pY, pZ) {
	return _in(0x00000000, 0x49763a4f, _fv(pX), _fv(pY), _fv(pZ), _i, _i);
};

global.FindTimePositionInRecording = function (car) {
	return _in(0x00000000, 0x08d25912, car, _r, _rf);
};

global.FindTrainDirection = function (train) {
	return _in(0x00000000, 0x013c1eb7, train, _r, _ri);
};

global.FinishStreamingRequestList = function () {
	return _in(0x00000000, 0x1788346e);
};

global.FinishWidgetCombo = function (Unk1112, Unk1113) {
	return _in(0x00000000, 0x2cca0d6a, Unk1112, Unk1113);
};

global.FirePedWeapon = function (ped, x, y, z) {
	return _in(0x00000000, 0x25bb7d67, ped, _fv(x), _fv(y), _fv(z));
};

global.FireSingleBullet = function (x, y, z, targetX, targetY, targetZ, unknown) {
	return _in(0x00000000, 0x30975326, _fv(x), _fv(y), _fv(z), _fv(targetX), _fv(targetY), _fv(targetZ), unknown);
};

global.FixAmbienceOrientation = function (fix) {
	return _in(0x00000000, 0x788f7a03, fix);
};

global.FixCar = function (vehicle) {
	return _in(0x00000000, 0x3d562f78, vehicle);
};

global.FixCarTyre = function (vehicle, tyre) {
	return _in(0x00000000, 0x0fda7965, vehicle, tyre);
};

global.FixScriptMicToCurrentPosisition = function () {
	return _in(0x00000000, 0x456c0c43);
};

global.FlashBlip = function (blip, on) {
	return _in(0x00000000, 0x4dfe09d6, blip, on);
};

global.FlashBlipAlt = function (blip, on) {
	return _in(0x00000000, 0x611948a3, blip, on);
};

global.FlashRadar = function (flash) {
	return _in(0x00000000, 0x265f6ff5, flash);
};

global.FlashRoute = function (flash) {
	return _in(0x00000000, 0x20e74a9c, flash);
};

global.FlashWeaponIcon = function (on) {
	return _in(0x00000000, 0x796a6b88, on);
};

global.Floor = function (value) {
	return _in(0x00000000, 0x49261ba6, _fv(value), _r, _ri);
};

global.FlushAllOutOfDateRadarBlipsFromMissionCleanupList = function () {
	return _in(0x00000000, 0x1f1c77e1);
};

global.FlushAllPlayerRespawnCoords = function () {
	return _in(0x00000000, 0x187b3202);
};

global.FlushAllSpawnBlockingAreas = function () {
	return _in(0x00000000, 0x65b05f3f);
};

global.FlushCoverBlockingAreas = function () {
	return _in(0x00000000, 0x5a535133);
};

global.FlushPatrolRoute = function () {
	return _in(0x00000000, 0x015f4f3e);
};

global.FlushScenarioBlockingAreas = function () {
	return _in(0x00000000, 0x754d0fc4);
};

global.ForceAirDragMultForPlayersCar = function (player, multiplier) {
	return _in(0x00000000, 0x554053ed, player, _fv(multiplier));
};

global.ForceAllVehicleLightsOff = function (off) {
	return _in(0x00000000, 0x0ce96445, off);
};

global.ForceCarLights = function (car, lights) {
	return _in(0x00000000, 0x71b81de7, car, lights);
};

global.ForceCharToDropWeapon = function (ped) {
	return _in(0x00000000, 0x214c5455, ped);
};

global.ForceFullVoice = function (ped) {
	return _in(0x00000000, 0x62285cad, ped);
};

global.ForceGameTelescopeCam = function (force) {
	return _in(0x00000000, 0x01c51e90, force);
};

global.ForceGenerateParkedCarsTooCloseToOthers = function (set) {
	return _in(0x00000000, 0x1b8f031d, set);
};

global.ForceHighLod = function (force) {
	return _in(0x00000000, 0x1efb0992, force);
};

global.ForceInitialPlayerStation = function (stationName) {
	return _in(0x00000000, 0x32d3165d, _ts(stationName));
};

global.ForceInteriorLightingForPlayer = function (player, force) {
	return _in(0x00000000, 0x45df1d92, player, force);
};

global.ForceLoadingScreen = function (force) {
	return _in(0x00000000, 0x4e68316c, force);
};

global.ForceNoCamPause = function (foce) {
	return _in(0x00000000, 0x2cc70e04, foce);
};

global.ForceNoiseOff = function (off) {
	return _in(0x00000000, 0x0cc0186a, off);
};

global.ForcePedPinnedDown = function (ped, force, timerMaybe) {
	return _in(0x00000000, 0x56a70f57, ped, force, timerMaybe);
};

global.ForcePedToFleeWhilstDrivingVehicle = function (ped, vehicle) {
	return _in(0x00000000, 0x2fed14f5, ped, vehicle);
};

global.ForcePedToLoadCover = function (ped, force) {
	return _in(0x00000000, 0x61d07789, ped, force);
};

global.ForcePopulationInit = function () {
	return _in(0x00000000, 0x42180729);
};

global.ForceRadioTrack = function (radiostation, trackname, Unk533, Unk534) {
	return _in(0x00000000, 0x6a7e47c9, _ts(radiostation), _ts(trackname), Unk533, Unk534);
};

global.ForceRandomCarModel = function (hash) {
	return _in(0x00000000, 0x521d0d5b, hash);
};

global.ForceRandomPedType = function (type) {
	return _in(0x00000000, 0x57e37103, type);
};

global.ForceSpawnScenarioPedsInArea = function (x, y, z, radius, Unk41) {
	return _in(0x00000000, 0x186d42a4, _fv(x), _fv(y), _fv(z), _fv(radius), Unk41);
};

global.ForceTimeOfDay = function (hour, minute) {
	return _in(0x00000000, 0x0b9b5070, hour, minute);
};

global.ForceWeather = function (weather) {
	return _in(0x00000000, 0x7efb5077, weather);
};

global.ForceWeatherNow = function (weather) {
	return _in(0x00000000, 0x63737d31, weather);
};

global.ForceWind = function (wind) {
	return _in(0x00000000, 0x310e75c9, _fv(wind));
};

/**
 * An internal function for converting a stack trace object to a string.
 */
global.FormatStackTrace = function (traceData) {
	return _in(0x00000000, 0xd70c3bca, ...(_obj(traceData)), _r, _s);
};

global.ForwardToTimeOfDay = function (hour, minute) {
	return _in(0x00000000, 0x456c6096, hour, minute);
};

global.FreezeCarPosition = function (vehicle, frozen) {
	return _in(0x00000000, 0x295c4c52, vehicle, frozen);
};

global.FreezeCarPositionAndDontLoadCollision = function (vehicle, frozen) {
	return _in(0x00000000, 0x588a27fb, vehicle, frozen);
};

global.FreezeCharPosition = function (ped, frozen) {
	return _in(0x00000000, 0x20266a86, ped, frozen);
};

global.FreezeCharPositionAndDontLoadCollision = function (ped, frozen) {
	return _in(0x00000000, 0x74576e37, ped, frozen);
};

global.FreezeObjectPosition = function (obj, set) {
	return _in(0x00000000, 0x7ca8382b, obj, set);
};

global.FreezeObjectPosition = function (obj, frozen) {
	return _in(0x00000000, 0x7ca8382b, obj, frozen);
};

global.FreezeObjectPositionAndDontLoadCollision = function (obj, freeze) {
	return _in(0x00000000, 0x668f64c7, obj, freeze);
};

global.FreezeOnscreenTimer = function (freeze) {
	return _in(0x00000000, 0x4b8b6f24, freeze);
};

global.FreezePositionOfClosestObjectOfType = function (x, y, z, radius, model, frozen) {
	return _in(0x00000000, 0x5a196b79, _fv(x), _fv(y), _fv(z), _fv(radius), model, frozen);
};

global.FreezeRadioStation = function (stationName) {
	return _in(0x00000000, 0x08a015cf, _ts(stationName));
};

global.GenerateDirections = function (x, y, z) {
	return _in(0x00000000, 0x203a137b, _fv(x), _fv(y), _fv(z), _i, _v);
};

global.GenerateRandomFloat = function (Unk1086) {
	return _in(0x00000000, 0x380c142a, _fi(Unk1086) /* may be optional */);
};

global.GenerateRandomFloatInRange = function (min, max, pValue) {
	return _in(0x00000000, 0x74c626eb, _fv(min), _fv(max), _fi(pValue) /* may be optional */);
};

global.GenerateRandomInt = function (Unk1087) {
	return _in(0x00000000, 0x335d0f34, _ii(Unk1087) /* may be optional */);
};

global.GenerateRandomIntInRange = function (min, max, pValue) {
	return _in(0x00000000, 0x168b1717, min, max, _ii(pValue) /* may be optional */);
};

global.GetAcceptButton = function () {
	return _in(0x00000000, 0x530f4572, _r, _ri);
};

/**
 * Returns all player indices for 'active' physical players known to the client.
 * The data returned adheres to the following layout:
 * ```
 * [127, 42, 13, 37]
 * ```
 * @return An object containing a list of player indices.
 */
global.GetActivePlayers = function () {
	return _in(0x00000000, 0xcf143fb9, _r, _ro);
};

global.GetAmmoInCharWeapon = function (ped, weapon, pAmmo) {
	return _in(0x00000000, 0x23e140a9, ped, weapon, _ii(pAmmo) /* may be optional */);
};

global.GetAmmoInClip = function (ped, weapon, pAmmo) {
	return _in(0x00000000, 0x612c748f, ped, weapon, _ii(pAmmo) /* may be optional */, _r);
};

global.GetAngleBetween_2dVectors = function (x1, y1, x2, y2, pResult) {
	return _in(0x00000000, 0x5bc4602d, _fv(x1), _fv(y1), _fv(x2), _fv(y2), _fi(pResult) /* may be optional */);
};

global.GetAnimGroupFromChar = function (ped) {
	return _in(0x00000000, 0x55eb748f, ped, _r, _s);
};

global.GetAsciiJustPressed = function (key, Unk830) {
	return _in(0x00000000, 0x092829d0, key, Unk830, _r, _ri);
};

global.GetAsciiPressed = function (key, Unk820) {
	return _in(0x00000000, 0x495f399d, key, _ii(Unk820) /* may be optional */, _r);
};

global.GetAspectRatio = function () {
	return _in(0x00000000, 0x36600272, _r, _rf);
};

global.GetAudibleMusicTrackTextId = function () {
	return _in(0x00000000, 0x18246ac8, _r, _ri);
};

global.GetAudioRoomId = function () {
	return _in(0x00000000, 0x03ac3097, _r, _ri);
};

global.GetBitsInRange = function (val, rangebegin, rangeend) {
	return _in(0x00000000, 0x58ae7c1d, val, rangebegin, rangeend, _r, _ri);
};

global.GetBlipAlpha = function (blip, alpha) {
	return _in(0x00000000, 0x61497585, blip, _ii(alpha) /* may be optional */);
};

global.GetBlipColour = function (blip, pColour) {
	return _in(0x00000000, 0x59b425da, blip, _ii(pColour) /* may be optional */);
};

global.GetBlipCoords = function (blip, pVector) {
	return _in(0x00000000, 0x4c1e75db, blip, _v);
};

global.GetBlipInfoIdCarIndex = function (blip) {
	return _in(0x00000000, 0x566d04c2, blip, _r, _ri);
};

global.GetBlipInfoIdDisplay = function (blip) {
	return _in(0x00000000, 0x1b731c3f, blip, _r, _ri);
};

global.GetBlipInfoIdObjectIndex = function (blip) {
	return _in(0x00000000, 0x7b05072c, blip, _r, _ri);
};

global.GetBlipInfoIdPedIndex = function (blip) {
	return _in(0x00000000, 0x5fd47b45, blip, _r, _ri);
};

global.GetBlipInfoIdPickupIndex = function (blip) {
	return _in(0x00000000, 0x059e3beb, blip, _r, _ri);
};

global.GetBlipInfoIdRotation = function (blip) {
	return _in(0x00000000, 0x6fba4274, blip, _r, _ri);
};

global.GetBlipInfoIdType = function (blip) {
	return _in(0x00000000, 0x6a9e5ce5, blip, _r, _ri);
};

global.GetBlipSprite = function (blip) {
	return _in(0x00000000, 0x30b1316b, blip, _r, _ri);
};

global.GetBufferedAscii = function (key, Unk821) {
	return _in(0x00000000, 0x21f43531, key, _ii(Unk821) /* may be optional */, _r);
};

global.GetCamFarClip = function (cam, clip) {
	return _in(0x00000000, 0x752643c9, cam, _fi(clip) /* may be optional */);
};

global.GetCamFarDof = function (cam, fardof) {
	return _in(0x00000000, 0x1cb27fe1, cam, _fi(fardof) /* may be optional */);
};

global.GetCamFov = function (camera, fov) {
	return _in(0x00000000, 0x7bf4652d, camera, _fi(fov) /* may be optional */);
};

/**
 * Returns the world matrix of the specified camera. To turn this into a view matrix, calculate the inverse.
 */
global.GetCamMatrix = function (camera) {
	return _in(0x00000000, 0x8f57a89d, camera, _v, _v, _v, _v);
};

global.GetCamMotionBlur = function (cam, blur) {
	return _in(0x00000000, 0x64ef411d, cam, _ii(blur) /* may be optional */);
};

global.GetCamNearClip = function (cam, clip) {
	return _in(0x00000000, 0x2ef477fd, cam, _fi(clip) /* may be optional */);
};

global.GetCamNearDof = function (cam, dof) {
	return _in(0x00000000, 0x50d15f0d, cam, _fi(dof) /* may be optional */);
};

global.GetCamPos = function (camera) {
	return _in(0x00000000, 0x60c22e93, camera, _f, _f, _f);
};

global.GetCamRot = function (camera) {
	return _in(0x00000000, 0x51a06698, camera, _f, _f, _f);
};

global.GetCamState = function (cam) {
	return _in(0x00000000, 0x22aa0984, cam, _r, _ri);
};

global.GetCameraFromNetworkId = function (ned_id, cam) {
	return _in(0x00000000, 0x7e656e50, ned_id, _ii(cam) /* may be optional */);
};

global.GetCarAnimCurrentTime = function (car, animname0, animname1, time) {
	return _in(0x00000000, 0x5b580dcc, car, _ts(animname0), _ts(animname1), _fi(time) /* may be optional */);
};

global.GetCarAnimTotalTime = function (car, animname0, animname1, time) {
	return _in(0x00000000, 0x295c34b8, car, _ts(animname0), _ts(animname1), _fi(time) /* may be optional */);
};

global.GetCarBlockingCar = function (car0, car1) {
	return _in(0x00000000, 0x66b43b06, car0, _ii(car1) /* may be optional */);
};

global.GetCarCharIsUsing = function (ped, pVehicle) {
	return _in(0x00000000, 0x1b067237, ped, _ii(pVehicle) /* may be optional */);
};

global.GetCarColours = function (vehicle) {
	return _in(0x00000000, 0x6cac3d62, vehicle, _i, _i);
};

global.GetCarCoordinates = function (vehicle) {
	return _in(0x00000000, 0x2d432eab, vehicle, _f, _f, _f);
};

global.GetCarDeformationAtPos = function (vehicle, x, y, z, pDeformation) {
	return _in(0x00000000, 0x1f913bc7, vehicle, _fv(x), _fv(y), _fv(z), _v);
};

global.GetCarDoorLockStatus = function (vehicle, pValue) {
	return _in(0x00000000, 0x774426c2, vehicle, _ii(pValue) /* may be optional */);
};

global.GetCarForwardVector = function (car, vec) {
	return _in(0x00000000, 0x7e4f49b5, car, _v);
};

global.GetCarForwardX = function (vehicle, pValue) {
	return _in(0x00000000, 0x47a21100, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarForwardY = function (vehicle, pValue) {
	return _in(0x00000000, 0x3bdb4496, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarHeading = function (vehicle, pValue) {
	return _in(0x00000000, 0x46803cfa, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarHealth = function (vehicle, pValue) {
	return _in(0x00000000, 0x4d417cd3, vehicle, _ii(pValue) /* may be optional */);
};

global.GetCarLivery = function (car, livery) {
	return _in(0x00000000, 0x10237666, car, _ii(livery) /* may be optional */);
};

global.GetCarMass = function (car, mass) {
	return _in(0x00000000, 0x5d7c4f08, car, _fi(mass) /* may be optional */);
};

global.GetCarModel = function (vehicle, pValue) {
	return _in(0x00000000, 0x5ff84497, vehicle, _ii(pValue) /* may be optional */);
};

global.GetCarModelValue = function (car, value) {
	return _in(0x00000000, 0x29d37792, car, _ii(value) /* may be optional */);
};

global.GetCarObjectIsAttachedTo = function (obj) {
	return _in(0x00000000, 0x2d215414, obj, _r, _ri);
};

global.GetCarPitch = function (vehicle, pValue) {
	return _in(0x00000000, 0x61ee5c9a, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarRoll = function (vehicle, pValue) {
	return _in(0x00000000, 0x09c95a65, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarSirenHealth = function (car) {
	return _in(0x00000000, 0x0896249a, car, _r, _ri);
};

global.GetCarSpeed = function (vehicle, pValue) {
	return _in(0x00000000, 0x16dd2d00, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCarSpeedVector = function (vehicle, unknownFalse) {
	return _in(0x00000000, 0x112e7fb1, vehicle, _v, unknownFalse);
};

global.GetCarUprightValue = function (vehicle, pValue) {
	return _in(0x00000000, 0x326e2886, vehicle, _fi(pValue) /* may be optional */);
};

global.GetCellphoneRanked = function () {
	return _in(0x00000000, 0x6b6019db, _r);
};

global.GetCharAllowedToRunOnBoats = function (ped) {
	return _in(0x00000000, 0x4c872a85, ped, _r);
};

global.GetCharAnimBlendAmount = function (ped, AnimName0, AnimName1, amount) {
	return _in(0x00000000, 0x1de37a21, ped, _ts(AnimName0), _ts(AnimName1), _fi(amount) /* may be optional */);
};

global.GetCharAnimCurrentTime = function (ped, animGroup, animName, pValue) {
	return _in(0x00000000, 0x555d3b8c, ped, _ts(animGroup), _ts(animName), _fi(pValue) /* may be optional */);
};

global.GetCharAnimIsEvent = function (ped, AnimName0, AnimName1, flag) {
	return _in(0x00000000, 0x118174ec, ped, _ts(AnimName0), _ts(AnimName1), flag, _r);
};

global.GetCharAnimTotalTime = function (ped, animGroup, animName, pValue) {
	return _in(0x00000000, 0x2e51318f, ped, _ts(animGroup), _ts(animName), _fi(pValue) /* may be optional */);
};

global.GetCharArmour = function (ped, pArmour) {
	return _in(0x00000000, 0x3c756e54, ped, _ii(pArmour) /* may be optional */);
};

global.GetCharCoordinates = function (ped) {
	return _in(0x00000000, 0x2b5c06e6, ped, _f, _f, _f);
};

global.GetCharDrawableVariation = function (ped, component) {
	return _in(0x00000000, 0x1a1a6d83, ped, component, _r, _ri);
};

global.GetCharExtractedDisplacement = function (ped, unknown) {
	return _in(0x00000000, 0x466b5aa0, ped, unknown, _f, _f, _f);
};

global.GetCharExtractedVelocity = function (ped, Unk5) {
	return _in(0x00000000, 0x7b3f0058, ped, Unk5, _f, _f, _f);
};

global.GetCharGravity = function (ped) {
	return _in(0x00000000, 0x746e7171, ped, _r, _ri);
};

global.GetCharHeading = function (ped, pValue) {
	return _in(0x00000000, 0x057a3ac7, ped, _fi(pValue) /* may be optional */);
};

global.GetCharHealth = function (ped, pHealth) {
	return _in(0x00000000, 0x4b6c2256, ped, _ii(pHealth) /* may be optional */);
};

global.GetCharHeightAboveGround = function (ped, pValue) {
	return _in(0x00000000, 0x79973c5a, ped, _fi(pValue) /* may be optional */);
};

global.GetCharHighestPriorityEvent = function (ped, event) {
	return _in(0x00000000, 0x061a75d3, ped, _ii(event) /* may be optional */);
};

global.GetCharInCarPassengerSeat = function (vehicle, seatIndex, pPed) {
	return _in(0x00000000, 0x5e756b51, vehicle, seatIndex, _ii(pPed) /* may be optional */);
};

global.GetCharLastDamageBone = function (ped, pBone) {
	return _in(0x00000000, 0x767e5013, ped, _ii(pBone) /* may be optional */, _r, _ri);
};

global.GetCharMaxMoveBlendRatio = function (ped) {
	return _in(0x00000000, 0x54ae4f4b, ped, _r, _rf);
};

global.GetCharMeleeActionFlag0 = function (ped) {
	return _in(0x00000000, 0x103f14e4, ped, _r);
};

global.GetCharMeleeActionFlag1 = function (ped) {
	return _in(0x00000000, 0x08a308f8, ped, _r);
};

global.GetCharMeleeActionFlag2 = function (ped) {
	return _in(0x00000000, 0x032f729b, ped, _r);
};

global.GetCharModel = function (ped, pModel) {
	return _in(0x00000000, 0x0a3d60ce, ped, _ii(pModel) /* may be optional */);
};

global.GetCharMoney = function (ped) {
	return _in(0x00000000, 0x7d675993, ped, _r, _ri);
};

global.GetCharMoveAnimSpeedMultiplier = function (ped, multiplier) {
	return _in(0x00000000, 0x325b1a34, ped, _fi(multiplier) /* may be optional */);
};

global.GetCharMovementAnimsBlocked = function (ped) {
	return _in(0x00000000, 0x11292c09, ped, _r);
};

global.GetCharPropIndex = function (ped, unknown, pIndex) {
	return _in(0x00000000, 0x3ac85db1, ped, unknown, _ii(pIndex) /* may be optional */);
};

global.GetCharReadyToBeExecuted = function (ped) {
	return _in(0x00000000, 0x3fff4de9, ped, _r);
};

global.GetCharReadyToBeStunned = function (ped) {
	return _in(0x00000000, 0x5c422066, ped, _r);
};

global.GetCharSpeed = function (ped, pValue) {
	return _in(0x00000000, 0x3e156afc, ped, _fi(pValue) /* may be optional */);
};

global.GetCharSwimState = function (ped, state) {
	return _in(0x00000000, 0x34460dd7, ped, _ii(state) /* may be optional */, _r);
};

global.GetCharTextureVariation = function (ped, component) {
	return _in(0x00000000, 0x3a7b78c5, ped, component, _r, _ri);
};

global.GetCharVelocity = function (ped) {
	return _in(0x00000000, 0x3b977fd4, ped, _f, _f, _f);
};

global.GetCharWalkAlongsideLeaderWhenAppropriate = function (ped) {
	return _in(0x00000000, 0x6d170b31, ped, _r);
};

global.GetCharWeaponInSlot = function (ped, slot) {
	return _in(0x00000000, 0x74ec7580, ped, slot, _i, _i, _i);
};

global.GetCharWillCowerInsteadOfFleeing = function (ped) {
	return _in(0x00000000, 0x69a52c96, ped, _r);
};

global.GetCharWillTryToLeaveBoatAfterLeader = function (ped) {
	return _in(0x00000000, 0x6d5f1592, ped, _r);
};

global.GetCharWillTryToLeaveWater = function (ped) {
	return _in(0x00000000, 0x7bc85e73, ped, _r);
};

global.GetCinematicCam = function (cam) {
	return _in(0x00000000, 0x00c87fb8, _ii(cam) /* may be optional */);
};

global.GetClosestCar = function (x, y, z, radius, unknownFalse, unknown70) {
	return _in(0x00000000, 0x2cb303f8, _fv(x), _fv(y), _fv(z), _fv(radius), unknownFalse, unknown70, _r, _ri);
};

global.GetClosestCarNode = function (x, y, z) {
	return _in(0x00000000, 0x27f87222, _fv(x), _fv(y), _fv(z), _f, _f, _f, _r);
};

global.GetClosestCarNodeFavourDirection = function (Unk802, x, y, z) {
	return _in(0x00000000, 0x2f2405d1, Unk802, _fv(x), _fv(y), _fv(z), _f, _f, _f, _f, _r);
};

global.GetClosestCarNodeWithHeading = function (x, y, z) {
	return _in(0x00000000, 0x371467e0, _fv(x), _fv(y), _fv(z), _f, _f, _f, _f, _r);
};

global.GetClosestChar = function (x, y, z, radius, unknown1, unknown2, pPed) {
	return _in(0x00000000, 0x0f4b0239, _fv(x), _fv(y), _fv(z), _fv(radius), unknown1, unknown2, _ii(pPed) /* may be optional */, _r);
};

global.GetClosestMajorCarNode = function (x, y, z) {
	return _in(0x00000000, 0x406a035e, _fv(x), _fv(y), _fv(z), _f, _f, _f, _r);
};

global.GetClosestNetworkRestartNode = function (Unk1008, Unk1009, Unk1010, Unk1011, Unk1012) {
	return _in(0x00000000, 0x46cd1d73, Unk1008, Unk1009, Unk1010, Unk1011, Unk1012, _r, _ri);
};

global.GetClosestRoad = function (x, y, z, Unk803, Unk804) {
	return _in(0x00000000, 0x63c00de7, _fv(x), _fv(y), _fv(z), _fv(Unk803), Unk804, _v, _v, _f, _f, _f, _r);
};

global.GetClosestStealableObject = function (x, y, z, radius, obj) {
	return _in(0x00000000, 0x27045521, _fv(x), _fv(y), _fv(z), _fv(radius), _ii(obj) /* may be optional */);
};

global.GetConsoleCommandToken = function () {
	return _in(0x00000000, 0x5d607947, _r, _ri);
};

global.GetContentsOfTextWidget = function (Unk1090) {
	return _in(0x00000000, 0x742e3376, Unk1090, _r, _ri);
};

global.GetControlValue = function (Unk831, controlid) {
	return _in(0x00000000, 0x06285788, Unk831, controlid, _r, _ri);
};

/**
 * Can be used to get a console variable of type `char*`, for example a string.
 * @param varName The console variable to look up.
 * @param default_ The default value to set if none is found.
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
global.GetConvar = function (varName, default_) {
	return _in(0x00000000, 0x6ccd2564, _ts(varName), _ts(default_), _r, _s);
};

/**
 * Can be used to get a console variable casted back to `bool`.
 * @param varName The console variable to look up.
 * @param defaultValue The default value to set if none is found.
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
global.GetConvarBool = function (varName, defaultValue) {
	return _in(0x00000000, 0x7e8ebfe5, _ts(varName), defaultValue, _r);
};

/**
 * This will have floating point inaccuracy.
 * @param varName The console variable to get
 * @param defaultValue The default value to set, if none are found.
 * @return Returns the value set in varName, or `default` if none are specified
 */
global.GetConvarFloat = function (varName, defaultValue) {
	return _in(0x00000000, 0x009e666d, _ts(varName), _fv(defaultValue), _r, _rf);
};

/**
 * Can be used to get a console variable casted back to `int` (an integer value).
 * @param varName The console variable to look up.
 * @param default_ The default value to set if none is found (variable not set using [SET_CONVAR](#\_0x341B16D2), or not accessible).
 * @return Returns the convar value if it can be found, otherwise it returns the assigned `default`.
 */
global.GetConvarInt = function (varName, default_) {
	return _in(0x00000000, 0x935c0ab2, _ts(varName), default_, _r, _ri);
};

global.GetCoordinatesForNetworkRestartNode = function (Unk914, Unk915, Unk916) {
	return _in(0x00000000, 0x2eaa3c4a, Unk914, Unk915, Unk916);
};

global.GetCorrectedColour = function (r, g, b) {
	return _in(0x00000000, 0x64d35e1d, r, g, b, _i, _i, _i);
};

global.GetCreateRandomCops = function () {
	return _in(0x00000000, 0x4f9342f3, _r);
};

global.GetCurrentBasicCopModel = function (pModel) {
	return _in(0x00000000, 0x1b305900, _ii(pModel) /* may be optional */);
};

global.GetCurrentBasicPoliceCarModel = function (pModel) {
	return _in(0x00000000, 0x76901a85, _ii(pModel) /* may be optional */);
};

global.GetCurrentCharWeapon = function (ped, pWeapon) {
	return _in(0x00000000, 0x5ab8289f, ped, _ii(pWeapon) /* may be optional */, _r);
};

global.GetCurrentCopModel = function (pModel) {
	return _in(0x00000000, 0x018b2055, _ii(pModel) /* may be optional */);
};

global.GetCurrentDate = function () {
	return _in(0x00000000, 0x2e5b068f, _i, _i);
};

global.GetCurrentDayOfWeek = function () {
	return _in(0x00000000, 0x39fd432b, _r, _ri);
};

global.GetCurrentEpisode = function () {
	return _in(0x00000000, 0x7d7619d2, _r, _ri);
};

/**
 * This native returns the currently used game's name.
 * @return The game name as a string, one of the following values: gta4, gta5, rdr3
 */
global.GetCurrentGameName = function () {
	return _in(0x00000000, 0xaca18ecd, _r, _s);
};

global.GetCurrentLanguage = function () {
	return _in(0x00000000, 0x1105259c, _r, _ri);
};

global.GetCurrentPlaybackNumberForCar = function (car) {
	return _in(0x00000000, 0x678813a4, car, _r, _ri);
};

global.GetCurrentPoliceCarModel = function (pModel) {
	return _in(0x00000000, 0x20a53b7f, _ii(pModel) /* may be optional */);
};

global.GetCurrentPopulationZoneType = function () {
	return _in(0x00000000, 0x30516a11, _r, _ri);
};

/**
 * Returns the name of the currently executing resource.
 * @return The name of the resource.
 */
global.GetCurrentResourceName = function () {
	return _in(0x00000000, 0xe5e9ebbb, _r, _s);
};

global.GetCurrentScriptedConversationLine = function () {
	return _in(0x00000000, 0x0de30821, _r, _ri);
};

/**
 * Returns the peer address of the remote game server that the user is currently connected to.
 * @return The peer address of the game server (e.g. `127.0.0.1:30120`), or NULL if not available.
 */
global.GetCurrentServerEndpoint = function () {
	return _in(0x00000000, 0xea11bfba, _r, _s);
};

global.GetCurrentStackSize = function () {
	return _in(0x00000000, 0x6ac52840, _r, _ri);
};

global.GetCurrentStationForTrain = function (train) {
	return _in(0x00000000, 0x10fe0fe9, train, _r, _ri);
};

global.GetCurrentTaxiCarModel = function (pModel) {
	return _in(0x00000000, 0x1d6d767e, _ii(pModel) /* may be optional */);
};

global.GetCurrentWeather = function (pWeather) {
	return _in(0x00000000, 0x27e421ea, _ii(pWeather) /* may be optional */);
};

global.GetCurrentWeatherFull = function () {
	return _in(0x00000000, 0x3ffa65ee, _i, _i, _i);
};

global.GetCurrentZoneScumminess = function () {
	return _in(0x00000000, 0x4b7b5f77, _r, _ri);
};

global.GetCutsceneAudioTimeMs = function () {
	return _in(0x00000000, 0x2b8a0c6b, _r, _ri);
};

global.GetCutscenePedPosition = function (unkped, pos) {
	return _in(0x00000000, 0x366b549f, unkped, _v);
};

global.GetCutsceneSectionPlaying = function () {
	return _in(0x00000000, 0x04c65beb, _r, _ri);
};

global.GetCutsceneTime = function () {
	return _in(0x00000000, 0x7df26c8c, _r, _ri);
};

global.GetDamageToPedBodyPart = function (ped, part) {
	return _in(0x00000000, 0x062a507a, ped, part, _r, _ri);
};

global.GetDeadCarCoordinates = function (vehicle) {
	return _in(0x00000000, 0x3bc827e6, vehicle, _f, _f, _f);
};

global.GetDeadCharPickupCoords = function (ped) {
	return _in(0x00000000, 0x2a7475d8, ped, _f, _f, _f);
};

global.GetDebugCam = function (cam) {
	return _in(0x00000000, 0x7d15544e, _ii(cam) /* may be optional */);
};

global.GetDestroyerOfNetworkId = function (playerIndex, id) {
	return _in(0x00000000, 0x11e80442, playerIndex, id, _r, _ri);
};

global.GetDisplayNameFromVehicleModel = function (model) {
	return _in(0x00000000, 0x404e0056, model, _r, _s);
};

global.GetDistanceBetweenCoords_2d = function (x1, y1, x2, y2, pDist) {
	return _in(0x00000000, 0x687107ca, _fv(x1), _fv(y1), _fv(x2), _fv(y2), _fi(pDist) /* may be optional */);
};

global.GetDistanceBetweenCoords_3d = function (x1, y1, z1, x2, y2, z2, pDist) {
	return _in(0x00000000, 0x23f772e7, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), _fi(pDist) /* may be optional */);
};

global.GetDoorAngleRatio = function (vehicle, door, pAngleRatio) {
	return _in(0x00000000, 0x44ea2669, vehicle, door, _fi(pAngleRatio) /* may be optional */);
};

global.GetDoorState = function (obj) {
	return _in(0x00000000, 0x64861559, obj, _i, _f);
};

global.GetDriverOfCar = function (vehicle, pPed) {
	return _in(0x00000000, 0x22457083, vehicle, _ii(pPed) /* may be optional */);
};

/**
 * Returns the NUI window handle for a specified DUI browser object.
 * @param duiObject The DUI browser handle.
 * @return The NUI window handle, for use in e.g. CREATE_RUNTIME_TEXTURE_FROM_DUI_HANDLE.
 */
global.GetDuiHandle = function (duiObject) {
	return _in(0x00000000, 0x1655d41d, duiObject, _r, _s);
};

global.GetEngineHealth = function (vehicle) {
	return _in(0x00000000, 0x2b0a05e0, vehicle, _r, _rf);
};

/**
 * ### Supported types
 * *   \[1] : Peds (including animals) and players.
 * *   \[2] : Vehicles.
 * *   \[3] : Objects (props), doors, and projectiles.
 * ### Coordinates need to be send unpacked (x,y,z)
 * ```lua
 * -- Define the allowed model hashes
 * local allowedModelHashes = { GetHashKey("p_crate03x"), GetHashKey("p_crate22x") }
 * -- Get the player's current coordinates
 * local playerCoords = GetEntityCoords(PlayerPedId())
 * -- Retrieve all entities of type Object (type 3) within a radius of 10.0 units
 * -- that match the allowed model hashes
 * -- and sort output entities by distance
 * local entities = GetEntitiesInRadius(playerCoords.x, playerCoords.y, playerCoords.z, 10.0, 3, true, allowedModelHashes)
 * -- Iterate through the list of entities and print their ids
 * for i = 1, #entities do
 * local entity = entities[i]
 * print(entity)
 * end
 * ```
 * @param x The X coordinate.
 * @param y The Y coordinate.
 * @param z The Z coordinate.
 * @param radius Max distance from coordinate to entity
 * @param entityType Entity types see list below
 * @param sortByDistance Sort output entites by distance from nearest to farthest
 * @param models List of allowed models its also optional
 * @return An array containing entity handles for each entity.
 */
global.GetEntitiesInRadius = function (x, y, z, radius, entityType, sortByDistance, models) {
	return _in(0x00000000, 0xdffba12f, _fv(x), _fv(y), _fv(z), _fv(radius), entityType, sortByDistance, ...(_obj(models)), _r, _ro);
};

/**
 * **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
 * Returns the memory address of an entity.
 * This native is intended for singleplayer debugging, and may not be available during multiplayer.
 * @param entity The handle of the entity to get the address of.
 * @return A pointer containing the memory address of the entity.
 */
global.GetEntityAddress = function (entity) {
	return _in(0x00000000, 0x9a3144bc, entity, _r, _rl);
};

/**
 * Returns the entity handle for the specified state bag name. For use with [ADD_STATE_BAG_CHANGE_HANDLER](#\_0x5BA35AAF).
 * @param bagName An internal state bag ID from the argument to a state bag change handler.
 * @return The entity handle or 0 if the state bag name did not refer to an entity, or the entity does not exist.
 */
global.GetEntityFromStateBagName = function (bagName) {
	return _in(0x00000000, 0x4bdf1867, _ts(bagName), _r, _ri);
};

global.GetEpisodeIndexFromSummons = function () {
	return _in(0x00000000, 0x704e638f, _r, _ri);
};

global.GetEpisodeName = function (episodeIndex) {
	return _in(0x00000000, 0x6004431b, episodeIndex, _r, _s);
};

/**
 * A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A float that contains the value stored in the Kvp or nil/null if none.
 */
global.GetExternalKvpFloat = function (resource, key) {
	return _in(0x00000000, 0x3cc98b25, _ts(resource), _ts(key), _r, _rf);
};

/**
 * A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A int that contains the value stored in the Kvp or nil/null if none.
 */
global.GetExternalKvpInt = function (resource, key) {
	return _in(0x00000000, 0x12b8d689, _ts(resource), _ts(key), _r, _ri);
};

/**
 * A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B), but for a specified resource.
 * @param resource The resource to fetch from.
 * @param key The key to fetch
 * @return A string that contains the value stored in the Kvp or nil/null if none.
 */
global.GetExternalKvpString = function (resource, key) {
	return _in(0x00000000, 0x9080363a, _ts(resource), _ts(key), _r, _s);
};

global.GetExtraCarColours = function (vehicle) {
	return _in(0x00000000, 0x25b87bca, vehicle, _i, _i);
};

global.GetFilterMenuOn = function () {
	return _in(0x00000000, 0x509d75e8, _r);
};

global.GetFilterSaveSetting = function (filterid) {
	return _in(0x00000000, 0x25ca45ea, filterid, _r, _ri);
};

global.GetFirstBlipInfoId = function (type) {
	return _in(0x00000000, 0x3bd729e9, type, _r, _ri);
};

global.GetFirstNCharactersOfLiteralString = function (literalString, chars) {
	return _in(0x00000000, 0x42d249e3, _ts(literalString), chars, _r, _s);
};

global.GetFirstNCharactersOfString = function (gxtName, chars) {
	return _in(0x00000000, 0x108b4a25, _ts(gxtName), chars, _r, _s);
};

global.GetFloatStat = function (stat) {
	return _in(0x00000000, 0x1d801fc0, stat, _r, _rf);
};

global.GetFollowVehicleCamSubmode = function (mode) {
	return _in(0x00000000, 0x4c7b7a29, _ii(mode) /* may be optional */);
};

global.GetFragmentDamageHealthOfClosestObjectOfType = function (x, y, z, radius, Unk77, flag) {
	return _in(0x00000000, 0x052803d0, _fv(x), _fv(y), _fv(z), _fv(radius), Unk77, flag, _r, _rf);
};

global.GetFrameCount = function () {
	return _in(0x00000000, 0x0da146aa, _r, _ri);
};

global.GetFrameTime = function (time) {
	return _in(0x00000000, 0x206420a6, _fi(time) /* may be optional */);
};

global.GetFreeCam = function (cam) {
	return _in(0x00000000, 0x538514cc, _ii(cam) /* may be optional */);
};

global.GetFrontendDesignValue = function (frontendid) {
	return _in(0x00000000, 0x747e681e, frontendid, _f, _f);
};

/**
 * Returns the internal build number of the current game being executed.
 * Possible values:
 * *   FiveM
 * *   1604
 * *   2060
 * *   2189
 * *   2372
 * *   2545
 * *   2612
 * *   2699
 * *   2802
 * *   2944
 * *   3095
 * *   3258
 * *   3323
 * *   3407
 * *   3570
 * *   RedM
 * *   1311
 * *   1355
 * *   1436
 * *   1491
 * *   LibertyM
 * *   43
 * *   FXServer
 * *   0
 * @return The build number, or **0** if no build number is known.
 */
global.GetGameBuildNumber = function () {
	return _in(0x00000000, 0x804b9f7b, _r, _ri);
};

global.GetGameCam = function (camera) {
	return _in(0x00000000, 0x0b2a2801, _ii(camera) /* may be optional */);
};

global.GetGameCamChild = function (camera) {
	return _in(0x00000000, 0x588f6bc0, _ii(camera) /* may be optional */);
};

/**
 * Returns the current game being executed.
 * Possible values:
 * | Return value | Meaning                        |
 * | ------------ | ------------------------------ |
 * | `fxserver`   | Server-side code ('Duplicity') |
 * | `fivem`      | FiveM for GTA V                |
 * | `libertym`   | LibertyM for GTA IV            |
 * | `redm`       | RedM for Red Dead Redemption 2 |
 * @return The game the script environment is running in.
 */
global.GetGameName = function () {
	return _in(0x00000000, 0xe8eaa18b, _r, _s);
};

/**
 * Returns a list of entity handles (script GUID) for all entities in the specified pool - the data returned is an array as
 * follows:
 * ```json
 * [ 770, 1026, 1282, 1538, 1794, 2050, 2306, 2562, 2818, 3074, 3330, 3586, 3842, 4098, 4354, 4610, ...]
 * ```
 * ### Supported pools
 * *   `CPed`: Peds (including animals) and players.
 * *   `CObject`: Objects (props), doors, and projectiles.
 * *   `CNetObject`: Networked objects
 * *   `CVehicle`: Vehicles.
 * *   `CPickup`: Pickups.
 * @param poolName The pool name to get a list of entities from.
 * @return An array containing entity handles for each entity in the named pool.
 */
global.GetGamePool = function (poolName) {
	return _in(0x00000000, 0x2b9d4f50, _ts(poolName), _r, _ro);
};

global.GetGameTimer = function (pTimer) {
	return _in(0x00000000, 0x022b2da9, _ii(pTimer) /* may be optional */);
};

global.GetGameViewportId = function (viewportid) {
	return _in(0x00000000, 0x57f7558b, _ii(viewportid) /* may be optional */);
};

global.GetGamerNetworkScore = function (playerIndex, Unk888, Unk889) {
	return _in(0x00000000, 0x6c507eac, playerIndex, Unk888, Unk889, _r, _ri);
};

global.GetGfwlHasSafeHouse = function () {
	return _in(0x00000000, 0x6cc85d46, _r);
};

global.GetGfwlIsReturningToSinglePlayer = function () {
	return _in(0x00000000, 0x2fdf565d, _r);
};

global.GetGroundZFor_3dCoord = function (x, y, z, pGroundZ) {
	return _in(0x00000000, 0x6d902ee3, _fv(x), _fv(y), _fv(z), _fi(pGroundZ) /* may be optional */, _r, _ri);
};

global.GetGroupCharDucksWhenAimedAt = function (ped) {
	return _in(0x00000000, 0x070b1c45, ped, _r);
};

global.GetGroupFormation = function (group, formation) {
	return _in(0x00000000, 0x596174e5, group, _ii(formation) /* may be optional */);
};

global.GetGroupFormationSpacing = function (group, spacing) {
	return _in(0x00000000, 0x67db4150, group, _fi(spacing) /* may be optional */);
};

global.GetGroupLeader = function (group, pPed) {
	return _in(0x00000000, 0x5dbb46b5, group, _ii(pPed) /* may be optional */);
};

global.GetGroupMember = function (group, index, pPed) {
	return _in(0x00000000, 0x2ff90ff5, group, index, _ii(pPed) /* may be optional */);
};

global.GetGroupSize = function (group) {
	return _in(0x00000000, 0x45ee4e9a, group, _i, _i);
};

global.GetHashKey = function (value) {
	return _in(0x00000000, 0x68ff7165, _ts(value), _r, _ri);
};

global.GetHeadingFromVector_2d = function (x, y, pHeading) {
	return _in(0x00000000, 0x09dd61e1, _fv(x), _fv(y), _fi(pHeading) /* may be optional */);
};

global.GetHeightOfVehicle = function (vehicle, x, y, z, unknownTrue1, unknownTrue2) {
	return _in(0x00000000, 0x5fad09ca, vehicle, _fv(x), _fv(y), _fv(z), unknownTrue1, unknownTrue2, _r, _rf);
};

global.GetHelpMessageBoxSize = function () {
	return _in(0x00000000, 0x267d251f, _f, _f);
};

global.GetHostId = function () {
	return _in(0x00000000, 0x79c84dbc, _r, _ri);
};

global.GetHostMatchOn = function () {
	return _in(0x00000000, 0x757a0eb8, _r);
};

global.GetHoursOfDay = function () {
	return _in(0x00000000, 0x0a9f7ba1, _r, _ri);
};

global.GetHudColour = function (type) {
	return _in(0x00000000, 0x07533ec9, type, _i, _i, _i, _i);
};

global.GetIdOfThisThread = function () {
	return _in(0x00000000, 0x051a131d, _r, _ri);
};

/**
 * GET_INSTANCE_ID
 */
global.GetInstanceId = function () {
	return _in(0x00000000, 0x9f1c4383, _r, _ri);
};

global.GetIntStat = function (stat) {
	return _in(0x00000000, 0x48994d58, stat, _r, _ri);
};

global.GetInteriorAtCoords = function (x, y, z, pInterior) {
	return _in(0x00000000, 0x29216610, _fv(x), _fv(y), _fv(z), _ii(pInterior) /* may be optional */);
};

global.GetInteriorFromCar = function (vehicle, pInterior) {
	return _in(0x00000000, 0x25714be4, vehicle, _ii(pInterior) /* may be optional */);
};

global.GetInteriorFromChar = function (ped, pInterior) {
	return _in(0x00000000, 0x028227f7, ped, _ii(pInterior) /* may be optional */);
};

global.GetInteriorHeading = function (interior, pHeading) {
	return _in(0x00000000, 0x73245ab3, interior, _fi(pHeading) /* may be optional */);
};

/**
 * GET_INVOKING_RESOURCE
 */
global.GetInvokingResource = function () {
	return _in(0x00000000, 0x4d52fe5b, _r, _s);
};

global.GetIsAutosaveOff = function () {
	return _in(0x00000000, 0x551c6295, _r);
};

global.GetIsDepositAnimRunning = function () {
	return _in(0x00000000, 0x3ccb4248, _r);
};

global.GetIsDisplayingsavemessage = function () {
	return _in(0x00000000, 0x34f9164d, _r);
};

global.GetIsHidef = function () {
	return _in(0x00000000, 0x19976813, _r);
};

global.GetIsProjectileTypeInArea = function (x0, y0, z0, x1, y1, z1, type) {
	return _in(0x00000000, 0x7b2e70f3, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), type, _r);
};

global.GetIsStickyBombStuckToObject = function (obj) {
	return _in(0x00000000, 0x04d623ff, obj, _r);
};

global.GetIsStickyBombStuckToVehicle = function (veh) {
	return _in(0x00000000, 0x29bf0233, veh, _r);
};

global.GetIsWidescreen = function () {
	return _in(0x00000000, 0x0f0269b5, _r);
};

global.GetKeyForCarInRoom = function (vehicle, pKey) {
	return _in(0x00000000, 0x0e390571, vehicle, _ii(pKey) /* may be optional */);
};

global.GetKeyForCharInRoom = function (ped, pKey) {
	return _in(0x00000000, 0x266d0801, ped, _ii(pKey) /* may be optional */);
};

global.GetKeyForViewportInRoom = function (viewportid, roomkey) {
	return _in(0x00000000, 0x10776aae, viewportid, _ii(roomkey) /* may be optional */);
};

global.GetKeyboardMoveInput = function () {
	return _in(0x00000000, 0x4af73456, _i, _i);
};

global.GetLatestConsoleCommand = function () {
	return _in(0x00000000, 0x670e3de3, _r, _ri);
};

global.GetLeftPlayerCashToReachLevel = function (playerRank) {
	return _in(0x00000000, 0x6dd754dd, playerRank, _r, _ri);
};

global.GetLengthOfLiteralString = function (literalString) {
	return _in(0x00000000, 0x02be2d97, _ts(literalString), _r, _ri);
};

global.GetLengthOfStringWithThisHashKey = function (gxtkey) {
	return _in(0x00000000, 0x6c013a17, gxtkey, _r, _ri);
};

global.GetLengthOfStringWithThisTextLabel = function (gxtName) {
	return _in(0x00000000, 0x6d795ec0, _ts(gxtName), _r, _ri);
};

global.GetLengthOfStringWithThisTextLabelInsNum = function (Unk608, Unk609, Unk610) {
	return _in(0x00000000, 0x5f02084d, Unk608, Unk609, Unk610, _r, _ri);
};

global.GetLevelDesignCoordsForObject = function (obj, Unk78) {
	return _in(0x00000000, 0x3e762d9d, obj, Unk78, _f, _f, _f, _r, _rf);
};

global.GetLineHeight = function () {
	return _in(0x00000000, 0x150b0c33, _r, _rf);
};

global.GetLocalGamerlevelFromProfilesettings = function () {
	return _in(0x00000000, 0x7c5f327e, _r, _ri);
};

global.GetLocalPlayerMpCash = function () {
	return _in(0x00000000, 0x76b068ca, _r, _ri);
};

global.GetLocalPlayerWeaponStat = function (wtype, wid) {
	return _in(0x00000000, 0x3ccc5afd, wtype, wid, _r, _rf);
};

global.GetMapAreaFromCoords = function (x, y, z) {
	return _in(0x00000000, 0x5ed33d46, _fv(x), _fv(y), _fv(z), _r, _ri);
};

global.GetMaxAmmo = function (ped, weapon, pMaxAmmo) {
	return _in(0x00000000, 0x7c6968f8, ped, weapon, _ii(pMaxAmmo) /* may be optional */, _r);
};

global.GetMaxAmmoInClip = function (ped, weapon, pMaxAmmo) {
	return _in(0x00000000, 0x01794a3c, ped, weapon, _ii(pMaxAmmo) /* may be optional */);
};

global.GetMaxWantedLevel = function (pMaxWantedLevel) {
	return _in(0x00000000, 0x71755e9b, _ii(pMaxWantedLevel) /* may be optional */);
};

global.GetMaximumNumberOfPassengers = function (vehicle, pMax) {
	return _in(0x00000000, 0x554014f1, vehicle, _ii(pMax) /* may be optional */);
};

global.GetMenuItemAccepted = function (menuid) {
	return _in(0x00000000, 0x0f322a6c, menuid, _r, _ri);
};

global.GetMenuItemSelected = function (menuid) {
	return _in(0x00000000, 0x22442a7f, menuid, _r, _ri);
};

global.GetMenuPosition = function (menuid) {
	return _in(0x00000000, 0x5b576767, menuid, _f, _f);
};

global.GetMinutesOfDay = function () {
	return _in(0x00000000, 0x3dfe691d, _r, _ri);
};

global.GetMinutesToTimeOfDay = function (hour, minute) {
	return _in(0x00000000, 0x740c4c84, hour, minute, _r, _ri);
};

global.GetMissionFlag = function () {
	return _in(0x00000000, 0x2bc64736, _r);
};

global.GetMobilePhoneRenderId = function (pRenderId) {
	return _in(0x00000000, 0x5e7b3816, _ii(pRenderId) /* may be optional */);
};

global.GetMobilePhoneScale = function () {
	return _in(0x00000000, 0x1e951606, _r, _rf);
};

global.GetMobilePhoneTaskSubTask = function (ped, Unk798) {
	return _in(0x00000000, 0x517b226e, ped, _ii(Unk798) /* may be optional */, _r);
};

global.GetModelDimensions = function (model) {
	return _in(0x00000000, 0x191b7021, model, _v, _v);
};

global.GetModelNameForDebug = function (model) {
	return _in(0x00000000, 0x4342350c, model, _r, _s);
};

global.GetModelPedIsHolding = function (ped) {
	return _in(0x00000000, 0x0af378d5, ped, _r, _ri);
};

global.GetMouseInput = function () {
	return _in(0x00000000, 0x447b154b, _i, _i);
};

global.GetMousePosition = function () {
	return _in(0x00000000, 0x0ecb2dee, _i, _i);
};

global.GetMouseSensitivity = function () {
	return _in(0x00000000, 0x41401d46, _r, _rf);
};

global.GetMouseWheel = function (Unk834) {
	return _in(0x00000000, 0x51870c68, _ii(Unk834) /* may be optional */);
};

global.GetNameOfInfoZone = function (x, y, z) {
	return _in(0x00000000, 0x5cad7949, _fv(x), _fv(y), _fv(z), _r, _s);
};

global.GetNameOfZone = function (x, y, z) {
	return _in(0x00000000, 0x25442df7, _fv(x), _fv(y), _fv(z), _r, _s);
};

global.GetNavmeshRouteResult = function (navmesh) {
	return _in(0x00000000, 0x4efe6b67, navmesh, _r, _ri);
};

global.GetNearestCableCar = function (x, y, z, radius, pVehicle) {
	return _in(0x00000000, 0x7f3a0e22, _fv(x), _fv(y), _fv(z), _fv(radius), _ii(pVehicle) /* may be optional */);
};

global.GetNetworkIdFromObject = function (obj, netid) {
	return _in(0x00000000, 0x50424095, obj, _ii(netid) /* may be optional */);
};

global.GetNetworkIdFromPed = function (ped, netid) {
	return _in(0x00000000, 0x7bee5003, ped, _ii(netid) /* may be optional */);
};

global.GetNetworkIdFromVehicle = function (vehicle, netid) {
	return _in(0x00000000, 0x1bc70617, vehicle, _ii(netid) /* may be optional */);
};

global.GetNetworkJoinFail = function () {
	return _in(0x00000000, 0x4a164056, _r);
};

global.GetNetworkTimer = function (Unk917) {
	return _in(0x00000000, 0x20fd4f4e, Unk917);
};

/**
 * GET_NETWORK_WALK_MODE
 */
global.GetNetworkWalkMode = function () {
	return _in(0x00000000, 0x2cafd5e9, _r);
};

global.GetNextBlipInfoId = function (type) {
	return _in(0x00000000, 0x154932f0, type, _r, _ri);
};

global.GetNextClosestCarNode = function (x, y, z) {
	return _in(0x00000000, 0x5935382a, _fv(x), _fv(y), _fv(z), _f, _f, _f, _r);
};

global.GetNextClosestCarNodeFavourDirection = function (x, y, z) {
	return _in(0x00000000, 0x6e3906e4, _fv(x), _fv(y), _fv(z), _f, _f, _f, _f, _r);
};

global.GetNextClosestCarNodeWithHeading = function (x, y, z) {
	return _in(0x00000000, 0x3d7a673f, _fv(x), _fv(y), _fv(z), _f, _f, _f, _f, _r);
};

global.GetNextClosestCarNodeWithHeadingOnIsland = function (x, y, z) {
	return _in(0x00000000, 0x320e1e3b, _fv(x), _fv(y), _fv(z), _f, _f, _f, _f, _r);
};

global.GetNextStationForTrain = function (train) {
	return _in(0x00000000, 0x4835637d, train, _r, _ri);
};

global.GetNoLawVehiclesDestroyedByLocalPlayer = function () {
	return _in(0x00000000, 0x63c50673, _r, _ri);
};

global.GetNoOfPlayersInTeam = function (team) {
	return _in(0x00000000, 0x1cfd32e5, team, _r, _ri);
};

global.GetNthClosestCarNode = function (x, y, z, n) {
	return _in(0x00000000, 0x740912c2, _fv(x), _fv(y), _fv(z), n, _f, _f, _f, _r);
};

global.GetNthClosestCarNodeFavourDirection = function (Unk810, x, y, z, n) {
	return _in(0x00000000, 0x6f766824, Unk810, _fv(x), _fv(y), _fv(z), n, _f, _f, _f, _f, _r);
};

global.GetNthClosestCarNodeWithHeading = function (x, y, z, nodeNum) {
	return _in(0x00000000, 0x1f6b3ff0, _fv(x), _fv(y), _fv(z), nodeNum, _f, _f, _f, _f, _r);
};

global.GetNthClosestCarNodeWithHeadingOnIsland = function (x, y, z, nodeNum, areaId) {
	return _in(0x00000000, 0x59db1ad1, _fv(x), _fv(y), _fv(z), nodeNum, areaId, _f, _f, _f, _f, _i, _r);
};

global.GetNthClosestWaterNodeWithHeading = function (x, y, z, flag0, flag1) {
	return _in(0x00000000, 0x36f453ff, _fv(x), _fv(y), _fv(z), flag0, flag1, _v, _f, _r);
};

global.GetNthGroupMember = function (group, n, ped) {
	return _in(0x00000000, 0x48ce0609, group, n, _ii(ped) /* may be optional */);
};

global.GetNthIntegerInString = function (gxtName, index) {
	return _in(0x00000000, 0x301545fd, _ts(gxtName), index, _r, _s);
};

/**
 * GET_NUI_CURSOR_POSITION
 */
global.GetNuiCursorPosition = function () {
	return _in(0x00000000, 0xbdba226f, _i, _i);
};

global.GetNumCarColours = function (vehicle, pNumColours) {
	return _in(0x00000000, 0x5aa025c2, vehicle, _ii(pNumColours) /* may be optional */);
};

global.GetNumCarLiveries = function (car, num) {
	return _in(0x00000000, 0x0a632bb4, car, _ii(num) /* may be optional */);
};

global.GetNumOfModelsKilledByPlayer = function (player, model, num) {
	return _in(0x00000000, 0x75b43a72, player, model, _ii(num) /* may be optional */);
};

/**
 * Gets the amount of metadata values with the specified key existing in the specified resource's manifest.
 * See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
 * @param resourceName The resource name.
 * @param metadataKey The key to look up in the resource manifest.
 */
global.GetNumResourceMetadata = function (resourceName, metadataKey) {
	return _in(0x00000000, 0x0776e864, _ts(resourceName), _ts(metadataKey), _r, _ri);
};

/**
 * GET_NUM_RESOURCES
 */
global.GetNumResources = function () {
	return _in(0x00000000, 0x0863f27b, _r, _ri);
};

global.GetNumStreamingRequests = function () {
	return _in(0x00000000, 0x53216168, _r, _ri);
};

global.GetNumberLines = function (Unk703, Unk704, str) {
	return _in(0x00000000, 0x67b725b2, _fv(Unk703), _fv(Unk704), _ts(str), _r, _ri);
};

global.GetNumberLinesWithLiteralStrings = function (Unk705, Unk706, str1, str2, str3) {
	return _in(0x00000000, 0x71de26a3, _fv(Unk705), _fv(Unk706), _ts(str1), _ts(str2), _ts(str3), _r, _ri);
};

global.GetNumberLinesWithSubstrings = function (Unk707, Unk708, str1, str2, str3) {
	return _in(0x00000000, 0x00541084, _fv(Unk707), _fv(Unk708), _ts(str1), _ts(str2), _ts(str3), _r, _ri);
};

global.GetNumberOfActiveStickyBombsOwnedByPed = function (ped) {
	return _in(0x00000000, 0x21b85da9, ped, _r, _ri);
};

global.GetNumberOfCharDrawableVariations = function (ped, component) {
	return _in(0x00000000, 0x3c293296, ped, component, _r, _ri);
};

global.GetNumberOfCharTextureVariations = function (ped, component, unknown1) {
	return _in(0x00000000, 0x06c4113e, ped, component, unknown1, _r, _ri);
};

global.GetNumberOfFiresInArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x1e144c8b, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r, _ri);
};

global.GetNumberOfFiresInRange = function (x, y, z, radius) {
	return _in(0x00000000, 0x283821d2, _fv(x), _fv(y), _fv(z), _fv(radius), _r, _ri);
};

global.GetNumberOfFollowers = function (ped, followers) {
	return _in(0x00000000, 0x303c3059, ped, _ii(followers) /* may be optional */);
};

global.GetNumberOfInjuredPedsInRange = function (x, y, z, radius) {
	return _in(0x00000000, 0x3bb313cb, _fv(x), _fv(y), _fv(z), _fv(radius), _r, _ri);
};

global.GetNumberOfInstancesOfStreamedScript = function (scriptName) {
	return _in(0x00000000, 0x5a1c52c7, _ts(scriptName), _r, _ri);
};

global.GetNumberOfPassengers = function (vehicle, pNumPassengers) {
	return _in(0x00000000, 0x5be30681, vehicle, _ii(pNumPassengers) /* may be optional */);
};

global.GetNumberOfPlayers = function () {
	return _in(0x00000000, 0x62405882, _r, _ri);
};

global.GetNumberOfStickyBombsStuckToObject = function (obj) {
	return _in(0x00000000, 0x4ad026ee, obj, _r, _ri);
};

global.GetNumberOfStickyBombsStuckToVehicle = function (veh) {
	return _in(0x00000000, 0x285d1184, veh, _r, _ri);
};

global.GetNumberOfWebPageLinks = function (htmlviewport) {
	return _in(0x00000000, 0x18a22ae4, htmlviewport, _r, _ri);
};

global.GetObjectAnimCurrentTime = function (obj, animname0, animname1, time) {
	return _in(0x00000000, 0x29f02cb1, obj, _ts(animname0), _ts(animname1), _fi(time) /* may be optional */);
};

global.GetObjectAnimTotalTime = function (obj, animname0, animname1, time) {
	return _in(0x00000000, 0x26e66df3, obj, _ts(animname0), _ts(animname1), _fi(time) /* may be optional */);
};

global.GetObjectCoordinates = function (obj) {
	return _in(0x00000000, 0x49da4f9e, obj, _f, _f, _f);
};

global.GetObjectFragmentDamageHealth = function (obj, unknown) {
	return _in(0x00000000, 0x79ca30b1, obj, unknown, _r, _rf);
};

global.GetObjectFromNetworkId = function (netid, obj) {
	return _in(0x00000000, 0x7aa91131, netid, _ii(obj) /* may be optional */);
};

global.GetObjectHeading = function (obj, pHeading) {
	return _in(0x00000000, 0x791d1778, obj, _fi(pHeading) /* may be optional */);
};

global.GetObjectHealth = function (obj, pHealth) {
	return _in(0x00000000, 0x4acb039b, obj, _fi(pHealth) /* may be optional */);
};

global.GetObjectMass = function (obj, mass) {
	return _in(0x00000000, 0x0b8b3941, obj, _fi(mass) /* may be optional */);
};

global.GetObjectModel = function (obj, pModel) {
	return _in(0x00000000, 0x5cc55619, obj, _ii(pModel) /* may be optional */);
};

global.GetObjectPedIsHolding = function (ped) {
	return _in(0x00000000, 0x45345838, ped, _r, _ri);
};

global.GetObjectQuaternion = function (obj) {
	return _in(0x00000000, 0x0f731898, obj, _f, _f, _f, _f);
};

global.GetObjectRotationVelocity = function (obj) {
	return _in(0x00000000, 0x492a71e2, obj, _f, _f, _f);
};

global.GetObjectSpeed = function (obj, pSpeed) {
	return _in(0x00000000, 0x1c2f57fb, obj, _fi(pSpeed) /* may be optional */);
};

global.GetObjectTurnMass = function (obj, turnmass) {
	return _in(0x00000000, 0x3c85109f, obj, _fi(turnmass) /* may be optional */);
};

global.GetObjectVelocity = function (obj) {
	return _in(0x00000000, 0x06d651a7, obj, _f, _f, _f);
};

global.GetOffsetFromCarGivenWorldCoords = function (vehicle, x, y, z) {
	return _in(0x00000000, 0x373b213c, vehicle, _fv(x), _fv(y), _fv(z), _f, _f, _f);
};

global.GetOffsetFromCarInWorldCoords = function (vehicle, x, y, z) {
	return _in(0x00000000, 0x7f8d3dd9, vehicle, _fv(x), _fv(y), _fv(z), _f, _f, _f);
};

global.GetOffsetFromCharInWorldCoords = function (ped, x, y, z) {
	return _in(0x00000000, 0x737f24f9, ped, _fv(x), _fv(y), _fv(z), _f, _f, _f);
};

global.GetOffsetFromInteriorInWorldCoords = function (interior, x, y, z, pOffset) {
	return _in(0x00000000, 0x68966670, interior, _fv(x), _fv(y), _fv(z), _fi(pOffset) /* may be optional */);
};

global.GetOffsetFromObjectInWorldCoords = function (obj, x, y, z) {
	return _in(0x00000000, 0x449f4165, obj, _fv(x), _fv(y), _fv(z), _f, _f, _f);
};

global.GetOffsetsForAttachCarToCar = function (car0, car1) {
	return _in(0x00000000, 0x2cad4e39, car0, car1, _v, _v);
};

global.GetOnlineLan = function () {
	return _in(0x00000000, 0x6b032a0b, _r, _ri);
};

global.GetOnlineScore = function (Unk887) {
	return _in(0x00000000, 0x6cfd3e5f, Unk887, _r, _rf);
};

global.GetPadPitchRoll = function (padIndex) {
	return _in(0x00000000, 0x767b7ec9, padIndex, _f, _f, _r);
};

global.GetPadState = function (Unk835, Unk836, Unk837) {
	return _in(0x00000000, 0x5d4c1d59, Unk835, Unk836, _ii(Unk837) /* may be optional */);
};

global.GetPedBonePosition = function (ped, bone, x, y, z, pPosition) {
	return _in(0x00000000, 0x43475bb3, ped, bone, _fv(x), _fv(y), _fv(z), _v);
};

global.GetPedClimbState = function (ped) {
	return _in(0x00000000, 0x391822a7, ped, _r, _ri);
};

global.GetPedFromNetworkId = function (netid, ped) {
	return _in(0x00000000, 0x69f11716, netid, _ii(ped) /* may be optional */);
};

global.GetPedGroupIndex = function (ped, pIndex) {
	return _in(0x00000000, 0x58e53b06, ped, _ii(pIndex) /* may be optional */);
};

global.GetPedModelFromIndex = function (index) {
	return _in(0x00000000, 0x124d4571, index, _r, _ri);
};

global.GetPedObjectIsAttachedTo = function (obj) {
	return _in(0x00000000, 0x755d6df8, obj, _r, _ri);
};

global.GetPedPathMayDropFromHeight = function (ped) {
	return _in(0x00000000, 0x45aa529d, ped, _r);
};

global.GetPedPathMayUseClimbovers = function (ped) {
	return _in(0x00000000, 0x714c1031, ped, _r);
};

global.GetPedPathMayUseLadders = function (ped) {
	return _in(0x00000000, 0x503e2d1e, ped, _r);
};

global.GetPedPathWillAvoidDynamicObjects = function (ped) {
	return _in(0x00000000, 0x74f97cf8, ped, _r);
};

global.GetPedSteersAroundObjects = function (ped) {
	return _in(0x00000000, 0x75e32257, ped, _r);
};

global.GetPedSteersAroundPeds = function (ped) {
	return _in(0x00000000, 0x179848e4, ped, _r);
};

global.GetPedType = function (ped, pType) {
	return _in(0x00000000, 0x18f477e1, ped, _ii(pType) /* may be optional */);
};

global.GetPetrolTankHealth = function (vehicle) {
	return _in(0x00000000, 0x2c835642, vehicle, _r, _rf);
};

global.GetPhysicalScreenResolution = function () {
	return _in(0x00000000, 0x3cd830d0, _f, _f);
};

global.GetPickupCoordinates = function (pickup) {
	return _in(0x00000000, 0x0f636c38, pickup, _f, _f, _f);
};

global.GetPlaneUndercarriagePosition = function (plane, pos) {
	return _in(0x00000000, 0x353f0568, plane, _fi(pos) /* may be optional */);
};

global.GetPlayerChar = function (playerIndex, pPed) {
	return _in(0x00000000, 0x511454a9, playerIndex, _ii(pPed) /* may be optional */);
};

global.GetPlayerColour = function (Player) {
	return _in(0x00000000, 0x25270a4b, Player, _r, _ri);
};

/**
 * Gets a local client's Player ID from its server ID counterpart, assuming the passed `serverId` exists on the client.
 * If no matching client is found, or an invalid value is passed over as the `serverId` native's parameter, the native result will be `-1`.
 * It's worth noting that this native method can only retrieve information about clients that are culled to the connected client.
 * @param serverId The player's server ID.
 * @return A valid Player ID if one is found, `-1` if not.
 */
global.GetPlayerFromServerId = function (serverId) {
	return _in(0x00000000, 0x344ea166, serverId, _r, _ri);
};

/**
 * On the server this will return the players source, on the client it will return the player handle.
 * @param bagName An internal state bag ID from the argument to a state bag change handler.
 * @return The player handle or 0 if the state bag name did not refer to a player, or the player does not exist.
 */
global.GetPlayerFromStateBagName = function (bagName) {
	return _in(0x00000000, 0xa56135e0, _ts(bagName), _r, _ri);
};

global.GetPlayerGroup = function (playerIndex, pGroup) {
	return _in(0x00000000, 0x41ab3c30, playerIndex, _ii(pGroup) /* may be optional */);
};

global.GetPlayerHasTracks = function () {
	return _in(0x00000000, 0x396844be, _r);
};

global.GetPlayerId = function () {
	return _in(0x00000000, 0x62e319c6, _r, _ri);
};

global.GetPlayerIdForThisPed = function (ped) {
	return _in(0x00000000, 0x733b61c6, ped, _r, _ri);
};

global.GetPlayerMaxArmour = function (playerIndex, pMaxArmour) {
	return _in(0x00000000, 0x17265607, playerIndex, _ii(pMaxArmour) /* may be optional */);
};

global.GetPlayerMaxHealth = function (player, maxhealth) {
	return _in(0x00000000, 0x52f27084, player, _ii(maxhealth) /* may be optional */);
};

global.GetPlayerName = function (playerIndex) {
	return _in(0x00000000, 0x570f5725, playerIndex, _r, _s);
};

global.GetPlayerRadioMode = function () {
	return _in(0x00000000, 0x32795678, _r, _ri);
};

global.GetPlayerRadioStationIndex = function () {
	return _in(0x00000000, 0x4e493aaf, _r, _ri);
};

global.GetPlayerRadioStationName = function () {
	return _in(0x00000000, 0x25136ac2, _r, _s);
};

global.GetPlayerRadioStationName = function () {
	return _in(0x00000000, 0x25136ac2, _r, _s);
};

global.GetPlayerRadioStationNameRoll = function () {
	return _in(0x00000000, 0x1a936344, _r, _s);
};

global.GetPlayerRankLevelDuringMp = function (playerIndex) {
	return _in(0x00000000, 0x7b31633e, playerIndex, _r, _ri);
};

global.GetPlayerRgbColour = function (Player) {
	return _in(0x00000000, 0x73bd71a9, Player, _i, _i, _i);
};

/**
 * GET_PLAYER_SERVER_ID
 */
global.GetPlayerServerId = function (player) {
	return _in(0x00000000, 0x4d97bcc7, player, _r, _ri);
};

global.GetPlayerTeam = function (Player) {
	return _in(0x00000000, 0x4c2879ad, Player, _r, _ri);
};

global.GetPlayerToPlaceBombInCar = function (vehicle) {
	return _in(0x00000000, 0x17572318, vehicle, _r, _ri);
};

global.GetPlayerWantedLevelIncrement = function (player, increment) {
	return _in(0x00000000, 0x44bb2306, player, _ii(increment) /* may be optional */);
};

global.GetPlayersLastCarNoSave = function (pVehicle) {
	return _in(0x00000000, 0x12067e8d, _ii(pVehicle) /* may be optional */);
};

global.GetPlayersettingsModelChoice = function () {
	return _in(0x00000000, 0x116e5a1f, _r, _ri);
};

global.GetPositionOfAnalogueSticks = function (padIndex) {
	return _in(0x00000000, 0x4f7f4fae, padIndex, _i, _i, _i, _i);
};

global.GetPositionOfCarRecordingAtTime = function (CarRec, time, pos) {
	return _in(0x00000000, 0x03b37165, CarRec, _fv(time), _fi(pos) /* may be optional */);
};

global.GetProfileSetting = function (settingid) {
	return _in(0x00000000, 0x575a3431, settingid, _r, _ri);
};

global.GetProgressPercentage = function () {
	return _in(0x00000000, 0x78e9500c, _r, _rf);
};

global.GetRadarViewportId = function (viewport) {
	return _in(0x00000000, 0x4a7c19fe, _ii(viewport) /* may be optional */);
};

global.GetRadioName = function (id) {
	return _in(0x00000000, 0x7ec9580e, id, _r, _s);
};

global.GetRandomCarBackBumperInSphere = function (x, y, z, radius, Unk812, Unk813, veh) {
	return _in(0x00000000, 0x2c37408c, _fv(x), _fv(y), _fv(z), _fv(radius), Unk812, Unk813, _ii(veh) /* may be optional */);
};

global.GetRandomCarFrontBumperInSphereNoSave = function (x, y, z, radius, flag0, flag1, flag2) {
	return _in(0x00000000, 0x13c91acd, _fv(x), _fv(y), _fv(z), _fv(radius), flag0, flag1, _i, flag2);
};

global.GetRandomCarInSphere = function (x, y, z, radius, model, Unk814, car) {
	return _in(0x00000000, 0x528f5ea7, _fv(x), _fv(y), _fv(z), _fv(radius), model, Unk814, _ii(car) /* may be optional */);
};

global.GetRandomCarInSphereNoSave = function (x, y, z, radius, model, flag, car) {
	return _in(0x00000000, 0x0a7e36e5, _fv(x), _fv(y), _fv(z), _fv(radius), model, flag, _ii(car) /* may be optional */);
};

global.GetRandomCarModelInMemory = function (MustIncludeSpecialModels) {
	return _in(0x00000000, 0x195c13bc, MustIncludeSpecialModels, _i, _i);
};

global.GetRandomCarNode = function (x, y, z, radius, flag0, flag1, flag2) {
	return _in(0x00000000, 0x588e1506, _fv(x), _fv(y), _fv(z), _fv(radius), flag0, flag1, flag2, _f, _f, _f, _f, _r);
};

global.GetRandomCarNodeIncludeSwitchedOffNodes = function (x, y, z, radius, flag0, flag1, flag2) {
	return _in(0x00000000, 0x2d1a5f8c, _fv(x), _fv(y), _fv(z), _fv(radius), flag0, flag1, flag2, _f, _f, _f, _f, _r);
};

global.GetRandomCarOfTypeInAngledAreaNoSave = function (Unk815, Unk816, Unk817, Unk818, Unk819, type, car) {
	return _in(0x00000000, 0x6d4746d8, _fv(Unk815), _fv(Unk816), _fv(Unk817), _fv(Unk818), _fv(Unk819), type, _ii(car) /* may be optional */);
};

global.GetRandomCarOfTypeInAreaNoSave = function (x0, y0, x1, y1, model, car) {
	return _in(0x00000000, 0x74af54f0, _fv(x0), _fv(y0), _fv(x1), _fv(y1), model, _ii(car) /* may be optional */);
};

global.GetRandomCharInAreaOffsetNoSave = function (x, y, z, sx, sy, sz, pPed) {
	return _in(0x00000000, 0x6ed17cf8, _fv(x), _fv(y), _fv(z), _fv(sx), _fv(sy), _fv(sz), _ii(pPed) /* may be optional */);
};

global.GetRandomNetworkRestartNode = function (Unk1013, Unk1014, Unk1015, Unk1016, Unk1017, Unk1018) {
	return _in(0x00000000, 0x0a2b76c2, Unk1013, Unk1014, Unk1015, Unk1016, Unk1017, Unk1018, _r, _ri);
};

global.GetRandomNetworkRestartNodeUsingGroupList = function (Unk1019, Unk1020, Unk1021, Unk1022, Unk1023, Unk1024) {
	return _in(0x00000000, 0x03ca3302, Unk1019, Unk1020, Unk1021, Unk1022, Unk1023, Unk1024, _r, _ri);
};

global.GetRandomWaterNode = function (x, y, z, radius, flag0, flag1, flag2, flag3) {
	return _in(0x00000000, 0x6fbe6ce6, _fv(x), _fv(y), _fv(z), _fv(radius), flag0, flag1, flag2, flag3, _f, _f, _f, _f, _r);
};

/**
 * Returns all commands that are registered in the command system.
 * The data returned adheres to the following layout:
 * ```
 * [
 * {
 * "name": "cmdlist",
 * "resource": "resource",
 * "arity" = -1,
 * },
 * {
 * "name": "command1"
 * "resource": "resource_2",
 * "arity" = -1,
 * }
 * ]
 * ```
 * @return An object containing registered commands.
 */
global.GetRegisteredCommands = function () {
	return _in(0x00000000, 0xd4bef069, _r, _ro);
};

/**
 * GET_RESOURCE_BY_FIND_INDEX
 * @param findIndex The index of the resource (starting at 0)
 * @return The resource name as a `string`
 */
global.GetResourceByFindIndex = function (findIndex) {
	return _in(0x00000000, 0x387246b7, findIndex, _r, _s);
};

/**
 * Returns all commands registered by the specified resource.
 * The data returned adheres to the following layout:
 * ```
 * [
 * {
 * "name": "cmdlist",
 * "resource": "example_resource",
 * "arity" = -1,
 * },
 * {
 * "name": "command1"
 * "resource": "example_resource2",
 * "arity" = -1,
 * }
 * ]
 * ```
 * @return An object containing registered commands.
 */
global.GetResourceCommands = function (resource) {
	return _in(0x00000000, 0x97628584, _ts(resource), _r, _ro);
};

/**
 * A getter for [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938).
 * @param key The key to fetch
 * @return The floating-point value stored under the specified key, or 0.0 if not found.
 */
global.GetResourceKvpFloat = function (key) {
	return _in(0x00000000, 0x35bdceea, _ts(key), _r, _rf);
};

/**
 * A getter for [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8).
 * @param key The key to fetch
 * @return The integer value stored under the specified key, or 0 if not found.
 */
global.GetResourceKvpInt = function (key) {
	return _in(0x00000000, 0x557b586a, _ts(key), _r, _ri);
};

/**
 * A getter for [SET_RESOURCE_KVP](#\_0x21C7A35B).
 * @param key The key to fetch
 * @return The string value stored under the specified key, or nil/null if not found.
 */
global.GetResourceKvpString = function (key) {
	return _in(0x00000000, 0x5240da5a, _ts(key), _r, _s);
};

/**
 * Gets the metadata value at a specified key/index from a resource's manifest.
 * See also: [Resource manifest](https://docs.fivem.net/docs/scripting-reference/resource-manifest/resource-manifest/)
 * @param resourceName The resource name.
 * @param metadataKey The key in the resource manifest.
 * @param index The value index, in a range from \[0..GET_NUM_RESOURCE_METDATA-1].
 */
global.GetResourceMetadata = function (resourceName, metadataKey, index) {
	return _in(0x00000000, 0x964bab1d, _ts(resourceName), _ts(metadataKey), index, _r, _s);
};

/**
 * Returns the current state of the specified resource.
 * @param resourceName The name of the resource.
 * @return The resource state. One of `"missing", "started", "starting", "stopped", "stopping", "uninitialized" or "unknown"`.
 */
global.GetResourceState = function (resourceName) {
	return _in(0x00000000, 0x4039b485, _ts(resourceName), _r, _s);
};

global.GetReturnToFilterMenu = function () {
	return _in(0x00000000, 0x2a055afa, _r);
};

global.GetRoomKeyFromObject = function (obj, pRoomKey) {
	return _in(0x00000000, 0x561509ad, obj, _ii(pRoomKey) /* may be optional */);
};

global.GetRoomKeyFromPickup = function (pickup, hash) {
	return _in(0x00000000, 0x28045c47, pickup, _ii(hash) /* may be optional */);
};

global.GetRootCam = function (rootcam) {
	return _in(0x00000000, 0x75e005f1, _ii(rootcam) /* may be optional */);
};

global.GetRouteSize = function () {
	return _in(0x00000000, 0x086138de, _r, _ri);
};

global.GetSafePickupCoords = function (x, y, z) {
	return _in(0x00000000, 0x1ae44443, _fv(x), _fv(y), _fv(z), _f, _f, _f);
};

global.GetSafePositionForChar = function (x, y, z, unknownTrue) {
	return _in(0x00000000, 0x5d877285, _fv(x), _fv(y), _fv(z), unknownTrue, _f, _f, _f, _r);
};

global.GetScreenFadeAlpha = function () {
	return _in(0x00000000, 0x04161e66, _r, _ri);
};

global.GetScreenResolution = function () {
	return _in(0x00000000, 0x0d8a1bcf, _f, _f);
};

global.GetScreenViewportId = function (viewportid) {
	return _in(0x00000000, 0x25271044, _ii(viewportid) /* may be optional */);
};

global.GetScriptCam = function (cam) {
	return _in(0x00000000, 0x5f00596c, _ii(cam) /* may be optional */);
};

global.GetScriptDrawCam = function (cam) {
	return _in(0x00000000, 0x30f71bc6, _ii(cam) /* may be optional */);
};

global.GetScriptFireCoords = function (fire) {
	return _in(0x00000000, 0x4f256f49, fire, _f, _f, _f);
};

global.GetScriptRendertargetRenderId = function (pRenderId) {
	return _in(0x00000000, 0x58296b19, _ii(pRenderId) /* may be optional */);
};

global.GetScriptTaskStatus = function (ped, task, status) {
	return _in(0x00000000, 0x74c14d31, ped, task, _ii(status) /* may be optional */);
};

global.GetSequenceProgress = function (seq, progress) {
	return _in(0x00000000, 0x1fbd3aca, seq, _ii(progress) /* may be optional */);
};

global.GetServerId = function () {
	return _in(0x00000000, 0x51983f94, _r, _ri);
};

global.GetSimpleBlipId = function () {
	return _in(0x00000000, 0x047b0898, _r, _ri);
};

global.GetSortedNetworkRestartNode = function (Unk1025, Unk1026, Unk1027, Unk1028, Unk1029, Unk1030, Unk1031, Unk1032, Unk1033) {
	return _in(0x00000000, 0x5bf71b87, Unk1025, Unk1026, Unk1027, Unk1028, Unk1029, Unk1030, Unk1031, Unk1032, Unk1033, _r, _ri);
};

global.GetSortedNetworkRestartNodeUsingGroupList = function (Unk1034, Unk1035, Unk1036, Unk1037, Unk1038, Unk1039, Unk1040, Unk1041, Unk1042) {
	return _in(0x00000000, 0x22463e22, Unk1034, Unk1035, Unk1036, Unk1037, Unk1038, Unk1039, Unk1040, Unk1041, Unk1042, _r, _ri);
};

global.GetSoundId = function () {
	return _in(0x00000000, 0x6342018a, _r, _ri);
};

global.GetSoundLevelAtCoords = function (ped, x, y, z, level) {
	return _in(0x00000000, 0x433e74c6, ped, _fv(x), _fv(y), _fv(z), _ii(level) /* may be optional */);
};

global.GetSpawnCoordinatesForCarNode = function (Unk918, Unk919, Unk920, Unk921, Unk922, Unk923) {
	return _in(0x00000000, 0x5b386b6c, Unk918, Unk919, Unk920, Unk921, Unk922, Unk923);
};

global.GetSpeechForEmergencyServiceCall = function () {
	return _in(0x00000000, 0x1b915945, _r, _s);
};

global.GetStartFromFilterMenu = function () {
	return _in(0x00000000, 0x45073c46, _r, _ri);
};

global.GetStatFrontendDisplayType = function (stat) {
	return _in(0x00000000, 0x347c4300, stat, _r, _ri);
};

global.GetStatFrontendVisibility = function (stat) {
	return _in(0x00000000, 0x38905687, stat, _r);
};

/**
 * GET_STATE_BAG_KEYS
 * @param bagName The name of the bag.
 * @return Returns an array containing all keys for which the state bag has associated values.
 */
global.GetStateBagKeys = function (bagName) {
	return _in(0x00000000, 0x78d864c7, _ts(bagName), _r, _ro);
};

/**
 * Returns the value of a state bag key.
 * @return Value.
 */
global.GetStateBagValue = function (bagName, key) {
	return _in(0x00000000, 0x637f4c75, _ts(bagName), _ts(key), _r, _ro);
};

global.GetStateOfClosestDoorOfType = function (model, x, y, z) {
	return _in(0x00000000, 0x14007ac6, model, _fv(x), _fv(y), _fv(z), _i, _f);
};

global.GetStaticEmitterPlaytime = function (StaticEmitterIndex) {
	return _in(0x00000000, 0x068774a4, StaticEmitterIndex, _r, _ri);
};

global.GetStationName = function (train, station) {
	return _in(0x00000000, 0x46f87f55, train, station, _r, _s);
};

global.GetStreamBeatInfo = function () {
	return _in(0x00000000, 0x6a3a2c88, _i, _i, _i);
};

global.GetStreamPlaytime = function () {
	return _in(0x00000000, 0x4b6211f2, _r, _ri);
};

global.GetStringFromHashKey = function (hash) {
	return _in(0x00000000, 0x16e14ea4, hash, _r, _s);
};

global.GetStringFromString = function (str, startsymb, endsymb) {
	return _in(0x00000000, 0x434534be, _ts(str), startsymb, endsymb, _r, _s);
};

global.GetStringFromTextFile = function (gxtentry) {
	return _in(0x00000000, 0x332f0e9a, _ts(gxtentry), _r, _s);
};

global.GetStringWidth = function (gxtName) {
	return _in(0x00000000, 0x64660709, _ts(gxtName), _r, _ri);
};

global.GetStringWidthWithNumber = function (gxtName, number) {
	return _in(0x00000000, 0x33e0601d, _ts(gxtName), number, _r, _ri);
};

global.GetStringWidthWithString = function (gxtName, literalString) {
	return _in(0x00000000, 0x48850e66, _ts(gxtName), _ts(literalString), _r, _rf);
};

global.GetStringWidthWithTextAndInt = function (gxtname, gxtnamenext, val) {
	return _in(0x00000000, 0x05267b97, _ts(gxtname), _ts(gxtnamenext), val, _r, _ri);
};

global.GetTaskPlaceCarBombUnsuccessful = function () {
	return _in(0x00000000, 0x0a4608e9, _r);
};

global.GetTeamRgbColour = function (team) {
	return _in(0x00000000, 0x42f561f2, team, _i, _i, _i);
};

global.GetTextInputActive = function () {
	return _in(0x00000000, 0x32a3647c, _r);
};

global.GetTexture = function (dictionary, textureName) {
	return _in(0x00000000, 0x0f5d1937, dictionary, _ts(textureName), _r, _ri);
};

global.GetTextureFromStreamedTxd = function (txdName, textureName) {
	return _in(0x00000000, 0x32c24491, _ts(txdName), _ts(textureName), _r, _ri);
};

global.GetTextureResolution = function (texture) {
	return _in(0x00000000, 0x01a75f0c, texture, _f, _f);
};

global.GetTimeOfDay = function () {
	return _in(0x00000000, 0x384b3876, _i, _i);
};

global.GetTimeSinceLastArrest = function () {
	return _in(0x00000000, 0x475d2bea, _r, _ri);
};

global.GetTimeSinceLastDeath = function () {
	return _in(0x00000000, 0x11162a93, _r, _ri);
};

global.GetTimeSincePlayerDroveAgainstTraffic = function (playerIndex) {
	return _in(0x00000000, 0x3b007e58, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerDroveOnPavement = function (playerIndex) {
	return _in(0x00000000, 0x19610e35, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerHitBuilding = function (playerIndex) {
	return _in(0x00000000, 0x126c0b99, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerHitCar = function (playerIndex) {
	return _in(0x00000000, 0x58c01823, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerHitObject = function (playerIndex) {
	return _in(0x00000000, 0x43c2796b, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerHitPed = function (playerIndex) {
	return _in(0x00000000, 0x40602b66, playerIndex, _r, _ri);
};

global.GetTimeSincePlayerRanLight = function (playerIndex) {
	return _in(0x00000000, 0x65d95395, playerIndex, _r, _ri);
};

global.GetTimeTilNextStation = function (train) {
	return _in(0x00000000, 0x142e7c40, train, _r, _rf);
};

/**
 * GET_TIMECYCLE_MODIFIER_COUNT
 * @return Returns the amount of timecycle modifiers loaded.
 */
global.GetTimecycleModifierCount = function () {
	return _in(0x00000000, 0xfe2a1d4d, _r, _ri);
};

/**
 * GET_TIMECYCLE_MODIFIER_INDEX_BY_NAME
 * @param modifierName The timecycle modifier name.
 * @return The timecycle modifier index.
 */
global.GetTimecycleModifierIndexByName = function (modifierName) {
	return _in(0x00000000, 0x5f4cd0e2, _ts(modifierName), _r, _ri);
};

/**
 * GET_TIMECYCLE_MODIFIER_NAME_BY_INDEX
 * @param modifierIndex The timecycle modifier index.
 * @return The timecycle modifier name.
 */
global.GetTimecycleModifierNameByIndex = function (modifierIndex) {
	return _in(0x00000000, 0x28cb8608, modifierIndex, _r, _s);
};

/**
 * A getter for [SET_TIMECYCLE_MODIFIER_STRENGTH](#\_0x82E7FFCD5B2326B3).
 * @return Returns current timecycle modifier strength.
 */
global.GetTimecycleModifierStrength = function () {
	return _in(0x00000000, 0xbe54124a, _r, _rf);
};

/**
 * GET_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @return Whether or not variable by name was found on the specified timecycle modifier.
 */
global.GetTimecycleModifierVar = function (modifierName, varName) {
	return _in(0x00000000, 0xa7109e12, _ts(modifierName), _ts(varName), _f, _f, _r);
};

/**
 * GET_TIMECYCLE_MODIFIER_VAR_COUNT
 * @param modifierName The timecycle modifier name.
 * @return The amount of variables used on a specified timecycle modifier.
 */
global.GetTimecycleModifierVarCount = function (modifierName) {
	return _in(0x00000000, 0x60fb60fe, _ts(modifierName), _r, _ri);
};

/**
 * GET_TIMECYCLE_MODIFIER_VAR_NAME_BY_INDEX
 * @param modifierName The name of timecycle modifier.
 * @param modifierVarIndex The index of a variable on the specified timecycle modifier.
 * @return The name of a variable by index.
 */
global.GetTimecycleModifierVarNameByIndex = function (modifierName, modifierVarIndex) {
	return _in(0x00000000, 0xe874ab1d, _ts(modifierName), modifierVarIndex, _r, _s);
};

/**
 * Returns the amount of variables available to be applied on timecycle modifiers.
 * @return The amount of available variables for timecycle modifiers.
 */
global.GetTimecycleVarCount = function () {
	return _in(0x00000000, 0x838b34d8, _r, _ri);
};

/**
 * See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
 * @param varIndex The index of variable.
 * @return The default value of a timecycle variable.
 */
global.GetTimecycleVarDefaultValueByIndex = function (varIndex) {
	return _in(0x00000000, 0x03b90238, varIndex, _r, _rf);
};

/**
 * See [GET_TIMECYCLE_VAR_COUNT](#\_0x838B34D8).
 * @param varIndex The index of variable.
 * @return The name of a timecycle variable.
 */
global.GetTimecycleVarNameByIndex = function (varIndex) {
	return _in(0x00000000, 0xc6c55aaf, varIndex, _r, _s);
};

global.GetTotalDurationOfCarRecording = function (CarRec) {
	return _in(0x00000000, 0x5f8c3937, CarRec, _r, _rf);
};

global.GetTotalNumberOfStats = function () {
	return _in(0x00000000, 0x6d823703, _r, _ri);
};

global.GetTrainCaboose = function (train, caboose) {
	return _in(0x00000000, 0x3fb72d27, train, _ii(caboose) /* may be optional */);
};

global.GetTrainCarriage = function (train, num, carriage) {
	return _in(0x00000000, 0x7f861e46, train, num, _ii(carriage) /* may be optional */);
};

global.GetTrainPlayerWouldEnter = function (player, train) {
	return _in(0x00000000, 0x30481141, player, _ii(train) /* may be optional */);
};

global.GetTxd = function (txdName) {
	return _in(0x00000000, 0x15d668d0, _ts(txdName), _r, _ri);
};

global.GetVehicleComponentInfo = function (veh, component_id, flag) {
	return _in(0x00000000, 0x3b5d0f27, veh, component_id, _v, _v, _i, flag, _r);
};

global.GetVehicleDirtLevel = function (vehicle, pIntensity) {
	return _in(0x00000000, 0x571152f5, vehicle, _fi(pIntensity) /* may be optional */);
};

global.GetVehicleEngineRevs = function (veh) {
	return _in(0x00000000, 0x2ffa0249, veh, _r, _rf);
};

global.GetVehicleFromNetworkId = function (netid, vehicle) {
	return _in(0x00000000, 0x794e4a82, netid, _ii(vehicle) /* may be optional */);
};

global.GetVehicleGear = function (veh) {
	return _in(0x00000000, 0x2d2f452d, veh, _r, _ri);
};

/**
 * **Note**: Flags are not the same based on your `gamebuild`. Please see [here](https://docs.fivem.net/docs/game-references/vehicle-references/vehicle-flags) to see a complete list of all vehicle flags.
 * Get vehicle.meta flag by index. Useful examples include `FLAG_LAW_ENFORCEMENT` (31), `FLAG_RICH_CAR` (36), `FLAG_IS_ELECTRIC` (43), `FLAG_IS_OFFROAD_VEHICLE` (48).
 * @param vehicle The vehicle to obtain flags for.
 * @param flagIndex Flag index.
 * @return A boolean for whether the flag is set.### Example```lua
 * 		local vehicleFlags = {
 * 		    FLAG_SMALL_WORKER = 0,
 * 		    FLAG_BIG = 1,
 * 		    FLAG_NO_BOOT = 2,
 * 		    FLAG_ONLY_DURING_OFFICE_HOURS = 3
 * 		    -- This is just a example, see fivem-docs to see all flags.
 * 		}
 * 		
 * 		local function getAllVehicleFlags(vehicle)
 * 		    local flags = {}
 * 		    for i = 0, 256 do
 * 		        if GetVehicleHasFlag(vehicle, i) then
 * 		            flags[#flags+1] = i
 * 		        end
 * 		    end
 * 		    return flags
 * 		end
 * 		
 * 		local flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
 * 		print(getAllVehicleFlags)
 * 		``````javascript
 * 		const VEHICLE_FLAGS = {
 * 		    FLAG_SMALL_WORKER: 0,
 * 		    FLAG_BIG: 1,
 * 		    FLAG_NO_BOOT: 2,
 * 		    FLAG_ONLY_DURING_OFFICE_HOURS: 3
 * 		    // This is just a example, see fivem-docs to see all flags.
 * 		};
 * 		
 * 		function getAllVehicleFlags(mVehicle = GetVehiclePedIsIn(PlayerPedId(), false)) {
 * 		    const flags = [];
 * 		    for (let i = 0; i < 204; i++) {
 * 		        if (GetVehicleHasFlag(mVehicle, i)) {
 * 		            flags.push(i);
 * 		        }
 * 		    }
 * 		    return flags;
 * 		}
 * 		
 * 		let flagsVehicle = GetVehiclePedIsIn(PlayerPedId(), false);
 * 		console.log(getAllVehicleFlags);
 * 		```
 */
global.GetVehicleHasFlag = function (vehicle, flagIndex) {
	return _in(0x00000000, 0xd85c9f57, vehicle, flagIndex, _r);
};

global.GetVehicleModelFromIndex = function (index) {
	return _in(0x00000000, 0x7e5c70bf, index, _r, _ri);
};

global.GetVehiclePlayerWouldEnter = function (player, veh) {
	return _in(0x00000000, 0x20430265, player, _ii(veh) /* may be optional */);
};

global.GetVehicleQuaternion = function (veh) {
	return _in(0x00000000, 0x6c5871d6, veh, _f, _f, _f, _f);
};

/**
 * Returns the type of the passed vehicle.
 * For client scripts, reference the more detailed [GET_VEHICLE_TYPE_RAW](#\_0xDE73BC10) native.
 * ### Vehicle types
 * *   automobile
 * *   bike
 * *   boat
 * *   heli
 * *   plane
 * *   submarine
 * *   trailer
 * *   train
 * @param vehicle The vehicle's entity handle.
 * @return If the entity is a vehicle, the vehicle type. If it is not a vehicle, the return value will be null.
 */
global.GetVehicleType = function (vehicle) {
	return _in(0x00000000, 0xa273060e, vehicle, _r, _s);
};

global.GetVehicleTypeOfModel = function (model) {
	return _in(0x00000000, 0x60f720f6, model, _r, _ri);
};

/**
 * Returns the model type of the vehicle as defined by:
 * ```cpp
 * enum VehicleType
 * {
 * VEHICLE_TYPE_NONE = -1,
 * VEHICLE_TYPE_CAR = 0,
 * VEHICLE_TYPE_PLANE = 1,
 * VEHICLE_TYPE_TRAILER = 2,
 * VEHICLE_TYPE_QUADBIKE = 3,
 * VEHICLE_TYPE_DRAFT = 4,
 * VEHICLE_TYPE_SUBMARINECAR = 5,
 * VEHICLE_TYPE_AMPHIBIOUS_AUTOMOBILE = 6,
 * VEHICLE_TYPE_AMPHIBIOUS_QUADBIKE = 7,
 * VEHICLE_TYPE_HELI = 8,
 * VEHICLE_TYPE_BLIMP = 9,
 * VEHICLE_TYPE_AUTOGYRO = 10,
 * VEHICLE_TYPE_BIKE = 11,
 * VEHICLE_TYPE_BICYCLE = 12,
 * VEHICLE_TYPE_BOAT = 13,
 * VEHICLE_TYPE_TRAIN = 14,
 * VEHICLE_TYPE_SUBMARINE = 15,
 * };
 * ```
 * @param vehicle The vehicle's entity handle.
 * @return Returns the vehicles model type
 */
global.GetVehicleTypeRaw = function (vehicle) {
	return _in(0x00000000, 0xde73bc10, vehicle, _r, _ri);
};

global.GetViewportPosAndSize = function (viewportid) {
	return _in(0x00000000, 0x4ddc6fb4, viewportid, _f, _f, _f, _f);
};

global.GetViewportPositionOfCoord = function (x, y, z) {
	return _in(0x00000000, 0x287a49a5, _fv(x), _fv(y), _fv(z), _f, _f, _f, _r);
};

/**
 * A getter for [SET_VISUAL_SETTING_FLOAT](#\_0xD1D31681).
 * @param name The name of the value to get, such as `pedLight.color.red`.
 * @return Returns the floating point value of the specified visual setting on success.
 */
global.GetVisualSettingFloat = function (name) {
	return _in(0x00000000, 0x15346b4d, _ts(name), _r, _rf);
};

global.GetWaterHeight = function (x, y, z, pheight) {
	return _in(0x00000000, 0x2bb9620f, _fv(x), _fv(y), _fv(z), _fi(pheight) /* may be optional */, _r);
};

global.GetWaterHeightNoWaves = function (x, y, z, height) {
	return _in(0x00000000, 0x67c82864, _fv(x), _fv(y), _fv(z), _fi(height) /* may be optional */, _r);
};

global.GetWeapontypeModel = function (weapontype, model) {
	return _in(0x00000000, 0x4fe23f25, weapontype, _ii(model) /* may be optional */);
};

global.GetWeapontypeSlot = function (weapon, slot) {
	return _in(0x00000000, 0x5e4f6de3, weapon, _ii(slot) /* may be optional */);
};

global.GetWebPageHeight = function (htmlviewport) {
	return _in(0x00000000, 0x09fd24f3, htmlviewport, _r, _rf);
};

global.GetWebPageLinkAtPosn = function (htmlviewport, x, y) {
	return _in(0x00000000, 0x0c1c5b1b, htmlviewport, _fv(x), _fv(y), _r, _ri);
};

global.GetWebPageLinkHref = function (htmlviewport, linkid) {
	return _in(0x00000000, 0x750c1cd7, htmlviewport, linkid, _r, _s);
};

global.GetWebPageLinkPosn = function (htmlviewport, linkid) {
	return _in(0x00000000, 0x717b5efb, htmlviewport, linkid, _f, _f);
};

global.GetWidthOfLiteralString = function (str) {
	return _in(0x00000000, 0x164b7363, _ts(str), _r, _ri);
};

global.GetWidthOfSubstringGivenTextLabel = function (gxtname, Unk611, Unk612, Unk613, Unk614) {
	return _in(0x00000000, 0x64e51535, _ts(gxtname), Unk611, Unk612, Unk613, Unk614, _r, _ri);
};

/**
 * Converts a screen coordinate into its relative world coordinate.
 * @param screenX A screen horizontal axis coordinate (0.0 - 1.0).
 * @param screenY A screen vertical axis coordinate (0.0 - 1.0).
 * @param worldVector The world coord vector pointer.
 * @param normalVector The screen normal vector pointer.
 * @return A Vector3 representing the world coordinates relative to the specified screen coordinates and a screen plane normal Vector3 (normalised).
 */
global.GetWorldCoordFromScreenCoord = function (screenX, screenY) {
	return _in(0x00000000, 0xc81d0659, _fv(screenX), _fv(screenY), _v, _v);
};

global.GiveDelayedWeaponToChar = function (ped, weapon, delaytime, flag) {
	return _in(0x00000000, 0x709154fc, ped, weapon, delaytime, flag);
};

global.GivePedAmbientObject = function (ped, model) {
	return _in(0x00000000, 0x44aa71f9, ped, model);
};

global.GivePedFakeNetworkName = function (ped, name, r, g, b, a) {
	return _in(0x00000000, 0x55e0158b, ped, _ts(name), r, g, b, a);
};

global.GivePedHelmet = function (ped) {
	return _in(0x00000000, 0x07a0177d, ped);
};

global.GivePedHelmetWithOpts = function (ped, Unk42) {
	return _in(0x00000000, 0x3b6e1d1e, ped, Unk42);
};

global.GivePedPickupObject = function (ped, obj, flag) {
	return _in(0x00000000, 0x684d1517, ped, obj, flag);
};

global.GivePlayerRagdollControl = function (player, give) {
	return _in(0x00000000, 0x5a1d7a2f, player, give);
};

global.GiveWeaponToChar = function (ped, weapon, ammo, unknown0) {
	return _in(0x00000000, 0x03e90416, ped, weapon, ammo, unknown0);
};

global.GrabNearbyObjectWithSpecialAttribute = function (attribute, obj) {
	return _in(0x00000000, 0x256472f1, attribute, _ii(obj) /* may be optional */);
};

global.HandVehicleControlBackToPlayer = function (veh) {
	return _in(0x00000000, 0x6c654678, veh);
};

global.HandleAudioAnimEvent = function (ped, AudioAnimEventName) {
	return _in(0x00000000, 0x56c15139, ped, _ts(AudioAnimEventName));
};

global.HasAchievementBeenPassed = function (achievement) {
	return _in(0x00000000, 0x32765f37, achievement, _r);
};

global.HasAdditionalTextLoaded = function (textIndex) {
	return _in(0x00000000, 0x4832644e, textIndex, _r);
};

global.HasCarBeenDamagedByCar = function (vehicle, otherCar) {
	return _in(0x00000000, 0x119a668d, vehicle, otherCar, _r);
};

global.HasCarBeenDamagedByChar = function (vehicle, ped) {
	return _in(0x00000000, 0x61487dbf, vehicle, ped, _r);
};

global.HasCarBeenDamagedByWeapon = function (vehicle, weapon) {
	return _in(0x00000000, 0x0ee34390, vehicle, weapon, _r);
};

global.HasCarBeenDroppedOff = function (car) {
	return _in(0x00000000, 0x024c3a6c, car, _r);
};

global.HasCarBeenResprayed = function (vehicle) {
	return _in(0x00000000, 0x3d0432f2, vehicle, _r);
};

global.HasCarRecordingBeenLoaded = function (CarRec) {
	return _in(0x00000000, 0x453f587d, CarRec, _r);
};

global.HasCarStoppedBecauseOfLight = function (car) {
	return _in(0x00000000, 0x40cd2bd4, car, _r);
};

global.HasCharAnimFinished = function (ped, AnimName0, AnimName1) {
	return _in(0x00000000, 0x53f34027, ped, _ts(AnimName0), _ts(AnimName1), _r);
};

global.HasCharBeenArrested = function (ped) {
	return _in(0x00000000, 0x210a0879, ped, _r);
};

global.HasCharBeenDamagedByCar = function (ped, vehicle) {
	return _in(0x00000000, 0x30a65021, ped, vehicle, _r);
};

global.HasCharBeenDamagedByChar = function (ped, otherChar, unknownFalse) {
	return _in(0x00000000, 0x1dd624a0, ped, otherChar, unknownFalse, _r);
};

global.HasCharBeenDamagedByWeapon = function (ped, weapon) {
	return _in(0x00000000, 0x6db26e07, ped, weapon, _r);
};

global.HasCharBeenPhotographed = function (ped) {
	return _in(0x00000000, 0x1f2928a6, ped, _r);
};

global.HasCharGotWeapon = function (ped, weapon) {
	return _in(0x00000000, 0x11f759de, ped, weapon, _r);
};

global.HasCharSpottedChar = function (ped, otherChar) {
	return _in(0x00000000, 0x1add68e8, ped, otherChar, _r);
};

global.HasCharSpottedCharInFront = function (ped, otherChar) {
	return _in(0x00000000, 0x156d5236, ped, otherChar, _r);
};

global.HasClosestObjectOfTypeBeenDamagedByCar = function (x, y, z, radius, type_or_model, car) {
	return _in(0x00000000, 0x4d6b3e20, _fv(x), _fv(y), _fv(z), _fv(radius), type_or_model, car, _r);
};

global.HasClosestObjectOfTypeBeenDamagedByChar = function (x, y, z, radius, objectModel, ped) {
	return _in(0x00000000, 0x1fc90c7c, _fv(x), _fv(y), _fv(z), _fv(radius), objectModel, ped, _r);
};

global.HasCollisionForModelLoaded = function (model) {
	return _in(0x00000000, 0x7c3939e7, model, _r);
};

global.HasControlOfNetworkId = function (netid) {
	return _in(0x00000000, 0x176c2db5, netid, _r);
};

global.HasCutsceneFinished = function () {
	return _in(0x00000000, 0x4ece1ad2, _r);
};

global.HasCutsceneLoaded = function () {
	return _in(0x00000000, 0x5de43980, _r);
};

global.HasDeatharrestExecuted = function () {
	return _in(0x00000000, 0x3b0c6738, _r);
};

global.HasFragmentRootOfClosestObjectOfTypeBeenDamaged = function (x, y, z, radius, Unk70) {
	return _in(0x00000000, 0x31b64d2b, _fv(x), _fv(y), _fv(z), _fv(radius), Unk70, _r);
};

global.HasGamerChangedNetworkModelSettings = function () {
	return _in(0x00000000, 0x7ebb00d7, _r);
};

global.HasModelLoaded = function (model) {
	return _in(0x00000000, 0x4e61480a, model, _r);
};

global.HasNetworkPlayerLeftGame = function (playerIndex) {
	return _in(0x00000000, 0x135154b0, playerIndex, _r);
};

global.HasObjectBeenDamaged = function (obj) {
	return _in(0x00000000, 0x7e0d6cb8, obj, _r);
};

global.HasObjectBeenDamagedByCar = function (obj, vehicle) {
	return _in(0x00000000, 0x50801274, obj, vehicle, _r);
};

global.HasObjectBeenDamagedByChar = function (obj, ped) {
	return _in(0x00000000, 0x0b464be8, obj, ped, _r);
};

global.HasObjectBeenDamagedByWeapon = function (obj, Unk71) {
	return _in(0x00000000, 0x547c42b1, obj, Unk71, _r);
};

global.HasObjectBeenPhotographed = function (obj) {
	return _in(0x00000000, 0x57895f38, obj, _r);
};

global.HasObjectBeenUprooted = function (obj) {
	return _in(0x00000000, 0x58737620, obj, _r);
};

global.HasObjectCollidedWithAnything = function (obj) {
	return _in(0x00000000, 0x106811e4, obj, _r);
};

global.HasObjectFragmentRootBeenDamaged = function (obj) {
	return _in(0x00000000, 0x3162071d, obj, _r);
};

global.HasOverridenSitIdleAnimFinished = function (ped) {
	return _in(0x00000000, 0x520a745d, ped, _r);
};

global.HasPickupBeenCollected = function (pickup) {
	return _in(0x00000000, 0x2f2226e5, pickup, _r);
};

global.HasPlayerCollectedPickup = function (playerIndex, pikcup) {
	return _in(0x00000000, 0x025d2170, playerIndex, pikcup, _r);
};

global.HasPlayerDamagedAtLeastOnePed = function (playerIndex) {
	return _in(0x00000000, 0x64e06cbb, playerIndex, _r);
};

global.HasPlayerDamagedAtLeastOneVehicle = function (playerIndex) {
	return _in(0x00000000, 0x674849b5, playerIndex, _r);
};

global.HasPoolObjectCollidedWithCushion = function (obj) {
	return _in(0x00000000, 0x3e8d7d3f, obj, _r);
};

global.HasPoolObjectCollidedWithObject = function (obj, otherObj) {
	return _in(0x00000000, 0x24d70069, obj, otherObj, _r);
};

global.HasReloadedWithMotionControl = function (ukn0, ukn) {
	return _in(0x00000000, 0x08c6502c, ukn0, _i /* actually bool */, _r);
};

global.HasResprayHappened = function () {
	return _in(0x00000000, 0x465574b0, _r);
};

global.HasScriptLoaded = function (scriptName) {
	return _in(0x00000000, 0x2a171915, _ts(scriptName), _r);
};

global.HasSoundFinished = function (sound) {
	return _in(0x00000000, 0x2ca53aa1, sound, _r);
};

global.HasStreamedTxdLoaded = function (txdName) {
	return _in(0x00000000, 0x5f9c43d4, _ts(txdName), _r);
};

global.HasThisAdditionalTextLoaded = function (textName, textIndex) {
	return _in(0x00000000, 0x6cf248fd, _ts(textName), textIndex, _r);
};

global.HaveAnimsLoaded = function (animName) {
	return _in(0x00000000, 0x1d3f681d, _ts(animName), _r);
};

global.HaveRequestedPathNodesBeenLoaded = function (requestId) {
	return _in(0x00000000, 0x54dd5868, requestId, _r);
};

global.HeliAudioShouldSkipStartup = function (heli, skip) {
	return _in(0x00000000, 0x4cc001ac, heli, skip);
};

global.HideCharWeaponForScriptedCutscene = function (ped, hide) {
	return _in(0x00000000, 0x2b7c5cfb, ped, hide);
};

global.HideHelpTextThisFrame = function () {
	return _in(0x00000000, 0x16af6deb);
};

global.HideHudAndRadarThisFrame = function () {
	return _in(0x00000000, 0x60320feb);
};

global.HighFallScream = function (ped) {
	return _in(0x00000000, 0x478976db, ped);
};

global.HighlightMenuItem = function (menuid, item, highlight) {
	return _in(0x00000000, 0x1abe6a4c, menuid, item, highlight);
};

global.HintCam = function (x, y, z, Unk559, Unk560, Unk561, Unk562) {
	return _in(0x00000000, 0x1b637a1c, _fv(x), _fv(y), _fv(z), Unk559, Unk560, Unk561, Unk562);
};

global.HowLongHasNetworkPlayerBeenDeadFor = function (playerIndex) {
	return _in(0x00000000, 0x4e6120a9, playerIndex, _r, _ri);
};

global.ImproveLowPerformanceMissionPerFrameFlag = function () {
	return _in(0x00000000, 0x2b64229c);
};

global.IncreasePlayerMaxArmour = function (player, armour) {
	return _in(0x00000000, 0x2232704d, player, _fv(armour));
};

global.IncreasePlayerMaxHealth = function (player, maxhealth) {
	return _in(0x00000000, 0x40a703a6, player, maxhealth);
};

global.IncrementFloatStat = function (stat, val) {
	return _in(0x00000000, 0x548e3afc, stat, _fv(val));
};

global.IncrementFloatStatNoMessage = function (stat, value) {
	return _in(0x00000000, 0x2c6564f2, stat, _fv(value));
};

global.IncrementIntStat = function (stat, value) {
	return _in(0x00000000, 0x14d242d9, stat, value);
};

global.IncrementIntStatNoMessage = function (stat, value) {
	return _in(0x00000000, 0x29827605, stat, value);
};

global.InitCutscene = function (name) {
	return _in(0x00000000, 0x47e50bd3, _ts(name));
};

global.InitDebugWidgets = function () {
	return _in(0x00000000, 0x73e911e8);
};

global.InitFrontendHelperText = function () {
	return _in(0x00000000, 0x617b191d);
};

/**
 * IS_ACE_ALLOWED
 */
global.IsAceAllowed = function (object) {
	return _in(0x00000000, 0x7ebb9929, _ts(object), _r);
};

global.IsAmbientSpeechDisabled = function (ped) {
	return _in(0x00000000, 0x563f4cc2, ped, _r);
};

global.IsAmbientSpeechPlaying = function (ped) {
	return _in(0x00000000, 0x032f24cb, ped, _r);
};

global.IsAnyCharShootingInArea = function (x0, y0, z0, x1, y1, z1, flag) {
	return _in(0x00000000, 0x19d16ace, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), flag, _r);
};

global.IsAnyPickupAtCoords = function (x, y, z) {
	return _in(0x00000000, 0x75dc4737, _fv(x), _fv(y), _fv(z), _r);
};

global.IsAnySpeechPlaying = function (ped) {
	return _in(0x00000000, 0x170f7e75, ped, _r);
};

global.IsAreaOccupied = function (x1, y1, z1, x2, y2, z2, unknownFalse1, unknownTrue, unknownFalse2, unknownFalse3, unknownFalse4) {
	return _in(0x00000000, 0x5be1238d, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), unknownFalse1, unknownTrue, unknownFalse2, unknownFalse3, unknownFalse4, _r);
};

global.IsAutoAimingOn = function () {
	return _in(0x00000000, 0x366b0444, _r);
};

global.IsAutoSaveInProgress = function () {
	return _in(0x00000000, 0x601a5770, _r);
};

global.IsBigVehicle = function (vehicle) {
	return _in(0x00000000, 0x60305168, vehicle, _r);
};

global.IsBitSet = function (val, bitnum) {
	return _in(0x00000000, 0x5373544e, val, bitnum, _r);
};

global.IsBlipShortRange = function (blip) {
	return _in(0x00000000, 0x32e84b6a, blip, _r);
};

global.IsBulletInArea = function (x, y, z, radius, unknownTrue) {
	return _in(0x00000000, 0x58493b8e, _fv(x), _fv(y), _fv(z), _fv(radius), unknownTrue, _r);
};

global.IsBulletInBox = function (x1, y1, z1, x2, y2, z2, unknown) {
	return _in(0x00000000, 0x60964db8, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), unknown, _r);
};

global.IsButtonJustPressed = function (padIndex, button) {
	return _in(0x00000000, 0x016c37cd, padIndex, button, _r);
};

global.IsButtonPressed = function (padIndex, button) {
	return _in(0x00000000, 0x7ff21081, padIndex, button, _r);
};

global.IsCamActive = function (camera) {
	return _in(0x00000000, 0x348d7af5, camera, _r);
};

global.IsCamHappy = function (cam) {
	return _in(0x00000000, 0x7d95313b, cam, _r);
};

global.IsCamInterpolating = function () {
	return _in(0x00000000, 0x1ae118f4, _r);
};

global.IsCamPropagating = function (camera) {
	return _in(0x00000000, 0x7eac3387, camera, _r);
};

global.IsCamSequenceComplete = function (Unk535) {
	return _in(0x00000000, 0x55727056, Unk535, _r);
};

global.IsCamShaking = function () {
	return _in(0x00000000, 0x089c57d7, _r);
};

global.IsCarAMissionCar = function (vehicle) {
	return _in(0x00000000, 0x7a422e14, vehicle, _r);
};

global.IsCarAttached = function (vehicle) {
	return _in(0x00000000, 0x6bdc40eb, vehicle, _r);
};

global.IsCarDead = function (vehicle) {
	return _in(0x00000000, 0x2aab340a, vehicle, _r);
};

global.IsCarDoorDamaged = function (vehicle, door) {
	return _in(0x00000000, 0x5afe791f, vehicle, door, _r);
};

global.IsCarDoorFullyOpen = function (vehicle, door) {
	return _in(0x00000000, 0x55444602, vehicle, door, _r);
};

global.IsCarHealthGreater = function (car, health) {
	return _in(0x00000000, 0x63f07a46, car, _fv(health), _r);
};

global.IsCarInAirProper = function (vehicle) {
	return _in(0x00000000, 0x37bf18ac, vehicle, _r);
};

global.IsCarInArea_2d = function (vehicle, x1, y1, x2, y2, unknownFalse) {
	return _in(0x00000000, 0x7ea03481, vehicle, _fv(x1), _fv(y1), _fv(x2), _fv(y2), unknownFalse, _r);
};

global.IsCarInArea_3d = function (vehicle, x1, y1, z1, x2, y2, z2, unknownFalse) {
	return _in(0x00000000, 0x289d3888, vehicle, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), unknownFalse, _r);
};

global.IsCarInGarageArea = function (garageName, vehicle) {
	return _in(0x00000000, 0x005868e2, _ts(garageName), vehicle, _r);
};

global.IsCarInWater = function (vehicle) {
	return _in(0x00000000, 0x0ff342b2, vehicle, _r);
};

global.IsCarLowRider = function (car) {
	return _in(0x00000000, 0x6b3d5d45, car, _r);
};

global.IsCarModel = function (vehicle, model) {
	return _in(0x00000000, 0x03d16145, vehicle, model, _r);
};

global.IsCarOnFire = function (vehicle) {
	return _in(0x00000000, 0x189a2bb1, vehicle, _r);
};

global.IsCarOnScreen = function (vehicle) {
	return _in(0x00000000, 0x59e3553f, vehicle, _r);
};

global.IsCarPassengerSeatFree = function (vehicle, seatIndex) {
	return _in(0x00000000, 0x1bda0da5, vehicle, seatIndex, _r);
};

global.IsCarPlayingAnim = function (car, animname0, animname1) {
	return _in(0x00000000, 0x49f619f1, car, _ts(animname0), _ts(animname1), _r);
};

global.IsCarSirenOn = function (vehicle) {
	return _in(0x00000000, 0x129a1569, vehicle, _r);
};

global.IsCarStopped = function (vehicle) {
	return _in(0x00000000, 0x4a000f52, vehicle, _r);
};

global.IsCarStoppedAtTrafficLights = function (vehicle) {
	return _in(0x00000000, 0x141b23a9, vehicle, _r);
};

global.IsCarStreetRacer = function (car) {
	return _in(0x00000000, 0x24df32cc, car, _r);
};

global.IsCarStuck = function (car) {
	return _in(0x00000000, 0x0cd276b4, car, _r);
};

global.IsCarStuckOnRoof = function (vehicle) {
	return _in(0x00000000, 0x46892d07, vehicle, _r);
};

global.IsCarTouchingCar = function (vehicle, otherCar) {
	return _in(0x00000000, 0x7b014306, vehicle, otherCar, _r);
};

global.IsCarTyreBurst = function (vehicle, tyre) {
	return _in(0x00000000, 0x1df623f9, vehicle, tyre, _r);
};

global.IsCarUpright = function (vehicle) {
	return _in(0x00000000, 0x1a212500, vehicle, _r);
};

global.IsCarUpsidedown = function (vehicle) {
	return _in(0x00000000, 0x2e291239, vehicle, _r);
};

global.IsCarWaitingForWorldCollision = function (vehicle) {
	return _in(0x00000000, 0x6ea72622, vehicle, _r);
};

global.IsCharArmed = function (ped, slot) {
	return _in(0x00000000, 0x046a4720, ped, slot, _r);
};

global.IsCharDead = function (ped) {
	return _in(0x00000000, 0x6a6b4f18, ped, _r);
};

global.IsCharDucking = function (ped) {
	return _in(0x00000000, 0x495d6021, ped, _r);
};

global.IsCharFacingChar = function (ped, otherChar, angle) {
	return _in(0x00000000, 0x05ad758a, ped, otherChar, _fv(angle), _r);
};

global.IsCharFatallyInjured = function (ped) {
	return _in(0x00000000, 0x4a7802cb, ped, _r);
};

global.IsCharGesturing = function (ped) {
	return _in(0x00000000, 0x07025a4a, ped, _r);
};

global.IsCharGettingInToACar = function (ped) {
	return _in(0x00000000, 0x5c8c2e39, ped, _r);
};

global.IsCharGettingUp = function (ped) {
	return _in(0x00000000, 0x4a906237, ped, _r);
};

global.IsCharHealthGreater = function (ped, health) {
	return _in(0x00000000, 0x7b75036e, ped, health, _r);
};

global.IsCharInAir = function (ped) {
	return _in(0x00000000, 0x23c15141, ped, _r);
};

global.IsCharInAngledArea_2d = function (ped, x1, y1, x2, y2, unknown, unknownFalse) {
	return _in(0x00000000, 0x7d591ead, ped, _fv(x1), _fv(y1), _fv(x2), _fv(y2), _fv(unknown), unknownFalse, _r);
};

global.IsCharInAngledArea_3d = function (ped, x1, y1, z1, x2, y2, z2, unknown, unknownFalse) {
	return _in(0x00000000, 0x610157c9, ped, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), _fv(unknown), unknownFalse, _r);
};

global.IsCharInAnyBoat = function (ped) {
	return _in(0x00000000, 0x210a4f1d, ped, _r);
};

global.IsCharInAnyCar = function (ped) {
	return _in(0x00000000, 0x71184da3, ped, _r);
};

global.IsCharInAnyHeli = function (ped) {
	return _in(0x00000000, 0x0fc40275, ped, _r);
};

global.IsCharInAnyPlane = function (ped) {
	return _in(0x00000000, 0x4bac2912, ped, _r);
};

global.IsCharInAnyPoliceVehicle = function (ped) {
	return _in(0x00000000, 0x4414660b, ped, _r);
};

global.IsCharInAnyTrain = function (ped) {
	return _in(0x00000000, 0x22434c20, ped, _r);
};

global.IsCharInAreaOnFoot_2d = function (ped, x1, y1, x2, y2, unknownFalse) {
	return _in(0x00000000, 0x3f2d7d06, ped, _fv(x1), _fv(y1), _fv(x2), _fv(y2), unknownFalse, _r);
};

global.IsCharInArea_2d = function (ped, x1, y1, x2, y2, unknownFalse) {
	return _in(0x00000000, 0x7f371477, ped, _fv(x1), _fv(y1), _fv(x2), _fv(y2), unknownFalse, _r);
};

global.IsCharInArea_3d = function (ped, x1, y1, z1, x2, y2, z2, unknownFalse) {
	return _in(0x00000000, 0x44a30283, ped, _fv(x1), _fv(y1), _fv(z1), _fv(x2), _fv(y2), _fv(z2), unknownFalse, _r);
};

global.IsCharInCar = function (ped, vehicle) {
	return _in(0x00000000, 0x7d037b40, ped, vehicle, _r);
};

global.IsCharInFlyingVehicle = function (ped) {
	return _in(0x00000000, 0x7fa763e8, ped, _r);
};

global.IsCharInMeleeCombat = function (ped) {
	return _in(0x00000000, 0x68855be7, ped, _r);
};

global.IsCharInModel = function (ped, model) {
	return _in(0x00000000, 0x45db5fe9, ped, model, _r);
};

global.IsCharInTaxi = function (ped) {
	return _in(0x00000000, 0x28a73bca, ped, _r);
};

global.IsCharInWater = function (ped) {
	return _in(0x00000000, 0x7b1f0130, ped, _r);
};

global.IsCharInZone = function (ped, zonename) {
	return _in(0x00000000, 0x435054b3, ped, _ts(zonename), _r);
};

global.IsCharInjured = function (ped) {
	return _in(0x00000000, 0x4ecb2267, ped, _r);
};

global.IsCharMale = function (ped) {
	return _in(0x00000000, 0x7d76127f, ped, _r);
};

global.IsCharModel = function (ped, model) {
	return _in(0x00000000, 0x6c403acc, ped, model, _r);
};

global.IsCharOnAnyBike = function (ped) {
	return _in(0x00000000, 0x0fb44f54, ped, _r);
};

global.IsCharOnFire = function (ped) {
	return _in(0x00000000, 0x358e21c5, ped, _r);
};

global.IsCharOnFoot = function (ped) {
	return _in(0x00000000, 0x10a86cf4, ped, _r);
};

global.IsCharOnScreen = function (ped) {
	return _in(0x00000000, 0x59471b11, ped, _r);
};

global.IsCharPlayingAnim = function (ped, animSet, animName) {
	return _in(0x00000000, 0x673e4cd2, ped, _ts(animSet), _ts(animName), _r);
};

global.IsCharRespondingToAnyEvent = function (ped) {
	return _in(0x00000000, 0x5ddb09f8, ped, _r);
};

global.IsCharRespondingToEvent = function (ped, eventid) {
	return _in(0x00000000, 0x32653482, ped, eventid, _r);
};

global.IsCharShooting = function (ped) {
	return _in(0x00000000, 0x324d1594, ped, _r);
};

global.IsCharShootingInArea = function (ped, x1, y1, x2, y2, unknownFalse) {
	return _in(0x00000000, 0x42941472, ped, _fv(x1), _fv(y1), _fv(x2), _fv(y2), unknownFalse, _r);
};

global.IsCharSittingIdle = function (ped) {
	return _in(0x00000000, 0x064621f1, ped, _r);
};

global.IsCharSittingInAnyCar = function (ped) {
	return _in(0x00000000, 0x1dbd7385, ped, _r);
};

global.IsCharSittingInCar = function (ped, vehicle) {
	return _in(0x00000000, 0x309c265b, ped, vehicle, _r);
};

global.IsCharStopped = function (ped) {
	return _in(0x00000000, 0x0ca614e6, ped, _r);
};

global.IsCharStuckUnderCar = function (ped) {
	return _in(0x00000000, 0x70bb021a, ped, _r);
};

global.IsCharSwimming = function (ped) {
	return _in(0x00000000, 0x75d21b78, ped, _r);
};

global.IsCharTouchingChar = function (ped, otherChar) {
	return _in(0x00000000, 0x03fb6ded, ped, otherChar, _r);
};

global.IsCharTouchingObject = function (ped, obj) {
	return _in(0x00000000, 0x3ab06137, ped, obj, _r);
};

global.IsCharTouchingObjectOnFoot = function (ped, obj) {
	return _in(0x00000000, 0x7c0b46c8, ped, obj, _r);
};

global.IsCharTouchingVehicle = function (ped, vehicle) {
	return _in(0x00000000, 0x307a4b8e, ped, vehicle, _r);
};

global.IsCharTryingToEnterALockedCar = function (ped) {
	return _in(0x00000000, 0x1c132038, ped, _r);
};

global.IsCharUsingAnyScenario = function (ped) {
	return _in(0x00000000, 0x64bd4664, ped, _r);
};

global.IsCharUsingMapAttractor = function (ped) {
	return _in(0x00000000, 0x60b26d74, ped, _r);
};

global.IsCharUsingScenario = function (ped, scenarioName) {
	return _in(0x00000000, 0x62842540, ped, _ts(scenarioName), _r);
};

global.IsCharVisible = function (ped) {
	return _in(0x00000000, 0x0a0f19d1, ped, _r);
};

global.IsCharWaitingForWorldCollision = function (ped) {
	return _in(0x00000000, 0x51453ea2, ped, _r);
};

global.IsClosestObjectOfTypeSmashedOrDamaged = function (x, y, z, radius, type_or_model, flag0, flag1) {
	return _in(0x00000000, 0x788026f4, _fv(x), _fv(y), _fv(z), _fv(radius), type_or_model, flag0, flag1, _r);
};

global.IsControlJustPressed = function (Unk822, controlid) {
	return _in(0x00000000, 0x4cb729f1, Unk822, controlid, _r);
};

global.IsControlPressed = function (Unk823, controlid) {
	return _in(0x00000000, 0x0e635761, Unk823, controlid, _r);
};

global.IsCopPedInArea_3dNoSave = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x01866cb5, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r);
};

global.IsCopVehicleInArea_3dNoSave = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x72f81072, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r);
};

global.IsDamageTrackerActiveOnNetworkId = function (Unk882) {
	return _in(0x00000000, 0x5a2f2dd1, Unk882, _r);
};

global.IsDebugCameraOn = function () {
	return _in(0x00000000, 0x4e26149c, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is pressed down, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
global.IsDisabledRawKeyDown = function (rawKeyIndex) {
	return _in(0x00000000, 0x36366ec3, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is pressed, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of pressed state.
 */
global.IsDisabledRawKeyPressed = function (rawKeyIndex) {
	return _in(0x00000000, 0x1f7cbbaa, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` was released, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of released state.
 */
global.IsDisabledRawKeyReleased = function (rawKeyIndex) {
	return _in(0x00000000, 0x72b66c09, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is up, even if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014).
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of up state.
 */
global.IsDisabledRawKeyUp = function (rawKeyIndex) {
	return _in(0x00000000, 0x2c033875, rawKeyIndex, _r);
};

/**
 * Returns whether or not a browser is created for a specified DUI browser object.
 * @param duiObject The DUI browser handle.
 * @return A boolean indicating TRUE if the browser is created.
 */
global.IsDuiAvailable = function (duiObject) {
	return _in(0x00000000, 0x7aac3b4c, duiObject, _r);
};

/**
 * Gets whether or not this is the CitizenFX server.
 * @return A boolean value.
 */
global.IsDuplicityVersion = function () {
	return _in(0x00000000, 0xcf24c52e, _r);
};

global.IsEmergencyServicesVehicle = function (veh) {
	return _in(0x00000000, 0x6aff0587, veh, _r);
};

global.IsEpisodeAvailable = function (episode) {
	return _in(0x00000000, 0x232800bd, episode, _r);
};

global.IsEpisodicDiscBuild = function () {
	return _in(0x00000000, 0x511a2ec9, _r);
};

global.IsExplosionInArea = function (expnum, x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x676b6bca, expnum, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r);
};

global.IsExplosionInSphere = function (expnum, x, y, z, radius) {
	return _in(0x00000000, 0x47a77d2e, expnum, _fv(x), _fv(y), _fv(z), _fv(radius), _r);
};

global.IsFollowVehicleCamOffsetActive = function () {
	return _in(0x00000000, 0x40072120, _r);
};

global.IsFontLoaded = function (font) {
	return _in(0x00000000, 0x69b53ada, font, _r);
};

global.IsFrontendFading = function () {
	return _in(0x00000000, 0x09fd7668, _r);
};

global.IsGameInControlOfMusic = function () {
	return _in(0x00000000, 0x4ff71989, _r);
};

global.IsGameKeyboardKeyJustPressed = function (key) {
	return _in(0x00000000, 0x540d127d, key, _r);
};

global.IsGameKeyboardKeyPressed = function (key) {
	return _in(0x00000000, 0x5fa96262, key, _r);
};

global.IsGameKeyboardNavDownPressed = function (Unk824) {
	return _in(0x00000000, 0x45e45b1d, Unk824, _r);
};

global.IsGameKeyboardNavLeftPressed = function (Unk825) {
	return _in(0x00000000, 0x793f238a, Unk825, _r);
};

global.IsGameKeyboardNavRightPressed = function (Unk826) {
	return _in(0x00000000, 0x3c156533, Unk826, _r);
};

global.IsGameKeyboardNavUpPressed = function (Unk827) {
	return _in(0x00000000, 0x14ab75ae, Unk827, _r);
};

global.IsGarageClosed = function (garageName) {
	return _in(0x00000000, 0x26bc1939, _ts(garageName), _r);
};

global.IsGarageOpen = function (garageName) {
	return _in(0x00000000, 0x65a80992, _ts(garageName), _r);
};

global.IsGroupLeader = function (ped, group) {
	return _in(0x00000000, 0x2cec22da, ped, group, _r);
};

global.IsGroupMember = function (ped, group) {
	return _in(0x00000000, 0x674d6f8e, ped, group, _r);
};

global.IsHeliPartBroken = function (heli, flag0, flag1, flag2) {
	return _in(0x00000000, 0x1e2d5a7b, heli, flag0, flag1, flag2, _r);
};

global.IsHelpMessageBeingDisplayed = function () {
	return _in(0x00000000, 0x6e4e1bec, _r);
};

global.IsHintRunning = function () {
	return _in(0x00000000, 0x323806b1, _r);
};

global.IsHudPreferenceSwitchedOn = function () {
	return _in(0x00000000, 0x69604ae2, _r);
};

global.IsHudReticuleComplex = function () {
	return _in(0x00000000, 0x4ddb5d59, _r);
};

global.IsInCarFireButtonPressed = function () {
	return _in(0x00000000, 0x63b70f7c, _r);
};

global.IsInLanMode = function () {
	return _in(0x00000000, 0x1b8e7eed, _r);
};

global.IsInPlayerSettingsMenu = function () {
	return _in(0x00000000, 0x18ca2d3a, _r);
};

global.IsInSpectatorMode = function () {
	return _in(0x00000000, 0x07cc3f86, _r);
};

global.IsInteriorScene = function () {
	return _in(0x00000000, 0x61da102e, _r);
};

global.IsJapaneseVersion = function () {
	return _in(0x00000000, 0x37d022e0, _r);
};

global.IsKeyboardKeyJustPressed = function (key) {
	return _in(0x00000000, 0x75c9772b, key, _r);
};

global.IsKeyboardKeyPressed = function (key) {
	return _in(0x00000000, 0x1d334237, key, _r);
};

global.IsLazlowStationLocked = function () {
	return _in(0x00000000, 0x1cb80079, _r);
};

global.IsLcpdDataValid = function () {
	return _in(0x00000000, 0x611d69bc, _r);
};

global.IsLookInverted = function () {
	return _in(0x00000000, 0x1817000b, _r);
};

global.IsMemoryCardInUse = function () {
	return _in(0x00000000, 0x38f61531, _r);
};

global.IsMessageBeingDisplayed = function () {
	return _in(0x00000000, 0x68ea6ebe, _r);
};

global.IsMinigameInProgress = function () {
	return _in(0x00000000, 0x68f06a02, _r);
};

global.IsMissionCompletePlaying = function () {
	return _in(0x00000000, 0x6c3b5917, _r);
};

global.IsMobilePhoneCallOngoing = function () {
	return _in(0x00000000, 0x698f6172, _r);
};

global.IsMobilePhoneRadioActive = function () {
	return _in(0x00000000, 0x4af14146, _r);
};

global.IsModelInCdimage = function (model) {
	return _in(0x00000000, 0x771c2838, model, _r);
};

global.IsMoneyPickupAtCoords = function (x, y, z) {
	return _in(0x00000000, 0x43167c6e, _fv(x), _fv(y), _fv(z), _r);
};

global.IsMouseButtonJustPressed = function (Unk828) {
	return _in(0x00000000, 0x27323e51, Unk828, _r);
};

global.IsMouseButtonPressed = function (Unk829) {
	return _in(0x00000000, 0x39e600d0, Unk829, _r);
};

global.IsMouseUsingVerticalInversion = function () {
	return _in(0x00000000, 0x64655f10, _r);
};

global.IsNetworkGamePending = function () {
	return _in(0x00000000, 0x7563071d, _r);
};

global.IsNetworkGameRunning = function () {
	return _in(0x00000000, 0x1cf773d4, _r);
};

global.IsNetworkPlayerActive = function (playerIndex) {
	return _in(0x00000000, 0x4e237943, playerIndex, _r);
};

global.IsNetworkSession = function () {
	return _in(0x00000000, 0x6e2b38f3, _r);
};

global.IsNextStationAllowed = function (veh) {
	return _in(0x00000000, 0x7b8b1d10, veh, _r);
};

global.IsNonFragObjectSmashed = function (x, y, z, radius, model) {
	return _in(0x00000000, 0x5c723f31, _fv(x), _fv(y), _fv(z), _fv(radius), model, _r);
};

/**
 * Checks if keyboard input is enabled during NUI focus using `SET_NUI_FOCUS_KEEP_INPUT`.
 * @return True or false.
 */
global.IsNuiFocusKeepingInput = function () {
	return _in(0x00000000, 0x39c9dc92, _r);
};

/**
 * Returns the current NUI focus state previously set with `SET_NUI_FOCUS`.
 * @return True or false.
 */
global.IsNuiFocused = function () {
	return _in(0x00000000, 0x98545e6d, _r);
};

global.IsNumlockEnabled = function () {
	return _in(0x00000000, 0x39487fb9, _r);
};

global.IsObjectAttached = function (obj) {
	return _in(0x00000000, 0x701f4004, obj, _r);
};

global.IsObjectInAngledArea_3d = function (obj, x0, y0, z0, x1, y1, z1, Unk72, flag) {
	return _in(0x00000000, 0x5d5a06f7, obj, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _fv(Unk72), flag, _r);
};

global.IsObjectInArea_2d = function (obj, x0, y0, x1, y2, flag) {
	return _in(0x00000000, 0x2c6d65ad, obj, _fv(x0), _fv(y0), _fv(x1), _fv(y2), flag, _r);
};

global.IsObjectInArea_3d = function (obj, x0, y0, z0, x1, y1, z1, flag) {
	return _in(0x00000000, 0x6d717883, obj, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), flag, _r);
};

global.IsObjectInWater = function (obj) {
	return _in(0x00000000, 0x7bf7646f, obj, _r);
};

global.IsObjectOnFire = function (obj) {
	return _in(0x00000000, 0x7a240412, obj, _r);
};

global.IsObjectOnScreen = function (obj) {
	return _in(0x00000000, 0x6a9a3b1f, obj, _r);
};

global.IsObjectPlayingAnim = function (obj, animname0, animname1) {
	return _in(0x00000000, 0x4d2e58d5, obj, _ts(animname0), _ts(animname1), _r);
};

global.IsObjectReassignmentInProgress = function () {
	return _in(0x00000000, 0x7d0d6779, _r);
};

global.IsObjectStatic = function (obj) {
	return _in(0x00000000, 0x7b181eb0, obj, _r);
};

global.IsObjectTouchingObject = function (obj0, obj1) {
	return _in(0x00000000, 0x6a2e514f, obj0, obj1, _r);
};

global.IsObjectUpright = function (obj, angle) {
	return _in(0x00000000, 0x1ee13e29, obj, _fv(angle), _r);
};

global.IsObjectWithinBrainActivationRange = function (obj) {
	return _in(0x00000000, 0x472c710b, obj, _r);
};

global.IsOurPlayerHigherPriorityForCarGeneration = function (playerIndex) {
	return _in(0x00000000, 0x504e03fc, playerIndex, _r);
};

global.IsPainPlaying = function (ped) {
	return _in(0x00000000, 0x32422759, ped, _r);
};

global.IsPartyMode = function () {
	return _in(0x00000000, 0x2a3a77fd, _r);
};

global.IsPauseMenuActive = function () {
	return _in(0x00000000, 0x6c4568a7, _r);
};

global.IsPayNSprayActive = function () {
	return _in(0x00000000, 0x1ee70376, _r);
};

global.IsPcUsingJoypad = function () {
	return _in(0x00000000, 0x7e8e06f8, _r);
};

global.IsPedAMissionPed = function (ped) {
	return _in(0x00000000, 0x05801768, ped, _r);
};

global.IsPedAttachedToAnyCar = function (ped) {
	return _in(0x00000000, 0x78dc034e, ped, _r);
};

global.IsPedAttachedToObject = function (ped, obj) {
	return _in(0x00000000, 0x0bce3423, ped, obj, _r);
};

global.IsPedBeingJacked = function (ped) {
	return _in(0x00000000, 0x68b829c7, ped, _r);
};

global.IsPedClimbing = function (ped) {
	return _in(0x00000000, 0x66f5118f, ped, _r);
};

global.IsPedDoingDriveby = function (ped) {
	return _in(0x00000000, 0x080f3b37, ped, _r);
};

global.IsPedFleeing = function (ped) {
	return _in(0x00000000, 0x5e486aa1, ped, _r);
};

global.IsPedHoldingAnObject = function (ped) {
	return _in(0x00000000, 0x22811897, ped, _r);
};

global.IsPedInCombat = function (ped) {
	return _in(0x00000000, 0x020106d6, ped, _r);
};

global.IsPedInCover = function (ped) {
	return _in(0x00000000, 0x5c825d83, ped, _r);
};

global.IsPedInCutsceneBlockingBounds = function (ped) {
	return _in(0x00000000, 0x55916d7a, ped, _r);
};

global.IsPedInGroup = function (ped) {
	return _in(0x00000000, 0x365054a7, ped, _r);
};

global.IsPedJacking = function (ped) {
	return _in(0x00000000, 0x676f0004, ped, _r);
};

global.IsPedLookingAtCar = function (ped, car) {
	return _in(0x00000000, 0x4859273f, ped, car, _r);
};

global.IsPedLookingAtObject = function (ped, obj) {
	return _in(0x00000000, 0x5dd231a2, ped, obj, _r);
};

global.IsPedLookingAtPed = function (ped, otherChar) {
	return _in(0x00000000, 0x7f206a7f, ped, otherChar, _r);
};

global.IsPedPinnedDown = function (ped) {
	return _in(0x00000000, 0x03b13377, ped, _r);
};

global.IsPedRagdoll = function (ped) {
	return _in(0x00000000, 0x3e251ade, ped, _r);
};

global.IsPedRetreating = function (ped) {
	return _in(0x00000000, 0x7a0b156b, ped, _r);
};

global.IsPedsVehicleHot = function (ped) {
	return _in(0x00000000, 0x470a7cbd, ped, _r);
};

global.IsPlaceCarBombActive = function () {
	return _in(0x00000000, 0x775f6665, _r);
};

global.IsPlaybackGoingOnForCar = function (car) {
	return _in(0x00000000, 0x375f145d, car, _r);
};

global.IsPlayerBeingArrested = function () {
	return _in(0x00000000, 0x79a95bf9, _r);
};

global.IsPlayerClimbing = function (playerIndex) {
	return _in(0x00000000, 0x3bf5404e, playerIndex, _r);
};

global.IsPlayerControlOn = function (playerIndex) {
	return _in(0x00000000, 0x30cd2f1f, playerIndex, _r);
};

global.IsPlayerDead = function (playerIndex) {
	return _in(0x00000000, 0x12ae0e27, playerIndex, _r);
};

global.IsPlayerFreeAimingAtChar = function (playerIndex, ped) {
	return _in(0x00000000, 0x30d427b4, playerIndex, ped, _r);
};

global.IsPlayerFreeForAmbientTask = function (playerIndex) {
	return _in(0x00000000, 0x63e7509e, playerIndex, _r);
};

global.IsPlayerInRemoteMode = function (player) {
	return _in(0x00000000, 0x526b7ba9, player, _r);
};

global.IsPlayerOnline = function () {
	return _in(0x00000000, 0x61c65fde, _r);
};

global.IsPlayerPerformingStoppie = function (player) {
	return _in(0x00000000, 0x2e815a94, player, _r);
};

global.IsPlayerPerformingWheelie = function (player) {
	return _in(0x00000000, 0x613510d0, player, _r);
};

global.IsPlayerPlaying = function (playerIndex) {
	return _in(0x00000000, 0x08274ba4, playerIndex, _r);
};

global.IsPlayerPressingHorn = function (playerIndex) {
	return _in(0x00000000, 0x583a7a8b, playerIndex, _r);
};

global.IsPlayerReadyForCutscene = function (player) {
	return _in(0x00000000, 0x29d46ff4, player, _r);
};

global.IsPlayerScriptControlOn = function (player) {
	return _in(0x00000000, 0x38861f3a, player, _r);
};

global.IsPlayerSignedInLocally = function () {
	return _in(0x00000000, 0x547523ee, _r);
};

global.IsPlayerTargettingAnything = function (playerIndex) {
	return _in(0x00000000, 0x665f6bb7, playerIndex, _r);
};

global.IsPlayerTargettingChar = function (playerIndex, ped) {
	return _in(0x00000000, 0x58a6457c, playerIndex, ped, _r);
};

global.IsPlayerTargettingObject = function (playerIndex, obj) {
	return _in(0x00000000, 0x679934f9, playerIndex, obj, _r);
};

global.IsPlayerVehicleEntryDisabled = function (player) {
	return _in(0x00000000, 0x4908091d, player, _r);
};

global.IsPointObscuredByAMissionEntity = function (pX, pY, pZ, sizeX, sizeY, sizeZ) {
	return _in(0x00000000, 0x7fbc713e, _fv(pX), _fv(pY), _fv(pZ), _fv(sizeX), _fv(sizeY), _fv(sizeZ), _r);
};

global.IsPosInCutsceneBlockingBounds = function (x, y, z) {
	return _in(0x00000000, 0x593a553b, _fv(x), _fv(y), _fv(z), _r);
};

/**
 * IS_PRINCIPAL_ACE_ALLOWED
 */
global.IsPrincipalAceAllowed = function (principal, object) {
	return _in(0x00000000, 0x37cf52ce, _ts(principal), _ts(object), _r);
};

global.IsProjectileInArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x7bb35fcf, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r);
};

global.IsRadioHudOn = function () {
	return _in(0x00000000, 0x45f249b7, _r);
};

global.IsRadioRetuning = function () {
	return _in(0x00000000, 0x45c344aa, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is pressed down on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of down state.
 */
global.IsRawKeyDown = function (rawKeyIndex) {
	return _in(0x00000000, 0xd95a7387, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is pressed on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of pressed state.
 */
global.IsRawKeyPressed = function (rawKeyIndex) {
	return _in(0x00000000, 0x69f7c29e, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` was just released on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of released state.
 */
global.IsRawKeyReleased = function (rawKeyIndex) {
	return _in(0x00000000, 0xeaa50861, rawKeyIndex, _r);
};

/**
 * Gets if the specified `rawKeyIndex` is up  on the keyboard.
 * This will not be triggered if the key is disabled with [DISABLE_RAW_KEY_THIS_FRAME](#\_0x8BCF0014)
 * Virtual key codes can be found [here](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
 * @param rawKeyIndex Index of raw key from keyboard.
 * @return Returns bool value of up state.
 */
global.IsRawKeyUp = function (rawKeyIndex) {
	return _in(0x00000000, 0x36f4e505, rawKeyIndex, _r);
};

global.IsRelationshipSet = function (Unk493, Unk494, Unk495) {
	return _in(0x00000000, 0x4c076b40, Unk493, Unk494, Unk495, _r);
};

global.IsReplaySaving = function () {
	return _in(0x00000000, 0x78021d03, _r);
};

global.IsReplaySystemSaving = function () {
	return _in(0x00000000, 0x318f65e6, _r);
};

global.IsScoreGreater = function (playerIndex, score) {
	return _in(0x00000000, 0x517b7068, playerIndex, score, _r);
};

global.IsScreenFadedIn = function () {
	return _in(0x00000000, 0x5e0713b2, _r);
};

global.IsScreenFadedOut = function () {
	return _in(0x00000000, 0x59ee3a11, _r);
};

global.IsScreenFading = function () {
	return _in(0x00000000, 0x73700561, _r);
};

global.IsScreenFadingIn = function () {
	return _in(0x00000000, 0x5d1425df, _r);
};

global.IsScreenFadingOut = function () {
	return _in(0x00000000, 0x0a940e03, _r);
};

global.IsScriptFireExtinguished = function (fire) {
	return _in(0x00000000, 0x394c1e55, fire, _r);
};

global.IsScriptedConversationOngoing = function () {
	return _in(0x00000000, 0x3ca23254, _r);
};

global.IsScriptedSpeechPlaying = function (ped) {
	return _in(0x00000000, 0x12d71b44, ped, _r);
};

global.IsSittingObjectNear = function (x, y, z, Unk73) {
	return _in(0x00000000, 0x120b4f15, _fv(x), _fv(y), _fv(z), Unk73, _r);
};

global.IsSniperBulletInArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x6e435bde, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r, _ri);
};

global.IsSniperInverted = function () {
	return _in(0x00000000, 0x50dc54b3, _r);
};

global.IsSpecificCamInterpolating = function (cam) {
	return _in(0x00000000, 0x17c37e6d, cam, _r);
};

global.IsSphereVisibleToAnotherMachine = function (Unk1043, Unk1044, Unk1045, Unk1046) {
	return _in(0x00000000, 0x11ee28d5, Unk1043, Unk1044, Unk1045, Unk1046, _r, _ri);
};

global.IsStreamingAdditionalText = function (textIndex) {
	return _in(0x00000000, 0x23b00129, textIndex, _r);
};

global.IsStreamingPriorityRequests = function () {
	return _in(0x00000000, 0x64342b55, _r);
};

global.IsStreamingThisAdditionalText = function (str0, Unk597, Unk598) {
	return _in(0x00000000, 0x4d077dba, _ts(str0), Unk597, Unk598, _r);
};

global.IsStringNull = function (str) {
	return _in(0x00000000, 0x49a75618, _ts(str), _r);
};

global.IsSystemUiShowing = function () {
	return _in(0x00000000, 0x5f643ee6, _r);
};

global.IsThisAMinigameScript = function () {
	return _in(0x00000000, 0x219a3af6, _r);
};

global.IsThisHelpMessageBeingDisplayed = function (gxtentry) {
	return _in(0x00000000, 0x505d37d8, _ts(gxtentry), _r);
};

global.IsThisHelpMessageWithNumberBeingDisplayed = function (gxtentry, number) {
	return _in(0x00000000, 0x09e878a4, _ts(gxtentry), number, _r);
};

global.IsThisHelpMessageWithStringBeingDisplayed = function (gxtentry, str) {
	return _in(0x00000000, 0x4d155ee8, _ts(gxtentry), _ts(str), _r);
};

global.IsThisMachineTheServer = function () {
	return _in(0x00000000, 0x2e5e1600, _r);
};

global.IsThisModelABike = function (model) {
	return _in(0x00000000, 0x57f46b33, model, _r);
};

global.IsThisModelABoat = function (model) {
	return _in(0x00000000, 0x43cc0913, model, _r);
};

global.IsThisModelACar = function (model) {
	return _in(0x00000000, 0x6ea92fd5, model, _r);
};

global.IsThisModelAHeli = function (model) {
	return _in(0x00000000, 0x62ea75e0, model, _r);
};

global.IsThisModelAPed = function (model) {
	return _in(0x00000000, 0x0e2438e5, model, _r);
};

global.IsThisModelAPlane = function (model) {
	return _in(0x00000000, 0x176f4d4c, model, _r);
};

global.IsThisModelATrain = function (model) {
	return _in(0x00000000, 0x7b8537f7, model, _r);
};

global.IsThisModelAVehicle = function (model) {
	return _in(0x00000000, 0x62bc0aee, model, _r);
};

global.IsThisPedAPlayer = function (ped) {
	return _in(0x00000000, 0x37c85316, ped, _r);
};

global.IsThisPrintBeingDisplayed = function (gxtentry, Unk615, Unk616, Unk617, Unk618, Unk619, Unk620, Unk621, Unk622, Unk623, Unk624) {
	return _in(0x00000000, 0x459a7f23, _ts(gxtentry), Unk615, Unk616, Unk617, Unk618, Unk619, Unk620, Unk621, Unk622, Unk623, Unk624, _r, _ri);
};

global.IsThreadActive = function (threadId) {
	return _in(0x00000000, 0x052a30f7, threadId, _r);
};

global.IsUsingController = function () {
	return _in(0x00000000, 0x669d053f, _r);
};

global.IsVehDriveable = function (vehicle) {
	return _in(0x00000000, 0x17bc668d, vehicle, _r);
};

global.IsVehStuck = function (veh, time, flag0, flag1, flag2) {
	return _in(0x00000000, 0x460d2ebb, veh, time, flag0, flag1, flag2, _r);
};

global.IsVehWindowIntact = function (vehicle, window) {
	return _in(0x00000000, 0x1d0b131a, vehicle, window, _r);
};

global.IsVehicleExtraTurnedOn = function (vehicle, extra) {
	return _in(0x00000000, 0x4b920e81, vehicle, extra, _r);
};

global.IsVehicleOnAllWheels = function (vehicle) {
	return _in(0x00000000, 0x4d460265, vehicle, _r);
};

global.IsVehicleTouchingObject = function (veh, obj) {
	return _in(0x00000000, 0x06cd4eb4, veh, obj, _r);
};

/**
 * Getter for [BREAK_OFF_VEHICLE_WHEEL](?\_0xA274CADB).
 * @param vehicle The vehicle handle.
 * @param wheelIndex The wheel index.
 */
global.IsVehicleWheelBrokenOff = function (vehicle, wheelIndex) {
	return _in(0x00000000, 0xcf1bc668, vehicle, wheelIndex, _r);
};

global.IsViewportActive = function (viewportid) {
	return _in(0x00000000, 0x5d2b2a9a, viewportid, _r);
};

global.IsWantedLevelGreater = function (playerIndex, level) {
	return _in(0x00000000, 0x7da4736d, playerIndex, level, _r);
};

global.IsWorldPointWithinBrainActivationRange = function () {
	return _in(0x00000000, 0x5e7b0f23, _r);
};

global.KnockPedOffBike = function (vehicle) {
	return _in(0x00000000, 0x6ca57960, vehicle);
};

global.LaunchLocalPlayerInNetworkGame = function () {
	return _in(0x00000000, 0x70fe415c);
};

global.LimitAngle = function (angle, anglelimited) {
	return _in(0x00000000, 0x4cae3b65, _fv(angle), _fi(anglelimited) /* may be optional */);
};

global.LimitTwoPlayerDistance = function (distance) {
	return _in(0x00000000, 0x50ad1f3e, _fv(distance));
};

global.Line = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x6c6f6052, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.ListenToPlayerGroupCommands = function (ped, set) {
	return _in(0x00000000, 0x34ac73d6, ped, set);
};

global.LoadAdditionalText = function (textName, textIndex) {
	return _in(0x00000000, 0x28897ebd, _ts(textName), textIndex);
};

global.LoadAllObjectsNow = function () {
	return _in(0x00000000, 0x4bf36a32);
};

global.LoadAllPathNodes = function (value) {
	return _in(0x00000000, 0x356c2ddb, value, _r, _ri);
};

global.LoadCharDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x7f7b4fc5, type, _ii(pDM) /* may be optional */);
};

global.LoadCombatDecisionMaker = function (type, pDM) {
	return _in(0x00000000, 0x0c7b14d9, type, _ii(pDM) /* may be optional */);
};

global.LoadPathNodesInArea = function (x, y, z, radius) {
	return _in(0x00000000, 0x44640c28, _fv(x), _fv(y), _fv(z), _fv(radius));
};

/**
 * Reads the contents of a text file in a specified resource.
 * If executed on the client, this file has to be included in `files` in the resource manifest.
 * Example: `local data = LoadResourceFile("devtools", "data.json")`
 * @param resourceName The resource name.
 * @param fileName The file in the resource.
 * @return The file contents
 */
global.LoadResourceFile = function (resourceName, fileName) {
	return _in(0x00000000, 0x76a9ee1f, _ts(resourceName), _ts(fileName), _r, _s);
};

global.LoadScene = function (x, y, z) {
	return _in(0x00000000, 0x39f62bfb, _fv(x), _fv(y), _fv(z));
};

global.LoadSceneForRoomByKey = function (interior, roomhash) {
	return _in(0x00000000, 0x6e904c1a, interior, roomhash);
};

global.LoadSettings = function () {
	return _in(0x00000000, 0x77745390);
};

global.LoadTextFont = function (font) {
	return _in(0x00000000, 0x2d371601, font);
};

global.LoadTxd = function (txdName) {
	return _in(0x00000000, 0x52fc763a, _ts(txdName), _r, _ri);
};

global.LoadWebPage = function (htmlviewport, webaddress) {
	return _in(0x00000000, 0x78c17971, htmlviewport, _ts(webaddress));
};

global.LocalPlayerIsReadyToStartPlaying = function () {
	return _in(0x00000000, 0x5c03585c, _r);
};

global.LocateCar_2d = function (car, x0, y0, xUnk48, yUnk49, flag) {
	return _in(0x00000000, 0x36f70af6, car, _fv(x0), _fv(y0), _fv(xUnk48), _fv(yUnk49), flag, _r);
};

global.LocateCar_3d = function (car, x, y, z, xa, ya, za, flag) {
	return _in(0x00000000, 0x2a221e97, car, _fv(x), _fv(y), _fv(z), _fv(xa), _fv(ya), _fv(za), flag, _r);
};

global.LocateCharAnyMeansCar_2d = function (ped, car, x, y, flag) {
	return _in(0x00000000, 0x1a455e51, ped, car, _fv(x), _fv(y), flag, _r);
};

global.LocateCharAnyMeansCar_3d = function (ped, car, x, y, z, flag) {
	return _in(0x00000000, 0x58dd4ccc, ped, car, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharAnyMeansChar_2d = function (ped, pednext, x, y, flag) {
	return _in(0x00000000, 0x18ea4926, ped, pednext, _fv(x), _fv(y), flag, _r);
};

global.LocateCharAnyMeansChar_3d = function (ped, pednext, x, y, z, flag) {
	return _in(0x00000000, 0x3e441a58, ped, pednext, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharAnyMeansObject_2d = function (ped, obj, x, y, flag) {
	return _in(0x00000000, 0x4fd34079, ped, obj, _fv(x), _fv(y), flag, _r);
};

global.LocateCharAnyMeansObject_3d = function (ped, obj, x, y, z, flag) {
	return _in(0x00000000, 0x6d0e1bce, ped, obj, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharAnyMeans_2d = function (ped, x0, y0, x1, y1, flag) {
	return _in(0x00000000, 0x5bb767ad, ped, _fv(x0), _fv(y0), _fv(x1), _fv(y1), flag, _r);
};

global.LocateCharAnyMeans_3d = function (ped, x0, y0, z0, x1, y1, z1, flag) {
	return _in(0x00000000, 0x0437222b, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), flag, _r);
};

global.LocateCharInCarCar_2d = function (ped, car, x, y, flag) {
	return _in(0x00000000, 0x53b429f9, ped, car, _fv(x), _fv(y), flag, _r);
};

global.LocateCharInCarCar_3d = function (ped, car, x, y, z, flag) {
	return _in(0x00000000, 0x4d3547d1, ped, car, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharInCarChar_2d = function (ped, pednext, x, y, flag) {
	return _in(0x00000000, 0x17bc4531, ped, pednext, _fv(x), _fv(y), flag, _r);
};

global.LocateCharInCarChar_3d = function (ped, pednext, x, y, z, flag) {
	return _in(0x00000000, 0x014f234f, ped, pednext, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharInCarObject_2d = function (ped, obj, x, y, flag) {
	return _in(0x00000000, 0x6ccb719d, ped, obj, _fv(x), _fv(y), flag, _r);
};

global.LocateCharInCarObject_3d = function (ped, obj, x, y, z, flag) {
	return _in(0x00000000, 0x0c26452d, ped, obj, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharInCar_2d = function (ped, x0, y0, x1, y1, flag) {
	return _in(0x00000000, 0x1dda54ef, ped, _fv(x0), _fv(y0), _fv(x1), _fv(y1), flag, _r);
};

global.LocateCharInCar_3d = function (ped, x0, y0, z0, x1, y1, z, flag) {
	return _in(0x00000000, 0x0ac92d36, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z), flag, _r);
};

global.LocateCharOnFootCar_2d = function (ped, car, x, y, flag) {
	return _in(0x00000000, 0x78a75ef4, ped, car, _fv(x), _fv(y), flag, _r);
};

global.LocateCharOnFootCar_3d = function (ped, car, x, y, z, flag) {
	return _in(0x00000000, 0x3c3e5fa0, ped, car, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharOnFootChar_2d = function (ped, pednext, x, y, flag) {
	return _in(0x00000000, 0x191e2f12, ped, pednext, _fv(x), _fv(y), flag, _r);
};

global.LocateCharOnFootChar_3d = function (ped, pednext, x, y, z, flag) {
	return _in(0x00000000, 0x4da362b0, ped, pednext, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharOnFootObject_2d = function (ped, obj, x, y, flag) {
	return _in(0x00000000, 0x67f518f0, ped, obj, _fv(x), _fv(y), flag, _r);
};

global.LocateCharOnFootObject_3d = function (ped, obj, x, y, z, flag) {
	return _in(0x00000000, 0x4a8e429a, ped, obj, _fv(x), _fv(y), _fv(z), flag, _r);
};

global.LocateCharOnFoot_2d = function (ped, x0, y0, x1, y1, flag) {
	return _in(0x00000000, 0x50ee161f, ped, _fv(x0), _fv(y0), _fv(x1), _fv(y1), flag, _r);
};

global.LocateCharOnFoot_3d = function (ped, x0, y0, z0, x1, y1, z1, flag) {
	return _in(0x00000000, 0x3d003090, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), flag, _r);
};

global.LocateDeadCar_3d = function (car, x, y, z, xa, ya, za, flag) {
	return _in(0x00000000, 0x584d0c79, car, _fv(x), _fv(y), _fv(z), _fv(xa), _fv(ya), _fv(za), flag, _r);
};

global.LocateObject_2d = function (obj, x0, y0, x1, y1, flag) {
	return _in(0x00000000, 0x59a57ba8, obj, _fv(x0), _fv(y0), _fv(x1), _fv(y1), flag, _r);
};

global.LocateObject_3d = function (obj, x, y, z, xr, yr, zr, flag) {
	return _in(0x00000000, 0x6db47487, obj, _fv(x), _fv(y), _fv(z), _fv(xr), _fv(yr), _fv(zr), flag, _r);
};

global.LockCarDoors = function (vehicle, value) {
	return _in(0x00000000, 0x6702757c, vehicle, value);
};

global.LockLazlowStation = function () {
	return _in(0x00000000, 0x1b215a3b);
};

global.LockPlayerSettingsGenreChange = function (lock_bit_mask) {
	return _in(0x00000000, 0x33f4498e, lock_bit_mask);
};

global.LookAtNearbyEntityWithSpecialAttribute = function (Unk98) {
	return _in(0x00000000, 0x6eb639e8, Unk98, _v, _f, _i, _i, _i, _r);
};

global.LoopRaceTrack = function (loop) {
	return _in(0x00000000, 0x77fd5097, loop);
};

global.MaintainFlashingStarAfterOffence = function (player, maintain) {
	return _in(0x00000000, 0x68880dcd, player, maintain);
};

global.MakeObjectTargettable = function (obj, targettable) {
	return _in(0x00000000, 0x228f1801, obj, targettable);
};

global.MakePlayerFireProof = function (player, proof) {
	return _in(0x00000000, 0x38293796, player, proof);
};

global.MakePlayerGangDisappear = function () {
	return _in(0x00000000, 0x34211cda);
};

global.MakePlayerGangReappear = function () {
	return _in(0x00000000, 0x295a652a);
};

global.MakePlayerSafeForCutscene = function (player) {
	return _in(0x00000000, 0x45852a03, player);
};

global.MarkCarAsConvoyCar = function (vehicle, convoyCar) {
	return _in(0x00000000, 0x79274447, vehicle, convoyCar);
};

global.MarkCarAsNoLongerNeeded = function (pVehicle) {
	return _in(0x00000000, 0x20c76fd1, _ii(pVehicle) /* may be optional */);
};

global.MarkCharAsNoLongerNeeded = function (pPed) {
	return _in(0x00000000, 0x0b774604, _ii(pPed) /* may be optional */);
};

global.MarkMissionTrainAsNoLongerNeeded = function (train) {
	return _in(0x00000000, 0x37ac2a95, train);
};

global.MarkMissionTrainsAsNoLongerNeeded = function () {
	return _in(0x00000000, 0x07e7104e);
};

global.MarkModelAsNoLongerNeeded = function (model) {
	return _in(0x00000000, 0x00fa0e33, model);
};

global.MarkObjectAsNoLongerNeeded = function (pObj) {
	return _in(0x00000000, 0x493b655b, _ii(pObj) /* may be optional */);
};

global.MarkRoadNodeAsDontWander = function (x, y, z) {
	return _in(0x00000000, 0x4c2621b6, _fv(x), _fv(y), _fv(z));
};

global.MarkScriptAsNoLongerNeeded = function (scriptName) {
	return _in(0x00000000, 0x09e405db, _ts(scriptName));
};

global.MarkStreamedTxdAsNoLongerNeeded = function (txdName) {
	return _in(0x00000000, 0x70ea2b89, _ts(txdName));
};

global.MissionAudioBankNoLongerNeeded = function () {
	return _in(0x00000000, 0x12c42f66);
};

global.ModifyCharMoveBlendRatio = function (ped, Unk6) {
	return _in(0x00000000, 0x3e657606, ped, Unk6);
};

global.ModifyCharMoveState = function (ped, state) {
	return _in(0x00000000, 0x5cd32071, ped, state);
};

global.MpGetAmountOfAnchorPoints = function (ped, id) {
	return _in(0x00000000, 0x6c7566f3, ped, id, _r, _ri);
};

global.MpGetAmountOfVariationComponent = function (ped, componentid) {
	return _in(0x00000000, 0x54dd6acf, ped, componentid, _r, _ri);
};

global.MpGetPreferenceValue = function (prefid) {
	return _in(0x00000000, 0x54f61c99, prefid, _r, _ri);
};

global.MpGetPropSetup = function (ped, ukn0, ukn1, ukn2, ukn3) {
	return _in(0x00000000, 0x1c00658b, ped, ukn0, ukn1, ukn2, ukn3, _r, _ri);
};

global.MpGetVariationSetup = function (ped, Unk890, Unk891, Unk892, Unk893) {
	return _in(0x00000000, 0x3775138e, ped, Unk890, Unk891, Unk892, Unk893, _r, _ri);
};

global.MpSetPreferenceValue = function (prefid, value) {
	return _in(0x00000000, 0x216804d3, prefid, value);
};

/**
 * Starts listening to the specified channel, when available.
 * @param channel A game voice channel ID.
 */
global.MumbleAddVoiceChannelListen = function (channel) {
	return _in(0x00000000, 0xc79f44bf, channel);
};

/**
 * Adds the specified channel to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param channel A game voice channel ID.
 */
global.MumbleAddVoiceTargetChannel = function (targetId, channel) {
	return _in(0x00000000, 0x4d386c9e, targetId, channel);
};

/**
 * Adds the specified player to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param player A game player index.
 */
global.MumbleAddVoiceTargetPlayer = function (targetId, player) {
	return _in(0x00000000, 0x32c5355a, targetId, player);
};

/**
 * Adds the specified player to the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param serverId The player's server id.
 */
global.MumbleAddVoiceTargetPlayerByServerId = function (targetId, serverId) {
	return _in(0x00000000, 0x25f2b65f, targetId, serverId);
};

/**
 * MUMBLE_CLEAR_VOICE_CHANNEL
 */
global.MumbleClearVoiceChannel = function () {
	return _in(0x00000000, 0xbf847807);
};

/**
 * Clears the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
global.MumbleClearVoiceTarget = function (targetId) {
	return _in(0x00000000, 0x8555dcba, targetId);
};

/**
 * Clears channels from the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
global.MumbleClearVoiceTargetChannels = function (targetId) {
	return _in(0x00000000, 0x5ea72e76, targetId);
};

/**
 * Clears players from the target list for the specified Mumble voice target ID.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 */
global.MumbleClearVoiceTargetPlayers = function (targetId) {
	return _in(0x00000000, 0x912e21da, targetId);
};

/**
 * Check whether specified channel exists on the Mumble server.
 * @param channel A game voice channel ID.
 * @return True if the specific channel exists. False otherwise.
 */
global.MumbleDoesChannelExist = function (channel) {
	return _in(0x00000000, 0x0cc8ca25, channel, _r);
};

/**
 * MUMBLE_GET_TALKER_PROXIMITY
 * @return Talker proximity value.
 */
global.MumbleGetTalkerProximity = function () {
	return _in(0x00000000, 0x84e02a32, _r, _rf);
};

/**
 * Returns the mumble voice channel from a player's server id.
 * @param serverId The player's server id.
 * @return Int representing the identifier of the voice channel.
 */
global.MumbleGetVoiceChannelFromServerId = function (serverId) {
	return _in(0x00000000, 0x221c09f1, serverId, _r, _ri);
};

/**
 * MUMBLE_IS_ACTIVE
 * @return True if the player has enabled voice chat.
 */
global.MumbleIsActive = function () {
	return _in(0x00000000, 0xe820bc10, _r);
};

/**
 * This native will return true if the user succesfully connected to the voice server.
 * If the user disabled the voice-chat setting it will return false.
 * @return True if the player is connected to a mumble server.
 */
global.MumbleIsConnected = function () {
	return _in(0x00000000, 0xb816370a, _r);
};

/**
 * MUMBLE_IS_PLAYER_TALKING
 * @param player The target player.
 * @return Whether or not the player is talking.
 */
global.MumbleIsPlayerTalking = function (player) {
	return _in(0x00000000, 0x33eef97f, player, _r);
};

/**
 * Stops listening to the specified channel.
 * @param channel A game voice channel ID.
 */
global.MumbleRemoveVoiceChannelListen = function (channel) {
	return _in(0x00000000, 0x231523b7, channel);
};

/**
 * Removes the specified voice channel from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_CHANNEL](#\_0x4D386C9E)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param channel The game voice channel ID to remove from the target.
 */
global.MumbleRemoveVoiceTargetChannel = function (targetId, channel) {
	return _in(0x00000000, 0x268db867, targetId, channel);
};

/**
 * Removes the specified player from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER](#\_0x32C5355A)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param player The player index to remove from the target.
 */
global.MumbleRemoveVoiceTargetPlayer = function (targetId, player) {
	return _in(0x00000000, 0x88cd646f, targetId, player);
};

/**
 * Removes the specified player from the user's voice targets.
 * Performs the opposite operation of [MUMBLE_ADD_VOICE_TARGET_PLAYER_BY_SERVER_ID](#\_0x25F2B65F)
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive).
 * @param serverId The player's server id to remove from the target.
 */
global.MumbleRemoveVoiceTargetPlayerByServerId = function (targetId, serverId) {
	return _in(0x00000000, 0x930bd34b, targetId, serverId);
};

/**
 * MUMBLE_SET_ACTIVE
 * @param state Voice chat state.
 */
global.MumbleSetActive = function (state) {
	return _in(0x00000000, 0xd932a3f3, state);
};

/**
 * Sets the current input distance. The player will be able to talk to other players within this distance.
 * @param distance The input distance.
 */
global.MumbleSetAudioInputDistance = function (distance) {
	return _in(0x00000000, 0x1b1052e2, _fv(distance));
};

/**
 * Use this native to disable noise suppression and high pass filters.
 * The possible intents for this are as follows (backticks are used to represent hashes):
 * | Index | Description |
 * |-|-|
 * | \`speech\` | Default intent |
 * | \`music\` | Disable noise suppression and high pass filter |
 * @param intentHash The intent hash.
 */
global.MumbleSetAudioInputIntent = function (intentHash) {
	return _in(0x00000000, 0x6383526b, _ch(intentHash));
};

/**
 * Sets the current output distance. The player will be able to hear other players talking within this distance.
 * @param distance The output distance.
 */
global.MumbleSetAudioOutputDistance = function (distance) {
	return _in(0x00000000, 0x74c597d9, _fv(distance));
};

/**
 * Changes the Mumble server address to connect to, and reconnects to the new address.
 * Setting the address to an empty string and the port to -1 will reset to the built in FXServer Mumble Implementation.
 * @param address The address of the mumble server.
 * @param port The port of the mumble server.
 */
global.MumbleSetServerAddress = function (address, port) {
	return _in(0x00000000, 0xe6eb2cd8, _ts(address), port);
};

/**
 * Sets the audio submix ID for a specified player using Mumble 'Native Audio' functionality.
 * @param serverId The player's server ID.
 * @param submixId The submix ID.
 */
global.MumbleSetSubmixForServerId = function (serverId, submixId) {
	return _in(0x00000000, 0xfe3a3054, serverId, submixId);
};

/**
 * MUMBLE_SET_TALKER_PROXIMITY
 * @param value Proximity value.
 */
global.MumbleSetTalkerProximity = function (value) {
	return _in(0x00000000, 0x74e927b0, _fv(value));
};

/**
 * MUMBLE_SET_VOICE_CHANNEL
 * @param channel A game voice channel ID.
 */
global.MumbleSetVoiceChannel = function (channel) {
	return _in(0x00000000, 0x8737eee8, channel);
};

/**
 * Sets the current Mumble voice target ID to broadcast voice to.
 * @param targetId A Mumble voice target ID, ranging from 1..30 (inclusive). 0 disables voice targets, and 31 is server loopback.
 */
global.MumbleSetVoiceTarget = function (targetId) {
	return _in(0x00000000, 0x960a4a95, targetId);
};

/**
 * Overrides the output volume for a particular player on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.
 * Set to -1.0 to reset the Volume override.
 * @param player A game player index.
 * @param volume The volume, ranging from 0.0 to 1.0 (or above).
 */
global.MumbleSetVolumeOverride = function (player, volume) {
	return _in(0x00000000, 0x61c309e3, player, _fv(volume));
};

/**
 * Overrides the output volume for a particular player with the specified server id and player name on Mumble. This will also bypass 3D audio and distance calculations. -1.0 to reset the override.
 * @param serverId The player's server id.
 * @param volume The volume, ranging from 0.0 to 1.0 (or above).
 */
global.MumbleSetVolumeOverrideByServerId = function (serverId, volume) {
	return _in(0x00000000, 0xce8e25b4, serverId, _fv(volume));
};

global.MuteGameworldAndPositionedRadioForTv = function (mute) {
	return _in(0x00000000, 0x79974e04, mute);
};

global.MuteGameworldAudio = function (mute) {
	return _in(0x00000000, 0x446677c6, mute);
};

global.MutePositionedRadio = function (mute) {
	return _in(0x00000000, 0x32c75195, mute);
};

global.MuteStaticEmitter = function (StaticEmitterIndex, mute) {
	return _in(0x00000000, 0x0fcc0410, StaticEmitterIndex, mute);
};

global.NetworkAcceptInvite = function (playerIndex) {
	return _in(0x00000000, 0x4fdd00ce, playerIndex, _r, _ri);
};

global.NetworkAdvertiseSession = function (advertise) {
	return _in(0x00000000, 0x1b9e5d07, advertise);
};

global.NetworkAllPartyMembersPresent = function () {
	return _in(0x00000000, 0x59c53fba, _r);
};

global.NetworkAmIBlockedByPlayer = function (playerIndex) {
	return _in(0x00000000, 0x4faf2007, playerIndex, _r);
};

global.NetworkAmIMutedByPlayer = function (playerIndex) {
	return _in(0x00000000, 0x448f486a, playerIndex, _r);
};

global.NetworkChangeExtendedGameConfig = function (Unk924) {
	return _in(0x00000000, 0x4cfe3998, Unk924);
};

global.NetworkChangeGameMode = function (Unk1047, Unk1048, Unk1049, Unk1050) {
	return _in(0x00000000, 0x3f054f44, Unk1047, Unk1048, Unk1049, Unk1050, _r, _ri);
};

global.NetworkChangeGameModePending = function () {
	return _in(0x00000000, 0x379930f3, _r);
};

global.NetworkChangeGameModeSucceeded = function () {
	return _in(0x00000000, 0x6d302da9, _r);
};

global.NetworkCheckInviteArrival = function () {
	return _in(0x00000000, 0x308e3719, _r);
};

global.NetworkClearInviteArrival = function () {
	return _in(0x00000000, 0x37282d4f);
};

global.NetworkClearSummons = function () {
	return _in(0x00000000, 0x6289239f);
};

global.NetworkDidInviteFriend = function (FRIENDNAME) {
	return _in(0x00000000, 0x3caa1340, _ts(FRIENDNAME), _r);
};

global.NetworkEndSession = function () {
	return _in(0x00000000, 0x75291bec);
};

global.NetworkEndSessionPending = function () {
	return _in(0x00000000, 0x489b0bb9, _r);
};

global.NetworkExpandTo_32Players = function () {
	return _in(0x00000000, 0x36511e0a);
};

global.NetworkFindGame = function (GameMode, ukn0, ukn1, ukn2) {
	return _in(0x00000000, 0x5d4d0c86, GameMode, ukn0, ukn1, ukn2);
};

global.NetworkFindGamePending = function () {
	return _in(0x00000000, 0x23d60810, _r);
};

global.NetworkFinishExtendedSearch = function () {
	return _in(0x00000000, 0x1e0a7ad8);
};

/**
 * Returns the owner ID of the specified entity.
 * @param entity The entity to get the owner for.
 * @return On the server, the server ID of the entity owner. On the client, returns the player/slot ID of the entity owner.
 */
global.NetworkGetEntityOwner = function (entity) {
	return _in(0x00000000, 0x526fee31, entity, _r, _ri);
};

global.NetworkGetFindResult = function (Unk925, Unk926) {
	return _in(0x00000000, 0x282d2caa, Unk925, Unk926);
};

global.NetworkGetFriendCount = function () {
	return _in(0x00000000, 0x5eea3f25, _r, _ri);
};

global.NetworkGetFriendName = function (id) {
	return _in(0x00000000, 0x17fd0934, id, _r, _s);
};

global.NetworkGetGameMode = function () {
	return _in(0x00000000, 0x29a75d1f, _r, _ri);
};

global.NetworkGetHostAverageRank = function (host) {
	return _in(0x00000000, 0x04261e4c, host, _r, _ri);
};

global.NetworkGetHostLatency = function (host) {
	return _in(0x00000000, 0x74093768, host, _r, _ri);
};

global.NetworkGetHostMatchProgress = function (host) {
	return _in(0x00000000, 0x59aa0635, host, _r, _ri);
};

global.NetworkGetHostServerName = function (host) {
	return _in(0x00000000, 0x031d740f, host, _r, _s);
};

global.NetworkGetLanSession = function () {
	return _in(0x00000000, 0x48a723c1, _r);
};

global.NetworkGetMaxPrivateSlots = function () {
	return _in(0x00000000, 0x2ef80425, _r, _ri);
};

global.NetworkGetMaxSlots = function () {
	return _in(0x00000000, 0x524f7543, _r, _ri);
};

global.NetworkGetMetPlayerName = function (Unk1051) {
	return _in(0x00000000, 0x01f35f5c, Unk1051, _r, _ri);
};

global.NetworkGetNextTextChat = function () {
	return _in(0x00000000, 0x314e106a, _r, _s);
};

global.NetworkGetNumOpenPublicSlots = function () {
	return _in(0x00000000, 0x4e323a0a, _r, _ri);
};

global.NetworkGetNumPartyMembers = function () {
	return _in(0x00000000, 0x27f65637, _r, _ri);
};

global.NetworkGetNumPlayersMet = function () {
	return _in(0x00000000, 0x33500089, _r, _ri);
};

global.NetworkGetNumUnacceptedInvites = function () {
	return _in(0x00000000, 0x13244634, _r, _ri);
};

global.NetworkGetNumUnfilledReservations = function () {
	return _in(0x00000000, 0x043c3b0b, _r, _ri);
};

global.NetworkGetNumberOfGames = function () {
	return _in(0x00000000, 0x10df4ced, _r, _ri);
};

global.NetworkGetPlayerIdOfNextTextChat = function () {
	return _in(0x00000000, 0x145b50af, _r, _ri);
};

global.NetworkGetRendezvousHostPlayerId = function () {
	return _in(0x00000000, 0x282d29fe, _r, _ri);
};

global.NetworkGetServerName = function () {
	return _in(0x00000000, 0x03665b8d, _r, _ri);
};

global.NetworkGetUnacceptedInviteEpisode = function (Unk894) {
	return _in(0x00000000, 0x3432536a, Unk894, _r, _ri);
};

global.NetworkGetUnacceptedInviteGameMode = function (Unk1052) {
	return _in(0x00000000, 0x5e44065d, Unk1052, _r, _ri);
};

global.NetworkGetUnacceptedInviterName = function (Unk886) {
	return _in(0x00000000, 0x1a7b3125, Unk886, _r, _s);
};

global.NetworkHasStrictNat = function () {
	return _in(0x00000000, 0x2704460e, _r);
};

global.NetworkHaveAcceptedInvite = function () {
	return _in(0x00000000, 0x0bc86fa7, _r);
};

global.NetworkHaveOnlinePrivileges = function () {
	return _in(0x00000000, 0x4b907716, _r);
};

global.NetworkHaveSummons = function () {
	return _in(0x00000000, 0x48726b45, _r);
};

global.NetworkHostGameE1 = function (Gamemode, Ranked, Slots, Private, Episode, MaxTeams) {
	return _in(0x00000000, 0x5bea05e2, Gamemode, Ranked, Slots, Private, Episode, MaxTeams, _r);
};

global.NetworkHostGamePending = function () {
	return _in(0x00000000, 0x391e4575, _r);
};

global.NetworkHostGameSucceeded = function () {
	return _in(0x00000000, 0x1ca77e94, _r);
};

global.NetworkHostRendezvousE1 = function (Gamemode, Slots, Episode) {
	return _in(0x00000000, 0x48032420, Gamemode, Slots, Episode, _r);
};

global.NetworkInviteFriend = function (friendname, ukn) {
	return _in(0x00000000, 0x62b15cd7, _ts(friendname), _ts(ukn));
};

global.NetworkIsBeingKicked = function () {
	return _in(0x00000000, 0x52364369, _r);
};

global.NetworkIsCommonEpisode = function (id) {
	return _in(0x00000000, 0x26094a53, id, _r);
};

global.NetworkIsFindResultUpdated = function (ukn0) {
	return _in(0x00000000, 0x7ed34379, ukn0, _r);
};

global.NetworkIsFindResultValid = function (Unk883) {
	return _in(0x00000000, 0x51df00d8, Unk883, _r);
};

global.NetworkIsFriendInSameTitle = function (friendid) {
	return _in(0x00000000, 0x4b5c4957, friendid, _r);
};

global.NetworkIsFriendOnline = function (Unk896) {
	return _in(0x00000000, 0x04783029, Unk896, _r, _ri);
};

global.NetworkIsGameRanked = function () {
	return _in(0x00000000, 0x50c72493, _r);
};

global.NetworkIsInviteeOnline = function () {
	return _in(0x00000000, 0x772b01cc, _r);
};

global.NetworkIsNetworkAvailable = function () {
	return _in(0x00000000, 0x04e11812, _r);
};

global.NetworkIsOperationPending = function () {
	return _in(0x00000000, 0x71ae456a, _r);
};

global.NetworkIsPlayerBlockedByMe = function (playerIndex) {
	return _in(0x00000000, 0x23b76f88, playerIndex, _r);
};

global.NetworkIsPlayerMutedByMe = function (playerIndex) {
	return _in(0x00000000, 0x120962e7, playerIndex, _r);
};

global.NetworkIsPlayerTalking = function (playerIndex) {
	return _in(0x00000000, 0x544625d9, playerIndex, _r);
};

global.NetworkIsPlayerTyping = function (playerIndex) {
	return _in(0x00000000, 0x5ae1245e, playerIndex, _r);
};

global.NetworkIsRendezvous = function () {
	return _in(0x00000000, 0x60560dae, _r);
};

global.NetworkIsRendezvousHost = function () {
	return _in(0x00000000, 0x6eb3047f, _r);
};

global.NetworkIsRockstartSessionIdValid = function () {
	return _in(0x00000000, 0x6c434e0b, _r);
};

global.NetworkIsSessionAdvertise = function () {
	return _in(0x00000000, 0x1b6716b8, _r);
};

global.NetworkIsSessionInvitable = function () {
	return _in(0x00000000, 0x4a8245f1, _r);
};

global.NetworkIsSessionStarted = function () {
	return _in(0x00000000, 0x65b83afb, _r);
};

global.NetworkIsTvt = function () {
	return _in(0x00000000, 0x73d87a5f, _r);
};

global.NetworkJoinGame = function (Unk1053) {
	return _in(0x00000000, 0x60806a0c, Unk1053, _r, _ri);
};

global.NetworkJoinGamePending = function () {
	return _in(0x00000000, 0x76c53927, _r);
};

global.NetworkJoinGameSucceeded = function () {
	return _in(0x00000000, 0x59f24327, _r);
};

global.NetworkJoinSummons = function () {
	return _in(0x00000000, 0x360751ae, _r);
};

global.NetworkKickPlayer = function (playerIndex, value) {
	return _in(0x00000000, 0x7e8c1c45, playerIndex, value);
};

global.NetworkLeaveGame = function () {
	return _in(0x00000000, 0x55d66e24);
};

global.NetworkLeaveGamePending = function () {
	return _in(0x00000000, 0x497e6745, _r);
};

global.NetworkLimitTo_16Players = function () {
	return _in(0x00000000, 0x0a1d6e36);
};

global.NetworkPlayerHasCommPrivs = function () {
	return _in(0x00000000, 0x2854024a, _r);
};

global.NetworkPlayerHasDiedRecently = function (playerIndex) {
	return _in(0x00000000, 0x75cd1a28, playerIndex, _r);
};

global.NetworkPlayerHasHeadset = function (Unk884) {
	return _in(0x00000000, 0x408e2f70, Unk884, _r);
};

global.NetworkPlayerHasKeyboard = function (playerIndex) {
	return _in(0x00000000, 0x04fe5c34, playerIndex, _r);
};

global.NetworkRestoreGameConfig = function (Unk1054) {
	return _in(0x00000000, 0x1e1b5c26, Unk1054, _r, _ri);
};

global.NetworkResultMatchesSearchCriteria = function (result) {
	return _in(0x00000000, 0x767f1e44, result, _r);
};

global.NetworkReturnToRendezvous = function () {
	return _in(0x00000000, 0x00031ec6, _r);
};

global.NetworkReturnToRendezvousPending = function () {
	return _in(0x00000000, 0x6a66149a, _r);
};

global.NetworkReturnToRendezvousSucceeded = function () {
	return _in(0x00000000, 0x208f671c, _r);
};

global.NetworkSendTextChat = function (playerIndex, Unk1055) {
	return _in(0x00000000, 0x18c67e6d, playerIndex, Unk1055, _r, _ri);
};

global.NetworkSetFriendlyFireOption = function (Unk927) {
	return _in(0x00000000, 0x5ac43965, Unk927);
};

global.NetworkSetHealthReticuleOption = function (Unk928) {
	return _in(0x00000000, 0x3998154e, Unk928);
};

global.NetworkSetLanSession = function (Unk929) {
	return _in(0x00000000, 0x6fda43a3, Unk929);
};

global.NetworkSetLocalPlayerIsTyping = function (playerIndex) {
	return _in(0x00000000, 0x141d24a6, playerIndex);
};

global.NetworkSetMatchProgress = function (Unk930) {
	return _in(0x00000000, 0x5c8d66ea, _fv(Unk930));
};

global.NetworkSetPlayerMuted = function (playerIndex, value) {
	return _in(0x00000000, 0x0b1562df, playerIndex, value, _r);
};

global.NetworkSetScriptLobbyState = function (Unk931) {
	return _in(0x00000000, 0x17767d95, Unk931);
};

global.NetworkSetServerName = function (name) {
	return _in(0x00000000, 0x580e1c3d, _ts(name), _r, _ri);
};

global.NetworkSetSessionInvitable = function (invitable) {
	return _in(0x00000000, 0x5fb15e81, invitable);
};

global.NetworkSetTalkerFocus = function (Unk932) {
	return _in(0x00000000, 0x753714f8, Unk932);
};

global.NetworkSetTalkerProximity = function (Unk933) {
	return _in(0x00000000, 0x2f542797, Unk933);
};

global.NetworkSetTeamOnlyChat = function (Unk934) {
	return _in(0x00000000, 0x31492174, Unk934);
};

global.NetworkSetTextChatRecipients = function (Unk935) {
	return _in(0x00000000, 0x3a2246bb, Unk935);
};

global.NetworkShowFriendProfileUi = function (Unk936) {
	return _in(0x00000000, 0x696021e6, Unk936);
};

global.NetworkShowMetPlayerFeedbackUi = function (metPlayerIndex) {
	return _in(0x00000000, 0x2cd73270, metPlayerIndex);
};

global.NetworkShowMetPlayerProfileUi = function (Unk937) {
	return _in(0x00000000, 0x1b183afe, Unk937);
};

global.NetworkShowPlayerFeedbackUi = function (payerIndex) {
	return _in(0x00000000, 0x6fc54c6b, payerIndex);
};

global.NetworkShowPlayerProfileUi = function (playerIndex) {
	return _in(0x00000000, 0x6f2a5430, playerIndex);
};

global.NetworkStartExtendedSearch = function (Unk938) {
	return _in(0x00000000, 0x07fd3c35, Unk938);
};

global.NetworkStartSession = function () {
	return _in(0x00000000, 0x58802ce5);
};

global.NetworkStartSessionPending = function () {
	return _in(0x00000000, 0x7f853ff4, _r);
};

global.NetworkStartSessionSucceeded = function () {
	return _in(0x00000000, 0x5873667b, _r);
};

global.NetworkStoreGameConfig = function (Unk939) {
	return _in(0x00000000, 0x30d373df, Unk939);
};

global.NetworkStoreSinglePlayerGame = function () {
	return _in(0x00000000, 0x08181609, _r, _ri);
};

global.NetworkStringVerifyPending = function () {
	return _in(0x00000000, 0x44aa32a7, _r);
};

global.NetworkStringVerifySucceeded = function () {
	return _in(0x00000000, 0x3f1d4677, _r);
};

global.NetworkVerifyUserString = function (Unk940) {
	return _in(0x00000000, 0x59884407, Unk940);
};

global.NewMobilePhoneCall = function () {
	return _in(0x00000000, 0x720e7ea6);
};

global.NewScriptedConversation = function () {
	return _in(0x00000000, 0x6c213305);
};

global.ObfuscateInt = function (Unk941, Unk942) {
	return _in(0x00000000, 0x31a219fa, Unk941, Unk942);
};

global.ObfuscateIntArray = function (Unk943, Unk944) {
	return _in(0x00000000, 0x3ef15b6a, Unk943, Unk944);
};

global.ObfuscateString = function (str) {
	return _in(0x00000000, 0x04f12617, _ts(str), _r, _s);
};

global.OnFireScream = function (ped) {
	return _in(0x00000000, 0x6be062df, ped);
};

global.OpenCarDoor = function (vehicle, door) {
	return _in(0x00000000, 0x1e352cef, vehicle, door);
};

global.OpenDebugFile = function () {
	return _in(0x00000000, 0x7a2b266d);
};

global.OpenGarage = function (name) {
	return _in(0x00000000, 0x5086785f, _ts(name));
};

global.OpenSequenceTask = function (pTaskSequence) {
	return _in(0x00000000, 0x14a67125, _ii(pTaskSequence) /* may be optional */);
};

global.OverrideFreezeFlags = function (Unk504) {
	return _in(0x00000000, 0x710e6d16, Unk504);
};

global.OverrideNextRestart = function (x, y, z, heading) {
	return _in(0x00000000, 0x27636b69, _fv(x), _fv(y), _fv(z), _fv(heading));
};

global.OverrideNumberOfParkedCars = function (num) {
	return _in(0x00000000, 0x7f483739, num);
};

global.PanicScream = function (ped) {
	return _in(0x00000000, 0x4f8b4507, ped);
};

global.PauseGame = function () {
	return _in(0x00000000, 0x7fb41425);
};

global.PausePlaybackRecordedCar = function (car) {
	return _in(0x00000000, 0x24256efb, car);
};

global.PauseScriptedConversation = function (pause) {
	return _in(0x00000000, 0x2a491a70, pause);
};

global.PedQueueConsiderPedsWithFlagFalse = function (flagid) {
	return _in(0x00000000, 0x555213b4, flagid);
};

global.PedQueueConsiderPedsWithFlagTrue = function (flagid) {
	return _in(0x00000000, 0x489c3a48, flagid);
};

global.PedQueueRejectPedsWithFlagFalse = function (flagid) {
	return _in(0x00000000, 0x61a812f5, flagid);
};

global.PedQueueRejectPedsWithFlagTrue = function (flagid) {
	return _in(0x00000000, 0x79e5237b, flagid);
};

global.PickupsPassTime = function (time) {
	return _in(0x00000000, 0x59da4975, time);
};

global.PlaceObjectRelativeToCar = function (obj, car, x, y, z) {
	return _in(0x00000000, 0x21de7496, obj, car, _fv(x), _fv(y), _fv(z));
};

global.PlaneStartsInAir = function (plane) {
	return _in(0x00000000, 0x0e1645cd, plane);
};

global.PlayAudioEvent = function (name) {
	return _in(0x00000000, 0x486f3d93, _ts(name));
};

global.PlayAudioEventFromObject = function (EventName, obj) {
	return _in(0x00000000, 0x4bb9178a, _ts(EventName), obj);
};

global.PlayAudioEventFromPed = function (name, ped) {
	return _in(0x00000000, 0x61064783, _ts(name), ped);
};

global.PlayAudioEventFromVehicle = function (name, veh) {
	return _in(0x00000000, 0x2f4b2a8b, _ts(name), veh);
};

global.PlayCarAnim = function (car, animname0, animname1, Unk50, flag0, flag1) {
	return _in(0x00000000, 0x03ee5f1c, car, _ts(animname0), _ts(animname1), _fv(Unk50), flag0, flag1, _r);
};

global.PlayFireSoundFromPosition = function (sound_id, x, y, z) {
	return _in(0x00000000, 0x4b6135e8, sound_id, _fv(x), _fv(y), _fv(z));
};

global.PlayMovie = function () {
	return _in(0x00000000, 0x3cd60f11);
};

global.PlayObjectAnim = function (obj, animname0, animname1, Unk74, flag0, flag1) {
	return _in(0x00000000, 0x5d3241e4, obj, _ts(animname0), _ts(animname1), _fv(Unk74), flag0, flag1, _r);
};

global.PlayScriptedConversationFrontend = function (play) {
	return _in(0x00000000, 0x001b1e5a, play);
};

global.PlaySound = function (SoundId, SoundName) {
	return _in(0x00000000, 0x47ca7c53, SoundId, _ts(SoundName));
};

global.PlaySoundFromObject = function (sound_id, name, obj) {
	return _in(0x00000000, 0x60ae0867, sound_id, _ts(name), obj);
};

global.PlaySoundFromPed = function (SoundId, SoundName, ped) {
	return _in(0x00000000, 0x56f37a81, SoundId, _ts(SoundName), ped);
};

global.PlaySoundFromPosition = function (sound_id, name, x, y, z) {
	return _in(0x00000000, 0x65752c65, sound_id, _ts(name), _fv(x), _fv(y), _fv(z));
};

global.PlaySoundFromVehicle = function (SoundId, SoundName, veh) {
	return _in(0x00000000, 0x763274b7, SoundId, _ts(SoundName), veh);
};

global.PlaySoundFrontend = function (sound, soundName) {
	return _in(0x00000000, 0x4daf2c87, sound, _ts(soundName));
};

global.PlayStreamFromObject = function (obj) {
	return _in(0x00000000, 0x4aa86394, obj);
};

global.PlayStreamFromPed = function (ped) {
	return _in(0x00000000, 0x0c47057f, ped);
};

global.PlayStreamFrontend = function () {
	return _in(0x00000000, 0x133c257f);
};

global.PlayerHasChar = function (playerIndex) {
	return _in(0x00000000, 0x22545844, playerIndex, _r);
};

global.PlayerHasFlashingStarsAboutToDrop = function (playerIndex) {
	return _in(0x00000000, 0x69804b35, playerIndex, _r);
};

global.PlayerHasGreyedOutStars = function (playerIndex) {
	return _in(0x00000000, 0x2b670cd0, playerIndex, _r);
};

global.PlayerIsInteractingWithGarage = function () {
	return _in(0x00000000, 0x2b446480, _r);
};

global.PlayerIsNearFirstPigeon = function (x, y, z) {
	return _in(0x00000000, 0x6d631ced, _fv(x), _fv(y), _fv(z), _r);
};

global.PlayerIsPissedOff = function (player) {
	return _in(0x00000000, 0x7fa21a1e, player, _r);
};

global.PlayerWantsToJoinNetworkGame = function (Unk885) {
	return _in(0x00000000, 0x7d99343c, Unk885, _r);
};

global.PlaystatsCheat = function (stat) {
	return _in(0x00000000, 0x0f9b3a1c, stat);
};

global.PlaystatsFloat = function (Unk785, Unk786) {
	return _in(0x00000000, 0x06b735ed, Unk785, _fv(Unk786));
};

global.PlaystatsInt = function (Unk787, Unk788) {
	return _in(0x00000000, 0x41fa2d0c, Unk787, Unk788);
};

global.PlaystatsIntFloat = function (Unk789, Unk790, Unk791) {
	return _in(0x00000000, 0x511200c7, Unk789, Unk790, _fv(Unk791));
};

global.PlaystatsIntInt = function (Unk792, Unk793, Unk794) {
	return _in(0x00000000, 0x07f35bfe, Unk792, Unk793, Unk794);
};

global.PlaystatsMissionCancelled = function (Unk795) {
	return _in(0x00000000, 0x60d94fa7, Unk795);
};

global.PlaystatsMissionFailed = function (Unk796) {
	return _in(0x00000000, 0x50bb02f7, Unk796);
};

global.PlaystatsMissionPassed = function (str0) {
	return _in(0x00000000, 0x437d3e19, _ts(str0));
};

global.PlaystatsMissionStarted = function (Unk797) {
	return _in(0x00000000, 0x26747ebe, Unk797);
};

global.PointCamAtCam = function (cam, camnext) {
	return _in(0x00000000, 0x44717cf9, cam, camnext);
};

global.PointCamAtCoord = function (cam, x, y, z) {
	return _in(0x00000000, 0x4496175c, cam, _fv(x), _fv(y), _fv(z));
};

global.PointCamAtObject = function (cam, obj) {
	return _in(0x00000000, 0x5e627d20, cam, obj);
};

global.PointCamAtPed = function (cam, ped) {
	return _in(0x00000000, 0x495b0b6f, cam, ped);
};

global.PointCamAtVehicle = function (cam, veh) {
	return _in(0x00000000, 0x69f02ba0, cam, veh);
};

global.PointFixedCam = function (x, y, z, Unk563) {
	return _in(0x00000000, 0x04ff3f49, _fv(x), _fv(y), _fv(z), Unk563);
};

global.PointFixedCamAtObj = function (obj, cam) {
	return _in(0x00000000, 0x02326335, obj, cam);
};

global.PointFixedCamAtPed = function (ped, cam) {
	return _in(0x00000000, 0x3d3b5d94, ped, cam);
};

global.PointFixedCamAtPos = function (x, y, z, cam) {
	return _in(0x00000000, 0x6d4e2a4a, _fv(x), _fv(y), _fv(z), cam);
};

global.PointFixedCamAtVehicle = function (veh, cam) {
	return _in(0x00000000, 0x52ff28df, veh, cam);
};

global.PopCarBoot = function (vehicle) {
	return _in(0x00000000, 0x3c78449f, vehicle);
};

global.PopulateNow = function () {
	return _in(0x00000000, 0x7e3a7e2a);
};

global.Pow = function (base, power) {
	return _in(0x00000000, 0x5add1f46, _fv(base), _fv(power), _r, _rf);
};

global.PreloadStream = function (name) {
	return _in(0x00000000, 0x39de515d, _ts(name), _r);
};

global.PreloadStreamWithStartOffset = function (StreamName, StartOffset) {
	return _in(0x00000000, 0x2b8836a6, _ts(StreamName), StartOffset, _r);
};

global.PreviewRingtone = function (RingtoneId) {
	return _in(0x00000000, 0x79660015, RingtoneId);
};

global.Print = function (gxtName, timeMS, enable) {
	return _in(0x00000000, 0x0a491cff, _ts(gxtName), timeMS, enable);
};

global.PrintBig = function (gxtName, timeMS, enable) {
	return _in(0x00000000, 0x2c8a5404, _ts(gxtName), timeMS, enable);
};

global.PrintBigQ = function (gxtentry, time, flag) {
	return _in(0x00000000, 0x2b2e39bb, _ts(gxtentry), time, flag);
};

global.PrintHelp = function (gxtName) {
	return _in(0x00000000, 0x71076bba, _ts(gxtName));
};

global.PrintHelpForever = function (gxtName) {
	return _in(0x00000000, 0x43f7517d, _ts(gxtName));
};

global.PrintHelpForeverWithNumber = function (gxtName, value) {
	return _in(0x00000000, 0x19836a5b, _ts(gxtName), value);
};

global.PrintHelpForeverWithString = function (gxtName, gxtText) {
	return _in(0x00000000, 0x36d60616, _ts(gxtName), _ts(gxtText));
};

global.PrintHelpForeverWithStringNoSound = function (gxtName, gxtText) {
	return _in(0x00000000, 0x55687797, _ts(gxtName), _ts(gxtText));
};

global.PrintHelpForeverWithTwoNumbers = function (gxtentry, Unk658, Unk659) {
	return _in(0x00000000, 0x795227ee, _ts(gxtentry), Unk658, Unk659);
};

global.PrintHelpOverFrontend = function (gxtentry) {
	return _in(0x00000000, 0x1c334022, _ts(gxtentry));
};

global.PrintHelpWithNumber = function (gxtName, value) {
	return _in(0x00000000, 0x4475789e, _ts(gxtName), value);
};

global.PrintHelpWithString = function (gxtName, gxtText) {
	return _in(0x00000000, 0x521035aa, _ts(gxtName), _ts(gxtText));
};

global.PrintHelpWithStringNoSound = function (gxtName, gxtText) {
	return _in(0x00000000, 0x15734852, _ts(gxtName), _ts(gxtText));
};

global.PrintHelpWithTwoNumbers = function (gxtentry, Unk660, Unk661) {
	return _in(0x00000000, 0x076d157a, _ts(gxtentry), Unk660, Unk661);
};

global.PrintNow = function (gxtName, timeMS, enable) {
	return _in(0x00000000, 0x73b01573, _ts(gxtName), timeMS, enable);
};

global.PrintStringInString = function (gxtName, gxtText, timeMS, enable) {
	return _in(0x00000000, 0x4daa221f, _ts(gxtName), _ts(gxtText), timeMS, enable);
};

global.PrintStringInStringNow = function (gxtName, gxtText, timeMS, enable) {
	return _in(0x00000000, 0x2bb65467, _ts(gxtName), _ts(gxtText), timeMS, enable);
};

global.PrintStringWithLiteralString = function (gxtentry, string, time, flag) {
	return _in(0x00000000, 0x3f89280b, _ts(gxtentry), _ts(string), time, flag);
};

global.PrintStringWithLiteralStringNow = function (gxtName, text, timeMS, enable) {
	return _in(0x00000000, 0x0ca539d6, _ts(gxtName), _ts(text), timeMS, enable);
};

global.PrintStringWithSubstringGivenHashKeyNow = function (gxtkey0, gxtkey1, time, style) {
	return _in(0x00000000, 0x00fd3647, _ts(gxtkey0), gxtkey1, time, style);
};

global.PrintStringWithTwoLiteralStrings = function (gxtentry, string1, string2, time, flag) {
	return _in(0x00000000, 0x19486759, _ts(gxtentry), _ts(string1), _ts(string2), time, flag);
};

global.PrintStringWithTwoLiteralStringsNow = function (gxtentry, string1, string2, time, flag) {
	return _in(0x00000000, 0x7de7708e, _ts(gxtentry), _ts(string1), _ts(string2), time, flag);
};

global.PrintWithNumber = function (gxtName, value, timeMS, enable) {
	return _in(0x00000000, 0x76a63b4c, _ts(gxtName), value, timeMS, enable);
};

global.PrintWithNumberBig = function (gxtName, value, timeMS, enable) {
	return _in(0x00000000, 0x49850843, _ts(gxtName), value, timeMS, enable);
};

global.PrintWithNumberNow = function (gxtName, value, timeMS, enable) {
	return _in(0x00000000, 0x3bda562e, _ts(gxtName), value, timeMS, enable);
};

global.PrintWith_2Numbers = function (gxtName, value1, value2, timeMS, enable) {
	return _in(0x00000000, 0x230a740f, _ts(gxtName), value1, value2, timeMS, enable);
};

global.PrintWith_2NumbersBig = function (gxtentry, Unk662, Unk663, time, flag) {
	return _in(0x00000000, 0x43197215, _ts(gxtentry), Unk662, Unk663, time, flag);
};

global.PrintWith_2NumbersNow = function (gxtName, value1, value2, timeMS, enable) {
	return _in(0x00000000, 0x5d251d72, _ts(gxtName), value1, value2, timeMS, enable);
};

global.PrintWith_3Numbers = function (gxtentry, Unk664, Unk665, Unk666, time, flag) {
	return _in(0x00000000, 0x5fe61572, _ts(gxtentry), Unk664, Unk665, Unk666, time, flag);
};

global.PrintWith_3NumbersNow = function (gxtentry, Unk667, Unk668, Unk669, time, flag) {
	return _in(0x00000000, 0x1a4d0c60, _ts(gxtentry), Unk667, Unk668, Unk669, time, flag);
};

global.PrintWith_4Numbers = function (gxtentry, Unk670, Unk671, Unk672, Unk673, time, flag) {
	return _in(0x00000000, 0x4d4f65ae, _ts(gxtentry), Unk670, Unk671, Unk672, Unk673, time, flag);
};

global.PrintWith_4NumbersNow = function (gxtentry, Unk674, Unk675, Unk676, Unk677, time, flag) {
	return _in(0x00000000, 0x5ccd150b, _ts(gxtentry), Unk674, Unk675, Unk676, Unk677, time, flag);
};

global.PrintWith_5Numbers = function (gxtentry, Unk678, Unk679, Unk680, Unk681, Unk682, time, flag) {
	return _in(0x00000000, 0x2cc356d0, _ts(gxtentry), Unk678, Unk679, Unk680, Unk681, Unk682, time, flag);
};

global.PrintWith_5NumbersNow = function (gxtentry, Unk683, Unk684, Unk685, Unk686, Unk687, time, flag) {
	return _in(0x00000000, 0x5ec2479b, _ts(gxtentry), Unk683, Unk684, Unk685, Unk686, Unk687, time, flag);
};

global.PrintWith_6Numbers = function (gxtentry, Unk688, Unk689, Unk690, Unk691, Unk692, Unk693, time, flag) {
	return _in(0x00000000, 0x03a01f39, _ts(gxtentry), Unk688, Unk689, Unk690, Unk691, Unk692, Unk693, time, flag);
};

global.PrintWith_6NumbersNow = function (gxtentry, Unk694, Unk695, Unk696, Unk697, Unk698, Unk699, time, flag) {
	return _in(0x00000000, 0x156e12ca, _ts(gxtentry), Unk694, Unk695, Unk696, Unk697, Unk698, Unk699, time, flag);
};

global.Printfloat = function (value) {
	return _in(0x00000000, 0x2f206763, _fv(value));
};

global.Printint = function (value) {
	return _in(0x00000000, 0x20421014, value);
};

global.Printnl = function () {
	return _in(0x00000000, 0x4013147b);
};

global.Printstring = function (value) {
	return _in(0x00000000, 0x616f492c, _ts(value));
};

global.Printvector = function (x, y, z) {
	return _in(0x00000000, 0x61965eb3, _fv(x), _fv(y), _fv(z));
};

global.PrioritizeStreamingRequest = function () {
	return _in(0x00000000, 0x1dd926ba);
};

global.ProcessMissionDeletionList = function () {
	return _in(0x00000000, 0x33565078);
};

/**
 * Scope entry for profiler.
 * @param scopeName Scope name.
 */
global.ProfilerEnterScope = function (scopeName) {
	return _in(0x00000000, 0xc795a4a9, _ts(scopeName));
};

/**
 * Scope exit for profiler.
 */
global.ProfilerExitScope = function () {
	return _in(0x00000000, 0xb39ca35c);
};

/**
 * Returns true if the profiler is active.
 * @return True or false.
 */
global.ProfilerIsRecording = function () {
	return _in(0x00000000, 0xf8b7d7bb, _r);
};

global.ProstituteCamActivate = function (activate) {
	return _in(0x00000000, 0x346d76e8, activate);
};

global.ReadKillFrenzyStatus = function () {
	return _in(0x00000000, 0x3f9f0cf5, _r, _ri);
};

global.RegisterBestPosition = function (Unk505, position) {
	return _in(0x00000000, 0x0c051fe2, Unk505, position);
};

global.RegisterClientBroadcastVariables = function (Unk945, Unk946, Unk947) {
	return _in(0x00000000, 0x499b6db6, Unk945, Unk946, Unk947);
};

/**
 * Registered commands can be executed by entering them in the client console (this works for client side and server side registered commands). Or by entering them in the server console/through an RCON client (only works for server side registered commands). Or if you use a supported chat resource, like the default one provided in the cfx-server-data repository, then you can enter the command in chat by prefixing it with a `/`.
 * Commands registered using this function can also be executed by resources, using the [`ExecuteCommand` native](#\_0x561C060B).
 * The restricted bool is not used on the client side. Permissions can only be checked on the server side, so if you want to limit your command with an ace permission automatically, make it a server command (by registering it in a server script).
 * **Example result**:
 * ![](https://i.imgur.com/TaCnG09.png)
 * @param commandName The command you want to register.
 * @param handler A handler function that gets called whenever the command is executed.
 * @param restricted If this is a server command and you set this to true, then players will need the command.yourCommandName ace permission to execute this command.
 */
global.RegisterCommand = function (commandName, handler, restricted) {
	return _in(0x00000000, 0x5fa79b0f, _ts(commandName), _mfr(handler), restricted);
};

global.RegisterFloatStat = function (stat, val) {
	return _in(0x00000000, 0x347e05f3, stat, _fv(val));
};

global.RegisterHatedTargetsAroundPed = function (ped, radius) {
	return _in(0x00000000, 0x70a62140, ped, _fv(radius));
};

global.RegisterHatedTargetsInArea = function (ped, x, y, z, radius) {
	return _in(0x00000000, 0x619e7657, ped, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.RegisterHostBroadcastVariables = function (Unk948, ukn0, ukn1) {
	return _in(0x00000000, 0x18db4caf, Unk948, ukn0, ukn1);
};

global.RegisterIntStat = function (stat, val) {
	return _in(0x00000000, 0x609d07db, stat, val);
};

global.RegisterKillInMultiplayerGame = function (playerIndex, id, ukn) {
	return _in(0x00000000, 0x7d6d0a6c, playerIndex, id, ukn);
};

global.RegisterMissionPassed = function (str) {
	return _in(0x00000000, 0x5fbe5f52, _ts(str));
};

global.RegisterMultiplayerGameWin = function (playerIndex, Unk949) {
	return _in(0x00000000, 0x43e41d81, playerIndex, Unk949);
};

global.RegisterNetworkBestGameScores = function (playerIndex, Unk950, Unk951) {
	return _in(0x00000000, 0x4adb10a4, playerIndex, Unk950, Unk951);
};

/**
 * REGISTER_NUI_CALLBACK
 */
global.RegisterNuiCallback = function (callbackType, callback) {
	return _in(0x00000000, 0xc59b980c, _ts(callbackType), _mfr(callback));
};

/**
 * REGISTER_NUI_CALLBACK_TYPE
 */
global.RegisterNuiCallbackType = function (callbackType) {
	return _in(0x00000000, 0xcd03cda9, _ts(callbackType));
};

global.RegisterOddjobMissionPassed = function () {
	return _in(0x00000000, 0x1b0963af);
};

global.RegisterPlayerRespawnCoords = function (playerIndex, x, y, z) {
	return _in(0x00000000, 0x001954a2, playerIndex, _fv(x), _fv(y), _fv(z));
};

/**
 * REGISTER_RAW_NUI_CALLBACK
 */
global.RegisterRawNuiCallback = function (callbackType, callback) {
	return _in(0x00000000, 0xa8ae9c2f, _ts(callbackType), _mfr(callback));
};

/**
 * An internal function which allows the current resource's HLL script runtimes to receive state for the specified event.
 * @param eventName An event name, or "\*" to disable HLL event filtering for this resource.
 */
global.RegisterResourceAsEventHandler = function (eventName) {
	return _in(0x00000000, 0xd233a168, _ts(eventName));
};

global.RegisterSaveHouse = function (x, y, z, unkf, name, unk0) {
	return _in(0x00000000, 0x7df45001, _fv(x), _fv(y), _fv(z), _fv(unkf), _ts(name), unk0, _r, _ri);
};

global.RegisterScriptWithAudio = function (reg) {
	return _in(0x00000000, 0x5b4452f3, reg);
};

global.RegisterStringForFrontendStat = function (stat, str) {
	return _in(0x00000000, 0x3c295451, stat, _ts(str));
};

global.RegisterTarget = function (ped, target) {
	return _in(0x00000000, 0x5f456b53, ped, target);
};

global.RegisterTrackNumber = function (number) {
	return _in(0x00000000, 0x4d7e12a7, number);
};

global.RegisterWorldPointScriptBrain = function (ScriptName, radius) {
	return _in(0x00000000, 0x32563e09, _ts(ScriptName), _fv(radius));
};

global.ReleaseMovie = function () {
	return _in(0x00000000, 0x55c84cb7);
};

global.ReleasePathNodes = function () {
	return _in(0x00000000, 0x2ce231dc);
};

global.ReleaseScriptControlledMicrophone = function () {
	return _in(0x00000000, 0x2f907ff2);
};

global.ReleaseSoundId = function (sound) {
	return _in(0x00000000, 0x211d390a, sound);
};

global.ReleaseTexture = function (texture) {
	return _in(0x00000000, 0x58524b04, texture);
};

global.ReleaseTimeOfDay = function () {
	return _in(0x00000000, 0x2ad2206e);
};

global.ReleaseWeather = function () {
	return _in(0x00000000, 0x3a115d9d);
};

global.ReloadWebPage = function (htmlviewport) {
	return _in(0x00000000, 0x565b0c3e, htmlviewport);
};

global.RemoveAdditionalPopulationModel = function (model) {
	return _in(0x00000000, 0x602112fc, model);
};

global.RemoveAllCharWeapons = function (ped) {
	return _in(0x00000000, 0x6ba520f0, ped);
};

global.RemoveAllInactiveGroupsFromCleanupList = function () {
	return _in(0x00000000, 0x622e3d34);
};

global.RemoveAllPickupsOfType = function (type) {
	return _in(0x00000000, 0x03622640, type);
};

global.RemoveAnims = function (animName) {
	return _in(0x00000000, 0x55e00e7e, _ts(animName));
};

global.RemoveBlip = function (blip) {
	return _in(0x00000000, 0x7bbf3625, blip);
};

global.RemoveBlipAndClearIndex = function (blip) {
	return _in(0x00000000, 0x66385b6c, blip);
};

global.RemoveCarRecording = function (CarRec) {
	return _in(0x00000000, 0x484964fe, CarRec);
};

global.RemoveCarWindow = function (car, windnum) {
	return _in(0x00000000, 0x038a7526, car, windnum);
};

global.RemoveCarsFromGeneratorsInArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x2bee5f97, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.RemoveCharDefensiveArea = function (ped) {
	return _in(0x00000000, 0x2bc44d7d, ped);
};

global.RemoveCharElegantly = function (ped) {
	return _in(0x00000000, 0x5731084a, ped);
};

global.RemoveCharFromCarMaintainPosition = function (ped, car) {
	return _in(0x00000000, 0x3da4533f, ped, car);
};

global.RemoveCharFromGroup = function (ped) {
	return _in(0x00000000, 0x649316b7, ped);
};

global.RemoveCloseMicPed = function (ped) {
	return _in(0x00000000, 0x72b73fba, ped);
};

/**
 * REMOVE_CONVAR_CHANGE_LISTENER
 * @param cookie The cookie returned from [ADD_CONVAR_CHANGE_LISTENER](#\_0xAB7F7241)
 */
global.RemoveConvarChangeListener = function (cookie) {
	return _in(0x00000000, 0xeac49841, cookie);
};

global.RemoveCoverPoint = function (coverPoint) {
	return _in(0x00000000, 0x4371502a, coverPoint);
};

global.RemoveDecisionMaker = function (dm) {
	return _in(0x00000000, 0x47147ec5, dm);
};

global.RemoveFakeNetworkNameFromPed = function (ped) {
	return _in(0x00000000, 0x37a86fbd, ped);
};

global.RemoveGroup = function (group) {
	return _in(0x00000000, 0x250c2d39, group);
};

global.RemoveIpl = function (iplName) {
	return _in(0x00000000, 0x787f38b5, _ts(iplName));
};

global.RemoveIplDiscreetly = function (iplname) {
	return _in(0x00000000, 0x658f21af, _ts(iplname));
};

global.RemoveNavmeshRequiredRegion = function (Unk599, Unk600) {
	return _in(0x00000000, 0x772660d7, _fv(Unk599), _fv(Unk600), _r);
};

global.RemovePedHelmet = function (ped, removed) {
	return _in(0x00000000, 0x15f033a6, ped, removed);
};

global.RemovePickup = function (pickup) {
	return _in(0x00000000, 0x2119007f, pickup);
};

global.RemovePlayerHelmet = function (playerIndex, remove) {
	return _in(0x00000000, 0x5cf1303d, playerIndex, remove);
};

global.RemoveProjtexFromObject = function (obj) {
	return _in(0x00000000, 0x7330132c, obj);
};

global.RemoveProjtexInRange = function (x, y, z, radius) {
	return _in(0x00000000, 0x170f0d58, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.RemovePtfx = function (ptfx) {
	return _in(0x00000000, 0x4af643d5, ptfx);
};

global.RemovePtfxFromObject = function (obj) {
	return _in(0x00000000, 0x4d7775ba, obj);
};

global.RemovePtfxFromPed = function (ped) {
	return _in(0x00000000, 0x2fc9782a, ped);
};

global.RemovePtfxFromVehicle = function (veh) {
	return _in(0x00000000, 0x3fb14ec5, veh);
};

global.RemoveScriptFire = function (fire) {
	return _in(0x00000000, 0x0e633c13, fire);
};

global.RemoveScriptMic = function () {
	return _in(0x00000000, 0x4307784f);
};

/**
 * **Experimental**: This native may be altered or removed in future versions of CitizenFX without warning.
 * Removes a handler for changes to a state bag.
 * @param cookie The cookie.
 */
global.RemoveStateBagChangeHandler = function (cookie) {
	return _in(0x00000000, 0xd36be661, cookie);
};

global.RemoveStuckCarCheck = function (vehicle) {
	return _in(0x00000000, 0x213308db, vehicle);
};

global.RemoveTemporaryRadarBlipsForPickups = function () {
	return _in(0x00000000, 0x6f797af3);
};

/**
 * REMOVE_TIMECYCLE_MODIFIER
 * @param modifierName The timecycle modifier name.
 */
global.RemoveTimecycleModifier = function (modifierName) {
	return _in(0x00000000, 0x36df8612, _ts(modifierName));
};

/**
 * REMOVE_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 */
global.RemoveTimecycleModifierVar = function (modifierName, varName) {
	return _in(0x00000000, 0x5a5e0d05, _ts(modifierName), _ts(varName));
};

global.RemoveTxd = function (txd) {
	return _in(0x00000000, 0x44c27071, txd);
};

global.RemoveUpsidedownCarCheck = function (vehicle) {
	return _in(0x00000000, 0x6a1244e9, vehicle);
};

global.RemoveWeaponFromChar = function (ped, weapon) {
	return _in(0x00000000, 0x2485231e, ped, weapon);
};

global.RenderRaceTrack = function (render) {
	return _in(0x00000000, 0x5062055b, render);
};

global.RenderWeaponPickupsBigger = function (value) {
	return _in(0x00000000, 0x003b6b13, value);
};

global.ReportCrime = function (x, y, z, name) {
	return _in(0x00000000, 0x076b4c7c, _fv(x), _fv(y), _fv(z), _ts(name));
};

global.ReportDispatch = function (id, x, y, z) {
	return _in(0x00000000, 0x388d6b44, id, _fv(x), _fv(y), _fv(z));
};

global.ReportPoliceSpottingSuspect = function (veh) {
	return _in(0x00000000, 0x07d97f81, veh);
};

global.ReportSuspectArrested = function () {
	return _in(0x00000000, 0x008932d3);
};

global.ReportSuspectDown = function () {
	return _in(0x00000000, 0x6a660231);
};

global.ReportTaggedRadioTrack = function (TrackTextId) {
	return _in(0x00000000, 0x0ed8621f, TrackTextId);
};

global.RequestAdditionalText = function (textName, textIndex) {
	return _in(0x00000000, 0x6a9f01af, _ts(textName), textIndex);
};

global.RequestAllSlodsInWorld = function () {
	return _in(0x00000000, 0x39264921);
};

global.RequestAmbientAudioBank = function (name) {
	return _in(0x00000000, 0x754e1999, _ts(name), _r);
};

global.RequestAnims = function (animName) {
	return _in(0x00000000, 0x65f874de, _ts(animName));
};

global.RequestCarRecording = function (CarRecId) {
	return _in(0x00000000, 0x041d045b, CarRecId);
};

global.RequestCollisionAtPosn = function (x, y, z) {
	return _in(0x00000000, 0x12ed0bc9, _fv(x), _fv(y), _fv(z));
};

global.RequestCollisionForModel = function (model) {
	return _in(0x00000000, 0x66e93537, model);
};

global.RequestControlOfNetworkId = function (netid) {
	return _in(0x00000000, 0x29926b20, netid, _r);
};

global.RequestInteriorModels = function (model, interiorName) {
	return _in(0x00000000, 0x302e113d, model, _ts(interiorName));
};

global.RequestIpl = function (iplName) {
	return _in(0x00000000, 0x59fd4e83, _ts(iplName));
};

global.RequestMissionAudioBank = function (name) {
	return _in(0x00000000, 0x335e603b, _ts(name), _r);
};

global.RequestModel = function (model) {
	return _in(0x00000000, 0x502b5185, model);
};

/**
 * Requests a resource file set with the specified name to be downloaded and mounted on top of the current resource.
 * Resource file sets are specified in `fxmanifest.lua` with the following syntax:
 * ```lua
 * file_set 'addon_ui' {
 * 'ui/addon/index.html',
 * 'ui/addon -- [[*.js',
 * }
 * ```
 * This command will trigger a script error if the request failed.
 * @param setName The name of the file set as specified in `fxmanifest.lua`.
 * @return `TRUE` if the set is mounted, `FALSE` if the request is still pending.
 */
global.RequestResourceFileSet = function (setName) {
	return _in(0x00000000, 0xe7490533, _ts(setName), _r);
};

global.RequestScript = function (scriptName) {
	return _in(0x00000000, 0x6ffe0dfd, _ts(scriptName));
};

global.RequestStreamedTxd = function (txdName, unknown) {
	return _in(0x00000000, 0x7c7b1237, _ts(txdName), unknown);
};

global.ReserveNetworkMissionObjectsForHost = function (count) {
	return _in(0x00000000, 0x2f7508e7, count);
};

global.ReserveNetworkMissionPedsForHost = function (Unk952) {
	return _in(0x00000000, 0x557c7c4a, Unk952);
};

global.ReserveNetworkMissionVehicles = function (Unk953) {
	return _in(0x00000000, 0x15652dc1, Unk953);
};

global.ReserveNetworkMissionVehiclesForHost = function (Unk954) {
	return _in(0x00000000, 0x3e9c7cd3, Unk954);
};

global.ResetAchievementsAward = function () {
	return _in(0x00000000, 0x11e22d1b);
};

global.ResetCamInterpCustomSpeedGraph = function () {
	return _in(0x00000000, 0x779f3ec6);
};

global.ResetCamSplineCustomSpeedGraph = function () {
	return _in(0x00000000, 0x13135c95);
};

global.ResetCarWheels = function (car, reset) {
	return _in(0x00000000, 0x78ce659d, car, reset);
};

global.ResetLocalPlayerWeaponStat = function (wtype, wid) {
	return _in(0x00000000, 0x6c1344c6, wtype, wid);
};

global.ResetNoLawVehiclesDestroyedByLocalPlayer = function () {
	return _in(0x00000000, 0x63615a6d);
};

global.ResetNumOfModelsKilledByPlayer = function (model) {
	return _in(0x00000000, 0x0fb17679, model);
};

global.ResetStuckTimer = function (car, timer_num) {
	return _in(0x00000000, 0x73260714, car, timer_num);
};

global.ResetVisiblePedDamage = function (ped) {
	return _in(0x00000000, 0x2a7247ef, ped);
};

global.RestartScriptedConversation = function () {
	return _in(0x00000000, 0x43a67f1b);
};

global.RestoreScriptArrayFromScratchpad = function (Unk955, Unk956, Unk957, Unk958) {
	return _in(0x00000000, 0x522b182b, Unk955, Unk956, Unk957, Unk958);
};

global.RestoreScriptValuesForNetworkGame = function (Unk1056) {
	return _in(0x00000000, 0x37cd55aa, Unk1056, _r, _ri);
};

global.ResurrectNetworkPlayer = function (playerIndex, x, y, z, ukn0) {
	return _in(0x00000000, 0x17901684, playerIndex, _fv(x), _fv(y), _fv(z), ukn0);
};

global.RetuneRadioDown = function () {
	return _in(0x00000000, 0x0e843cea);
};

global.RetuneRadioToStationIndex = function (radioStation) {
	return _in(0x00000000, 0x48ed6432, radioStation);
};

global.RetuneRadioToStationName = function (name) {
	return _in(0x00000000, 0x58ba4401, _ts(name));
};

global.RetuneRadioUp = function () {
	return _in(0x00000000, 0x6b1c6027);
};

global.ReviveInjuredPed = function (ped) {
	return _in(0x00000000, 0x54eb576a, ped);
};

global.RotateObject = function (obj, x, y, flag) {
	return _in(0x00000000, 0x12b524b7, obj, _fv(x), _fv(y), flag, _r);
};

global.Round = function (Unk1085) {
	return _in(0x00000000, 0x7ca5476a, _fv(Unk1085), _r, _ri);
};

global.SaveFloatToDebugFile = function (Unk1117) {
	return _in(0x00000000, 0x66317064, Unk1117);
};

global.SaveIntToDebugFile = function (Unk1118) {
	return _in(0x00000000, 0x65ef0cb8, Unk1118);
};

global.SaveNewlineToDebugFile = function () {
	return _in(0x00000000, 0x69d90f11);
};

global.SaveScriptArrayInScratchpad = function (Unk959, Unk960, Unk961, Unk962) {
	return _in(0x00000000, 0x331f7e6f, Unk959, Unk960, Unk961, Unk962);
};

global.SaveSettings = function () {
	return _in(0x00000000, 0x584c3830);
};

global.SaveStringToDebugFile = function (Unk1119) {
	return _in(0x00000000, 0x27fa32d4, Unk1119);
};

global.SayAmbientSpeech = function (ped, phraseName, flag0, flag1, style) {
	return _in(0x00000000, 0x5cf149c8, ped, _ts(phraseName), flag0, flag1, style);
};

global.SayAmbientSpeechWithVoice = function (ped, SpeechName, VoiceName, flag0, flag1, style) {
	return _in(0x00000000, 0x2fa55669, ped, _ts(SpeechName), _ts(VoiceName), flag0, flag1, style);
};

global.ScriptAssert = function (text) {
	return _in(0x00000000, 0x10c75bda, _ts(text));
};

global.ScriptIsMovingMobilePhoneOffscreen = function (set) {
	return _in(0x00000000, 0x04804149, set);
};

global.ScriptIsUsingMobilePhone = function (set) {
	return _in(0x00000000, 0x1b0741ba, set);
};

global.SearchCriteriaConsiderPedsWithFlagFalse = function (flagid) {
	return _in(0x00000000, 0x2a860e89, flagid);
};

global.SearchCriteriaConsiderPedsWithFlagTrue = function (flagId) {
	return _in(0x00000000, 0x20ec5b84, flagId);
};

global.SearchCriteriaRejectPedsWithFlagFalse = function (flagid) {
	return _in(0x00000000, 0x0a0444b3, flagid);
};

global.SearchCriteriaRejectPedsWithFlagTrue = function (flagId) {
	return _in(0x00000000, 0x27211b1a, flagId);
};

global.SecuromSpotCheck1 = function () {
	return _in(0x00000000, 0x63576e53, _r);
};

global.SecuromSpotCheck2 = function () {
	return _in(0x00000000, 0x1f40505c, _r);
};

global.SecuromSpotCheck3 = function () {
	return _in(0x00000000, 0x5d1c0a6a, _r);
};

global.SecuromSpotCheck4 = function () {
	return _in(0x00000000, 0x764236ce, _r);
};

global.SelectWeaponsForVehicle = function (veh, weapon) {
	return _in(0x00000000, 0x7ad71a55, veh, weapon);
};

global.SendClientBroadcastVariablesNow = function () {
	return _in(0x00000000, 0x36b40989);
};

/**
 * Sends a message to the specific DUI root page. This is similar to SEND_NUI_MESSAGE.
 * @param duiObject The DUI browser handle.
 * @param jsonString The message, encoded as JSON.
 */
global.SendDuiMessage = function (duiObject, jsonString) {
	return _in(0x00000000, 0xcd380da9, duiObject, _ts(jsonString));
};

/**
 * Injects a 'mouse down' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
 * @param duiObject The DUI browser handle.
 * @param button Either `'left'`, `'middle'` or `'right'`.
 */
global.SendDuiMouseDown = function (duiObject, button) {
	return _in(0x00000000, 0x5d01f191, duiObject, _ts(button));
};

/**
 * Injects a 'mouse move' event for a DUI object. Coordinates are in browser space.
 * @param duiObject The DUI browser handle.
 * @param x The mouse X position.
 * @param y The mouse Y position.
 */
global.SendDuiMouseMove = function (duiObject, x, y) {
	return _in(0x00000000, 0xd9d7a0aa, duiObject, x, y);
};

/**
 * Injects a 'mouse up' event for a DUI object. Coordinates are expected to be set using SEND_DUI_MOUSE_MOVE.
 * @param duiObject The DUI browser handle.
 * @param button Either `'left'`, `'middle'` or `'right'`.
 */
global.SendDuiMouseUp = function (duiObject, button) {
	return _in(0x00000000, 0x1d735b93, duiObject, _ts(button));
};

/**
 * Injects a 'mouse wheel' event for a DUI object.
 * @param duiObject The DUI browser handle.
 * @param deltaY The wheel Y delta.
 * @param deltaX The wheel X delta.
 */
global.SendDuiMouseWheel = function (duiObject, deltaY, deltaX) {
	return _in(0x00000000, 0x2d62133a, duiObject, deltaY, deltaX);
};

/**
 * Sends a message to the `loadingScreen` NUI frame, which contains the HTML page referenced in `loadscreen` resources.
 * @param jsonString The JSON-encoded message.
 * @return A success value.
 */
global.SendLoadingScreenMessage = function (jsonString) {
	return _in(0x00000000, 0x8bbe6cc0, _ts(jsonString), _r);
};

global.SendNmMessage = function (ped) {
	return _in(0x00000000, 0x75ac2519, ped);
};

/**
 * SEND_NUI_MESSAGE
 */
global.SendNuiMessage = function (jsonString) {
	return _in(0x00000000, 0x78608acb, _ts(jsonString), _r);
};

global.SetActivateObjectPhysicsAsSoonAsItIsUnfrozen = function (obj, set) {
	return _in(0x00000000, 0x378531f8, obj, set);
};

global.SetAdvancedBoolInDecisionMaker = function (dm, Unk844, Unk845, Unk846, Unk847) {
	return _in(0x00000000, 0x709d2036, dm, Unk844, Unk845, Unk846, Unk847);
};

global.SetAllCarGeneratorsBackToActive = function () {
	return _in(0x00000000, 0x399e1a43);
};

global.SetAllCarsCanBeDamaged = function (set) {
	return _in(0x00000000, 0x3ea5269d, set);
};

global.SetAllPickupsOfTypeCollectableByCar = function (pickuptype, set) {
	return _in(0x00000000, 0x54b054d0, pickuptype, set);
};

global.SetAllRandomPedsFlee = function (player, set) {
	return _in(0x00000000, 0x110957ef, player, set);
};

global.SetAllowDummyConversions = function (set) {
	return _in(0x00000000, 0x08ab2787, set);
};

global.SetAlwaysDisplayWeaponPickupMessage = function (set) {
	return _in(0x00000000, 0x3f0a2a72, set);
};

global.SetAmbientPlanesSpeedMultiplier = function (multiplier) {
	return _in(0x00000000, 0x4b470947, _fv(multiplier));
};

global.SetAmbientVoiceName = function (ped, name) {
	return _in(0x00000000, 0x426a4ed8, ped, _ts(name));
};

global.SetAmmoInClip = function (ped, weapon, ammo) {
	return _in(0x00000000, 0x6e1a0a84, ped, weapon, ammo, _r, _ri);
};

global.SetAnimGroupForChar = function (ped, grname) {
	return _in(0x00000000, 0x4cd43e46, ped, _ts(grname));
};

global.SetArmourPickupNetworkRegenTime = function (timeMS) {
	return _in(0x00000000, 0x53cc1d3c, timeMS);
};

/**
 * Sets a floating-point parameter for a submix effect.
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix. It is expected that the effect is set in this slot beforehand.
 * @param paramIndex The parameter index for the effect.
 * @param paramValue The parameter value to set.
 */
global.SetAudioSubmixEffectParamFloat = function (submixId, effectSlot, paramIndex, paramValue) {
	return _in(0x00000000, 0x9a209b3c, submixId, effectSlot, paramIndex, _fv(paramValue));
};

/**
 * Sets an integer parameter for a submix effect.
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix. It is expected that the effect is set in this slot beforehand.
 * @param paramIndex The parameter index for the effect.
 * @param paramValue The parameter value to set.
 */
global.SetAudioSubmixEffectParamInt = function (submixId, effectSlot, paramIndex, paramValue) {
	return _in(0x00000000, 0x77fae2b8, submixId, effectSlot, paramIndex, paramValue);
};

/**
 * Assigns a RadioFX effect to a submix effect slot.
 * The parameter values for this effect are as follows (backticks are used to represent hashes):
 * | Index | Type | Description |
 * |-|-|-|
 * | \`enabled\` | int | Enables or disables RadioFX on this DSP. |
 * | \`default\` | int | Sets default parameters for the RadioFX DSP and enables it. |
 * | \`freq_low\` | float |  |
 * | \`freq_hi\` | float |  |
 * | \`fudge\` | float |  |
 * | \`rm_mod_freq\` | float |  |
 * | \`rm_mix\` | float |  |
 * | \`o_freq_lo\` | float |  |
 * | \`o_freq_hi\` | float |  |
 * @param submixId The submix.
 * @param effectSlot The effect slot for the submix.
 */
global.SetAudioSubmixEffectRadioFx = function (submixId, effectSlot) {
	return _in(0x00000000, 0xaaa94d53, submixId, effectSlot);
};

/**
 * Sets the volumes for the sound channels in a submix effect.
 * Values can be between 0.0 and 1.0.
 * Channel 5 and channel 6 are not used in voice chat but are believed to be center and LFE channels.
 * Output slot starts at 0 for the first ADD_AUDIO_SUBMIX_OUTPUT call then incremented by 1 on each subsequent call.
 * @param submixId The submix.
 * @param outputSlot The output slot index.
 * @param frontLeftVolume The volume for the front left channel.
 * @param frontRightVolume The volume for the front right channel.
 * @param rearLeftVolume The volume for the rear left channel.
 * @param rearRightVolume The volume for the rear right channel.
 * @param channel5Volume The volume for channel 5.
 * @param channel6Volume The volume for channel 6.
 */
global.SetAudioSubmixOutputVolumes = function (submixId, outputSlot, frontLeftVolume, frontRightVolume, rearLeftVolume, rearRightVolume, channel5Volume, channel6Volume) {
	return _in(0x00000000, 0x825dc0d1, submixId, outputSlot, _fv(frontLeftVolume), _fv(frontRightVolume), _fv(rearLeftVolume), _fv(rearRightVolume), _fv(channel5Volume), _fv(channel6Volume));
};

global.SetBikeRiderWillPutFootDownWhenStopped = function (bike, set) {
	return _in(0x00000000, 0x6e77153d, bike, set);
};

global.SetBit = function (bit) {
	return _in(0x00000000, 0x39551b76, _i, bit);
};

global.SetBitsInRange = function (rangebegin, rangeend, val) {
	return _in(0x00000000, 0x14dd5f87, _i, rangebegin, rangeend, val);
};

global.SetBlipAsFriendly = function (blip, value) {
	return _in(0x00000000, 0x0580462a, blip, value);
};

global.SetBlipAsShortRange = function (blip, value) {
	return _in(0x00000000, 0x2ed90276, blip, value);
};

global.SetBlipCoordinates = function (blip, x, y, z) {
	return _in(0x00000000, 0x3d91564e, blip, _fv(x), _fv(y), _fv(z));
};

global.SetBlipMarkerLongDistance = function (blip, set) {
	return _in(0x00000000, 0x150a6532, blip, set);
};

global.SetBlipThrottleRandomly = function (veh, set) {
	return _in(0x00000000, 0x12a619e9, veh, set);
};

global.SetBlockCameraToggle = function (set) {
	return _in(0x00000000, 0x45c63b22, set);
};

global.SetBlockingOfNonTemporaryEvents = function (ped, value) {
	return _in(0x00000000, 0x76247429, ped, value);
};

global.SetBriansMood = function (mood) {
	return _in(0x00000000, 0x34f128f9, mood);
};

global.SetCamActive = function (camera, value) {
	return _in(0x00000000, 0x43e42686, camera, value);
};

global.SetCamAttachOffset = function (cam, x, y, z) {
	return _in(0x00000000, 0x72e93e13, cam, _fv(x), _fv(y), _fv(z));
};

global.SetCamAttachOffsetIsRelative = function (cam, set) {
	return _in(0x00000000, 0x44984033, cam, set);
};

global.SetCamBehindPed = function (ped) {
	return _in(0x00000000, 0x48740598, ped);
};

global.SetCamComponentShake = function (cam, componentid, Unk564, time, x, y, z) {
	return _in(0x00000000, 0x52ce5d9f, cam, componentid, Unk564, time, _fv(x), _fv(y), _fv(z));
};

global.SetCamDofFocuspoint = function (cam, x, y, z, Unk565) {
	return _in(0x00000000, 0x39dc5aeb, cam, _fv(x), _fv(y), _fv(z), _fv(Unk565));
};

global.SetCamFarClip = function (cam, clip) {
	return _in(0x00000000, 0x181f6b00, cam, _fv(clip));
};

global.SetCamFarDof = function (cam, fardof) {
	return _in(0x00000000, 0x52f543a3, cam, _fv(fardof));
};

global.SetCamFov = function (camera, fov) {
	return _in(0x00000000, 0x55d470c2, camera, _fv(fov));
};

global.SetCamInFrontOfPed = function (ped) {
	return _in(0x00000000, 0x423661a7, ped);
};

global.SetCamInheritRollObject = function (cam, obj) {
	return _in(0x00000000, 0x208b4a6a, cam, obj);
};

global.SetCamInheritRollPed = function (cam, ped) {
	return _in(0x00000000, 0x09a34209, cam, ped);
};

global.SetCamInheritRollVehicle = function (cam, veh) {
	return _in(0x00000000, 0x51ad2993, cam, veh);
};

global.SetCamInterpCustomSpeedGraph = function (speed) {
	return _in(0x00000000, 0x03102fee, _fv(speed));
};

global.SetCamInterpDetailRotStyleAngles = function (Unk566) {
	return _in(0x00000000, 0x5f7307f4, Unk566);
};

global.SetCamInterpDetailRotStyleQuats = function (Unk567) {
	return _in(0x00000000, 0x439c47d5, Unk567);
};

global.SetCamInterpStyleCore = function (cam0, cam1, cam2, time, flag) {
	return _in(0x00000000, 0x72297cdc, cam0, cam1, cam2, time, flag);
};

global.SetCamInterpStyleDetailed = function (cam, Unk568, Unk569, Unk570, Unk571) {
	return _in(0x00000000, 0x683927f5, cam, Unk568, Unk569, Unk570, Unk571);
};

global.SetCamMotionBlur = function (cam, blur) {
	return _in(0x00000000, 0x693d7b21, cam, _fv(blur));
};

global.SetCamName = function (cam, camname) {
	return _in(0x00000000, 0x2ae87b02, cam, _ts(camname));
};

global.SetCamNearClip = function (cam, clip) {
	return _in(0x00000000, 0x298827fc, cam, _fv(clip));
};

global.SetCamNearDof = function (cam, dof) {
	return _in(0x00000000, 0x60ad2fe0, cam, _fv(dof));
};

global.SetCamPointDampingParams = function (cam, x, y, z) {
	return _in(0x00000000, 0x57ac39f5, cam, _fv(x), _fv(y), _fv(z));
};

global.SetCamPointOffset = function (cam, x, y, z) {
	return _in(0x00000000, 0x1c887939, cam, _fv(x), _fv(y), _fv(z));
};

global.SetCamPointOffsetIsRelative = function (cam, set) {
	return _in(0x00000000, 0x12f20552, cam, set);
};

global.SetCamPos = function (camera, pX, pY, pZ) {
	return _in(0x00000000, 0x152f6314, camera, _fv(pX), _fv(pY), _fv(pZ));
};

global.SetCamPropagate = function (camera, value) {
	return _in(0x00000000, 0x44414e60, camera, value);
};

global.SetCamRoll = function (cam, roll) {
	return _in(0x00000000, 0x4c5142c0, cam, _fv(roll));
};

global.SetCamRot = function (camera, angleX, angleY, angleZ) {
	return _in(0x00000000, 0x746744d1, camera, _fv(angleX), _fv(angleY), _fv(angleZ));
};

global.SetCamShake = function (cam, Unk572, shakeval) {
	return _in(0x00000000, 0x686b6395, cam, Unk572, shakeval);
};

global.SetCamSplineCustomSpeedGraph = function (speed) {
	return _in(0x00000000, 0x391b5a76, _fv(speed));
};

global.SetCamSplineDuration = function (cam, duration) {
	return _in(0x00000000, 0x4adb6f79, cam, duration);
};

global.SetCamSplineProgress = function (cam, progress) {
	return _in(0x00000000, 0x5a712f63, cam, _fv(progress));
};

global.SetCamSplineSpeedConstant = function (cam, set) {
	return _in(0x00000000, 0x2cf72eb7, cam, set);
};

global.SetCamSplineSpeedGraph = function (cam, Unk573) {
	return _in(0x00000000, 0x47ac289c, cam, Unk573);
};

global.SetCamTargetPed = function (camera, ped) {
	return _in(0x00000000, 0x50e21e4c, camera, ped);
};

global.SetCameraAutoScriptActivation = function (set) {
	return _in(0x00000000, 0x31d53b3d, set);
};

global.SetCameraBeginCamCommandsRequired = function (set) {
	return _in(0x00000000, 0x03b12ed0, set);
};

global.SetCameraControlsDisabledWithPlayerControls = function (value) {
	return _in(0x00000000, 0x3c714f12, value);
};

global.SetCameraState = function (cam, state) {
	return _in(0x00000000, 0x4ed45146, cam, state);
};

global.SetCanBurstCarTyres = function (car, set) {
	return _in(0x00000000, 0x24de2039, car, set);
};

global.SetCanResprayCar = function (car, can) {
	return _in(0x00000000, 0x76a2739d, car, can);
};

global.SetCanTargetCharWithoutLos = function (ped, set) {
	return _in(0x00000000, 0x3fa651a7, ped, set);
};

global.SetCarAllowedToDrown = function (car, allowed) {
	return _in(0x00000000, 0x31026ce0, car, allowed);
};

global.SetCarAlwaysCreateSkids = function (car, set) {
	return _in(0x00000000, 0x0b9f0356, car, set);
};

global.SetCarAnimCurrentTime = function (car, animname0, animname1, time) {
	return _in(0x00000000, 0x04485574, car, _ts(animname0), _ts(animname1), _fv(time));
};

global.SetCarAnimSpeed = function (car, animname0, animname1, speed) {
	return _in(0x00000000, 0x74cd7d1f, car, _ts(animname0), _ts(animname1), _fv(speed));
};

global.SetCarAsMissionCar = function (car) {
	return _in(0x00000000, 0x210a33b2, car);
};

global.SetCarCanBeDamaged = function (vehicle, value) {
	return _in(0x00000000, 0x394e733e, vehicle, value);
};

global.SetCarCanBeVisiblyDamaged = function (vehicle, value) {
	return _in(0x00000000, 0x4727446b, vehicle, value);
};

global.SetCarCollision = function (car, set) {
	return _in(0x00000000, 0x6a9033b3, car, set);
};

global.SetCarColourCombination = function (car, combination) {
	return _in(0x00000000, 0x0b823c8d, car, combination);
};

global.SetCarCoordinates = function (vehicle, pX, pY, pZ) {
	return _in(0x00000000, 0x567b6c56, vehicle, _fv(pX), _fv(pY), _fv(pZ));
};

global.SetCarCoordinatesNoOffset = function (car, x, y, z) {
	return _in(0x00000000, 0x12d64378, car, _fv(x), _fv(y), _fv(z));
};

global.SetCarDensityMultiplier = function (density) {
	return _in(0x00000000, 0x0aa73a12, _fv(density));
};

global.SetCarDistanceAheadMultiplier = function (car, multiplier) {
	return _in(0x00000000, 0x071b6690, car, _fv(multiplier));
};

global.SetCarDoorLatched = function (car, door, flag0, flag1) {
	return _in(0x00000000, 0x0ead6cfb, car, door, flag0, flag1);
};

global.SetCarEngineOn = function (car, flag0, flag1) {
	return _in(0x00000000, 0x0caa42d0, car, flag0, flag1);
};

global.SetCarExistsOnAllMachines = function (vehicle, exists) {
	return _in(0x00000000, 0x7bac73df, vehicle, exists);
};

global.SetCarForwardSpeed = function (vehicle, speed) {
	return _in(0x00000000, 0x65bb0060, vehicle, _fv(speed));
};

global.SetCarFovFadeMult = function (multiplier) {
	return _in(0x00000000, 0x5eee6adb, _fv(multiplier));
};

global.SetCarFovMax = function (maxfov) {
	return _in(0x00000000, 0x3fbf13bd, _fv(maxfov));
};

global.SetCarFovMin = function (minfov) {
	return _in(0x00000000, 0x068f59e3, _fv(minfov));
};

global.SetCarFovRate = function (rate) {
	return _in(0x00000000, 0x536b4f4a, _fv(rate));
};

global.SetCarFovStartSpeed = function (speed) {
	return _in(0x00000000, 0x3cf41d47, _fv(speed));
};

global.SetCarFovStartSpeedBoat = function (speed) {
	return _in(0x00000000, 0x40fc5520, _fv(speed));
};

global.SetCarGeneratorsActiveInArea = function (x0, y0, z0, x1, y1, z1, set) {
	return _in(0x00000000, 0x69ce154f, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), set);
};

global.SetCarHeading = function (vehicle, dir) {
	return _in(0x00000000, 0x75e40528, vehicle, _fv(dir));
};

global.SetCarHealth = function (vehicle, Value) {
	return _in(0x00000000, 0x49b6525c, vehicle, Value);
};

global.SetCarInCutscene = function (car, set) {
	return _in(0x00000000, 0x32593711, car, set);
};

global.SetCarLaneShift = function (car, shift) {
	return _in(0x00000000, 0x10fd2442, car, _fv(shift));
};

global.SetCarLightMultiplier = function (car, multiplier) {
	return _in(0x00000000, 0x74824ada, car, _fv(multiplier));
};

global.SetCarLivery = function (car, livery) {
	return _in(0x00000000, 0x2e9e149d, car, livery);
};

global.SetCarMotionBlurEffectBoat = function (blur) {
	return _in(0x00000000, 0x7d106167, _fv(blur));
};

global.SetCarNotDamagedByRelationshipGroup = function (car, set, group) {
	return _in(0x00000000, 0x3aad447a, car, set, group);
};

global.SetCarOnGroundProperly = function (vehicle) {
	return _in(0x00000000, 0x0e717e98, vehicle, _r, _ri);
};

global.SetCarOnlyDamagedByPlayer = function (car, set) {
	return _in(0x00000000, 0x2880077c, car, set);
};

global.SetCarOnlyDamagedByRelationshipGroup = function (car, set, group) {
	return _in(0x00000000, 0x783f287a, car, set, group);
};

global.SetCarProofs = function (vehicle, bulletProof, fireProof, explosionProof, collisionProof, meleeProof) {
	return _in(0x00000000, 0x137c35ba, vehicle, bulletProof, fireProof, explosionProof, collisionProof, meleeProof);
};

global.SetCarRandomRouteSeed = function (car, seed) {
	return _in(0x00000000, 0x19d302ae, car, seed);
};

global.SetCarStayInFastLane = function (car, set) {
	return _in(0x00000000, 0x5ead47e8, car, set);
};

global.SetCarStayInSlowLane = function (car, set) {
	return _in(0x00000000, 0x1b8b3973, car, set);
};

global.SetCarStrong = function (vehicle, strong) {
	return _in(0x00000000, 0x61f40670, vehicle, strong);
};

global.SetCarTraction = function (car, traction) {
	return _in(0x00000000, 0x278f2d0a, car, _fv(traction));
};

global.SetCarVisible = function (vehicle, value) {
	return _in(0x00000000, 0x02d13d06, vehicle, value);
};

global.SetCarWatertight = function (car, set) {
	return _in(0x00000000, 0x31017e6e, car, set);
};

global.SetCellphoneRanked = function (toggle) {
	return _in(0x00000000, 0x47e03e87, toggle);
};

global.SetCharAccuracy = function (ped, value) {
	return _in(0x00000000, 0x1958471a, ped, value);
};

global.SetCharAllAnimsSpeed = function (ped, speed) {
	return _in(0x00000000, 0x5bdb7e2c, ped, _fv(speed));
};

global.SetCharAllowedToDuck = function (ped, set) {
	return _in(0x00000000, 0x6e2e55b5, ped, set);
};

global.SetCharAllowedToRunOnBoats = function (ped, set) {
	return _in(0x00000000, 0x662235a5, ped, set);
};

global.SetCharAmmo = function (ped, weapon, ammo) {
	return _in(0x00000000, 0x437d247e, ped, weapon, ammo);
};

global.SetCharAngledDefensiveArea = function (ped, x0, y0, z0, x1, y1, z1, angle) {
	return _in(0x00000000, 0x0dbd5654, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _fv(angle));
};

global.SetCharAnimBlendOutDelta = function (ped, AnimName0, AnimName1, delta) {
	return _in(0x00000000, 0x000a1fce, ped, _ts(AnimName0), _ts(AnimName1), _fv(delta));
};

global.SetCharAnimCurrentTime = function (ped, AnimName0, AnimName1, time) {
	return _in(0x00000000, 0x245f424f, ped, _ts(AnimName0), _ts(AnimName1), _fv(time));
};

global.SetCharAnimPlayingFlag = function (ped, AnimName0, AnimName1, flag) {
	return _in(0x00000000, 0x52da430a, ped, _ts(AnimName0), _ts(AnimName1), flag, _r);
};

global.SetCharAnimSpeed = function (ped, AnimName0, AnimName1, speed) {
	return _in(0x00000000, 0x3c2a3334, ped, _ts(AnimName0), _ts(AnimName1), _fv(speed));
};

global.SetCharAsEnemy = function (ped, value) {
	return _in(0x00000000, 0x1c35407f, ped, value);
};

global.SetCharAsMissionChar = function (ped) {
	return _in(0x00000000, 0x60ec0540, ped);
};

global.SetCharBleeding = function (ped, set) {
	return _in(0x00000000, 0x38330b4a, ped, set);
};

global.SetCharBulletproofVest = function (ped, set) {
	return _in(0x00000000, 0x076a7e4e, ped, set);
};

global.SetCharCanBeKnockedOffBike = function (ped, value) {
	return _in(0x00000000, 0x30c54cd2, ped, value);
};

global.SetCharCanBeShotInVehicle = function (ped, enabled) {
	return _in(0x00000000, 0x79912adc, ped, enabled);
};

global.SetCharCanSmashGlass = function (ped, set) {
	return _in(0x00000000, 0x0f634f9d, ped, set);
};

global.SetCharCantBeDraggedOut = function (ped, enabled) {
	return _in(0x00000000, 0x2e5c36c0, ped, enabled);
};

global.SetCharClimbAnimRate = function (ped, rate) {
	return _in(0x00000000, 0x68ab2dd9, ped, _fv(rate));
};

global.SetCharCollision = function (ped, set) {
	return _in(0x00000000, 0x2a7413eb, ped, set);
};

global.SetCharComponentVariation = function (ped, component, modelVariation, textureVariation) {
	return _in(0x00000000, 0x71a52973, ped, component, modelVariation, textureVariation);
};

global.SetCharCoordinates = function (ped, x, y, z) {
	return _in(0x00000000, 0x689d0f5f, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharCoordinatesDontClearPlayerTasks = function (ped, x, y, z) {
	return _in(0x00000000, 0x3458600c, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharCoordinatesDontWarpGang = function (ped, x, y, z) {
	return _in(0x00000000, 0x624e5833, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharCoordinatesDontWarpGangNoOffset = function (ped, x, y, z) {
	return _in(0x00000000, 0x355f3feb, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharCoordinatesNoOffset = function (ped, x, y, z) {
	return _in(0x00000000, 0x57c758f0, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharCurrentWeaponVisible = function (ped, visble) {
	return _in(0x00000000, 0x6dab7270, ped, visble);
};

global.SetCharDecisionMaker = function (ped, dm) {
	return _in(0x00000000, 0x01f8116c, ped, dm);
};

global.SetCharDecisionMakerToDefault = function (ped) {
	return _in(0x00000000, 0x73cb1489, ped);
};

global.SetCharDefaultComponentVariation = function (ped) {
	return _in(0x00000000, 0x4fb30db6, ped);
};

global.SetCharDefensiveAreaAttachedToPed = function (ped, pednext, x0, y0, z0, x1, y1, z1, Unk7, Unk8) {
	return _in(0x00000000, 0x51c00627, ped, pednext, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), Unk7, Unk8);
};

global.SetCharDesiredHeading = function (ped, heading) {
	return _in(0x00000000, 0x6ef64079, ped, _fv(heading));
};

global.SetCharDiesInstantlyInWater = function (ped, allow) {
	return _in(0x00000000, 0x0cca5cfc, ped, allow);
};

global.SetCharDropsWeaponsWhenDead = function (ped, value) {
	return _in(0x00000000, 0x2d43113a, ped, value);
};

global.SetCharDrownsInSinkingVehicle = function (ped, set) {
	return _in(0x00000000, 0x1e805412, ped, set);
};

global.SetCharDrownsInWater = function (ped, set) {
	return _in(0x00000000, 0x0c2a7847, ped, set);
};

global.SetCharDruggedUp = function (ped, drugged) {
	return _in(0x00000000, 0x458c333d, ped, drugged);
};

global.SetCharDucking = function (ped, set) {
	return _in(0x00000000, 0x64302f16, ped, set, _r, _ri);
};

global.SetCharDuckingTimed = function (ped, timed) {
	return _in(0x00000000, 0x003a7647, ped, timed);
};

global.SetCharFireDamageMultiplier = function (ped, multiplier) {
	return _in(0x00000000, 0x29ae70a8, ped, _fv(multiplier));
};

global.SetCharForceDieInCar = function (ped, set) {
	return _in(0x00000000, 0x54af2f7a, ped, set);
};

global.SetCharGestureGroup = function (ped, AnimGroup) {
	return _in(0x00000000, 0x1106579b, ped, _ts(AnimGroup));
};

global.SetCharGetOutUpsideDownCar = function (ped, set) {
	return _in(0x00000000, 0x1aaf54be, ped, set);
};

global.SetCharGravity = function (ped, value) {
	return _in(0x00000000, 0x602c46e7, ped, _fv(value));
};

global.SetCharHeading = function (ped, heading) {
	return _in(0x00000000, 0x46b5523b, ped, _fv(heading));
};

global.SetCharHealth = function (ped, health) {
	return _in(0x00000000, 0x575e2880, ped, health);
};

global.SetCharInCutscene = function (ped, set) {
	return _in(0x00000000, 0x12850007, ped, set);
};

global.SetCharInvincible = function (ped, enable) {
	return _in(0x00000000, 0x2a58578b, ped, enable);
};

global.SetCharIsTargetPriority = function (ped, enable) {
	return _in(0x00000000, 0x163a1d77, ped, enable);
};

global.SetCharKeepTask = function (ped, value) {
	return _in(0x00000000, 0x264009d3, ped, value);
};

global.SetCharMaxHealth = function (ped, value) {
	return _in(0x00000000, 0x08a453c9, ped, value);
};

global.SetCharMaxMoveBlendRatio = function (ped, ratio) {
	return _in(0x00000000, 0x640e7764, ped, _fv(ratio));
};

global.SetCharMaxTimeInWater = function (ped, time) {
	return _in(0x00000000, 0x45f32596, ped, _fv(time));
};

global.SetCharMaxTimeUnderwater = function (ped, time) {
	return _in(0x00000000, 0x7110790b, ped, _fv(time));
};

global.SetCharMeleeActionFlag0 = function (ped, set) {
	return _in(0x00000000, 0x771f3d7d, ped, set);
};

global.SetCharMeleeActionFlag1 = function (ped, set) {
	return _in(0x00000000, 0x2ef60aa6, ped, set);
};

global.SetCharMeleeActionFlag2 = function (ped, set) {
	return _in(0x00000000, 0x265e37e1, ped, set);
};

global.SetCharMeleeMovementConstaintBox = function (ped, x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x5a7d2c3c, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SetCharMoney = function (ped, amount) {
	return _in(0x00000000, 0x7b44224f, ped, amount);
};

global.SetCharMoveAnimSpeedMultiplier = function (ped, multiplier) {
	return _in(0x00000000, 0x5dc456de, ped, _fv(multiplier));
};

global.SetCharMovementAnimsBlocked = function (ped, set) {
	return _in(0x00000000, 0x346b4fe7, ped, set);
};

global.SetCharNameDebug = function (ped, debugName) {
	return _in(0x00000000, 0x751967fd, ped, _ts(debugName));
};

global.SetCharNeverLeavesGroup = function (ped, value) {
	return _in(0x00000000, 0x0f4c513e, ped, value);
};

global.SetCharNeverTargetted = function (ped, set) {
	return _in(0x00000000, 0x5ea84115, ped, set);
};

global.SetCharNotDamagedByRelationshipGroup = function (ped, relationshipGroup, enable) {
	return _in(0x00000000, 0x077a0221, ped, relationshipGroup, enable);
};

global.SetCharOnlyDamagedByPlayer = function (ped, set) {
	return _in(0x00000000, 0x440d0a91, ped, set);
};

global.SetCharOnlyDamagedByRelationshipGroup = function (ped, set, relgroup) {
	return _in(0x00000000, 0x506c2898, ped, set, relgroup);
};

global.SetCharProofs = function (ped, unknown0, fallingDamage, unknown1, unknown2, unknown3) {
	return _in(0x00000000, 0x76f25b4b, ped, unknown0, fallingDamage, unknown1, unknown2, unknown3);
};

global.SetCharPropIndex = function (ped, propType, index) {
	return _in(0x00000000, 0x5fe95249, ped, propType, index);
};

global.SetCharPropIndexTexture = function (ped, Unk9, Unk10, Unk11) {
	return _in(0x00000000, 0x57390041, ped, Unk9, Unk10, Unk11);
};

global.SetCharProvideCoveringFire = function (ped, set) {
	return _in(0x00000000, 0x1a827b2c, ped, set);
};

global.SetCharRandomComponentVariation = function (ped) {
	return _in(0x00000000, 0x47d9437c, ped);
};

global.SetCharReadyToBeExecuted = function (ped, set) {
	return _in(0x00000000, 0x5f58606a, ped, set);
};

global.SetCharReadyToBeStunned = function (ped, set) {
	return _in(0x00000000, 0x2b416a06, ped, set);
};

global.SetCharRelationship = function (ped, relationshipLevel, relationshipGroup) {
	return _in(0x00000000, 0x6d9538e1, ped, relationshipLevel, relationshipGroup);
};

global.SetCharRelationshipGroup = function (ped, relationshipGroup) {
	return _in(0x00000000, 0x61822a3c, ped, relationshipGroup);
};

global.SetCharRotation = function (ped, xr, yr, zr) {
	return _in(0x00000000, 0x70e13826, ped, _fv(xr), _fv(yr), _fv(zr));
};

global.SetCharShootRate = function (ped, rate) {
	return _in(0x00000000, 0x2ae979dc, ped, rate);
};

global.SetCharSignalAfterKill = function (ped, set) {
	return _in(0x00000000, 0x6c6c1cf3, ped, set);
};

global.SetCharSphereDefensiveArea = function (ped, x, y, z, radius) {
	return _in(0x00000000, 0x56ad2409, ped, _fv(x), _fv(y), _fv(z), _fv(radius));
};

global.SetCharStayInCarWhenJacked = function (ped, set) {
	return _in(0x00000000, 0x1a02748f, ped, set);
};

global.SetCharSuffersCriticalHits = function (ped, value) {
	return _in(0x00000000, 0x154e450e, ped, value);
};

global.SetCharUsesDeafultAnimGroupWhenFleeing = function (ped, set) {
	return _in(0x00000000, 0x0dd71ba9, ped, set);
};

global.SetCharUsesUpperbodyDamageAnimsOnly = function (ped, set) {
	return _in(0x00000000, 0x268f1413, ped, set);
};

global.SetCharVelocity = function (ped, x, y, z) {
	return _in(0x00000000, 0x07c76803, ped, _fv(x), _fv(y), _fv(z));
};

global.SetCharVisible = function (ped, value) {
	return _in(0x00000000, 0x04cf0105, ped, value);
};

global.SetCharWalkAlongsideLeaderWhenAppropriate = function (ped, set) {
	return _in(0x00000000, 0x41121d51, ped, set);
};

global.SetCharWantedByPolice = function (ped, wanted) {
	return _in(0x00000000, 0x05c619d7, ped, wanted);
};

global.SetCharWatchMelee = function (ped, set) {
	return _in(0x00000000, 0x142a5e83, ped, set);
};

global.SetCharWeaponSkill = function (ped, skill) {
	return _in(0x00000000, 0x441b1eaf, ped, skill);
};

global.SetCharWillCowerInsteadOfFleeing = function (ped, set) {
	return _in(0x00000000, 0x58fb0bc1, ped, set);
};

global.SetCharWillDoDrivebys = function (ped, value) {
	return _in(0x00000000, 0x2c9e0483, ped, value);
};

global.SetCharWillFlyThroughWindscreen = function (ped, value) {
	return _in(0x00000000, 0x6fc75abd, ped, value);
};

global.SetCharWillLeaveCarInCombat = function (ped, set) {
	return _in(0x00000000, 0x7cfc39cb, ped, set);
};

global.SetCharWillMoveWhenInjured = function (ped, value) {
	return _in(0x00000000, 0x1ef36397, ped, value);
};

global.SetCharWillOnlyFireWithClearLos = function (ped, set) {
	return _in(0x00000000, 0x4458184a, ped, set);
};

global.SetCharWillRemainOnBoatAfterMissionEnds = function (ped, set) {
	return _in(0x00000000, 0x5e8d08ce, ped, set);
};

global.SetCharWillTryToLeaveBoatAfterLeader = function (ped, set) {
	return _in(0x00000000, 0x62ab2ab4, ped, set);
};

global.SetCharWillTryToLeaveWater = function (ped, set) {
	return _in(0x00000000, 0x1d1b6750, ped, set);
};

global.SetCharWillUseCarsInCombat = function (ped, value) {
	return _in(0x00000000, 0x2fd83fb5, ped, value);
};

global.SetCharWillUseCover = function (ped, value) {
	return _in(0x00000000, 0x5f2f1680, ped, value);
};

global.SetCinematicButtonEnabled = function (set) {
	return _in(0x00000000, 0x0f13355a, set);
};

global.SetClearHelpInMissionCleanup = function (set) {
	return _in(0x00000000, 0x4371559f, set);
};

global.SetClearManifolds = function (set) {
	return _in(0x00000000, 0x5b7a738c, set);
};

global.SetCollectable1Total = function (total) {
	return _in(0x00000000, 0x79574b3b, total);
};

global.SetCollideWithPeds = function (set) {
	return _in(0x00000000, 0x5fdf1493, set);
};

global.SetCombatDecisionMaker = function (ped, dm) {
	return _in(0x00000000, 0x526b048c, ped, dm);
};

global.SetContentsOfTextWidget = function (Unk1115, Unk1116) {
	return _in(0x00000000, 0x6b9c6127, Unk1115, Unk1116);
};

global.SetConvertibleRoof = function (car, set) {
	return _in(0x00000000, 0x3a9a0869, car, set);
};

global.SetCreateRandomCops = function (set) {
	return _in(0x00000000, 0x5c832c1f, set);
};

global.SetCreditsToRenderBeforeFade = function (set) {
	return _in(0x00000000, 0x35fa026d, set);
};

global.SetCurrentCharWeapon = function (ped, w, unknownTrue) {
	return _in(0x00000000, 0x6cf44dd6, ped, w, unknownTrue);
};

global.SetCurrentMovie = function (filename) {
	return _in(0x00000000, 0x5af23f31, _ts(filename));
};

global.SetCutsceneExtraRoomPos = function (x, y, z) {
	return _in(0x00000000, 0x226a7227, _fv(x), _fv(y), _fv(z));
};

global.SetDanceShakeActiveThisUpdate = function (shake) {
	return _in(0x00000000, 0x1e880709, _fv(shake));
};

global.SetDanceShakeInactiveImmediately = function () {
	return _in(0x00000000, 0x2dae50c0);
};

global.SetDeadCharCoordinates = function (ped, x, y, z) {
	return _in(0x00000000, 0x68c57282, ped, _fv(x), _fv(y), _fv(z));
};

global.SetDeadPedsDropWeapons = function (set) {
	return _in(0x00000000, 0x2a5262c0, set);
};

global.SetDeathWeaponsPersist = function (ped, set) {
	return _in(0x00000000, 0x49f86791, ped, set);
};

global.SetDebugTextVisible = function (Unk1120) {
	return _in(0x00000000, 0x39d87bd6, Unk1120);
};

global.SetDecisionMakerAttributeCanChangeTarget = function (dm, value) {
	return _in(0x00000000, 0x51f54148, dm, value);
};

global.SetDecisionMakerAttributeCaution = function (dm, value) {
	return _in(0x00000000, 0x6bac2781, dm, value);
};

global.SetDecisionMakerAttributeFireRate = function (dm, value) {
	return _in(0x00000000, 0x31fc3392, dm, value);
};

global.SetDecisionMakerAttributeLowHealth = function (dm, value) {
	return _in(0x00000000, 0x2ffa6c89, dm, value);
};

global.SetDecisionMakerAttributeMovementStyle = function (dm, value) {
	return _in(0x00000000, 0x0273134e, dm, value);
};

global.SetDecisionMakerAttributeNavigationStyle = function (dm, value) {
	return _in(0x00000000, 0x26a1722c, dm, value);
};

global.SetDecisionMakerAttributeRetreatingBehaviour = function (dm, value) {
	return _in(0x00000000, 0x67890049, dm, value);
};

global.SetDecisionMakerAttributeSightRange = function (dm, value) {
	return _in(0x00000000, 0x2f444f95, dm, value);
};

global.SetDecisionMakerAttributeStandingStyle = function (dm, value) {
	return _in(0x00000000, 0x7d767108, dm, value);
};

global.SetDecisionMakerAttributeTargetInjuredReaction = function (dm, value) {
	return _in(0x00000000, 0x7cae2557, dm, value);
};

global.SetDecisionMakerAttributeTargetLossResponse = function (dm, value) {
	return _in(0x00000000, 0x65490a3d, dm, value);
};

global.SetDecisionMakerAttributeTeamwork = function (dm, value) {
	return _in(0x00000000, 0x7eae7f2f, dm, value);
};

global.SetDecisionMakerAttributeWeaponAccuracy = function (dm, value) {
	return _in(0x00000000, 0x21b8337f, dm, value);
};

global.SetDefaultGlobalInstancePriority = function () {
	return _in(0x00000000, 0x58e835e4);
};

global.SetDefaultTargetScoringFunction = function (ped, Unk132) {
	return _in(0x00000000, 0x0b164ef2, ped, Unk132);
};

global.SetDisablePlayerShoveAnimation = function (ped, disable) {
	return _in(0x00000000, 0x73f869cf, ped, disable);
};

/**
 * This native sets the app id for the discord rich presence implementation.
 * @param appId A valid Discord API App Id, can be generated at https://discordapp.com/developers/applications/
 */
global.SetDiscordAppId = function (appId) {
	return _in(0x00000000, 0x6a02254d, _ts(appId));
};

/**
 * Sets a clickable button to be displayed in a player's Discord rich presence.
 * @param index The button index, either 0 or 1.
 * @param label The text to display on the button.
 * @param url The URL to open when clicking the button. This has to start with `fivem://connect/` or `https://`.
 */
global.SetDiscordRichPresenceAction = function (index, label, url) {
	return _in(0x00000000, 0xcbbc3fac, index, _ts(label), _ts(url));
};

/**
 * This native sets the image asset for the discord rich presence implementation.
 * @param assetName The name of a valid asset registered on Discordapp's developer dashboard. note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.
 */
global.SetDiscordRichPresenceAsset = function (assetName) {
	return _in(0x00000000, 0x53dfd530, _ts(assetName));
};

/**
 * This native sets the small image asset for the discord rich presence implementation.
 * @param assetName The name of a valid asset registered on Discordapp's developer dashboard. Note that the asset has to be registered under the same discord API application set using the SET_DISCORD_APP_ID native.
 */
global.SetDiscordRichPresenceAssetSmall = function (assetName) {
	return _in(0x00000000, 0xf61d04c4, _ts(assetName));
};

/**
 * This native sets the hover text of the small image asset for the discord rich presence implementation.
 * @param text Text to be displayed when hovering over small image asset. Note that you must also set a valid small image asset using the SET_DISCORD_RICH_PRESENCE_ASSET_SMALL native.
 */
global.SetDiscordRichPresenceAssetSmallText = function (text) {
	return _in(0x00000000, 0x35e62b6a, _ts(text));
};

/**
 * This native sets the hover text of the image asset for the discord rich presence implementation.
 * @param text Text to be displayed when hovering over image asset. Note that you must also set a valid image asset using the SET_DISCORD_RICH_PRESENCE_ASSET native.
 */
global.SetDiscordRichPresenceAssetText = function (text) {
	return _in(0x00000000, 0xb029d2fa, _ts(text));
};

global.SetDisplayPlayerNameAndIcon = function (playerIndex, set) {
	return _in(0x00000000, 0x07370330, playerIndex, set);
};

global.SetDitchPoliceModels = function (set) {
	return _in(0x00000000, 0x25ac586e, set);
};

global.SetDoNotSpawnParkedCarsOnTop = function (pickup, set) {
	return _in(0x00000000, 0x7a93645c, pickup, set);
};

global.SetDontActivateRagdollFromPlayerImpact = function (ped, set) {
	return _in(0x00000000, 0x5a676bcd, ped, set);
};

global.SetDoorState = function (door, flag, Unk95) {
	return _in(0x00000000, 0x7e3d3430, door, flag, _fv(Unk95));
};

global.SetDrawPlayerComponent = function (component, set) {
	return _in(0x00000000, 0x3efe3dc8, component, set);
};

global.SetDriveTaskCruiseSpeed = function (ped, speed) {
	return _in(0x00000000, 0x499700ef, ped, _fv(speed));
};

global.SetDrunkCam = function (cam, val, time) {
	return _in(0x00000000, 0x74b90c48, cam, _fv(val), time);
};

/**
 * Navigates the specified DUI browser to a different URL.
 * @param duiObject The DUI browser handle.
 * @param url The new URL.
 */
global.SetDuiUrl = function (duiObject, url) {
	return _in(0x00000000, 0xf761d9f3, duiObject, _ts(url));
};

global.SetEnableNearClipScan = function (set) {
	return _in(0x00000000, 0x35cc3267, set);
};

global.SetEnableRcDetonate = function (set) {
	return _in(0x00000000, 0x1fc96a99, set);
};

global.SetEnableRcDetonateOnContact = function (set) {
	return _in(0x00000000, 0x7bd06e31, set);
};

global.SetEngineHealth = function (vehicle, health) {
	return _in(0x00000000, 0x3f413561, vehicle, _fv(health));
};

global.SetEveryoneIgnorePlayer = function (playerIndex, value) {
	return _in(0x00000000, 0x059901b9, playerIndex, value);
};

global.SetExtraCarColours = function (vehicle, colour1, colour2) {
	return _in(0x00000000, 0x6cb14354, vehicle, colour1, colour2);
};

global.SetExtraHospitalRestartPoint = function (x, y, z, Unk489, Unk490) {
	return _in(0x00000000, 0x4b6e368d, _fv(x), _fv(y), _fv(z), _fv(Unk489), _fv(Unk490));
};

global.SetExtraPoliceStationRestartPoint = function (x, y, z, Unk491, Unk492) {
	return _in(0x00000000, 0x1c4e7a79, _fv(x), _fv(y), _fv(z), _fv(Unk491), _fv(Unk492));
};

global.SetFadeInAfterLoad = function (set) {
	return _in(0x00000000, 0x5384065b, set);
};

global.SetFakeWantedCircle = function (x, y, radius) {
	return _in(0x00000000, 0x3cee0376, _fv(x), _fv(y), _fv(radius));
};

global.SetFakeWantedLevel = function (lvl) {
	return _in(0x00000000, 0x29d91f3d, lvl);
};

global.SetFilterMenuOn = function (toggle) {
	return _in(0x00000000, 0x18f43649, toggle);
};

global.SetFilterSaveSetting = function (filterid, setting) {
	return _in(0x00000000, 0x47f971e8, filterid, setting);
};

global.SetFixedCamPos = function (x, y, z) {
	return _in(0x00000000, 0x511a3b01, _fv(x), _fv(y), _fv(z));
};

global.SetFloatStat = function (stat, value) {
	return _in(0x00000000, 0x5213511b, stat, _fv(value));
};

global.SetFollowPedPitchLimitDown = function (pitchdownlim) {
	return _in(0x00000000, 0x31db4020, _fv(pitchdownlim));
};

global.SetFollowPedPitchLimitUp = function (pitchuplim) {
	return _in(0x00000000, 0x360e2977, _fv(pitchuplim));
};

global.SetFollowVehicleCamOffset = function (Unk574, x, y, z) {
	return _in(0x00000000, 0x56507469, Unk574, _fv(x), _fv(y), _fv(z));
};

global.SetFollowVehicleCamSubmode = function (mode) {
	return _in(0x00000000, 0x20bc708e, mode);
};

global.SetFollowVehiclePitchLimitDown = function (pitchdownlim) {
	return _in(0x00000000, 0x02f65cb2, _fv(pitchdownlim));
};

global.SetFollowVehiclePitchLimitUp = function (pitchuplim) {
	return _in(0x00000000, 0x5567728e, _fv(pitchuplim));
};

global.SetForceLookBehind = function (set) {
	return _in(0x00000000, 0x64961488, set);
};

global.SetForcePlayerToEnterThroughDirectDoor = function (ped, set) {
	return _in(0x00000000, 0x79b73666, ped, set);
};

global.SetFovChannelScript = function (set) {
	return _in(0x00000000, 0x68ab6e72, set);
};

global.SetFreeHealthCare = function (player, set) {
	return _in(0x00000000, 0x30be3463, player, set);
};

global.SetFreeResprays = function (set) {
	return _in(0x00000000, 0x00710a49, set);
};

global.SetFreebiesInVehicle = function (veh, set) {
	return _in(0x00000000, 0x25541dbe, veh, set);
};

global.SetGameCamHeading = function (heading) {
	return _in(0x00000000, 0x45fb5ce1, _fv(heading));
};

global.SetGameCamPitch = function (pitch) {
	return _in(0x00000000, 0x1bc772ac, _fv(pitch));
};

global.SetGameCameraControlsActive = function (active) {
	return _in(0x00000000, 0x57952546, active);
};

global.SetGangCar = function (car, set) {
	return _in(0x00000000, 0x3a8531e8, car, set);
};

global.SetGarageLeaveCameraAlone = function (garageName, set) {
	return _in(0x00000000, 0x5bc10979, _ts(garageName), set);
};

global.SetGfwlHasSafeHouse = function (ukn) {
	return _in(0x00000000, 0x06136b6a, ukn);
};

global.SetGfwlIsReturningToSinglePlayer = function (Unk963) {
	return _in(0x00000000, 0x755f292d, Unk963);
};

global.SetGlobalInstancePriority = function (priority) {
	return _in(0x00000000, 0x573f5b48, priority);
};

global.SetGlobalRenderFlags = function (Unk507, Unk508, Unk509, Unk510) {
	return _in(0x00000000, 0x4fe23851, Unk507, Unk508, Unk509, Unk510);
};

global.SetGpsRemainsWhenTargetReachedFlag = function (set) {
	return _in(0x00000000, 0x4c9b749f, set);
};

global.SetGpsTestIn_3dFlag = function (set) {
	return _in(0x00000000, 0x28d17798, set);
};

global.SetGpsVoiceForVehicle = function (veh, VoiceId) {
	return _in(0x00000000, 0x356876bf, veh, VoiceId);
};

global.SetGravityOff = function (set) {
	return _in(0x00000000, 0x3cda1a07, set);
};

global.SetGroupCharDecisionMaker = function (group, dm) {
	return _in(0x00000000, 0x14166075, group, dm);
};

global.SetGroupCharDucksWhenAimedAt = function (ped, value) {
	return _in(0x00000000, 0x5c8c7f9e, ped, value);
};

global.SetGroupCombatDecisionMaker = function (group, dm) {
	return _in(0x00000000, 0x58123f7a, group, dm);
};

global.SetGroupFollowStatus = function (group, status) {
	return _in(0x00000000, 0x64b9757e, group, status);
};

global.SetGroupFormation = function (group, formation) {
	return _in(0x00000000, 0x6d05484f, group, formation);
};

global.SetGroupFormationSpacing = function (group, space) {
	return _in(0x00000000, 0x69315157, group, _fv(space));
};

global.SetGroupLeader = function (group, leader) {
	return _in(0x00000000, 0x04c85e23, group, leader);
};

global.SetGroupMember = function (group, member) {
	return _in(0x00000000, 0x5e0f611e, group, member);
};

global.SetGroupSeparationRange = function (group, seperation) {
	return _in(0x00000000, 0x22dd329e, group, _fv(seperation));
};

global.SetGunshotSenseRangeForRiot2 = function (range) {
	return _in(0x00000000, 0x1a081f78, _fv(range));
};

global.SetHasBeenOwnedByPlayer = function (car, set) {
	return _in(0x00000000, 0x25750e4f, car, set);
};

global.SetHasBeenOwnedForCarGenerator = function (CarGen, set) {
	return _in(0x00000000, 0x60e335fa, CarGen, set);
};

global.SetHeadingLimitForAttachedPed = function (ped, heading0, heading1) {
	return _in(0x00000000, 0x15b07d4d, ped, _fv(heading0), _fv(heading1));
};

global.SetHeadingOfClosestObjectOfType = function (x, y, z, radius, type_or_model, heading) {
	return _in(0x00000000, 0x7abd4d4d, _fv(x), _fv(y), _fv(z), _fv(radius), type_or_model, _fv(heading));
};

global.SetHealthPickupNetworkRegenTime = function (timeMS) {
	return _in(0x00000000, 0x072516b4, timeMS);
};

global.SetHeliBladesFullSpeed = function (heli) {
	return _in(0x00000000, 0x557c3641, heli);
};

global.SetHeliForceEngineOn = function (heli, set) {
	return _in(0x00000000, 0x3b8f5e20, heli, set, _r, _ri);
};

global.SetHeliStabiliser = function (heli, set) {
	return _in(0x00000000, 0x4e653bcc, heli, set);
};

global.SetHelpMessageBoxSize = function (Unk773) {
	return _in(0x00000000, 0x4fb069ed, _fv(Unk773));
};

global.SetHelpMessageBoxSizeF = function (size) {
	return _in(0x00000000, 0x7a521650, _fv(size));
};

global.SetHideWeaponIcon = function (set) {
	return _in(0x00000000, 0x0f1b1aa1, set);
};

global.SetHintAdvancedParams = function (Unk575, Unk576, Unk577, Unk578, Unk579) {
	return _in(0x00000000, 0x2e096356, _fv(Unk575), _fv(Unk576), _fv(Unk577), _fv(Unk578), Unk579);
};

global.SetHintFov = function (fov) {
	return _in(0x00000000, 0x2f9751e2, _fv(fov));
};

global.SetHintMoveInDist = function (dist) {
	return _in(0x00000000, 0x661a0ccc, _fv(dist));
};

global.SetHintMoveInDistDefault = function () {
	return _in(0x00000000, 0x449264b6);
};

global.SetHintTimes = function (Unk580, Unk581, Unk582) {
	return _in(0x00000000, 0x4cc81fcb, _fv(Unk580), _fv(Unk581), _fv(Unk582));
};

global.SetHintTimesDefault = function () {
	return _in(0x00000000, 0x6adf2929);
};

global.SetHostMatchOn = function (Unk964) {
	return _in(0x00000000, 0x2c41421a, Unk964);
};

global.SetHotWeaponSwap = function (set) {
	return _in(0x00000000, 0x7ff260d0, set);
};

global.SetIgnoreLowPriorityShockingEvents = function (ped, value) {
	return _in(0x00000000, 0x05cc3da1, ped, value);
};

global.SetIgnoreNoGpsFlag = function (set) {
	return _in(0x00000000, 0x1fc06a1b, set);
};

global.SetIkDisabledForNetworkPlayer = function (playerIndex, Unk965) {
	return _in(0x00000000, 0x13b27ffe, playerIndex, Unk965);
};

global.SetInMpTutorial = function (set) {
	return _in(0x00000000, 0x1aeb793a, set);
};

global.SetInSpectatorMode = function (spectate) {
	return _in(0x00000000, 0x40035d5d, spectate);
};

global.SetInformRespectedFriends = function (ped, Unk43, Unk44) {
	return _in(0x00000000, 0x509f236d, ped, Unk43, Unk44);
};

global.SetInstantWidescreenBorders = function (set) {
	return _in(0x00000000, 0x728c1cc0, set);
};

global.SetIntStat = function (stat, value) {
	return _in(0x00000000, 0x1b64665b, stat, value);
};

global.SetInterpFromGameToScript = function (Unk604, Unk605) {
	return _in(0x00000000, 0x45ce21ca, Unk604, Unk605);
};

global.SetInterpFromScriptToGame = function (Unk606, Unk607) {
	return _in(0x00000000, 0x69b140f6, Unk606, Unk607);
};

global.SetInterpInOutVehicleEnabledThisFrame = function (set) {
	return _in(0x00000000, 0x120d3155, set);
};

/**
 * Toggles the visibility of resource names in the FiveM key mapping page.
 * @param hide `true` will disable the display of resource names, and `false` will enable it.
 */
global.SetKeyMappingHideResources = function (hide) {
	return _in(0x00000000, 0xcb0241b5, hide);
};

global.SetKillstreak = function () {
	return _in(0x00000000, 0x7d070604);
};

global.SetLoadCollisionForCarFlag = function (car, set) {
	return _in(0x00000000, 0x1e5c50b5, car, set);
};

global.SetLoadCollisionForCharFlag = function (ped, set) {
	return _in(0x00000000, 0x4aa762a4, ped, set);
};

global.SetLoadCollisionForObjectFlag = function (obj, set) {
	return _in(0x00000000, 0x70d13342, obj, set);
};

global.SetLobbyMuteOverride = function (set) {
	return _in(0x00000000, 0x10800fd6, set);
};

global.SetLocalPlayerPainVoice = function (name) {
	return _in(0x00000000, 0x1ddd0073, _ts(name));
};

global.SetLocalPlayerVoice = function (name) {
	return _in(0x00000000, 0x07092dc4, _ts(name));
};

global.SetLoudVehicleRadio = function (veh, set) {
	return _in(0x00000000, 0x34686b92, veh, set);
};

/**
 * Sets whether or not `SHUTDOWN_LOADING_SCREEN` automatically shuts down the NUI frame for the loading screen. If this is enabled,
 * you will have to manually invoke `SHUTDOWN_LOADING_SCREEN_NUI` whenever you want to hide the NUI loading screen.
 * @param manualShutdown TRUE to manually shut down the loading screen NUI.
 */
global.SetManualShutdownLoadingScreenNui = function (manualShutdown) {
	return _in(0x00000000, 0x1722c938, manualShutdown);
};

global.SetMask = function (Unk774, Unk775, Unk776, Unk777) {
	return _in(0x00000000, 0x0d3a3160, _fv(Unk774), _fv(Unk775), _fv(Unk776), _fv(Unk777));
};

global.SetMaxFireGenerations = function (max) {
	return _in(0x00000000, 0x03ba036b, max);
};

global.SetMaxWantedLevel = function (lvl) {
	return _in(0x00000000, 0x5d622498, lvl);
};

global.SetMenuColumn = function (menuid, Unk866, Unk867, Unk868, Unk869, Unk870, Unk871, Unk872, Unk873, Unk874, Unk875, Unk876, Unk877, Unk878, Unk879) {
	return _in(0x00000000, 0x4d317353, menuid, Unk866, Unk867, Unk868, Unk869, Unk870, Unk871, Unk872, Unk873, Unk874, Unk875, Unk876, Unk877, Unk878, Unk879);
};

global.SetMenuColumnOrientation = function (menuid, column, orientation) {
	return _in(0x00000000, 0x7cc63464, menuid, column, orientation);
};

global.SetMenuColumnWidth = function (menuid, column, width) {
	return _in(0x00000000, 0x0dbf663c, menuid, column, _fv(width));
};

global.SetMenuItemWithNumber = function (menuid, item, Unk881, gxtkey, number) {
	return _in(0x00000000, 0x32e45138, menuid, item, Unk881, _ts(gxtkey), number);
};

global.SetMenuItemWith_2Numbers = function (menuid, item, Unk880, gxtkey, number0, number1) {
	return _in(0x00000000, 0x7c4e54ed, menuid, item, Unk880, _ts(gxtkey), number0, number1);
};

global.SetMessageFormatting = function (Unk700, Unk701, Unk702) {
	return _in(0x00000000, 0x679a474e, Unk700, Unk701, Unk702);
};

global.SetMessagesWaiting = function (set) {
	return _in(0x00000000, 0x7dc061f5, set);
};

global.SetMinMaxPedAccuracy = function (ped, min, max) {
	return _in(0x00000000, 0x74627538, ped, _fv(min), _fv(max));
};

global.SetMinigameInProgress = function (set) {
	return _in(0x00000000, 0x3ed135ad, set);
};

global.SetMissionFlag = function (isMission) {
	return _in(0x00000000, 0x4fe923dc, isMission);
};

global.SetMissionPassedCash = function (add, cash, Unk511) {
	return _in(0x00000000, 0x60dc6e25, add, cash, Unk511);
};

global.SetMissionPickupSound = function (model, SoundName) {
	return _in(0x00000000, 0x3f0f4e0c, model, _ts(SoundName));
};

global.SetMissionRespectTotal = function (respect) {
	return _in(0x00000000, 0x3fa46eb8, _fv(respect));
};

global.SetMissionTrainCoordinates = function (train, x, y, z) {
	return _in(0x00000000, 0x2a3f654a, train, _fv(x), _fv(y), _fv(z));
};

global.SetMobilePhonePosition = function (x, y, z) {
	return _in(0x00000000, 0x463832f7, _fv(x), _fv(y), _fv(z));
};

global.SetMobilePhoneRadioState = function (state) {
	return _in(0x00000000, 0x52c83902, state);
};

global.SetMobilePhoneRotation = function (x, y, z) {
	return _in(0x00000000, 0x7e7e4879, _fv(x), _fv(y), _fv(z));
};

global.SetMobilePhoneScale = function (scale) {
	return _in(0x00000000, 0x61c921ef, _fv(scale));
};

global.SetMobileRadioEnabledDuringGameplay = function (set) {
	return _in(0x00000000, 0x688557e4, set);
};

global.SetMobileRingType = function (type) {
	return _in(0x00000000, 0x24885050, type);
};

global.SetMoneyCarriedByAllNewPeds = function (money) {
	return _in(0x00000000, 0x64ca2868, money);
};

global.SetMoneyCarriedByPedWithModel = function (model, m0, m1) {
	return _in(0x00000000, 0x047d3bd6, model, m0, m1);
};

global.SetMovieTime = function (time) {
	return _in(0x00000000, 0x37871a37, _fv(time));
};

global.SetMovieVolume = function (volume) {
	return _in(0x00000000, 0x32486214, _fv(volume));
};

global.SetMsgForLoadingScreen = function (label) {
	return _in(0x00000000, 0x4e4c2f92, _ts(label));
};

global.SetMultiplayerHudCash = function (cash) {
	return _in(0x00000000, 0x051742d5, cash);
};

global.SetMultiplayerHudTime = function (str) {
	return _in(0x00000000, 0x3a820d46, _ts(str));
};

global.SetNeedsToBeHotwired = function (veh, set) {
	return _in(0x00000000, 0x40a708a6, veh, set);
};

global.SetNetworkIdCanMigrate = function (netid, value) {
	return _in(0x00000000, 0x2fa5601d, netid, value);
};

global.SetNetworkIdExistsOnAllMachines = function (netID, set) {
	return _in(0x00000000, 0x4e2c764d, netID, set);
};

global.SetNetworkIdStopCloning = function (id, Unk966) {
	return _in(0x00000000, 0x086452e7, id, Unk966);
};

global.SetNetworkJoinFail = function (ukn0) {
	return _in(0x00000000, 0x5849311b, ukn0);
};

global.SetNetworkPedUsingParachute = function (ped) {
	return _in(0x00000000, 0x6e8b7611, ped);
};

global.SetNetworkPlayerAsVip = function (playerIndex, Unk967) {
	return _in(0x00000000, 0x28251e62, playerIndex, Unk967);
};

global.SetNetworkVehicleRespotTimer = function (id, ukn4000) {
	return _in(0x00000000, 0x266f327c, id, ukn4000);
};

/**
 * SET_NETWORK_WALK_MODE
 */
global.SetNetworkWalkMode = function (enabled) {
	return _in(0x00000000, 0x55188d2d, enabled);
};

global.SetNextDesiredMoveState = function (state) {
	return _in(0x00000000, 0x02033258, state);
};

global.SetNmAnimPose = function (ped, AnimName0, AnimName1, pose) {
	return _in(0x00000000, 0x50311928, ped, _ts(AnimName0), _ts(AnimName1), _fv(pose));
};

global.SetNmMessageBool = function (id, value) {
	return _in(0x00000000, 0x202f384e, id, value);
};

global.SetNmMessageFloat = function (id, value) {
	return _in(0x00000000, 0x6ce00370, id, _fv(value));
};

global.SetNmMessageInstanceIndex = function (id, ped, car, obj) {
	return _in(0x00000000, 0x48543aed, id, ped, car, obj);
};

global.SetNmMessageInt = function (id, value) {
	return _in(0x00000000, 0x49105005, id, value);
};

global.SetNmMessageString = function (id, string) {
	return _in(0x00000000, 0x3f296f78, id, _ts(string));
};

global.SetNmMessageVec3 = function (id, x, y, z) {
	return _in(0x00000000, 0x6e8f7fa4, id, _fv(x), _fv(y), _fv(z));
};

global.SetNoResprays = function (set) {
	return _in(0x00000000, 0x418d0889, set);
};

/**
 * SET_NUI_FOCUS
 */
global.SetNuiFocus = function (hasFocus, hasCursor) {
	return _in(0x00000000, 0x5b98ae30, hasFocus, hasCursor);
};

/**
 * SET_NUI_FOCUS_KEEP_INPUT
 */
global.SetNuiFocusKeepInput = function (keepInput) {
	return _in(0x00000000, 0x3ff5e5f8, keepInput);
};

/**
 * Set the z-index of the NUI resource.
 * @param zIndex New z-index value.
 */
global.SetNuiZindex = function (zIndex) {
	return _in(0x00000000, 0x3734aaff, zIndex);
};

global.SetObjectAlpha = function (obj, alpha) {
	return _in(0x00000000, 0x7f0040de, obj, alpha);
};

global.SetObjectAnimCurrentTime = function (obj, animname0, animname1, time) {
	return _in(0x00000000, 0x368274da, obj, _ts(animname0), _ts(animname1), _fv(time));
};

global.SetObjectAnimPlayingFlag = function (obj, animname0, animname1, flag) {
	return _in(0x00000000, 0x6a7236c9, obj, _ts(animname0), _ts(animname1), flag);
};

global.SetObjectAnimSpeed = function (obj, animname0, animname1, speed) {
	return _in(0x00000000, 0x168b18ed, obj, _ts(animname0), _ts(animname1), _fv(speed));
};

global.SetObjectAsStealable = function (obj, set) {
	return _in(0x00000000, 0x2dde3785, obj, set);
};

global.SetObjectCcd = function (obj, set) {
	return _in(0x00000000, 0x677861e1, obj, set);
};

global.SetObjectCollision = function (obj, value) {
	return _in(0x00000000, 0x24a40229, obj, value);
};

global.SetObjectCoordinates = function (obj, pX, pY, pZ) {
	return _in(0x00000000, 0x52fd30eb, obj, _fv(pX), _fv(pY), _fv(pZ));
};

global.SetObjectDrawLast = function (obj, set) {
	return _in(0x00000000, 0x19dd44f2, obj, set);
};

global.SetObjectDynamic = function (obj, set) {
	return _in(0x00000000, 0x2c591ccd, obj, set);
};

global.SetObjectExistsOnAllMachines = function (obj, exists) {
	return _in(0x00000000, 0x672139f0, obj, exists);
};

global.SetObjectHeading = function (obj, value) {
	return _in(0x00000000, 0x4f5d027c, obj, _fv(value));
};

global.SetObjectHealth = function (obj, health) {
	return _in(0x00000000, 0x46c41ea8, obj, _fv(health));
};

global.SetObjectInitialRotationVelocity = function (obj, x, y, z) {
	return _in(0x00000000, 0x1c7c4b89, obj, _fv(x), _fv(y), _fv(z));
};

global.SetObjectInitialVelocity = function (obj, x, y, z) {
	return _in(0x00000000, 0x41ed206b, obj, _fv(x), _fv(y), _fv(z));
};

global.SetObjectInvincible = function (obj, set) {
	return _in(0x00000000, 0x1d2f46ae, obj, set);
};

global.SetObjectLights = function (obj, lights) {
	return _in(0x00000000, 0x45d71590, obj, lights);
};

global.SetObjectOnlyDamagedByPlayer = function (obj, set) {
	return _in(0x00000000, 0x2e321155, obj, set);
};

global.SetObjectPhysicsParams = function (obj, Unk96, Unk97, v0x, v0y, v0z, v1x, v1y, v1z, flag0, flag1) {
	return _in(0x00000000, 0x1b9a44d4, obj, _fv(Unk96), _fv(Unk97), _fv(v0x), _fv(v0y), _fv(v0z), _fv(v1x), _fv(v1y), _fv(v1z), flag0, flag1);
};

global.SetObjectProofs = function (obj, unknown0, fallingDamage, unknown1, unknown2, unknown3) {
	return _in(0x00000000, 0x352865d2, obj, unknown0, fallingDamage, unknown1, unknown2, unknown3);
};

global.SetObjectQuaternion = function (obj, qx, qy, qz, qw) {
	return _in(0x00000000, 0x71270d73, obj, _fv(qx), _fv(qy), _fv(qz), _fv(qw));
};

global.SetObjectRecordsCollisions = function (obj, set) {
	return _in(0x00000000, 0x0ca93513, obj, set);
};

global.SetObjectRenderScorched = function (obj, set) {
	return _in(0x00000000, 0x1ad3394a, obj, set);
};

global.SetObjectRotation = function (obj, Pitch, Roll, Yaw) {
	return _in(0x00000000, 0x081d549c, obj, _fv(Pitch), _fv(Roll), _fv(Yaw));
};

global.SetObjectScale = function (obj, scale) {
	return _in(0x00000000, 0x145b13c7, obj, _fv(scale));
};

global.SetObjectUsedInPoolGame = function (obj, set) {
	return _in(0x00000000, 0x07b23203, obj, set);
};

global.SetObjectVisible = function (obj, value) {
	return _in(0x00000000, 0x372c7b2a, obj, value);
};

global.SetOnlineLan = function (Unk968) {
	return _in(0x00000000, 0x7e113020, Unk968);
};

global.SetOnlineScore = function (Unk1059, Unk1060) {
	return _in(0x00000000, 0x6b9c7392, Unk1059, Unk1060);
};

global.SetOnscreenCounterFlashWhenFirstDisplayed = function (counterid, flash) {
	return _in(0x00000000, 0x06f54963, counterid, flash);
};

global.SetOverrideNoSprintingOnPhoneInMultiplayer = function (Unk969) {
	return _in(0x00000000, 0x75f85826, Unk969);
};

global.SetParkedCarDensityMultiplier = function (multiplier) {
	return _in(0x00000000, 0x010c7044, _fv(multiplier));
};

global.SetPedAllowMissionOnlyDrivebyUse = function (ped, set) {
	return _in(0x00000000, 0x6e7c6687, ped, set);
};

global.SetPedAlpha = function (ped, alpha) {
	return _in(0x00000000, 0x5aa1795c, ped, alpha);
};

global.SetPedComponentsToNetworkPlayersettingsModel = function (ped) {
	return _in(0x00000000, 0x5c3053c0, ped);
};

global.SetPedDensityMultiplier = function (density) {
	return _in(0x00000000, 0x540f2df7, _fv(density));
};

global.SetPedDiesWhenInjured = function (ped, value) {
	return _in(0x00000000, 0x3bf93ed7, ped, value);
};

global.SetPedDontDoEvasiveDives = function (ped, value) {
	return _in(0x00000000, 0x1ead1d7d, ped, value);
};

global.SetPedDontUseVehicleSpecificAnims = function (ped, set) {
	return _in(0x00000000, 0x0b6e6107, ped, set);
};

global.SetPedEnableLegIk = function (ped, set) {
	return _in(0x00000000, 0x695c429d, ped, set);
};

global.SetPedExistsOnAllMachines = function (ped, exists) {
	return _in(0x00000000, 0x79700852, ped, exists);
};

global.SetPedFallOffBikesWhenShot = function (ped, set) {
	return _in(0x00000000, 0x78e00c86, ped, set);
};

global.SetPedFireFxLodScaler = function (scale) {
	return _in(0x00000000, 0x679c4276, _fv(scale));
};

global.SetPedForceFlyThroughWindscreen = function (ped, set) {
	return _in(0x00000000, 0x6e354b41, ped, set);
};

global.SetPedForceVisualiseHeadDamageFromBullets = function (ped, set) {
	return _in(0x00000000, 0x2ba92322, ped, set);
};

global.SetPedGeneratesDeadBodyEvents = function (ped, set) {
	return _in(0x00000000, 0x3dbf53e0, ped, set);
};

global.SetPedHeedsTheEveryoneIgnorePlayerFlag = function (ped, set) {
	return _in(0x00000000, 0x3bbe6dbe, ped, set);
};

global.SetPedHeliPilotRespectsMinimummHeight = function (ped, set) {
	return _in(0x00000000, 0x20bb5507, ped, set);
};

global.SetPedHelmetTextureIndex = function (ped, index) {
	return _in(0x00000000, 0x6ac14091, ped, index);
};

global.SetPedInstantBlendsWeaponAnims = function (ped, set) {
	return _in(0x00000000, 0x2cb572b5, ped, set);
};

global.SetPedIsBlindRaging = function (ped, value) {
	return _in(0x00000000, 0x05d800a4, ped, value);
};

global.SetPedIsDrunk = function (ped, value) {
	return _in(0x00000000, 0x67cc007c, ped, value);
};

global.SetPedMobileRingType = function (ped, RingtoneId) {
	return _in(0x00000000, 0x7e1c01d7, ped, RingtoneId);
};

global.SetPedMotionBlur = function (ped, set) {
	return _in(0x00000000, 0x73e6005b, ped, set);
};

global.SetPedNonCreationArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x3dab7d72, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SetPedNonRemovalArea = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x52d34ed3, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SetPedPathMayDropFromHeight = function (ped, value) {
	return _in(0x00000000, 0x4f37648c, ped, value);
};

global.SetPedPathMayUseClimbovers = function (ped, value) {
	return _in(0x00000000, 0x34bd72d7, ped, value);
};

global.SetPedPathMayUseLadders = function (ped, value) {
	return _in(0x00000000, 0x6b2838c7, ped, value);
};

global.SetPedPathWillAvoidDynamicObjects = function (ped, set) {
	return _in(0x00000000, 0x1e901bb6, ped, set);
};

global.SetPedSkipsComplexCoverCollisionChecks = function (ped, set) {
	return _in(0x00000000, 0x2cd33526, ped, set);
};

global.SetPedSteersAroundObjects = function (ped, set) {
	return _in(0x00000000, 0x7d071ee0, ped, set);
};

global.SetPedSteersAroundPeds = function (ped, set) {
	return _in(0x00000000, 0x57a236f0, ped, set);
};

global.SetPedWindyClothingScale = function (ped, scale) {
	return _in(0x00000000, 0x12865550, ped, _fv(scale));
};

global.SetPedWithBrainCanBeConvertedToDummyPed = function (ped, set) {
	return _in(0x00000000, 0x1461418c, ped, set);
};

global.SetPedWontAttackPlayerWithoutWantedLevel = function (ped, set) {
	return _in(0x00000000, 0x3bf06336, ped, set);
};

global.SetPetrolTankHealth = function (vehicle, value) {
	return _in(0x00000000, 0x17e2319c, vehicle, _fv(value));
};

global.SetPetrolTankWeakpoint = function (car, set) {
	return _in(0x00000000, 0x667517ab, car, set);
};

global.SetPhoneHudItem = function (id, gxttext, Unk800) {
	return _in(0x00000000, 0x43a13718, id, _ts(gxttext), Unk800);
};

global.SetPhysCcdHandlesRotation = function (set) {
	return _in(0x00000000, 0x0c7b7cf4, set);
};

global.SetPickupCollectableByCar = function (pickup, set) {
	return _in(0x00000000, 0x6da91393, pickup, set);
};

global.SetPickupsFixCars = function (set) {
	return _in(0x00000000, 0x59dc6b9a, set);
};

global.SetPlaneThrottle = function (plane, throttle) {
	return _in(0x00000000, 0x05b2442a, plane, _fv(throttle));
};

global.SetPlaneUndercarriageUp = function (plain, set) {
	return _in(0x00000000, 0x7953702c, plain, set);
};

global.SetPlaybackSpeed = function (car, speed) {
	return _in(0x00000000, 0x0eaf6a68, car, _fv(speed));
};

global.SetPlayerAsCop = function (player, set) {
	return _in(0x00000000, 0x1d161bb8, player, set);
};

global.SetPlayerAsDamagedPlayer = function (playerIndex, Unk1057, Unk1058) {
	return _in(0x00000000, 0x633a012b, playerIndex, Unk1057, Unk1058);
};

global.SetPlayerCanBeHassledByGangs = function (playerIndex, value) {
	return _in(0x00000000, 0x09c5648c, playerIndex, value);
};

global.SetPlayerCanDoDriveBy = function (playerIndex, value) {
	return _in(0x00000000, 0x561471fb, playerIndex, value);
};

global.SetPlayerCanDropWeaponsInCar = function (set) {
	return _in(0x00000000, 0x4f884e33, set);
};

global.SetPlayerCanUseCover = function (playerIndex, value) {
	return _in(0x00000000, 0x4ac023c4, playerIndex, value);
};

global.SetPlayerControl = function (playerIndex, value) {
	return _in(0x00000000, 0x1a6203ea, playerIndex, value);
};

global.SetPlayerControlAdvanced = function (playerIndex, unknown1, unknown2, unknown3) {
	return _in(0x00000000, 0x31e25160, playerIndex, unknown1, unknown2, unknown3);
};

global.SetPlayerControlForAmbientScript = function (player, flag0, flag1) {
	return _in(0x00000000, 0x647e2bf7, player, flag0, flag1);
};

global.SetPlayerControlForNetwork = function (playerIndex, unknownTrue, unknownFalse) {
	return _in(0x00000000, 0x2af07cc8, playerIndex, unknownTrue, unknownFalse);
};

global.SetPlayerControlForTextChat = function (player, set) {
	return _in(0x00000000, 0x13267663, player, set);
};

global.SetPlayerControlOnInMissionCleanup = function (set) {
	return _in(0x00000000, 0x06f271b2, set);
};

global.SetPlayerDisableCrouch = function (player, set) {
	return _in(0x00000000, 0x3bb57426, player, set);
};

global.SetPlayerDisableJump = function (player, set) {
	return _in(0x00000000, 0x4b5832be, player, set);
};

global.SetPlayerFastReload = function (playerIndex, value) {
	return _in(0x00000000, 0x29b53dff, playerIndex, value);
};

global.SetPlayerForcedAim = function (player, set) {
	return _in(0x00000000, 0x7e603872, player, set);
};

global.SetPlayerGroupRecruitment = function (player, set) {
	return _in(0x00000000, 0x7a9b6e17, player, set);
};

global.SetPlayerGroupToFollowAlways = function (playerIndex, value) {
	return _in(0x00000000, 0x700165c2, playerIndex, value);
};

global.SetPlayerGroupToFollowNever = function (player, set) {
	return _in(0x00000000, 0x4f29072e, player, set);
};

global.SetPlayerIconColour = function (colour) {
	return _in(0x00000000, 0x689d5eee, colour);
};

global.SetPlayerInvincible = function (playerIndex, value) {
	return _in(0x00000000, 0x7e9e02e1, playerIndex, value);
};

global.SetPlayerInvisibleToAi = function (set) {
	return _in(0x00000000, 0x68083431, set);
};

global.SetPlayerIsInStadium = function (set) {
	return _in(0x00000000, 0x349d5c27, set);
};

global.SetPlayerKeepsWeaponsWhenRespawned = function (set) {
	return _in(0x00000000, 0x6c321179, set);
};

global.SetPlayerMayOnlyEnterThisVehicle = function (player, veh) {
	return _in(0x00000000, 0x6bc05942, player, veh);
};

global.SetPlayerMoodNormal = function (playerIndex) {
	return _in(0x00000000, 0x546f5326, playerIndex);
};

global.SetPlayerMoodPissedOff = function (playerIndex, unknown150) {
	return _in(0x00000000, 0x5e061170, playerIndex, unknown150);
};

global.SetPlayerMpModifier = function (player, Unk12, modifier) {
	return _in(0x00000000, 0x2b111e69, player, Unk12, _fv(modifier));
};

global.SetPlayerNeverGetsTired = function (playerIndex, value) {
	return _in(0x00000000, 0x0ddc19f4, playerIndex, value);
};

global.SetPlayerPainRootBankName = function (name) {
	return _in(0x00000000, 0x70af1d38, _ts(name));
};

global.SetPlayerPlayerTargetting = function (set) {
	return _in(0x00000000, 0x46920944, set);
};

global.SetPlayerSettingsGenre = function (ped) {
	return _in(0x00000000, 0x379b0a8f, ped);
};

/**
 * the status of default voip system. It affects on `NETWORK_IS_PLAYER_TALKING` and `mp_facial` animation.
 * This function doesn't need to be called every frame, it works like a switcher.
 * @param player The target player.
 * @param state Overriding state.
 */
global.SetPlayerTalkingOverride = function (player, state) {
	return _in(0x00000000, 0xfc02caf6, player, state);
};

global.SetPlayerTeam = function (Player, team) {
	return _in(0x00000000, 0x3e733990, Player, team);
};

global.SetPlayersDropMoneyInNetworkGame = function (toggle) {
	return _in(0x00000000, 0x01651fba, toggle);
};

global.SetPlayersettingsModelVariationsChoice = function (playerIndex) {
	return _in(0x00000000, 0x27650f37, playerIndex);
};

global.SetPoliceFocusWillTrackCar = function (car, set) {
	return _in(0x00000000, 0x0d374615, car, set);
};

global.SetPoliceIgnorePlayer = function (playerIndex, value) {
	return _in(0x00000000, 0x619d51d3, playerIndex, value);
};

global.SetPoliceRadarBlips = function (set) {
	return _in(0x00000000, 0x14790f9f, set);
};

global.SetPtfxCamInsideVehicle = function (set) {
	return _in(0x00000000, 0x137e6800, set);
};

global.SetRadarAsInteriorThisFrame = function () {
	return _in(0x00000000, 0x5c3f7e39);
};

global.SetRadarScale = function (scale) {
	return _in(0x00000000, 0x75ed39cf, _fv(scale));
};

global.SetRadarZoom = function (zoom) {
	return _in(0x00000000, 0x35e37826, _fv(zoom));
};

global.SetRailtrackResistanceMult = function (resistance) {
	return _in(0x00000000, 0x3d7b10e7, _fv(resistance));
};

global.SetRandomCarDensityMultiplier = function (density) {
	return _in(0x00000000, 0x073505e0, _fv(density));
};

global.SetRandomSeed = function (seed) {
	return _in(0x00000000, 0x1ba8350b, seed);
};

global.SetRecordingToPointNearestToCoors = function (cat, x, y, z) {
	return _in(0x00000000, 0x7b732460, cat, _fv(x), _fv(y), _fv(z));
};

global.SetReducePedModelBudget = function (set) {
	return _in(0x00000000, 0x44474526, set);
};

global.SetReduceVehicleModelBudget = function (set) {
	return _in(0x00000000, 0x71f965b4, set);
};

global.SetRelationship = function (relationshipLevel, relationshipGroup1, relationshipGroup2) {
	return _in(0x00000000, 0x03d916e4, relationshipLevel, relationshipGroup1, relationshipGroup2);
};

global.SetRenderTrainAsDerailed = function (train, set) {
	return _in(0x00000000, 0x08240fb7, train, set);
};

/**
 * A setter for [GET_RESOURCE_KVP_STRING](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvp = function (key, value) {
	return _in(0x00000000, 0x21c7a35b, _ts(key), _ts(value));
};

/**
 * A setter for [GET_RESOURCE_KVP_FLOAT](#\_0x35BDCEEA).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvpFloat = function (key, value) {
	return _in(0x00000000, 0x9add2938, _ts(key), _fv(value));
};

/**
 * Nonsynchronous [SET_RESOURCE_KVP_FLOAT](#\_0x9ADD2938) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvpFloatNoSync = function (key, value) {
	return _in(0x00000000, 0x3517bfbe, _ts(key), _fv(value));
};

/**
 * A setter for [GET_RESOURCE_KVP_INT](#\_0x557B586A).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvpInt = function (key, value) {
	return _in(0x00000000, 0x06a2b1e8, _ts(key), value);
};

/**
 * Nonsynchronous [SET_RESOURCE_KVP_INT](#\_0x6A2B1E8) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvpIntNoSync = function (key, value) {
	return _in(0x00000000, 0x26aeb707, _ts(key), value);
};

/**
 * Nonsynchronous [SET_RESOURCE_KVP](#\_0x21C7A35B) operation; see [FLUSH_RESOURCE_KVP](#\_0x5240DA5A).
 * @param key The key to set
 * @param value The value to write
 */
global.SetResourceKvpNoSync = function (key, value) {
	return _in(0x00000000, 0x0cf9a2ff, _ts(key), _ts(value));
};

global.SetReturnToFilterMenu = function (Unk970) {
	return _in(0x00000000, 0x733846d5, Unk970);
};

global.SetRichPresence = function (Unk971, Unk972, Unk973, Unk974, Unk975) {
	return _in(0x00000000, 0x73ab2028, Unk971, Unk972, Unk973, Unk974, Unk975);
};

/**
 * Sets the player's rich presence detail state for social platform providers to a specified string.
 * @param presenceState The rich presence string to set.
 */
global.SetRichPresence = function (presenceState) {
	return _in(0x00000000, 0x7bdcbd45, _ts(presenceState));
};

global.SetRichPresenceTemplatefilter = function () {
	return _in(0x00000000, 0x6b434d0d);
};

global.SetRichPresenceTemplatelobby = function (Unk976) {
	return _in(0x00000000, 0x77d72045, Unk976);
};

global.SetRichPresenceTemplatemp1 = function (Unk977, Unk978, Unk979, Unk980) {
	return _in(0x00000000, 0x6c236a54, Unk977, Unk978, Unk979, Unk980);
};

global.SetRichPresenceTemplatemp2 = function (Unk981) {
	return _in(0x00000000, 0x5afa67d7, Unk981);
};

global.SetRichPresenceTemplatemp3 = function (Unk982, Unk983) {
	return _in(0x00000000, 0x612062db, Unk982, Unk983);
};

global.SetRichPresenceTemplatemp4 = function (Unk984, Unk985) {
	return _in(0x00000000, 0x2bf8368e, Unk984, Unk985);
};

global.SetRichPresenceTemplatemp5 = function (Unk986, Unk987, Unk988) {
	return _in(0x00000000, 0x314f6dd3, Unk986, Unk987, Unk988);
};

global.SetRichPresenceTemplatemp6 = function (Unk989, Unk990, Unk991) {
	return _in(0x00000000, 0x05d70fe8, Unk989, Unk990, Unk991);
};

global.SetRichPresenceTemplateparty = function () {
	return _in(0x00000000, 0x422055c7);
};

global.SetRichPresenceTemplatesp1 = function (Unk992, Unk993, Unk994) {
	return _in(0x00000000, 0x00132487, Unk992, Unk993, Unk994);
};

global.SetRichPresenceTemplatesp2 = function (Unk995) {
	return _in(0x00000000, 0x09766174, Unk995);
};

global.SetRocketLauncherFreebieInHeli = function (set) {
	return _in(0x00000000, 0x77a97169, set);
};

global.SetRomansMood = function (moood) {
	return _in(0x00000000, 0x126f1175, moood);
};

global.SetRoomForCarByKey = function (car, roomkey) {
	return _in(0x00000000, 0x1e106a88, car, roomkey);
};

global.SetRoomForCarByName = function (car, roomname) {
	return _in(0x00000000, 0x2667609a, car, _ts(roomname));
};

global.SetRoomForCharByKey = function (ped, key) {
	return _in(0x00000000, 0x620c26d8, ped, key);
};

global.SetRoomForCharByName = function (ped, roomname) {
	return _in(0x00000000, 0x2e9b1f77, ped, _ts(roomname));
};

global.SetRoomForViewportByKey = function (viewportid, roomkey) {
	return _in(0x00000000, 0x07ee2a45, viewportid, roomkey);
};

global.SetRoomForViewportByName = function (viewportid, roomname) {
	return _in(0x00000000, 0x3daf3f94, viewportid, _ts(roomname));
};

global.SetRotOrder = function (order) {
	return _in(0x00000000, 0x662e4376, order);
};

global.SetRotationForAttachedPed = function (ped, xr, yr, zr) {
	return _in(0x00000000, 0x1fe21cf0, ped, _fv(xr), _fv(yr), _fv(zr));
};

global.SetRoute = function (blip, value) {
	return _in(0x00000000, 0x7b8d68e7, blip, value);
};

global.SetScenarioPedDensityMultiplier = function (density, densitynext) {
	return _in(0x00000000, 0x3f0022f7, _fv(density), _fv(densitynext));
};

global.SetScreenFade = function (viewportid, Unk778, Unk779, Unk780, r, g, b, a, Unk781, Unk782, Unk783) {
	return _in(0x00000000, 0x188e0fac, viewportid, Unk778, Unk779, Unk780, r, g, b, a, Unk781, _fv(Unk782), _fv(Unk783));
};

global.SetScriptLimitToGangSize = function (size) {
	return _in(0x00000000, 0x352921c4, size);
};

global.SetScriptMicLookAt = function (x, y, z) {
	return _in(0x00000000, 0x4dd43ffd, _fv(x), _fv(y), _fv(z));
};

global.SetScriptMicPosition = function (x, y, z) {
	return _in(0x00000000, 0x295d3a87, _fv(x), _fv(y), _fv(z));
};

global.SetScriptedAnimSeatOffset = function (ped, offset) {
	return _in(0x00000000, 0x718939ef, ped, _fv(offset));
};

global.SetScriptedConversionCentre = function (x, y, z) {
	return _in(0x00000000, 0x40f61d4a, _fv(x), _fv(y), _fv(z));
};

global.SetSelectedMenuItem = function (menuid, item) {
	return _in(0x00000000, 0x70291096, menuid, item);
};

global.SetSenseRange = function (ped, value) {
	return _in(0x00000000, 0x44d56f66, ped, _fv(value));
};

global.SetSequenceToRepeat = function (seq, repeat) {
	return _in(0x00000000, 0x22e91f1f, seq, repeat);
};

global.SetServerId = function (id) {
	return _in(0x00000000, 0x575136ac, id);
};

global.SetSirenWithNoDriver = function (car, set) {
	return _in(0x00000000, 0x47fd2517, car, set);
};

global.SetSleepModeActive = function (set) {
	return _in(0x00000000, 0x1c5552e9, set);
};

/**
 * SET_SNAKEOIL_FOR_ENTRY
 */
global.SetSnakeoilForEntry = function (name, path, data) {
	return _in(0x00000000, 0xa7dd3209, _ts(name), _ts(path), _ts(data));
};

global.SetSniperZoomFactor = function (factor) {
	return _in(0x00000000, 0x42690f6b, _fv(factor));
};

global.SetSpecificPassengerIndexToUseInGroups = function (ped, index) {
	return _in(0x00000000, 0x0ea118d0, ped, index);
};

global.SetSpritesDrawBeforeFade = function (set) {
	return _in(0x00000000, 0x615959ba, set);
};

global.SetStartFromFilterMenu = function (Unk996) {
	return _in(0x00000000, 0x3f6b5975, Unk996);
};

global.SetStatFrontendAlwaysVisible = function (set) {
	return _in(0x00000000, 0x656f1a7a, set);
};

global.SetStatFrontendDisplayType = function (stat, type) {
	return _in(0x00000000, 0x10436a86, stat, type);
};

global.SetStatFrontendNeverVisible = function (stat) {
	return _in(0x00000000, 0x3a6b0308, stat);
};

global.SetStatFrontendVisibility = function (stat, set) {
	return _in(0x00000000, 0x45d23711, stat, set);
};

global.SetStatFrontendVisibleAfterIncremented = function (stat) {
	return _in(0x00000000, 0x12d67ada, stat);
};

/**
 * Internal function for setting a state bag value.
 */
global.SetStateBagValue = function (bagName, keyName, valueData, valueLength, replicated) {
	return _in(0x00000000, 0x8d50e33a, _ts(bagName), _ts(keyName), _ts(valueData), valueLength, replicated);
};

global.SetStateOfClosestDoorOfType = function (model, x, y, z, state, Unk601) {
	return _in(0x00000000, 0x10974b70, model, _fv(x), _fv(y), _fv(z), state, _fv(Unk601));
};

global.SetStreamParams = function (rolloff, UnkTime) {
	return _in(0x00000000, 0x16cb4f86, _fv(rolloff), UnkTime);
};

global.SetStreamingRequestListTime = function (time) {
	return _in(0x00000000, 0x01ff6618, time);
};

global.SetSuppressHeadlightSwitch = function (set) {
	return _in(0x00000000, 0x43ef56ee, set);
};

global.SetSwimSpeed = function (ped, speed) {
	return _in(0x00000000, 0x32b4293b, ped, _fv(speed));
};

global.SetSyncWeatherAndGameTime = function (Unk997) {
	return _in(0x00000000, 0x51112e95, Unk997);
};

global.SetTargetCarForMissionGarage = function (garage, car) {
	return _in(0x00000000, 0x6ef667a4, garage, car);
};

global.SetTaxiGarageRadioState = function (radiostate) {
	return _in(0x00000000, 0x299c5ebc, radiostate);
};

global.SetTaxiLights = function (car, set) {
	return _in(0x00000000, 0x460837f9, car, set);
};

global.SetTelescopeCamAngleLimits = function (Unk583, Unk584, Unk585, Unk586, Unk587, Unk588) {
	return _in(0x00000000, 0x6680196b, _fv(Unk583), _fv(Unk584), _fv(Unk585), _fv(Unk586), _fv(Unk587), _fv(Unk588));
};

global.SetTextBackground = function (value) {
	return _in(0x00000000, 0x768f5140, value);
};

global.SetTextCentre = function (value) {
	return _in(0x00000000, 0x204a6aa4, value);
};

global.SetTextCentreWrapx = function (wrapx) {
	return _in(0x00000000, 0x716308c6, _fv(wrapx));
};

/**
 * SET_TEXT_CHAT_ENABLED
 */
global.SetTextChatEnabled = function (enabled) {
	return _in(0x00000000, 0x97b2f9f8, enabled, _r);
};

global.SetTextColour = function (r, g, b, a) {
	return _in(0x00000000, 0x19c967b5, r, g, b, a);
};

global.SetTextDrawBeforeFade = function (value) {
	return _in(0x00000000, 0x6cfd0610, value);
};

global.SetTextDropshadow = function (displayShadow, r, g, b, a) {
	return _in(0x00000000, 0x58f5023f, displayShadow, r, g, b, a);
};

global.SetTextEdge = function (displayEdge, r, g, b, a) {
	return _in(0x00000000, 0x2d7a725d, displayEdge, r, g, b, a);
};

global.SetTextFont = function (font) {
	return _in(0x00000000, 0x75363bb5, font);
};

global.SetTextInputActive = function (set) {
	return _in(0x00000000, 0x2a28684c, set);
};

global.SetTextJustify = function (value) {
	return _in(0x00000000, 0x049d23f9, value);
};

global.SetTextLineDisplay = function (unk1, unk2) {
	return _in(0x00000000, 0x1f6a54b6, unk1, unk2);
};

global.SetTextLineHeightMult = function (lineHeight) {
	return _in(0x00000000, 0x5bf53817, _fv(lineHeight));
};

global.SetTextProportional = function (value) {
	return _in(0x00000000, 0x15585a65, value);
};

global.SetTextRenderId = function (renderId) {
	return _in(0x00000000, 0x2b1b0290, renderId);
};

global.SetTextRightJustify = function (value) {
	return _in(0x00000000, 0x748b78b6, value);
};

global.SetTextScale = function (w, h) {
	return _in(0x00000000, 0x02c069e5, _fv(w), _fv(h));
};

global.SetTextToUseTextFileColours = function (value) {
	return _in(0x00000000, 0x52ce650b, value);
};

global.SetTextUseUnderscore = function (value) {
	return _in(0x00000000, 0x0ad54d75, value);
};

global.SetTextViewportId = function (id) {
	return _in(0x00000000, 0x3f9b2dd6, id);
};

global.SetTextWrap = function (unk1, unk2) {
	return _in(0x00000000, 0x19d006eb, _fv(unk1), _fv(unk2));
};

global.SetThisMachineRunningServerScript = function (host) {
	return _in(0x00000000, 0x382a19be, host);
};

global.SetThisScriptCanRemoveBlipsCreatedByAnyScript = function (allow) {
	return _in(0x00000000, 0x29d64e72, allow);
};

global.SetTimeCycleFarClipDisabled = function (set) {
	return _in(0x00000000, 0x13c75e16, set);
};

global.SetTimeOfDay = function (hour, minute) {
	return _in(0x00000000, 0x52100540, hour, minute);
};

global.SetTimeOfNextAppointment = function (time) {
	return _in(0x00000000, 0x0a7d3af9, time);
};

global.SetTimeOneDayBack = function () {
	return _in(0x00000000, 0x18136217);
};

global.SetTimeOneDayForward = function () {
	return _in(0x00000000, 0x79cf27ac);
};

global.SetTimeScale = function (scale) {
	return _in(0x00000000, 0x24d467cc, _fv(scale));
};

global.SetTimecycleModifier = function (name) {
	return _in(0x00000000, 0x3c997e4c, _ts(name));
};

/**
 * SET_TIMECYCLE_MODIFIER_VAR
 * @param modifierName The name of timecycle modifier.
 * @param varName The name of timecycle variable.
 * @param value1 The first value of variable.
 * @param value2 The second value of variable.
 */
global.SetTimecycleModifierVar = function (modifierName, varName, value1, value2) {
	return _in(0x00000000, 0x6e0a422b, _ts(modifierName), _ts(varName), _fv(value1), _fv(value2));
};

global.SetTimerBeepCountdownTime = function (timerid, beeptime) {
	return _in(0x00000000, 0x66b93e8c, timerid, beeptime);
};

global.SetTotalNumberOfMissions = function (floatstatval) {
	return _in(0x00000000, 0x09de74e5, _fv(floatstatval));
};

global.SetTrainAudioRolloff = function (train, rolloff) {
	return _in(0x00000000, 0x01c21158, train, _fv(rolloff));
};

global.SetTrainCruiseSpeed = function (train, speed) {
	return _in(0x00000000, 0x02e93a3e, train, _fv(speed));
};

global.SetTrainForcedToSlowDown = function (train, set) {
	return _in(0x00000000, 0x475267b0, train, set);
};

global.SetTrainIsStoppedAtStation = function (train) {
	return _in(0x00000000, 0x270c7ab3, train);
};

global.SetTrainSpeed = function (train, speed) {
	return _in(0x00000000, 0x3f4950ac, train, _fv(speed));
};

global.SetTrainStopsForStations = function (train, set) {
	return _in(0x00000000, 0x5d154995, train, set);
};

global.SetUpsidedownCarNotDamaged = function (car, set) {
	return _in(0x00000000, 0x353317c7, car, set);
};

global.SetUseHighdof = function (set) {
	return _in(0x00000000, 0x4a1d15d5, set);
};

global.SetUseLegIk = function (player, set) {
	return _in(0x00000000, 0x4f705478, player, set);
};

global.SetUsePoolGamePhysicsSettings = function (set) {
	return _in(0x00000000, 0x5c162d0d, set);
};

global.SetUsesCollisionOfClosestObjectOfType = function (x, y, z, radius, type_or_model, flag) {
	return _in(0x00000000, 0x07bc4223, _fv(x), _fv(y), _fv(z), _fv(radius), type_or_model, flag);
};

global.SetVariableOnSound = function (sound, varname, value) {
	return _in(0x00000000, 0x39200b83, sound, _ts(varname), _fv(value));
};

global.SetVehAlarm = function (veh, set) {
	return _in(0x00000000, 0x0cf76ee0, veh, set);
};

global.SetVehAlarmDuration = function (veh, duration) {
	return _in(0x00000000, 0x5ffe33ec, veh, duration);
};

global.SetVehHasStrongAxles = function (veh, set) {
	return _in(0x00000000, 0x63de7a05, veh, set);
};

global.SetVehHazardlights = function (vehicle, on) {
	return _in(0x00000000, 0x24b42ed2, vehicle, on);
};

global.SetVehIndicatorlights = function (veh, set) {
	return _in(0x00000000, 0x71d72486, veh, set);
};

global.SetVehInteriorlight = function (veh, set) {
	return _in(0x00000000, 0x49ea22c8, veh, set);
};

global.SetVehicleAlpha = function (veh, alpha) {
	return _in(0x00000000, 0x0c4b7dd3, veh, alpha);
};

global.SetVehicleAlwaysRender = function (veh) {
	return _in(0x00000000, 0x4a4b0f18, veh);
};

global.SetVehicleCanBeTargetted = function (veh, set) {
	return _in(0x00000000, 0x2b9b35c3, veh, set);
};

/**
 * SET_VEHICLE_CURRENT_GEAR
 * @param vehicle The vehicle handle.
 * @param gear The gear you want the vehicle to use.
 */
global.SetVehicleCurrentGear = function (vehicle, gear) {
	return _in(0x00000000, 0x8923dd42, vehicle, gear);
};

global.SetVehicleDeformationMult = function (veh, multiplier) {
	return _in(0x00000000, 0x7b65266b, veh, _fv(multiplier));
};

global.SetVehicleDirtLevel = function (vehicle, intensity) {
	return _in(0x00000000, 0x02a57428, vehicle, _fv(intensity));
};

global.SetVehicleExplodesOnHighExplosionDamage = function (veh, set) {
	return _in(0x00000000, 0x7b4a7cd6, veh, set);
};

/**
 * This native is a setter for [`GET_VEHICLE_HAS_FLAG`](#\_0xD85C9F57).
 * @param vehicle The vehicle to set flag for.
 * @param flagIndex Flag index.
 * @param value `true` to enable the flag, `false` to disable it.
 */
global.SetVehicleFlag = function (vehicle, flagIndex, value) {
	return _in(0x00000000, 0x63ae1a34, vehicle, flagIndex, value, _r);
};

global.SetVehicleIsConsideredByPlayer = function (veh, set) {
	return _in(0x00000000, 0x720673d9, veh, set);
};

/**
 * SET_VEHICLE_NEXT_GEAR
 * @param vehicle The vehicle handle.
 * @param nextGear The vehicles next gear.
 */
global.SetVehicleNextGear = function (vehicle, nextGear) {
	return _in(0x00000000, 0x3a4566f4, vehicle, nextGear);
};

global.SetVehicleQuaternion = function (veh, qx, qy, qz, qw) {
	return _in(0x00000000, 0x43573596, veh, _fv(qx), _fv(qy), _fv(qz), _fv(qw));
};

global.SetVehicleRenderScorched = function (veh, set) {
	return _in(0x00000000, 0x07205796, veh, set);
};

global.SetVehicleSteerBias = function (veh, val) {
	return _in(0x00000000, 0x091d1480, veh, _fv(val));
};

global.SetViewport = function (viewportid, Unk589, Unk590, Unk591, Unk592) {
	return _in(0x00000000, 0x0ee87310, viewportid, _fv(Unk589), _fv(Unk590), _fv(Unk591), _fv(Unk592));
};

global.SetViewportDestination = function (viewportid, x, y, z, Unk593, Unk594, Unk595) {
	return _in(0x00000000, 0x1c810358, viewportid, _fv(x), _fv(y), _fv(z), _fv(Unk593), Unk594, Unk595);
};

global.SetViewportMirrored = function (viewportid, set) {
	return _in(0x00000000, 0x61784349, viewportid, set);
};

global.SetViewportPriority = function (viewportid, priority) {
	return _in(0x00000000, 0x5da1752f, viewportid, priority);
};

global.SetViewportShape = function (cam, shape) {
	return _in(0x00000000, 0x43ed66e3, cam, shape);
};

global.SetVisibilityOfClosestObjectOfType = function (x, y, z, radius, type_or_model, set) {
	return _in(0x00000000, 0x20a04bee, _fv(x), _fv(y), _fv(z), _fv(radius), type_or_model, set);
};

global.SetVisibilityOfNearbyEntityWithSpecialAttribute = function (attribute, set) {
	return _in(0x00000000, 0x6ddd201d, attribute, set);
};

/**
 * Overrides a floating point value from `visualsettings.dat` temporarily.
 * @param name The name of the value to set, such as `pedLight.color.red`.
 * @param value The value to write.
 */
global.SetVisualSettingFloat = function (name, value) {
	return _in(0x00000000, 0xd1d31681, _ts(name), _fv(value));
};

global.SetVoiceIdFromHeadComponent = function (ped, VoiceId, IsMale) {
	return _in(0x00000000, 0x02794e6b, ped, VoiceId, IsMale);
};

global.SetWantedMultiplier = function (multiplier) {
	return _in(0x00000000, 0x51e14c1b, _fv(multiplier));
};

global.SetWeaponPickupNetworkRegenTime = function (weaponType, timeMS) {
	return _in(0x00000000, 0x40d01439, weaponType, timeMS);
};

/**
 * SET_WEATHER_CYCLE_ENTRY
 * @param index The index of the entry to set. Must be between 0 and 255
 * @param typeName The name of the weather type for this cycle
 * @param timeMult The relative duration of this cycle, which is multiplied by `msPerCycle` during ['APPLY_WEATHER_CYCLES'](#\_0x3422291C). Must be between 1 and 255
 * @return Returns true if all parameters were valid, otherwise false.
 */
global.SetWeatherCycleEntry = function (index, typeName, timeMult) {
	return _in(0x00000000, 0xd264d4e1, index, _ts(typeName), timeMult, _r);
};

/**
 * Sets whether or not the weather should be owned by the network subsystem.
 * To be able to use [\_SET_WEATHER_TYPE_TRANSITION](#\_0x578C752848ECFA0C), this has to be set to false.
 * @param network true to let the network control weather, false to not use network weather behavior.
 */
global.SetWeatherOwnedByNetwork = function (network) {
	return _in(0x00000000, 0x2703d582, network);
};

global.SetWebPageLinkActive = function (htmlviewport, linkid, active) {
	return _in(0x00000000, 0x5f5e7f39, htmlviewport, linkid, active);
};

global.SetWebPageScroll = function (htmlviewport, scroll) {
	return _in(0x00000000, 0x55de40ee, htmlviewport, _fv(scroll));
};

global.SetWidescreenBorders = function (set) {
	return _in(0x00000000, 0x06c71148, set);
};

global.SetWidescreenFormat = function (wideformatid) {
	return _in(0x00000000, 0x7bde2caf, wideformatid);
};

global.SetZoneNoCops = function (name, set) {
	return _in(0x00000000, 0x64f37f05, _ts(name), set);
};

global.SetZonePopulationType = function (zone, poptype) {
	return _in(0x00000000, 0x70582d53, _ts(zone), poptype);
};

global.SetZoneScumminess = function (zone, scumminess) {
	return _in(0x00000000, 0x5e5e4252, _ts(zone), scumminess);
};

global.Settimera = function (value) {
	return _in(0x00000000, 0x32501b1e, value);
};

global.Settimerb = function (value) {
	return _in(0x00000000, 0x3b4c2e2e, value);
};

global.Settimerc = function (Unk1088) {
	return _in(0x00000000, 0x499852db, Unk1088);
};

global.ShakePad = function (Unk838, Unk839, Unk840) {
	return _in(0x00000000, 0x66cc16bd, Unk838, Unk839, Unk840);
};

global.ShakePadInCutscene = function (Unk841, Unk842, Unk843) {
	return _in(0x00000000, 0x2d040da9, Unk841, Unk842, Unk843);
};

global.ShakePlayerpadWhenControllerDisabled = function () {
	return _in(0x00000000, 0x691970fd);
};

global.ShiftLeft = function (val, shifts) {
	return _in(0x00000000, 0x102a0a6c, val, shifts, _r, _ri);
};

global.ShiftRight = function (val, shifts) {
	return _in(0x00000000, 0x64dd173c, val, shifts, _r, _ri);
};

global.ShowBlipOnAltimeter = function (blip, show) {
	return _in(0x00000000, 0x1dd86c2a, blip, show);
};

global.ShowSigninUi = function () {
	return _in(0x00000000, 0x72397ecd);
};

global.ShowUpdateStats = function (show) {
	return _in(0x00000000, 0x59486829, show);
};

global.ShutCarDoor = function (vehicle, door) {
	return _in(0x00000000, 0x5e7a620e, vehicle, door);
};

global.ShutdownAndLaunchNetworkGame = function (episode) {
	return _in(0x00000000, 0x1bc5050e, episode);
};

global.ShutdownAndLaunchSinglePlayerGame = function () {
	return _in(0x00000000, 0x49fd2621);
};

/**
 * Shuts down the `loadingScreen` NUI frame, similarly to `SHUTDOWN_LOADING_SCREEN`.
 */
global.ShutdownLoadingScreenNui = function () {
	return _in(0x00000000, 0xb9234afb);
};

global.SimulateUpdateLoadScene = function () {
	return _in(0x00000000, 0x246d47ce);
};

global.Sin = function (value) {
	return _in(0x00000000, 0x1ec10ce1, _fv(value), _r, _rf);
};

global.SkipInPlaybackRecordedCar = function (car, time) {
	return _in(0x00000000, 0x2c8c61ba, car, _fv(time));
};

global.SkipRadioForward = function () {
	return _in(0x00000000, 0x12a86e89);
};

global.SkipTimeInPlaybackRecordedCar = function (CarRec, time) {
	return _in(0x00000000, 0x255059bb, CarRec, _fv(time));
};

global.SkipToEndAndStopPlaybackRecordedCar = function (car) {
	return _in(0x00000000, 0x0d192f80, car);
};

global.SkipToNextAllowedStation = function (train) {
	return _in(0x00000000, 0x653b5374, train);
};

global.SkipToNextScriptedConversationLine = function () {
	return _in(0x00000000, 0x294c35b0);
};

global.SlideObject = function (obj, x, y, z, xs, ys, zs, flag) {
	return _in(0x00000000, 0x11b76edf, obj, _fv(x), _fv(y), _fv(z), _fv(xs), _fv(ys), _fv(zs), flag, _r);
};

global.SmashCarWindow = function (car, windownum) {
	return _in(0x00000000, 0x2cdf628c, car, windownum);
};

global.SmashGlassOnObject = function (x, y, z, Unk75, model, Unk76) {
	return _in(0x00000000, 0x2f877e8a, _fv(x), _fv(y), _fv(z), _fv(Unk75), model, _fv(Unk76), _r);
};

global.SnapshotCam = function (cam, Unk596) {
	return _in(0x00000000, 0x34bf456a, cam, Unk596);
};

global.SoundCarHorn = function (vehicle, duration) {
	return _in(0x00000000, 0x024859b5, vehicle, duration);
};

global.SpecifyScriptPopulationZoneArea = function (Unk848, Unk849, Unk850, Unk851, Unk852, Unk853) {
	return _in(0x00000000, 0x5a07394a, Unk848, Unk849, Unk850, Unk851, Unk852, Unk853);
};

global.SpecifyScriptPopulationZoneGroups = function (Unk854, Unk855, Unk856, Unk857, Unk858) {
	return _in(0x00000000, 0x70f0538f, Unk854, Unk855, Unk856, Unk857, Unk858);
};

global.SpecifyScriptPopulationZoneNumCars = function (num) {
	return _in(0x00000000, 0x1b886584, num);
};

global.SpecifyScriptPopulationZoneNumParkedCars = function (num) {
	return _in(0x00000000, 0x2eb751cc, num);
};

global.SpecifyScriptPopulationZoneNumPeds = function (num) {
	return _in(0x00000000, 0x159a4ed4, num);
};

global.SpecifyScriptPopulationZoneNumScenarioPeds = function (num) {
	return _in(0x00000000, 0x6a733e6c, num);
};

global.SpecifyScriptPopulationZonePercentageCops = function (percentage) {
	return _in(0x00000000, 0x49ff799a, percentage);
};

global.SpotCheck5 = function () {
	return _in(0x00000000, 0x6b4d6fc6, _r);
};

global.SpotCheck6 = function () {
	return _in(0x00000000, 0x52277fb2, _r);
};

global.SpotCheck7 = function () {
	return _in(0x00000000, 0x46cc31b4, _r);
};

global.SpotCheck8 = function () {
	return _in(0x00000000, 0x7b1b14bd, _r);
};

global.Sqrt = function (value) {
	return _in(0x00000000, 0x2c297c5d, _fv(value), _r, _rf);
};

global.StartCarFire = function (vehicle) {
	return _in(0x00000000, 0x3d703ed7, vehicle, _r, _ri);
};

global.StartCharFire = function (ped) {
	return _in(0x00000000, 0x5fb31295, ped, _r, _ri);
};

global.StartCredits = function () {
	return _in(0x00000000, 0x7f3222fd);
};

global.StartCustomMobilePhoneRinging = function (RingtoneId) {
	return _in(0x00000000, 0x59406eb1, RingtoneId);
};

global.StartCutscene = function () {
	return _in(0x00000000, 0x5f752f19);
};

global.StartCutsceneNow = function (name) {
	return _in(0x00000000, 0x53591dd7, _ts(name));
};

global.StartEndCreditsMusic = function () {
	return _in(0x00000000, 0x587e55d3);
};

/**
 * Equivalent of [START_FIND_KVP](#\_0xDD379006), but for another resource than the current one.
 * @param resourceName The resource to try finding the key/values for
 * @param prefix A prefix match
 * @return A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
 */
global.StartFindExternalKvp = function (resourceName, prefix) {
	return _in(0x00000000, 0x8f2eecc3, _ts(resourceName), _ts(prefix), _r, _ri);
};

/**
 * START_FIND_KVP
 * @param prefix A prefix match
 * @return A KVP find handle to use with [FIND_KVP](#\_0xBD7BEBC5) and close with [END_FIND_KVP](#\_0xB3210203)
 */
global.StartFindKvp = function (prefix) {
	return _in(0x00000000, 0xdd379006, _ts(prefix), _r, _ri);
};

global.StartFiringAmnesty = function () {
	return _in(0x00000000, 0x5db83661);
};

global.StartGpsRaceTrack = function (trackid) {
	return _in(0x00000000, 0x422c1818, trackid);
};

global.StartKillFrenzy = function (gxtname, Unk512, Unk513, Unk514, Unk515, Unk516, Unk517, Unk518, Unk519) {
	return _in(0x00000000, 0x077b17b5, _ts(gxtname), Unk512, Unk513, Unk514, Unk515, Unk516, Unk517, Unk518, Unk519);
};

global.StartLoadScene = function (x, y, z) {
	return _in(0x00000000, 0x54320b58, _fv(x), _fv(y), _fv(z));
};

global.StartMobilePhoneCall = function (callfrom, callfromvoice, callto, calltovoice, flag0, flag1) {
	return _in(0x00000000, 0x7939764f, callfrom, _ts(callfromvoice), callto, _ts(calltovoice), flag0, flag1);
};

global.StartMobilePhoneCalling = function () {
	return _in(0x00000000, 0x67114b98);
};

global.StartMobilePhoneRinging = function () {
	return _in(0x00000000, 0x372c0df1);
};

global.StartNewScript = function (scriptName, stacksize) {
	return _in(0x00000000, 0x4e2260b9, _ts(scriptName), stacksize, _r, _ri);
};

global.StartNewScriptWithArgs = function (scriptname, paramcount, stacksize) {
	return _in(0x00000000, 0x706707e6, _ts(scriptname), _i, paramcount, stacksize, _r, _ri);
};

global.StartNewWidgetCombo = function () {
	return _in(0x00000000, 0x03893a3a);
};

global.StartObjectFire = function (obj) {
	return _in(0x00000000, 0x2d7d5dd2, obj, _r, _ri);
};

global.StartPedMobileRinging = function (ped, Unk801) {
	return _in(0x00000000, 0x79a12a52, ped, Unk801);
};

global.StartPlaybackRecordedCar = function (car, CarRec) {
	return _in(0x00000000, 0x53335a45, car, CarRec);
};

global.StartPlaybackRecordedCarLooped = function (car, Unk69) {
	return _in(0x00000000, 0x01e33e33, car, Unk69);
};

global.StartPlaybackRecordedCarUsingAi = function (car, CarRec) {
	return _in(0x00000000, 0x5d900560, car, CarRec);
};

global.StartPlaybackRecordedCarWithOffset = function (car, CarRec, x, y, z) {
	return _in(0x00000000, 0x02491769, car, CarRec, _fv(x), _fv(y), _fv(z));
};

global.StartPtfx = function (name, x, y, z, yaw, pitch, roll, scale) {
	return _in(0x00000000, 0x3a774777, _ts(name), _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), _fv(scale), _r, _ri);
};

global.StartPtfxOnObj = function (name, obj, x, y, z, yaw, pitch, roll, scale) {
	return _in(0x00000000, 0x0d8407e9, _ts(name), obj, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), _fv(scale), _r, _ri);
};

global.StartPtfxOnObjBone = function (name, obj, x, y, z, yaw, pitch, roll, objbone, scale) {
	return _in(0x00000000, 0x60980323, _ts(name), obj, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), objbone, _fv(scale), _r, _ri);
};

global.StartPtfxOnPed = function (name, ped, x, y, z, yaw, pitch, roll, scale) {
	return _in(0x00000000, 0x381c1f1c, _ts(name), ped, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), _fv(scale), _r, _ri);
};

global.StartPtfxOnPedBone = function (name, ped, x, y, z, yaw, pitch, roll, pedbone, scale) {
	return _in(0x00000000, 0x2209116c, _ts(name), ped, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), pedbone, _fv(scale), _r, _ri);
};

global.StartPtfxOnVeh = function (name, veh, x, y, z, yaw, pitch, roll, scale) {
	return _in(0x00000000, 0x5c4b1a8a, _ts(name), veh, _fv(x), _fv(y), _fv(z), _fv(yaw), _fv(pitch), _fv(roll), _fv(scale), _r, _ri);
};

global.StartScriptConversation = function (flag0, flag1) {
	return _in(0x00000000, 0x288e50a3, flag0, flag1);
};

global.StartScriptFire = function (x, y, z, numGenerationsAllowed, strength) {
	return _in(0x00000000, 0x24742bb9, _fv(x), _fv(y), _fv(z), numGenerationsAllowed, strength, _r, _ri);
};

global.StartStreamingRequestList = function (name) {
	return _in(0x00000000, 0x7858750e, _ts(name));
};

/**
 * STATE_BAG_HAS_KEY
 * @param bagName The name of the bag.
 * @param key The key used to check data existence.
 * @return Returns true if the data associated with the specified key exists; otherwise, returns false.
 */
global.StateBagHasKey = function (bagName, key) {
	return _in(0x00000000, 0x0012a330, _ts(bagName), _ts(key), _r);
};

global.StopCarBreaking = function (car, stop) {
	return _in(0x00000000, 0x29305d67, car, stop);
};

global.StopCredits = function () {
	return _in(0x00000000, 0x4f0f2aa8);
};

global.StopCutscene = function () {
	return _in(0x00000000, 0x50ff1428);
};

global.StopEndCreditsMusic = function () {
	return _in(0x00000000, 0x47e93cb8);
};

global.StopMobilePhoneRinging = function () {
	return _in(0x00000000, 0x27356f3a);
};

global.StopMovie = function () {
	return _in(0x00000000, 0x2e6f4c82);
};

global.StopPedDoingFallOffTestsWhenShot = function (ped) {
	return _in(0x00000000, 0x4e386c7b, ped);
};

global.StopPedMobileRinging = function (ped) {
	return _in(0x00000000, 0x07827ae1, ped);
};

global.StopPedSpeaking = function (ped, stopspeaking) {
	return _in(0x00000000, 0x710b2bd3, ped, stopspeaking);
};

global.StopPedWeaponFiringWhenDropped = function (ped) {
	return _in(0x00000000, 0x6e0026ef, ped);
};

global.StopPlaybackRecordedCar = function (car) {
	return _in(0x00000000, 0x71c91921, car);
};

global.StopPreviewRingtone = function () {
	return _in(0x00000000, 0x5b1d57ef);
};

global.StopPtfx = function (ptfx) {
	return _in(0x00000000, 0x0eaa4429, ptfx);
};

global.StopSound = function (sound) {
	return _in(0x00000000, 0x09db00b9, sound);
};

global.StopStream = function () {
	return _in(0x00000000, 0x66915ce9);
};

global.StopSyncingScriptAnimations = function (Unk1061) {
	return _in(0x00000000, 0x47f430be, Unk1061);
};

global.StopVehicleAlwaysRender = function (veh) {
	return _in(0x00000000, 0x7cdd7b0e, veh);
};

global.StoreCarCharIsInNoSave = function (ped, car) {
	return _in(0x00000000, 0x21cc647f, ped, _ii(car) /* may be optional */);
};

global.StoreDamageTrackerForNetworkPlayer = function (playerIndex, ukn57, Unk895) {
	return _in(0x00000000, 0x68373878, playerIndex, ukn57, Unk895, _r, _ri);
};

global.StoreScore = function (playerIndex, value) {
	return _in(0x00000000, 0x1e203014, playerIndex, _ii(value) /* may be optional */);
};

global.StoreScriptValuesForNetworkGame = function (Unk998) {
	return _in(0x00000000, 0x1dff5b06, Unk998);
};

global.StoreWantedLevel = function (playerIndex, value) {
	return _in(0x00000000, 0x12aa6d71, playerIndex, _ii(value) /* may be optional */);
};

global.StreamCutscene = function () {
	return _in(0x00000000, 0x0f0d2025);
};

global.StringDifference = function (str0, str1) {
	return _in(0x00000000, 0x25204f8b, _ts(str0), _ts(str1), _r, _ri);
};

global.StringString = function (str0, str1) {
	return _in(0x00000000, 0x6c0e191f, _ts(str0), _ts(str1), _r, _ri);
};

global.StringToInt = function (str, intval) {
	return _in(0x00000000, 0x5c3248b5, _ts(str), _ii(intval) /* may be optional */, _r);
};

global.SuppressCarModel = function (model) {
	return _in(0x00000000, 0x768f640f, model);
};

global.SuppressFadeInAfterDeathArrest = function (set) {
	return _in(0x00000000, 0x3fb83379, set);
};

global.SuppressPedModel = function (model) {
	return _in(0x00000000, 0x4c5475e3, model);
};

global.SwapNearestBuildingModel = function (x, y, z, radius, modelfrom, modelto) {
	return _in(0x00000000, 0x5e077484, _fv(x), _fv(y), _fv(z), _fv(radius), modelfrom, modelto);
};

global.SwitchAmbientPlanes = function (on) {
	return _in(0x00000000, 0x4e637988, on);
};

global.SwitchArrowAboveBlippedPickups = function (on) {
	return _in(0x00000000, 0x3a323c67, on);
};

global.SwitchCarGenerator = function (handle, type) {
	return _in(0x00000000, 0x7ce83a30, handle, type);
};

global.SwitchCarSiren = function (car, siren) {
	return _in(0x00000000, 0x7781290f, car, siren);
};

global.SwitchGarbageTrucks = function (on) {
	return _in(0x00000000, 0x060669fe, on);
};

global.SwitchMadDrivers = function (on) {
	return _in(0x00000000, 0x34cb6291, on);
};

global.SwitchObjectBrains = function (brain, switchstate) {
	return _in(0x00000000, 0x35213375, brain, switchstate);
};

global.SwitchOffWaypoint = function () {
	return _in(0x00000000, 0x1b5b4ed9);
};

global.SwitchPedPathsOff = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x008a2256, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchPedPathsOn = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x67d908df, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchPedRoadsBackToOriginal = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x6aa20b7e, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchPedToAnimated = function (ped, unknownTrue) {
	return _in(0x00000000, 0x762301c8, ped, unknownTrue);
};

global.SwitchPedToRagdoll = function (ped, Unk14, time, flag0, flag1, flag2, flag3) {
	return _in(0x00000000, 0x1a0f56c5, ped, Unk14, time, flag0, flag1, flag2, flag3, _r);
};

global.SwitchPedToRagdollWithFall = function (ped, Unk15, Unk16, Unk17, Unk18, Unk19, Unk20, Unk21, Unk22, Unk23, Unk24, Unk25, Unk26, Unk27) {
	return _in(0x00000000, 0x13e4042d, ped, Unk15, Unk16, Unk17, _fv(Unk18), _fv(Unk19), Unk20, _fv(Unk21), _fv(Unk22), _fv(Unk23), _fv(Unk24), _fv(Unk25), _fv(Unk26), _fv(Unk27), _r);
};

global.SwitchPoliceHelis = function (set) {
	return _in(0x00000000, 0x0ca46b08, set);
};

global.SwitchRandomBoats = function (on) {
	return _in(0x00000000, 0x7fc65855, on);
};

global.SwitchRandomTrains = function (on) {
	return _in(0x00000000, 0x0ffd1a92, on);
};

global.SwitchRoadsBackToOriginal = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x6251618f, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchRoadsOff = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x4c3c1f3c, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchRoadsOn = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x56553f38, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1));
};

global.SwitchStreaming = function (on) {
	return _in(0x00000000, 0x6e397d96, on);
};

global.SynchAmbientPlanes = function (Unk520, Unk521) {
	return _in(0x00000000, 0x5afd2049, Unk520, Unk521);
};

global.SynchRecordingWithWater = function () {
	return _in(0x00000000, 0x018a0ee0, _r);
};

global.TakeCarOutOfParkedCarsBudget = function (car, out) {
	return _in(0x00000000, 0x60ef0519, car, out);
};

global.Tan = function (value) {
	return _in(0x00000000, 0x24cc682b, _fv(value), _r, _rf);
};

global.TaskAchieveHeading = function (ped, heading) {
	return _in(0x00000000, 0x6d6a1261, ped, _fv(heading));
};

global.TaskAimGunAtChar = function (ped, targetPed, duration) {
	return _in(0x00000000, 0x4437501b, ped, targetPed, duration);
};

global.TaskAimGunAtCoord = function (ped, tX, tY, tZ, duration) {
	return _in(0x00000000, 0x0aa202b0, ped, _fv(tX), _fv(tY), _fv(tZ), duration);
};

global.TaskCarDriveToCoord = function (ped, veh, Unk133, Unk134, Unk135, Unk136, Unk137, Unk138, Unk139, Unk140, Unk141) {
	return _in(0x00000000, 0x69715285, ped, veh, _fv(Unk133), _fv(Unk134), _fv(Unk135), _fv(Unk136), Unk137, Unk138, Unk139, _fv(Unk140), Unk141);
};

global.TaskCarDriveToCoordNotAgainstTraffic = function (ped, Unk142, Unk143, Unk144, Unk145, Unk146, Unk147, Unk148, Unk149, Unk150, Unk151) {
	return _in(0x00000000, 0x483a62ab, ped, Unk142, Unk143, Unk144, Unk145, Unk146, Unk147, Unk148, Unk149, Unk150, Unk151);
};

global.TaskCarDriveWander = function (ped, vehicle, speed, drivingStyle) {
	return _in(0x00000000, 0x1e9635a9, ped, vehicle, _fv(speed), drivingStyle);
};

global.TaskCarMission = function (ped, vehicle, targetEntity, missionType, speed, drivingStyle, unknown6_10, unknown7_5) {
	return _in(0x00000000, 0x36273536, ped, vehicle, targetEntity, missionType, _fv(speed), drivingStyle, unknown6_10, unknown7_5);
};

global.TaskCarMissionCoorsTarget = function (ped, vehicle, x, y, z, unknown0_4, speed, unknown2_1, unknown3_5, unknown4_10) {
	return _in(0x00000000, 0x36d51ddf, ped, vehicle, _fv(x), _fv(y), _fv(z), unknown0_4, _fv(speed), unknown2_1, unknown3_5, unknown4_10);
};

global.TaskCarMissionCoorsTargetNotAgainstTraffic = function (ped, vehicle, x, y, z, unknown0_4, speed, unknown2_1, unknown3_5, unknown4_10) {
	return _in(0x00000000, 0x3cb4693b, ped, vehicle, _fv(x), _fv(y), _fv(z), unknown0_4, _fv(speed), unknown2_1, unknown3_5, unknown4_10);
};

global.TaskCarMissionNotAgainstTraffic = function (ped, vehicle, targetEntity, missionType, speed, drivingStyle, unknown6_10, unknown7_5) {
	return _in(0x00000000, 0x3be7444a, ped, vehicle, targetEntity, missionType, _fv(speed), drivingStyle, unknown6_10, unknown7_5);
};

global.TaskCarMissionPedTarget = function (ped, vehicle, target, unknown0_4, speed, unknown2_1, unknown3_5, unknown4_10) {
	return _in(0x00000000, 0x39c2663e, ped, vehicle, target, unknown0_4, _fv(speed), unknown2_1, unknown3_5, unknown4_10);
};

global.TaskCarMissionPedTargetNotAgainstTraffic = function (ped, Unk152, Unk153, Unk154, Unk155, Unk156, Unk157, Unk158) {
	return _in(0x00000000, 0x178332ff, ped, Unk152, Unk153, Unk154, Unk155, Unk156, Unk157, Unk158);
};

global.TaskCarTempAction = function (ped, vehicle, action, duration) {
	return _in(0x00000000, 0x11612815, ped, vehicle, action, duration);
};

global.TaskCharArrestChar = function (ped0, ped1) {
	return _in(0x00000000, 0x71a05ff1, ped0, ped1);
};

global.TaskCharSlideToCoord = function (ped, Unk159, Unk160, Unk161, Unk162, Unk163) {
	return _in(0x00000000, 0x04962f82, ped, Unk159, Unk160, Unk161, Unk162, Unk163);
};

global.TaskCharSlideToCoordAndPlayAnim = function (ped, Unk164, Unk165, Unk166, Unk167, Unk168, Unk169, Unk170, Unk171, Unk172, Unk173, Unk174, Unk175, Unk176) {
	return _in(0x00000000, 0x79bb1d64, ped, Unk164, Unk165, Unk166, Unk167, Unk168, Unk169, Unk170, Unk171, Unk172, Unk173, Unk174, Unk175, Unk176);
};

global.TaskCharSlideToCoordHdgRate = function (ped, Unk177, Unk178, Unk179, Unk180, Unk181, Unk182) {
	return _in(0x00000000, 0x33d756a0, ped, Unk177, Unk178, Unk179, Unk180, Unk181, Unk182);
};

global.TaskChatWithChar = function (ped, pednext, Unk183, Unk184) {
	return _in(0x00000000, 0x5c9807ca, ped, pednext, Unk183, Unk184);
};

global.TaskClearLookAt = function (ped) {
	return _in(0x00000000, 0x05745aca, ped);
};

global.TaskClimb = function (ped, Unk185) {
	return _in(0x00000000, 0x4678769c, ped, Unk185);
};

global.TaskClimbLadder = function (ped, Unk186) {
	return _in(0x00000000, 0x0abe3fa8, ped, Unk186);
};

global.TaskCombat = function (ped, target) {
	return _in(0x00000000, 0x1f157fd3, ped, target);
};

global.TaskCombatHatedTargetsAroundChar = function (ped, radius) {
	return _in(0x00000000, 0x127669d3, ped, _fv(radius));
};

global.TaskCombatHatedTargetsAroundCharTimed = function (ped, radius, duration) {
	return _in(0x00000000, 0x15012850, ped, _fv(radius), duration);
};

global.TaskCombatHatedTargetsInArea = function (ped, Unk187, Unk188, Unk189, Unk190) {
	return _in(0x00000000, 0x06b840f1, ped, Unk187, Unk188, Unk189, Unk190);
};

global.TaskCombatRoll = function (ped, Unk191) {
	return _in(0x00000000, 0x131a0c84, ped, Unk191);
};

global.TaskCombatTimed = function (ped, target, duration) {
	return _in(0x00000000, 0x56f04a05, ped, target, duration);
};

global.TaskCower = function (ped) {
	return _in(0x00000000, 0x29103e08, ped);
};

global.TaskDead = function (ped) {
	return _in(0x00000000, 0x3e1051e0, ped);
};

global.TaskDestroyCar = function (ped, car) {
	return _in(0x00000000, 0x787a3d4c, ped, car);
};

global.TaskDie = function (ped) {
	return _in(0x00000000, 0x7eed364b, ped);
};

global.TaskDriveBy = function (ped, pednext, Unk192, x, y, z, angle, Unk193, Unk194, Unk195) {
	return _in(0x00000000, 0x3fb22ee2, ped, pednext, Unk192, _fv(x), _fv(y), _fv(z), _fv(angle), Unk193, Unk194, Unk195);
};

global.TaskDrivePointRoute = function (ped, point, radius) {
	return _in(0x00000000, 0x2c18736e, ped, point, _fv(radius));
};

global.TaskDrivePointRouteAdvanced = function (ped, Unk197, Unk198, Unk199, Unk200, Unk201) {
	return _in(0x00000000, 0x7a0a1063, ped, Unk197, Unk198, Unk199, Unk200, Unk201);
};

global.TaskDuck = function (ped, Unk202) {
	return _in(0x00000000, 0x72bf79f1, ped, Unk202);
};

global.TaskEnterCarAsDriver = function (ped, vehicle, duration) {
	return _in(0x00000000, 0x5bf03315, ped, vehicle, duration);
};

global.TaskEnterCarAsPassenger = function (ped, vehicle, duration, seatIndex) {
	return _in(0x00000000, 0x0a2c70af, ped, vehicle, duration, seatIndex);
};

global.TaskEveryoneLeaveCar = function (vehicle) {
	return _in(0x00000000, 0x41e45be5, vehicle);
};

global.TaskExtendRoute = function (ped, Unk203, Unk204) {
	return _in(0x00000000, 0x75353ea4, ped, Unk203, Unk204);
};

global.TaskFallAndGetUp = function (ped, Unk205, Unk206) {
	return _in(0x00000000, 0x069433a8, ped, Unk205, Unk206);
};

global.TaskFleeCharAnyMeans = function (ped, Unk207, Unk208, Unk209, Unk210, Unk211, Unk212, Unk213) {
	return _in(0x00000000, 0x32517ae2, ped, Unk207, Unk208, Unk209, Unk210, Unk211, Unk212, Unk213);
};

global.TaskFlushRoute = function () {
	return _in(0x00000000, 0x760e0a0f);
};

global.TaskFollowFootsteps = function (ped, Unk214) {
	return _in(0x00000000, 0x45df7cca, ped, Unk214);
};

global.TaskFollowNavMeshAndSlideToCoord = function (ped, x, y, z, Unk215, Unk216, Unk217, angle) {
	return _in(0x00000000, 0x36537ce1, ped, _fv(x), _fv(y), _fv(z), Unk215, Unk216, _fv(Unk217), _fv(angle));
};

global.TaskFollowNavMeshAndSlideToCoordHdgRate = function (ped, x, y, z, Unk218, Unk219, Unk220, angle, rate) {
	return _in(0x00000000, 0x38824bfe, ped, _fv(x), _fv(y), _fv(z), Unk218, Unk219, _fv(Unk220), _fv(angle), _fv(rate));
};

global.TaskFollowNavMeshToCoord = function (ped, x, y, z, unknown0_2, unknown1_minus1, unknown2_1) {
	return _in(0x00000000, 0x1b31390e, ped, _fv(x), _fv(y), _fv(z), unknown0_2, unknown1_minus1, _fv(unknown2_1));
};

global.TaskFollowNavMeshToCoordNoStop = function (ped, x, y, z, unknown0_2, unknown1_minus1, unknown2_1) {
	return _in(0x00000000, 0x1bf67441, ped, _fv(x), _fv(y), _fv(z), unknown0_2, unknown1_minus1, _fv(unknown2_1));
};

global.TaskGetOffBoat = function (ped, timeout) {
	return _in(0x00000000, 0x6c63251d, ped, timeout);
};

global.TaskGoStraightToCoord = function (ped, x, y, z, unknown2, unknown45000) {
	return _in(0x00000000, 0x19591255, ped, _fv(x), _fv(y), _fv(z), unknown2, unknown45000);
};

global.TaskGoStraightToCoordRelativeToCar = function (ped, Unk227, Unk228, Unk229, Unk230, Unk231, Unk232) {
	return _in(0x00000000, 0x498b3be4, ped, Unk227, Unk228, Unk229, Unk230, Unk231, Unk232);
};

global.TaskGoToChar = function (ped, Unk233, Unk234, Unk235) {
	return _in(0x00000000, 0x664d06ff, ped, Unk233, Unk234, Unk235);
};

global.TaskGoToCoordAnyMeans = function (ped, Unk236, Unk237, Unk238, Unk239, Unk240) {
	return _in(0x00000000, 0x04f72e4c, ped, Unk236, Unk237, Unk238, Unk239, Unk240);
};

global.TaskGoToCoordWhileAiming = function (ped, Unk241, Unk242, Unk243, Unk244, Unk245, Unk246, Unk247, Unk248, Unk249, Unk250, Unk251) {
	return _in(0x00000000, 0x2a2959da, ped, Unk241, Unk242, Unk243, Unk244, Unk245, Unk246, Unk247, Unk248, Unk249, Unk250, Unk251);
};

global.TaskGoToCoordWhileShooting = function (ped, Unk252, Unk253, Unk254, Unk255, Unk256, Unk257, Unk258, Unk259) {
	return _in(0x00000000, 0x10cb1413, ped, Unk252, Unk253, Unk254, Unk255, Unk256, Unk257, Unk258, Unk259);
};

global.TaskGoToObject = function (ped, Unk260, Unk261, Unk262) {
	return _in(0x00000000, 0x5b1b2699, ped, Unk260, Unk261, Unk262);
};

global.TaskGotoCar = function (ped, Unk221, Unk222, Unk223) {
	return _in(0x00000000, 0x3ea116f7, ped, Unk221, Unk222, Unk223);
};

global.TaskGotoCharAiming = function (ped, Unk224, Unk225, Unk226) {
	return _in(0x00000000, 0x65eb71cc, ped, Unk224, Unk225, Unk226);
};

global.TaskGotoCharOffset = function (ped, target, duration, offsetRight, offsetFront) {
	return _in(0x00000000, 0x658028ba, ped, target, duration, _fv(offsetRight), _fv(offsetFront));
};

global.TaskGuardAngledDefensiveArea = function (ped, Unk263, Unk264, Unk265, Unk266, Unk267, Unk268, Unk269, Unk270, Unk271, Unk272, Unk273, Unk274, Unk275) {
	return _in(0x00000000, 0x030e0224, ped, Unk263, Unk264, Unk265, Unk266, Unk267, Unk268, Unk269, Unk270, Unk271, Unk272, Unk273, Unk274, Unk275);
};

global.TaskGuardAssignedDefensiveArea = function (ped, Unk276, Unk277, Unk278, Unk279, Unk280, Unk281) {
	return _in(0x00000000, 0x07e21c28, ped, Unk276, Unk277, Unk278, Unk279, Unk280, Unk281);
};

global.TaskGuardCurrentPosition = function (ped, unknown0_15, unknown1_10, unknown2_1) {
	return _in(0x00000000, 0x3e6137cb, ped, _fv(unknown0_15), _fv(unknown1_10), unknown2_1);
};

global.TaskGuardSphereDefensiveArea = function (ped, Unk282, Unk283, Unk284, Unk285, Unk286, Unk287, Unk288, Unk289, Unk290, Unk291) {
	return _in(0x00000000, 0x01795753, ped, Unk282, Unk283, Unk284, Unk285, Unk286, Unk287, Unk288, Unk289, Unk290, Unk291);
};

global.TaskHandsUp = function (ped, duration) {
	return _in(0x00000000, 0x68232d31, ped, duration);
};

global.TaskHeliMission = function (ped, heli, uk0_0, uk1_0, pX, pY, pZ, uk2_4, speed, uk3_5, uk4_minus1, uk5_round_z_plus_1, uk6_40) {
	return _in(0x00000000, 0x0f227d5a, ped, heli, uk0_0, uk1_0, _fv(pX), _fv(pY), _fv(pZ), uk2_4, _fv(speed), uk3_5, _fv(uk4_minus1), uk5_round_z_plus_1, uk6_40);
};

global.TaskJump = function (ped, flag) {
	return _in(0x00000000, 0x5e97106e, ped, flag);
};

global.TaskLeaveAnyCar = function (ped) {
	return _in(0x00000000, 0x1114089d, ped);
};

global.TaskLeaveCar = function (ped, vehicle) {
	return _in(0x00000000, 0x6b85214e, ped, vehicle);
};

global.TaskLeaveCarAndFlee = function (ped, Unk292, Unk293, Unk294, Unk295) {
	return _in(0x00000000, 0x6cea50d8, ped, Unk292, Unk293, Unk294, Unk295);
};

global.TaskLeaveCarDontCloseDoor = function (ped, vehicle) {
	return _in(0x00000000, 0x1c9a376d, ped, vehicle);
};

global.TaskLeaveCarImmediately = function (ped, vehicle) {
	return _in(0x00000000, 0x7bfb484f, ped, vehicle);
};

global.TaskLeaveCarInDirection = function (ped, car, direction) {
	return _in(0x00000000, 0x18740b3d, ped, car, direction);
};

global.TaskLeaveGroup = function (ped) {
	return _in(0x00000000, 0x1905109f, ped);
};

global.TaskLookAtChar = function (ped, targetPed, duration, unknown_0) {
	return _in(0x00000000, 0x2dd35b3f, ped, targetPed, duration, unknown_0);
};

global.TaskLookAtCoord = function (ped, x, y, z, duration, unknown_0) {
	return _in(0x00000000, 0x26e27605, ped, _fv(x), _fv(y), _fv(z), duration, unknown_0);
};

global.TaskLookAtObject = function (ped, targetObject, duration, unknown_0) {
	return _in(0x00000000, 0x27c740d0, ped, targetObject, duration, unknown_0);
};

global.TaskLookAtVehicle = function (ped, targetVehicle, duration, unknown_0) {
	return _in(0x00000000, 0x4a2c5544, ped, targetVehicle, duration, unknown_0);
};

global.TaskMobileConversation = function (ped, Unk296) {
	return _in(0x00000000, 0x64903364, ped, Unk296);
};

global.TaskOpenDriverDoor = function (ped, vehicle, unknown0) {
	return _in(0x00000000, 0x1fa41244, ped, vehicle, unknown0);
};

global.TaskOpenPassengerDoor = function (ped, vehicle, seatIndex, unknown0) {
	return _in(0x00000000, 0x58f814c4, ped, vehicle, seatIndex, unknown0);
};

global.TaskPause = function (ped, duration) {
	return _in(0x00000000, 0x5e702e2c, ped, duration);
};

global.TaskPerformSequence = function (ped, taskSequence) {
	return _in(0x00000000, 0x36a33c21, ped, taskSequence);
};

global.TaskPerformSequenceFromProgress = function (ped, Unk297, Unk298, Unk299) {
	return _in(0x00000000, 0x62701af8, ped, Unk297, Unk298, Unk299);
};

global.TaskPerformSequenceLocally = function (ped, Unk300) {
	return _in(0x00000000, 0x326b576f, ped, Unk300);
};

global.TaskPickupAndCarryObject = function (ped, Unk301, Unk302, Unk303, Unk304, Unk305) {
	return _in(0x00000000, 0x76d72d89, ped, Unk301, Unk302, Unk303, Unk304, Unk305);
};

global.TaskPlayAnim = function (ped, Unk306, Unk307, Unk308, Unk309, Unk310, Unk311, Unk312, Unk313) {
	return _in(0x00000000, 0x28ee78d8, ped, Unk306, Unk307, Unk308, Unk309, Unk310, Unk311, Unk312, Unk313);
};

global.TaskPlayAnimFacial = function (ped, Unk314, Unk315, Unk316, Unk317, Unk318, Unk319) {
	return _in(0x00000000, 0x71f001d2, ped, Unk314, Unk315, Unk316, Unk317, Unk318, Unk319);
};

global.TaskPlayAnimNonInterruptable = function (ped, animname0, animname1, Unk320, Unk321, Unk322, Unk323, Unk324, Unk325) {
	return _in(0x00000000, 0x52202e76, ped, _ts(animname0), _ts(animname1), _fv(Unk320), Unk321, Unk322, Unk323, Unk324, Unk325);
};

global.TaskPlayAnimOnClone = function (ped, Unk326, Unk327, Unk328, Unk329, Unk330, Unk331, Unk332, Unk333) {
	return _in(0x00000000, 0x10fb7b5f, ped, Unk326, Unk327, Unk328, Unk329, Unk330, Unk331, Unk332, Unk333);
};

global.TaskPlayAnimReadyToBeExecuted = function (ped, Unk334, Unk335, Unk336) {
	return _in(0x00000000, 0x040a0537, ped, Unk334, Unk335, Unk336);
};

global.TaskPlayAnimSecondary = function (ped, Unk337, Unk338, Unk339, Unk340, Unk341, Unk342, Unk343, Unk344) {
	return _in(0x00000000, 0x273c2d35, ped, Unk337, Unk338, Unk339, Unk340, Unk341, Unk342, Unk343, Unk344);
};

global.TaskPlayAnimSecondaryInCar = function (ped, Unk345, Unk346, Unk347, Unk348, Unk349, Unk350, Unk351, Unk352) {
	return _in(0x00000000, 0x482b2b74, ped, Unk345, Unk346, Unk347, Unk348, Unk349, Unk350, Unk351, Unk352);
};

global.TaskPlayAnimSecondaryNoInterrupt = function (ped, Unk353, Unk354, Unk355, Unk356, Unk357, Unk358, Unk359, Unk360) {
	return _in(0x00000000, 0x56524b94, ped, Unk353, Unk354, Unk355, Unk356, Unk357, Unk358, Unk359, Unk360);
};

global.TaskPlayAnimSecondaryUpperBody = function (ped, Unk361, Unk362, Unk363, Unk364, Unk365, Unk366, Unk367, Unk368) {
	return _in(0x00000000, 0x34574b2a, ped, Unk361, Unk362, Unk363, Unk364, Unk365, Unk366, Unk367, Unk368);
};

global.TaskPlayAnimUpperBody = function (ped, Unk369, Unk370, Unk371, Unk372, Unk373, Unk374, Unk375, Unk376) {
	return _in(0x00000000, 0x02534709, ped, Unk369, Unk370, Unk371, Unk372, Unk373, Unk374, Unk375, Unk376);
};

global.TaskPlayAnimWithAdvancedFlags = function (ped, Unk377, Unk378, Unk379, Unk380, Unk381, Unk382, Unk383, Unk384, Unk385, Unk386, Unk387) {
	return _in(0x00000000, 0x30ba2716, ped, Unk377, Unk378, Unk379, Unk380, Unk381, Unk382, Unk383, Unk384, Unk385, Unk386, Unk387);
};

global.TaskPlayAnimWithFlags = function (ped, animName, animSet, unknown0_8, unknown1_0, flags) {
	return _in(0x00000000, 0x75533e74, ped, _ts(animName), _ts(animSet), _fv(unknown0_8), unknown1_0, flags);
};

global.TaskPlayAnimWithFlagsAndStartPhase = function (ped, Unk388, Unk389, Unk390, Unk391, Unk392, Unk393) {
	return _in(0x00000000, 0x1a122d03, ped, Unk388, Unk389, Unk390, Unk391, Unk392, Unk393);
};

global.TaskPutCharDirectlyIntoCover = function (Unk394, Unk395, Unk396, Unk397, Unk398) {
	return _in(0x00000000, 0x1fdd4860, Unk394, Unk395, Unk396, Unk397, Unk398);
};

global.TaskSeekCoverFromPed = function (ped, Unk399, Unk400) {
	return _in(0x00000000, 0x2d9c3d5e, ped, Unk399, Unk400);
};

global.TaskSeekCoverFromPos = function (ped, Unk401, Unk402, Unk403, Unk404) {
	return _in(0x00000000, 0x2bdf7b7e, ped, Unk401, Unk402, Unk403, Unk404);
};

global.TaskSeekCoverToCoords = function (ped, Unk405, Unk406, Unk407, Unk408, Unk409, Unk410, Unk411) {
	return _in(0x00000000, 0x142f31ef, ped, Unk405, Unk406, Unk407, Unk408, Unk409, Unk410, Unk411);
};

global.TaskSeekCoverToCoverPoint = function (ped, Unk412, Unk413, Unk414, Unk415, Unk416) {
	return _in(0x00000000, 0x143358d3, ped, Unk412, Unk413, Unk414, Unk415, Unk416);
};

global.TaskSeekCoverToObject = function (ped, Unk417, Unk418, Unk419, Unk420, Unk421) {
	return _in(0x00000000, 0x4db55df5, ped, Unk417, Unk418, Unk419, Unk420, Unk421);
};

global.TaskSetCharDecisionMaker = function (ped, dm) {
	return _in(0x00000000, 0x1cb2670d, ped, dm);
};

global.TaskSetCombatDecisionMaker = function (ped, dm) {
	return _in(0x00000000, 0x499c0c01, ped, dm);
};

global.TaskSetIgnoreWeaponRangeFlag = function (ped, ignore) {
	return _in(0x00000000, 0x6ce277e7, ped, ignore);
};

global.TaskShakeFist = function (ped) {
	return _in(0x00000000, 0x0f7f3837, ped);
};

global.TaskShimmy = function (ped, Unk422) {
	return _in(0x00000000, 0x53230256, ped, Unk422);
};

global.TaskShimmyClimbUp = function (ped) {
	return _in(0x00000000, 0x36ad6480, ped, _r);
};

global.TaskShimmyInDirection = function (ped, Unk109) {
	return _in(0x00000000, 0x7b1a5333, ped, Unk109, _r);
};

global.TaskShimmyLetGo = function (ped) {
	return _in(0x00000000, 0x1aa32729, ped, _r);
};

global.TaskShootAtChar = function (shooter, victim, time, shootmode) {
	return _in(0x00000000, 0x08022967, shooter, victim, time, shootmode);
};

global.TaskShootAtCoord = function (ped, Unk423, Unk424, Unk425, Unk426, Unk427) {
	return _in(0x00000000, 0x705231a9, ped, Unk423, Unk424, Unk425, Unk426, Unk427);
};

global.TaskShuffleToNextCarSeat = function (ped, Unk428) {
	return _in(0x00000000, 0x011d360d, ped, Unk428);
};

global.TaskSitDown = function (ped, Unk429, Unk430, Unk431) {
	return _in(0x00000000, 0x264c5448, ped, Unk429, Unk430, Unk431);
};

global.TaskSitDownInstantly = function (ped, Unk432, Unk433, Unk434) {
	return _in(0x00000000, 0x6cc1560f, ped, Unk432, Unk433, Unk434);
};

global.TaskSitDownOnNearestObject = function (ped, Unk435, Unk436, Unk437, Unk438, Unk439, Unk440, Unk441, Unk442, Unk443) {
	return _in(0x00000000, 0x725654f4, ped, Unk435, Unk436, Unk437, Unk438, Unk439, Unk440, Unk441, Unk442, Unk443);
};

global.TaskSitDownOnObject = function (ped, Unk444, Unk445, Unk446, Unk447, Unk448, Unk449, Unk450, Unk451, Unk452) {
	return _in(0x00000000, 0x515c3218, ped, Unk444, Unk445, Unk446, Unk447, Unk448, Unk449, Unk450, Unk451, Unk452);
};

global.TaskSitDownOnSeat = function (ped, Unk453, Unk454, Unk455, Unk456, Unk457, Unk458, Unk459) {
	return _in(0x00000000, 0x2cbe4daf, ped, Unk453, Unk454, Unk455, Unk456, Unk457, Unk458, Unk459);
};

global.TaskSmartFleeChar = function (ped, fleeFromPed, unknown0_100, duration) {
	return _in(0x00000000, 0x1880639c, ped, fleeFromPed, _fv(unknown0_100), duration);
};

global.TaskSmartFleeCharPreferringPavements = function (ped, fleeFromPed, unknown0_100, duration) {
	return _in(0x00000000, 0x57ac66e9, ped, fleeFromPed, _fv(unknown0_100), duration);
};

global.TaskSmartFleePoint = function (ped, x, y, z, unknown0_100, duration) {
	return _in(0x00000000, 0x7381337a, ped, _fv(x), _fv(y), _fv(z), _fv(unknown0_100), duration);
};

global.TaskSmartFleePointPreferringPavements = function (ped, x, y, z, radius, time_prob) {
	return _in(0x00000000, 0x3ceb6c7b, ped, _fv(x), _fv(y), _fv(z), _fv(radius), time_prob);
};

global.TaskStandGuard = function (ped, x, y, z, Unk460, Unk461, Unk462, Unk463) {
	return _in(0x00000000, 0x59523479, ped, _fv(x), _fv(y), _fv(z), _fv(Unk460), _fv(Unk461), Unk462, Unk463);
};

global.TaskStandStill = function (ped, duration) {
	return _in(0x00000000, 0x524c4cb5, ped, duration);
};

global.TaskStartScenarioAtPosition = function (ped, Unk464, Unk465, Unk466, Unk467, Unk468) {
	return _in(0x00000000, 0x0f296c2e, ped, Unk464, Unk465, Unk466, Unk467, Unk468);
};

global.TaskStartScenarioInPlace = function (ped, Unk469, Unk470) {
	return _in(0x00000000, 0x261f18a3, ped, Unk469, Unk470);
};

global.TaskSwapWeapon = function (ped, weapon) {
	return _in(0x00000000, 0x72ae63c8, ped, weapon);
};

global.TaskSwimToCoord = function (ped, x, y, z) {
	return _in(0x00000000, 0x098d5da6, ped, _fv(x), _fv(y), _fv(z));
};

global.TaskTired = function (ped, Unk471) {
	return _in(0x00000000, 0x702041f2, ped, Unk471);
};

global.TaskToggleDuck = function (ped, Unk472) {
	return _in(0x00000000, 0x319e3a87, ped, Unk472);
};

global.TaskTogglePedThreatScanner = function (ped, Unk473, Unk474, Unk475) {
	return _in(0x00000000, 0x5d515c4d, ped, Unk473, Unk474, Unk475);
};

global.TaskTurnCharToFaceChar = function (ped, targetPed) {
	return _in(0x00000000, 0x0a462b7a, ped, targetPed);
};

global.TaskTurnCharToFaceCoord = function (ped, x, y, z) {
	return _in(0x00000000, 0x51517b11, ped, _fv(x), _fv(y), _fv(z));
};

global.TaskUseMobilePhone = function (ped, use) {
	return _in(0x00000000, 0x417f6ebd, ped, use);
};

global.TaskUseMobilePhoneTimed = function (ped, duration) {
	return _in(0x00000000, 0x0bad1a62, ped, duration);
};

global.TaskUseNearestScenarioToPos = function (ped, Unk476, Unk477, Unk478, Unk479) {
	return _in(0x00000000, 0x743f30b3, ped, Unk476, Unk477, Unk478, Unk479);
};

global.TaskUseNearestScenarioToPosWarp = function (ped, Unk480, Unk481, Unk482, Unk483) {
	return _in(0x00000000, 0x47787a40, ped, Unk480, Unk481, Unk482, Unk483);
};

global.TaskWanderStandard = function (ped) {
	return _in(0x00000000, 0x43f5151f, ped);
};

global.TaskWarpCharIntoCarAsDriver = function (ped, vehicle) {
	return _in(0x00000000, 0x6f363a21, ped, vehicle);
};

global.TaskWarpCharIntoCarAsPassenger = function (ped, vehicle, seatIndex) {
	return _in(0x00000000, 0x06b30cbf, ped, vehicle, seatIndex);
};

global.TellNetPlayerToStartPlaying = function (playerIndex, Unk999) {
	return _in(0x00000000, 0x465d424d, playerIndex, Unk999);
};

global.TerminateAllScriptsForNetworkGame = function () {
	return _in(0x00000000, 0x2cea47e9);
};

global.TerminateAllScriptsWithThisName = function (name) {
	return _in(0x00000000, 0x72452672, _ts(name));
};

global.TerminateThisScript = function () {
	return _in(0x00000000, 0x2bcd1eca);
};

global.ThisScriptIsSafeForNetworkGame = function () {
	return _in(0x00000000, 0x63ab65dc);
};

global.ThisScriptShouldBeSaved = function () {
	return _in(0x00000000, 0x48573cf7);
};

global.Timera = function () {
	return _in(0x00000000, 0x75706300, _r, _ri);
};

global.Timerb = function () {
	return _in(0x00000000, 0x62984ab7, _r, _ri);
};

global.Timerc = function () {
	return _in(0x00000000, 0x1bf55d6f, _r, _ri);
};

global.Timestep = function () {
	return _in(0x00000000, 0x35694ddc, _r, _ri);
};

global.Timestepunwarped = function () {
	return _in(0x00000000, 0x49283645, _r, _rf);
};

global.ToFloat = function (value) {
	return _in(0x00000000, 0x259e305f, value, _r, _rf);
};

global.ToggleCharDucking = function (ped) {
	return _in(0x00000000, 0x265544f9, ped, _r, _ri);
};

global.ToggleToplevelSprite = function (toggle) {
	return _in(0x00000000, 0x51643697, toggle);
};

global.TrainLeaveStation = function (train) {
	return _in(0x00000000, 0x37890b14, train);
};

/**
 * The backing function for TriggerEvent.
 */
global.TriggerEventInternal = function (eventName, eventPayload, payloadLength) {
	return _in(0x00000000, 0x91310870, _ts(eventName), _ts(eventPayload), payloadLength);
};

/**
 * The backing function for TriggerLatentServerEvent.
 */
global.TriggerLatentServerEventInternal = function (eventName, eventPayload, payloadLength, bps) {
	return _in(0x00000000, 0x128737ea, _ts(eventName), _ts(eventPayload), payloadLength, bps);
};

global.TriggerLoadingMusicOnNextFade = function () {
	return _in(0x00000000, 0x1c4b1189);
};

global.TriggerMissionCompleteAudio = function (id) {
	return _in(0x00000000, 0x4baf0213, id);
};

global.TriggerPoliceReport = function (name) {
	return _in(0x00000000, 0x78d01893, _ts(name));
};

global.TriggerPtfx = function (name, x, y, z, Unk1062, Unk1063, Unk1064, flags) {
	return _in(0x00000000, 0x21c44026, _ts(name), _fv(x), _fv(y), _fv(z), _fv(Unk1062), _fv(Unk1063), _fv(Unk1064), flags, _r);
};

global.TriggerPtfxOnObj = function (name, obj, x, y, z, Unk1065, Unk1066, Unk1067, flags) {
	return _in(0x00000000, 0x50307f63, _ts(name), obj, _fv(x), _fv(y), _fv(z), _fv(Unk1065), _fv(Unk1066), _fv(Unk1067), flags, _r);
};

global.TriggerPtfxOnObjBone = function (name, obj, x, y, z, Unk1068, Unk1069, Unk1070, objbone, flags) {
	return _in(0x00000000, 0x3a2a77f9, _ts(name), obj, _fv(x), _fv(y), _fv(z), _fv(Unk1068), _fv(Unk1069), _fv(Unk1070), objbone, flags, _r);
};

global.TriggerPtfxOnPed = function (name, ped, x, y, z, Unk1071, Unk1072, Unk1073, flags) {
	return _in(0x00000000, 0x0a76502f, _ts(name), ped, _fv(x), _fv(y), _fv(z), _fv(Unk1071), _fv(Unk1072), _fv(Unk1073), flags, _r);
};

global.TriggerPtfxOnPedBone = function (name, ped, x, y, z, Unk1074, Unk1075, Unk1076, pedbone, flags) {
	return _in(0x00000000, 0x7d3c3c9d, _ts(name), ped, _fv(x), _fv(y), _fv(z), _fv(Unk1074), _fv(Unk1075), _fv(Unk1076), pedbone, flags, _r);
};

global.TriggerPtfxOnVeh = function (name, veh, x, y, z, Unk1077, Unk1078, Unk1079, Unk1080) {
	return _in(0x00000000, 0x3c7b6092, _ts(name), veh, _fv(x), _fv(y), _fv(z), _fv(Unk1077), _fv(Unk1078), _fv(Unk1079), _fv(Unk1080), _r);
};

/**
 * The backing function for TriggerServerEvent.
 */
global.TriggerServerEventInternal = function (eventName, eventPayload, payloadLength) {
	return _in(0x00000000, 0x7fdd1128, _ts(eventName), _ts(eventPayload), payloadLength);
};

global.TriggerVehAlarm = function (car) {
	return _in(0x00000000, 0x5e5047ac, car);
};

global.TriggerVigilanteCrime = function (id, x, y, z) {
	return _in(0x00000000, 0x195d582e, id, _fv(x), _fv(y), _fv(z));
};

global.TurnCarToFaceCoord = function (car, x, y) {
	return _in(0x00000000, 0x16184716, car, _fv(x), _fv(y));
};

global.TurnOffRadiohudInLobby = function () {
	return _in(0x00000000, 0x4ed6764c);
};

global.TurnOffVehicleExtra = function (veh, extra, turnoff) {
	return _in(0x00000000, 0x05966824, veh, extra, turnoff);
};

global.UnattachCam = function (cam) {
	return _in(0x00000000, 0x278305ae, cam);
};

global.UnfreezeRadioStation = function (radiostation) {
	return _in(0x00000000, 0x3e5b7e59, _ts(radiostation));
};

global.UninheritCamRoll = function (cam) {
	return _in(0x00000000, 0x38ad2830, cam);
};

global.UnloadTextFont = function () {
	return _in(0x00000000, 0x3e0229eb);
};

global.UnlockGenericNewsStory = function (StoryId) {
	return _in(0x00000000, 0x06be0dd3, StoryId);
};

global.UnlockLazlowStation = function () {
	return _in(0x00000000, 0x7b6f4b91);
};

global.UnlockMissionNewsStory = function (id) {
	return _in(0x00000000, 0x2f0718ca, id);
};

global.UnlockRagdoll = function (ped, value) {
	return _in(0x00000000, 0x2f2f51e9, ped, value);
};

global.UnmarkAllRoadNodesAsDontWander = function () {
	return _in(0x00000000, 0x2bba7bf0);
};

global.UnobfuscateInt = function (count, val) {
	return _in(0x00000000, 0x118d1aa3, count, _ii(val) /* may be optional */);
};

global.UnobfuscateIntArray = function (Unk1000, Unk1001) {
	return _in(0x00000000, 0x6314421a, Unk1000, Unk1001);
};

global.UnobfuscateString = function (str) {
	return _in(0x00000000, 0x2186777e, _ts(str), _r, _s);
};

global.UnpauseGame = function () {
	return _in(0x00000000, 0x2a783a43);
};

global.UnpausePlaybackRecordedCar = function (car) {
	return _in(0x00000000, 0x361a01ad, car);
};

global.UnpauseRadio = function () {
	return _in(0x00000000, 0x78f7286f);
};

global.UnpointCam = function (cam) {
	return _in(0x00000000, 0x212b4014, cam);
};

/**
 * Will unregister and cleanup a registered NUI callback handler.
 * Use along side the REGISTER_RAW_NUI_CALLBACK native.
 * @param callbackType The callback type to target
 */
global.UnregisterRawNuiCallback = function (callbackType) {
	return _in(0x00000000, 0x7fb46432, _ts(callbackType));
};

global.UnregisterScriptWithAudio = function () {
	return _in(0x00000000, 0x698f762e);
};

global.UnsetCharMeleeMovementConstaintBox = function (ped) {
	return _in(0x00000000, 0x3ac90796, ped);
};

global.UpdateLoadScene = function () {
	return _in(0x00000000, 0x513d68db, _r);
};

global.UpdateNetworkRelativeScore = function (Unk1002, Unk1003, Unk1004) {
	return _in(0x00000000, 0x384e3f3a, Unk1002, Unk1003, Unk1004);
};

global.UpdateNetworkStatistics = function (playerIndex, ukn0, ukn1, ukn2) {
	return _in(0x00000000, 0x70b45e01, playerIndex, ukn0, ukn1, ukn2);
};

global.UpdatePedPhysicalAttachmentPosition = function (ped, x0, y0, z0, x1, y1) {
	return _in(0x00000000, 0x10a62603, ped, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1));
};

global.UpdatePtfxOffsets = function (ptfx, x, y, z, Unk1081, Unk1082, Unk1083) {
	return _in(0x00000000, 0x45472e9d, ptfx, _fv(x), _fv(y), _fv(z), _fv(Unk1081), _fv(Unk1082), _fv(Unk1083));
};

global.UpdatePtfxTint = function (ptfx, r, g, b, a) {
	return _in(0x00000000, 0x42fc2c31, ptfx, _fv(r), _fv(g), _fv(b), _fv(a));
};

global.UseMask = function (use) {
	return _in(0x00000000, 0x6a9b79d8, use);
};

global.UsePlayerColourInsteadOfTeamColour = function (Unk1005) {
	return _in(0x00000000, 0x759b6bbe, Unk1005);
};

global.UsePreviousFontSettings = function () {
	return _in(0x00000000, 0x36fc5cfb);
};

global.UsingStandardControls = function () {
	return _in(0x00000000, 0x5f4571e5, _r);
};

global.Vdist = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x4674049b, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r, _rf);
};

global.Vdist2 = function (x0, y0, z0, x1, y1, z1) {
	return _in(0x00000000, 0x69ae0805, _fv(x0), _fv(y0), _fv(z0), _fv(x1), _fv(y1), _fv(z1), _r, _rf);
};

global.VehicleCanBeTargettedByHsMissile = function (car, set) {
	return _in(0x00000000, 0x27607f64, car, set);
};

global.VehicleDoesProvideCover = function (veh, cover) {
	return _in(0x00000000, 0x0c4f5021, veh, cover);
};

global.Vmag = function (x, y, z) {
	return _in(0x00000000, 0x405b02b7, _fv(x), _fv(y), _fv(z), _r, _rf);
};

global.Vmag2 = function (x, y, z) {
	return _in(0x00000000, 0x787206f8, _fv(x), _fv(y), _fv(z), _r, _rf);
};

global.Wait = function (timeMS) {
	return _in(0x00000000, 0x266716ac, timeMS);
};

global.WantedStarsAreFlashing = function () {
	return _in(0x00000000, 0x00746edf, _r);
};

global.WarpCharFromCarToCar = function (ped, vehicle, seatIndex) {
	return _in(0x00000000, 0x3ae77439, ped, vehicle, seatIndex);
};

global.WarpCharFromCarToCoord = function (ped, x, y, z) {
	return _in(0x00000000, 0x6a77506a, ped, _fv(x), _fv(y), _fv(z));
};

global.WarpCharIntoCar = function (ped, vehicle) {
	return _in(0x00000000, 0x73d3504a, ped, vehicle);
};

global.WarpCharIntoCarAsPassenger = function (ped, vehicle, seatIndex) {
	return _in(0x00000000, 0x172376fe, ped, vehicle, seatIndex);
};

global.WasCutsceneSkipped = function () {
	return _in(0x00000000, 0x18f01e80, _r);
};

/**
 * Returns whether or not the currently executing event was canceled.
 * @return A boolean.
 */
global.WasEventCanceled = function () {
	return _in(0x00000000, 0x58382a19, _r);
};

global.WasPedKilledByHeadshot = function (ped) {
	return _in(0x00000000, 0x084f7b9f, ped, _r);
};

global.WasPedSkeletonUpdated = function (ped) {
	return _in(0x00000000, 0x3e8443e0, ped, _r);
};

global.WashVehicleTextures = function (vehicle, intensity) {
	return _in(0x00000000, 0x69491cfa, vehicle, intensity);
};

global.WhatWillPlayerPickup = function (player) {
	return _in(0x00000000, 0x2f9b0583, player, _r, _ri);
};

global.WinchCanPickObjectUp = function (obj, can) {
	return _in(0x00000000, 0x73246fc0, obj, can);
};

global.m = function (cam, heading) {
	return _in(0x00000000, 0x3970702e, cam, heading);
};

