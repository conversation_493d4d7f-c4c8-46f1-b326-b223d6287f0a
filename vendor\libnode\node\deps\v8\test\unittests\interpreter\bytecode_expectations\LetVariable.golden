#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  let x = 10;
"
frame size: 1
parameter count: 1
bytecode array length: 5
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
                B(LdaUndefined),
  /*   46 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  let x = 10; return x;
"
frame size: 1
parameter count: 1
bytecode array length: 4
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   55 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  let x = (x = 20);
"
frame size: 2
parameter count: 1
bytecode array length: 14
bytecodes: [
                B(LdaTheHole),
                B(Star0),
  /*   42 S> */ B(LdaSmi), I8(20),
                B(Star1),
                B(Ldar), R(0),
  /*   45 E> */ B(ThrowReferenceErrorIfHole), U8(0),
                B(Mov), R(1), R(0),
                B(LdaUndefined),
  /*   52 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["x"],
]
handlers: [
]

---
snippet: "
  let x = 10; x = 20;
"
frame size: 1
parameter count: 1
bytecode array length: 8
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(10),
                B(Star0),
  /*   46 S> */ B(LdaSmi), I8(20),
                B(Star0),
                B(LdaUndefined),
  /*   54 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

