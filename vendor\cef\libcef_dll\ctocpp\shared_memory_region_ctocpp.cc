// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=fd0a4c1bd80a53778d6e40e4ebcd9d4484f91269$
//

#include "libcef_dll/ctocpp/shared_memory_region_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") bool CefSharedMemoryRegionCToCpp::IsValid() {
  shutdown_checker::AssertNotShutdown();

  cef_shared_memory_region_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_valid))
    return false;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_valid(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") size_t CefSharedMemoryRegionCToCpp::Size() {
  shutdown_checker::AssertNotShutdown();

  cef_shared_memory_region_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, size))
    return 0;

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  size_t _retval = _struct->size(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall") const void* CefSharedMemoryRegionCToCpp::Memory() {
  shutdown_checker::AssertNotShutdown();

  cef_shared_memory_region_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, memory))
    return nullptr;

  // Execute
  const void* _retval = _struct->memory(_struct);

  // Return type: simple
  return _retval;
}

// CONSTRUCTOR - Do not edit by hand.

CefSharedMemoryRegionCToCpp::CefSharedMemoryRegionCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefSharedMemoryRegionCToCpp::~CefSharedMemoryRegionCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_shared_memory_region_t* CefCToCppRefCounted<
    CefSharedMemoryRegionCToCpp,
    CefSharedMemoryRegion,
    cef_shared_memory_region_t>::UnwrapDerived(CefWrapperType type,
                                               CefSharedMemoryRegion* c) {
  NOTREACHED() << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefSharedMemoryRegionCToCpp,
                                   CefSharedMemoryRegion,
                                   cef_shared_memory_region_t>::kWrapperType =
    WT_SHARED_MEMORY_REGION;
