// Copyright 2013 the V8 project authors. All rights reserved.
// Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions
// are met:
// 1.  Redistributions of source code must retain the above copyright
//     notice, this list of conditions and the following disclaimer.
// 2.  Redistributions in binary form must reproduce the above copyright
//     notice, this list of conditions and the following disclaimer in the
//     documentation and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
// EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
// ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

description(
"This tests that storing into something that is not array does not crash."
);

theCode = "\n\
function storeFooByValOrDoArithmetic(o, p1, p2, v) {\n\
    var x;\n\
    if (p1) {\n\
        x = o.foo;\n\
    } else {\n\
        x = v;\n\
        if (p2) {\n\
            x--;\n\
        } else {\n\
            x++;\n\
        }\n\
    }\n\
    x[5] = \"foo\";\n\
}\n\
\n\
function runTheTest(p1, p2) {\n\
    var o = new Object();\n\
    o.foo = new Object();\n\
    storeFooByValOrDoArithmetic(o, p1, p2, 1);\n\
    return o.foo[5];\n\
}\n";

function runWithPredicates(predicateArray) {
    var myCode = theCode;

    for (var i = 0; i < predicateArray.length; ++i) {
        myCode += "result = runTheTest(" + predicateArray[i][0] + ", " + predicateArray[i][1] + ");\n";
        myCode += "shouldBe(\"result\", " + predicateArray[i][2] + ");\n";
    }

    eval(myCode);
}

runWithPredicates([[true, true, "\"\\\"foo\\\"\""], [true, false, "\"\\\"foo\\\"\""], [false, true, "\"undefined\""], [false, false, "\"undefined\""]]);
runWithPredicates([[false, false, "\"undefined\""], [true, false, "\"\\\"foo\\\"\""], [false, true, "\"undefined\""], [true, true, "\"\\\"foo\\\"\""]]);
runWithPredicates([[true, true, "\"\\\"foo\\\"\""], [false, true, "\"undefined\""], [true, false, "\"\\\"foo\\\"\""], [false, false, "\"undefined\""]]);
runWithPredicates([[false, false, "\"undefined\""], [false, true, "\"undefined\""], [true, false, "\"\\\"foo\\\"\""], [true, true, "\"\\\"foo\\\"\""]]);
