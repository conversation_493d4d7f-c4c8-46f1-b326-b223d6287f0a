#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      a;
      ['b'];
    }
  
    class B {
      a = 1;
      ['b'] = this.a;
    }
    new A;
    new B;
  }
"
frame size: 8
parameter count: 1
bytecode array length: 102
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
  /*   60 S> */ B(LdaConstant), U8(3),
                B(StaCurrentContextSlot), U8(2),
                B(Star7),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(4),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(SetNamedProperty), R(3), U8(5), U8(0),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(6),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(8), U8(2), U8(2),
                B(Star3),
                B(LdaConstant), U8(7),
                B(Star4),
  /*   99 S> */ B(LdaConstant), U8(3),
                B(StaCurrentContextSlot), U8(2),
                B(Star7),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(4),
                B(CreateClosure), U8(9), U8(3), U8(2),
                B(SetNamedProperty), R(3), U8(5), U8(2),
                B(PopContext), R(2),
                B(Mov), R(5), R(1),
  /*  120 S> */ B(Ldar), R(0),
  /*  120 E> */ B(Construct), R(0), R(0), U8(0), U8(4),
  /*  129 S> */ B(Ldar), R(1),
  /*  129 E> */ B(Construct), R(1), R(0), U8(0), U8(6),
                B(LdaUndefined),
  /*  138 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A extends class {} {
      a;
      ['b'];
    }
  
    class B extends class {} {
      a = 1;
      ['b'] = this.a;
      foo() { return 1; }
      constructor() {
        super();
      }
    }
  
    class C extends B {
      a = 1;
      ['b'] = this.a;
      constructor() {
        (() => super())();
      }
    }
  
    new A;
    new B;
    new C;
  }
"
frame size: 12
parameter count: 1
bytecode array length: 196
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star8),
                B(LdaConstant), U8(2),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star4),
                B(LdaConstant), U8(1),
                B(Star5),
  /*   77 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
                B(Mov), R(4), R(6),
                B(Mov), R(10), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(4),
                B(CreateClosure), U8(6), U8(2), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(0),
                B(PopContext), R(3),
                B(Mov), R(6), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(8),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
                B(LdaTheHole),
                B(Star11),
                B(CreateClosure), U8(11), U8(3), U8(2),
                B(Star8),
                B(LdaConstant), U8(10),
                B(Star9),
                B(Mov), R(8), R(10),
                B(CallRuntime), U16(Runtime::kDefineClass), R(9), U8(3),
                B(CreateClosure), U8(12), U8(4), U8(2),
                B(Star4),
                B(LdaConstant), U8(9),
                B(Star5),
  /*  133 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
                B(CreateClosure), U8(13), U8(5), U8(2),
                B(Star9),
                B(Mov), R(4), R(6),
                B(Mov), R(10), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(5),
                B(CreateClosure), U8(14), U8(6), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(2),
                B(PopContext), R(3),
                B(Mov), R(6), R(1),
  /*   90 E> */ B(CreateBlockContext), U8(15),
                B(PushContext), R(3),
                B(LdaTheHole),
                B(StaCurrentContextSlot), U8(2),
  /*  236 E> */ B(CreateClosure), U8(17), U8(7), U8(2),
                B(Star4),
                B(LdaConstant), U8(16),
                B(Star5),
  /*  256 S> */ B(LdaConstant), U8(5),
                B(StaCurrentContextSlot), U8(2),
                B(Star8),
                B(Mov), R(4), R(6),
                B(Mov), R(1), R(7),
                B(CallRuntime), U16(Runtime::kDefineClass), R(5), U8(4),
                B(CreateClosure), U8(18), U8(8), U8(2),
                B(SetNamedProperty), R(4), U8(7), U8(4),
                B(PopContext), R(3),
                B(Mov), R(6), R(2),
  /*  329 S> */ B(Ldar), R(0),
  /*  329 E> */ B(Construct), R(0), R(0), U8(0), U8(6),
  /*  338 S> */ B(Ldar), R(1),
  /*  338 E> */ B(Construct), R(1), R(0), U8(0), U8(8),
  /*  347 S> */ B(Ldar), R(2),
  /*  347 E> */ B(Construct), R(2), R(0), U8(0), U8(10),
                B(LdaUndefined),
  /*  356 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["b"],
  SHARED_FUNCTION_INFO_TYPE,
  SYMBOL_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

