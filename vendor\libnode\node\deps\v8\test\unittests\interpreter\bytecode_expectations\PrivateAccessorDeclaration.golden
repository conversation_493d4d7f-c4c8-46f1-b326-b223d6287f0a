#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  {
    class A {
      get #a() { return 1; }
      set #a(val) { }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 56
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaConstant), U8(2),
                B(Star3),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(3), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star3),
                B(CreateClosure), U8(5), U8(2), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*  101 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["A"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class B {
      get #b() { return 1; }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 53
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaConstant), U8(2),
                B(Star3),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(3), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star3),
                B(LdaNull),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*   81 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["B"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class C {
      set #c(val) { }
    }
  }
"
frame size: 6
parameter count: 1
bytecode array length: 53
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaConstant), U8(2),
                B(Star3),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(3), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(LdaNull),
                B(Star3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(3), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(1),
                B(Mov), R(2), R(0),
                B(LdaUndefined),
  /*   74 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["C"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class D {
      get #d() { return 1; }
      set #d(val) { }
    }
  
    class E extends D {
      get #e() { return 2; }
      set #e(val) { }
    }
  }
"
frame size: 7
parameter count: 1
bytecode array length: 111
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaConstant), U8(2),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(3), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(CreateClosure), U8(4), U8(1), U8(2),
                B(Star4),
                B(CreateClosure), U8(5), U8(2), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(4), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(2),
                B(Mov), R(3), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(6),
                B(PushContext), R(2),
                B(LdaConstant), U8(8),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
  /*  118 E> */ B(CreateClosure), U8(9), U8(3), U8(2),
                B(Star3),
                B(LdaConstant), U8(7),
                B(Star4),
                B(Mov), R(3), R(5),
                B(Mov), R(0), R(6),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(CreateClosure), U8(10), U8(4), U8(2),
                B(Star4),
                B(CreateClosure), U8(11), U8(5), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(4), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(2),
                B(Mov), R(3), R(1),
                B(LdaUndefined),
  /*  175 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["D"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["E"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A { foo() {} }
    class C extends A {
      get #a() { return super.foo; }
    }
    new C();
  }
"
frame size: 8
parameter count: 1
bytecode array length: 99
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star7),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(4),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(4),
                B(PushContext), R(2),
                B(LdaConstant), U8(6),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
  /*   77 E> */ B(CreateClosure), U8(7), U8(2), U8(2),
                B(Star3),
                B(LdaConstant), U8(5),
                B(Star4),
                B(Mov), R(3), R(5),
                B(Mov), R(0), R(6),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(StaCurrentContextSlot), U8(4),
                B(Ldar), R(5),
                B(StaCurrentContextSlot), U8(5),
                B(CreateClosure), U8(8), U8(3), U8(2),
                B(Star4),
                B(LdaNull),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(4), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(2),
                B(Mov), R(3), R(1),
  /*  122 S> */ B(Ldar), R(1),
  /*  122 E> */ B(Construct), R(1), R(0), U8(0), U8(0),
                B(LdaUndefined),
  /*  133 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["C"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

---
snippet: "
  {
    class A { foo(val) {} }
    class C extends A {
      set #a(val) { super.foo(val); }
    }
    new C();
  }
"
frame size: 8
parameter count: 1
bytecode array length: 99
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(2),
                B(LdaTheHole),
                B(Star6),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star3),
                B(LdaConstant), U8(1),
                B(Star4),
                B(CreateClosure), U8(3), U8(1), U8(2),
                B(Star7),
                B(Mov), R(3), R(5),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(4),
                B(PopContext), R(2),
                B(Mov), R(5), R(0),
  /*   38 E> */ B(CreateBlockContext), U8(4),
                B(PushContext), R(2),
                B(LdaConstant), U8(6),
                B(Star4),
                B(CallRuntime), U16(Runtime::kCreatePrivateBrandSymbol), R(4), U8(1),
                B(StaCurrentContextSlot), U8(3),
  /*   80 E> */ B(CreateClosure), U8(7), U8(2), U8(2),
                B(Star3),
                B(LdaConstant), U8(5),
                B(Star4),
                B(Mov), R(3), R(5),
                B(Mov), R(0), R(6),
                B(CallRuntime), U16(Runtime::kDefineClass), R(4), U8(3),
                B(StaCurrentContextSlot), U8(4),
                B(Ldar), R(5),
                B(StaCurrentContextSlot), U8(5),
                B(LdaNull),
                B(Star4),
                B(CreateClosure), U8(8), U8(3), U8(2),
                B(Star5),
                B(CallRuntime), U16(Runtime::kCreatePrivateAccessors), R(4), U8(2),
                B(StaCurrentContextSlot), U8(2),
                B(PopContext), R(2),
                B(Mov), R(3), R(1),
  /*  126 S> */ B(Ldar), R(1),
  /*  126 E> */ B(Construct), R(1), R(0), U8(0), U8(0),
                B(LdaUndefined),
  /*  137 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["C"],
  SHARED_FUNCTION_INFO_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
]
handlers: [
]

