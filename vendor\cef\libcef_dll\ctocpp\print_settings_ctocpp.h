// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=e74e75adf68001ef29e441fa1bbac27e3aa5c3c1$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_PRINT_SETTINGS_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_PRINT_SETTINGS_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_print_settings_capi.h"
#include "include/cef_print_settings.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefPrintSettingsCToCpp
    : public CefCToCppRefCounted<CefPrintSettingsCToCpp,
                                 CefPrintSettings,
                                 cef_print_settings_t> {
 public:
  CefPrintSettingsCToCpp();
  virtual ~CefPrintSettingsCToCpp();

  // CefPrintSettings methods.
  bool IsValid() override;
  bool IsReadOnly() override;
  void SetOrientation(bool landscape) override;
  bool IsLandscape() override;
  void SetPrinterPrintableArea(const CefSize& physical_size_device_units,
                               const CefRect& printable_area_device_units,
                               bool landscape_needs_flip) override;
  void SetDeviceName(const CefString& name) override;
  CefString GetDeviceName() override;
  void SetDPI(int dpi) override;
  int GetDPI() override;
  void SetPageRanges(const PageRangeList& ranges) override;
  size_t GetPageRangesCount() override;
  void GetPageRanges(PageRangeList& ranges) override;
  void SetSelectionOnly(bool selection_only) override;
  bool IsSelectionOnly() override;
  void SetCollate(bool collate) override;
  bool WillCollate() override;
  void SetColorModel(ColorModel model) override;
  ColorModel GetColorModel() override;
  void SetCopies(int copies) override;
  int GetCopies() override;
  void SetDuplexMode(DuplexMode mode) override;
  DuplexMode GetDuplexMode() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_PRINT_SETTINGS_CTOCPP_H_
