#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
top level: yes

---
snippet: "
  
        l = {
          'aa': 1.1,
          'bb': 2.2
        };
  
        v = l['aa'] + l['bb'];
        l['bb'] = 7;
        l['aa'] = l['bb'];
       
"
frame size: 3
parameter count: 1
bytecode array length: 63
bytecodes: [
  /*    7 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
  /*    9 E> */ B(StaGlobal), U8(1), U8(1),
  /*   66 S> */ B(LdaGlobal), U8(1), U8(4),
                B(Star1),
  /*   71 E> */ B(GetNamedProperty), R(1), U8(2), U8(6),
                B(Star1),
  /*   80 E> */ B(LdaGlobal), U8(1), U8(4),
                B(Star2),
  /*   81 E> */ B(GetNamedProperty), R(2), U8(3), U8(8),
  /*   78 E> */ B(Add), R(1), U8(3),
  /*   68 E> */ B(StaGlobal), U8(4), U8(10),
  /*   95 S> */ B(LdaGlobal), U8(1), U8(4),
                B(Star1),
                B(LdaSmi), I8(7),
  /*  103 E> */ B(SetNamedProperty), R(1), U8(3), U8(12),
  /*  114 S> */ B(LdaGlobal), U8(1), U8(4),
                B(Star1),
  /*  124 E> */ B(LdaGlobal), U8(1), U8(4),
                B(Star2),
  /*  125 E> */ B(GetNamedProperty), R(2), U8(3), U8(8),
                B(Star2),
  /*  122 E> */ B(SetNamedProperty), R(1), U8(2), U8(14),
                B(Mov), R(2), R(0),
                B(Ldar), R(0),
  /*  139 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["l"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["aa"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["bb"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["v"],
]
handlers: [
]

---
snippet: "
  
        l = {
          'cc': 3.1,
          'dd': 4.2
        };
        if (l['cc'] < 3) {
          l['cc'] = 3;
        } else {
          l['dd'] = 3;
        }
       
"
frame size: 3
parameter count: 1
bytecode array length: 60
bytecodes: [
  /*    7 S> */ B(CreateObjectLiteral), U8(0), U8(0), U8(41),
  /*    9 E> */ B(StaGlobal), U8(1), U8(1),
  /*   65 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
  /*   70 E> */ B(GetNamedProperty), R(1), U8(2), U8(5),
                B(Star1),
                B(LdaSmi), I8(3),
  /*   77 E> */ B(TestLessThan), R(1), U8(7),
                B(JumpIfFalse), U8(20),
  /*   92 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
                B(LdaSmi), I8(3),
                B(Star2),
  /*  100 E> */ B(SetNamedProperty), R(1), U8(2), U8(8),
                B(Mov), R(2), R(0),
                B(Ldar), R(2),
                B(Jump), U8(18),
  /*  128 S> */ B(LdaGlobal), U8(1), U8(3),
                B(Star1),
                B(LdaSmi), I8(3),
                B(Star2),
  /*  136 E> */ B(SetNamedProperty), R(1), U8(3), U8(10),
                B(Mov), R(2), R(0),
                B(Ldar), R(2),
                B(Ldar), R(0),
  /*  155 S> */ B(Return),
]
constant pool: [
  OBJECT_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["l"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["cc"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["dd"],
]
handlers: [
]

