#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  var a = 1;
  var b = 2;
  return a + b + 'string';
"
frame size: 3
parameter count: 1
bytecode array length: 18
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(Ldar), R(1),
  /*   65 E> */ B(Add), R(0), U8(0),
                B(Star2),
                B(LdaConstant), U8(0),
  /*   69 E> */ B(Add), R(2), U8(1),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return 'string' + a + b;
"
frame size: 3
parameter count: 1
bytecode array length: 21
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
                B(Star2),
                B(Ldar), R(0),
  /*   72 E> */ B(Add), R(2), U8(0),
                B(Star2),
                B(Ldar), R(1),
  /*   76 E> */ B(Add), R(2), U8(1),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return a + 'string' + b;
"
frame size: 3
parameter count: 1
bytecode array length: 18
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
  /*   65 E> */ B(Add), R(0), U8(0),
                B(Star2),
                B(Ldar), R(1),
  /*   76 E> */ B(Add), R(2), U8(1),
  /*   80 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return 'foo' + a + 'bar' + b + 'baz' + 1;
"
frame size: 3
parameter count: 1
bytecode array length: 36
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
                B(Star2),
                B(Ldar), R(0),
  /*   69 E> */ B(Add), R(2), U8(0),
                B(Star2),
                B(LdaConstant), U8(1),
  /*   73 E> */ B(Add), R(2), U8(1),
                B(Star2),
                B(Ldar), R(1),
  /*   81 E> */ B(Add), R(2), U8(2),
                B(Star2),
                B(LdaConstant), U8(2),
  /*   85 E> */ B(Add), R(2), U8(3),
  /*   93 E> */ B(AddSmi), I8(1), U8(4),
  /*   97 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["foo"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["bar"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["baz"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  return (a + 'string') + ('string' + b);
"
frame size: 4
parameter count: 1
bytecode array length: 24
bytecodes: [
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   56 S> */ B(LdaConstant), U8(0),
  /*   66 E> */ B(Add), R(0), U8(0),
                B(Star2),
                B(LdaConstant), U8(0),
                B(Star3),
                B(Ldar), R(1),
  /*   90 E> */ B(Add), R(3), U8(1),
  /*   78 E> */ B(Add), R(2), U8(2),
  /*   95 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

---
snippet: "
  var a = 1;
  var b = 2;
  function foo(a, b) { };
  return 'string' + foo(a, b) + a + b;
"
frame size: 4
parameter count: 1
bytecode array length: 35
bytecodes: [
  /*   30 E> */ B(CreateClosure), U8(0), U8(0), U8(2),
                B(Star2),
  /*   42 S> */ B(LdaSmi), I8(1),
                B(Star0),
  /*   53 S> */ B(LdaSmi), I8(2),
                B(Star1),
  /*   80 S> */ B(LdaConstant), U8(1),
                B(Star3),
  /*   98 E> */ B(CallUndefinedReceiver2), R(2), R(0), R(1), U8(0),
  /*   96 E> */ B(Add), R(3), U8(2),
                B(Star3),
                B(Ldar), R(0),
  /*  108 E> */ B(Add), R(3), U8(3),
                B(Star3),
                B(Ldar), R(1),
  /*  112 E> */ B(Add), R(3), U8(4),
  /*  116 S> */ B(Return),
]
constant pool: [
  SHARED_FUNCTION_INFO_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["string"],
]
handlers: [
]

