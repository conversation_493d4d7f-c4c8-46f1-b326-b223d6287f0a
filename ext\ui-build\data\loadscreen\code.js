(()=>{"use strict";var e={759:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(645),o=n.n(r),i=n(667),a=n.n(i),l=new URL(n(896),n.b),c=o()((function(e){return e[1]})),u=a()(l);c.push([e.id,":root{--s: calc(100vh / 720)}*{margin:0;padding:0;user-select:none}body{background-image:url("+u+');background-size:cover;background-repeat:no-repeat;background-attachment:fixed;font-family:"Segoe UI",sans-serif;font-size:calc(16 * var(--s))}#root{height:100vh;position:relative}@media only screen and (min-aspect-ratio: 17/9){#root{width:calc(100% - calc(100vw * ((17 - 16) / 17)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 18/9){#root{width:calc(100% - calc(100vw * ((18 - 16) / 18)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 19/9){#root{width:calc(100% - calc(100vw * ((19 - 16) / 19)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 20/9){#root{width:calc(100% - calc(100vw * ((20 - 16) / 20)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 21/9){#root{width:calc(100% - calc(100vw * ((21 - 16) / 21)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 22/9){#root{width:calc(100% - calc(100vw * ((22 - 16) / 22)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 23/9){#root{width:calc(100% - calc(100vw * ((23 - 16) / 23)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 24/9){#root{width:calc(100% - calc(100vw * ((24 - 16) / 24)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 25/9){#root{width:calc(100% - calc(100vw * ((25 - 16) / 25)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 26/9){#root{width:calc(100% - calc(100vw * ((26 - 16) / 26)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 27/9){#root{width:calc(100% - calc(100vw * ((27 - 16) / 27)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 28/9){#root{width:calc(100% - calc(100vw * ((28 - 16) / 28)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 29/9){#root{width:calc(100% - calc(100vw * ((29 - 16) / 29)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 30/9){#root{width:calc(100% - calc(100vw * ((30 - 16) / 30)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 31/9){#root{width:calc(100% - calc(100vw * ((31 - 16) / 31)) - 5vw);margin-left:auto;margin-right:auto}}@media only screen and (min-aspect-ratio: 32/9){#root{width:calc(100% - calc(100vw * ((32 - 16) / 32)) - 5vw);margin-left:auto;margin-right:auto}}.progressList{list-style:none}.progressList dl{height:calc(20 * var(--s))}.progressList dt{display:inline-block;height:100%;line-height:calc(20 * var(--s));vertical-align:middle;margin:calc(10 * var(--s));margin-top:0px;margin-bottom:0px;font-size:calc(18 * var(--s));width:calc(120 * var(--s));color:#fff}.progressList dd{display:inline-block;height:100%;vertical-align:middle;position:relative}.progressList dd span,.progressList dd i{position:absolute;top:0px;left:0px;display:block;height:calc(20 * var(--s))}.progressList dd span{transition:width 200ms linear}.progressList dd i{width:100%}.progressList .not-done i{animation:glowy 750ms ease-in-out infinite alternate}.log{position:absolute;left:calc(10 * var(--s));right:calc(10 * var(--s));bottom:calc(10 * var(--s));top:calc(10 * var(--s));-webkit-mask-image:linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, black 20%);z-index:-999;overflow:hidden;color:#fff;display:flex;flex-direction:column;justify-content:flex-end;list-style:none;font-size:70%;font-family:"Lucida Console",monospace}.log li:before{content:"> "}.log li:last-child:before{content:""}.log li:last-child:after{overflow:hidden;display:inline-block;vertical-align:bottom;animation:ellipsis steps(4, end) 1500ms infinite;content:"...";width:0px}@keyframes glowy{from{opacity:1}to{opacity:.7}}@keyframes ellipsis{to{width:2.1em}}',""]);const s=c},645:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var i=0;i<this.length;i++){var a=this[i][0];null!=a&&(o[a]=!0)}for(var l=0;l<e.length;l++){var c=[].concat(e[l]);r&&o[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),t.push(c))}},t}},667:e=>{e.exports=function(e,t){return t||(t={}),e?(e=String(e.__esModule?e.default:e),/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]|(%20)/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e):e}},379:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},a=[],l=0;l<e.length;l++){var c=e[l],u=r.base?c[0]+r.base:c[0],s=i[u]||0,_="".concat(u," ").concat(s);i[u]=s+1;var f=n(_),d={css:c[1],media:c[2],sourceMap:c[3]};-1!==f?(t[f].references++,t[f].updater(d)):t.push({identifier:_,updater:o(d,r),references:1}),a.push(_)}return a}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var l=n(i[a]);t[l].references--}for(var c=r(e,o),u=0;u<i.length;u++){var s=n(i[u]);0===t[s].references&&(t[s].updater(),t.splice(s,1))}i=c}}},569:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},216:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t),t}},565:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},795:e=>{e.exports=function(e){var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r=n.css,o=n.media,i=n.sourceMap;o?e.setAttribute("media",o):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},896:(e,t,n)=>{e.exports=n.p+"ea097d53311d8e118a31.jpg"}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={id:r,exports:{}};return e[r](i,i.exports,n),i.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");r.length&&(e=r[r.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e})(),n.b=document.baseURI||self.location.href,n.nc=void 0,(()=>{var e,t,r,o,i,a,l={},c=[],u=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function s(e,t){for(var n in t)e[n]=t[n];return e}function _(e){var t=e.parentNode;t&&t.removeChild(e)}function f(t,n,r){var o,i,a,l={};for(a in n)"key"==a?o=n[a]:"ref"==a?i=n[a]:l[a]=n[a];if(arguments.length>2&&(l.children=arguments.length>3?e.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===l[a]&&(l[a]=t.defaultProps[a]);return d(t,l,o,i,null)}function d(e,n,o,i,a){var l={type:e,props:n,key:o,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==a?++r:a};return null!=t.vnode&&t.vnode(l),l}function p(e){return e.children}function h(e,t){this.props=e,this.context=t}function m(e,t){if(null==t)return e.__?m(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?m(e):null}function v(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return v(e)}}function y(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!g.__r++||a!==t.debounceRendering)&&((a=t.debounceRendering)||i)(g)}function g(){for(var e;g.__r=o.length;)e=o.sort((function(e,t){return e.__v.__b-t.__v.__b})),o=[],e.some((function(e){var t,n,r,o,i,a;e.__d&&(i=(o=(t=e).__v).__e,(a=t.__P)&&(n=[],(r=s({},o)).__v=o.__v+1,C(a,o,r,t.__n,void 0!==a.ownerSVGElement,null!=o.__h?[i]:null,n,null==i?m(o):i,o.__h),T(n,o),o.__e!=i&&v(o)))}))}function w(e,t,n,r,o,i,a,u,s,_){var f,h,v,y,g,w,I,x=r&&r.__k||c,E=x.length;for(n.__k=[],f=0;f<t.length;f++)if(null!=(y=n.__k[f]=null==(y=t[f])||"boolean"==typeof y?null:"string"==typeof y||"number"==typeof y||"bigint"==typeof y?d(null,y,null,null,y):Array.isArray(y)?d(p,{children:y},null,null,null):y.__b>0?d(y.type,y.props,y.key,null,y.__v):y)){if(y.__=n,y.__b=n.__b+1,null===(v=x[f])||v&&y.key==v.key&&y.type===v.type)x[f]=void 0;else for(h=0;h<E;h++){if((v=x[h])&&y.key==v.key&&y.type===v.type){x[h]=void 0;break}v=null}C(e,y,v=v||l,o,i,a,u,s,_),g=y.__e,(h=y.ref)&&v.ref!=h&&(I||(I=[]),v.ref&&I.push(v.ref,null,y),I.push(h,y.__c||g,y)),null!=g?(null==w&&(w=g),"function"==typeof y.type&&null!=y.__k&&y.__k===v.__k?y.__d=s=b(y,s,e):s=k(e,y,v,x,g,s),_||"option"!==n.type?"function"==typeof n.type&&(n.__d=s):e.value=""):s&&v.__e==s&&s.parentNode!=e&&(s=m(v))}for(n.__e=w,f=E;f--;)null!=x[f]&&("function"==typeof n.type&&null!=x[f].__e&&x[f].__e==n.__d&&(n.__d=m(r,f+1)),L(x[f],x[f]));if(I)for(f=0;f<I.length;f++)N(I[f],I[++f],I[++f])}function b(e,t,n){var r,o;for(r=0;r<e.__k.length;r++)(o=e.__k[r])&&(o.__=e,t="function"==typeof o.type?b(o,t,n):k(n,o,o,e.__k,o.__e,t));return t}function k(e,t,n,r,o,i){var a,l,c;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||o!=i||null==o.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(o),a=null;else{for(l=i,c=0;(l=l.nextSibling)&&c<r.length;c+=2)if(l==o)break e;e.insertBefore(o,i),a=i}return void 0!==a?a:o.nextSibling}function I(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||u.test(t)?n:n+"px"}function x(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||I(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||I(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?S:E,i):e.removeEventListener(t,i?S:E,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function E(e){this.l[e.type+!1](t.event?t.event(e):e)}function S(e){this.l[e.type+!0](t.event?t.event(e):e)}function C(e,n,r,o,i,a,l,c,u){var _,f,d,m,v,y,g,b,k,I,x,E=n.type;if(void 0!==n.constructor)return null;null!=r.__h&&(u=r.__h,c=n.__e=r.__e,n.__h=null,a=[c]),(_=t.__b)&&_(n);try{e:if("function"==typeof E){if(b=n.props,k=(_=E.contextType)&&o[_.__c],I=_?k?k.props.value:_.__:o,r.__c?g=(f=n.__c=r.__c).__=f.__E:("prototype"in E&&E.prototype.render?n.__c=f=new E(b,I):(n.__c=f=new h(b,I),f.constructor=E,f.render=P),k&&k.sub(f),f.props=b,f.state||(f.state={}),f.context=I,f.__n=o,d=f.__d=!0,f.__h=[]),null==f.__s&&(f.__s=f.state),null!=E.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=s({},f.__s)),s(f.__s,E.getDerivedStateFromProps(b,f.__s))),m=f.props,v=f.state,d)null==E.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==E.getDerivedStateFromProps&&b!==m&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(b,I),!f.__e&&null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(b,f.__s,I)||n.__v===r.__v){f.props=b,f.state=f.__s,n.__v!==r.__v&&(f.__d=!1),f.__v=n,n.__e=r.__e,n.__k=r.__k,n.__k.forEach((function(e){e&&(e.__=n)})),f.__h.length&&l.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(b,f.__s,I),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(m,v,y)}))}f.context=I,f.props=b,f.state=f.__s,(_=t.__r)&&_(n),f.__d=!1,f.__v=n,f.__P=e,_=f.render(f.props,f.state,f.context),f.state=f.__s,null!=f.getChildContext&&(o=s(s({},o),f.getChildContext())),d||null==f.getSnapshotBeforeUpdate||(y=f.getSnapshotBeforeUpdate(m,v)),x=null!=_&&_.type===p&&null==_.key?_.props.children:_,w(e,Array.isArray(x)?x:[x],n,r,o,i,a,l,c,u),f.base=n.__e,n.__h=null,f.__h.length&&l.push(f),g&&(f.__E=f.__=null),f.__e=!1}else null==a&&n.__v===r.__v?(n.__k=r.__k,n.__e=r.__e):n.__e=O(r.__e,n,r,o,i,a,l,u);(_=t.diffed)&&_(n)}catch(e){n.__v=null,(u||null!=a)&&(n.__e=c,n.__h=!!u,a[a.indexOf(c)]=null),t.__e(e,n,r)}}function T(e,n){t.__c&&t.__c(n,e),e.some((function(n){try{e=n.__h,n.__h=[],e.some((function(e){e.call(n)}))}catch(e){t.__e(e,n.__v)}}))}function O(t,n,r,o,i,a,c,u){var s,f,d,p=r.props,h=n.props,v=n.type,y=0;if("svg"===v&&(i=!0),null!=a)for(;y<a.length;y++)if((s=a[y])&&(s===t||(v?s.localName==v:3==s.nodeType))){t=s,a[y]=null;break}if(null==t){if(null===v)return document.createTextNode(h);t=i?document.createElementNS("http://www.w3.org/2000/svg",v):document.createElement(v,h.is&&h),a=null,u=!1}if(null===v)p===h||u&&t.data===h||(t.data=h);else{if(a=a&&e.call(t.childNodes),f=(p=r.props||l).dangerouslySetInnerHTML,d=h.dangerouslySetInnerHTML,!u){if(null!=a)for(p={},y=0;y<t.attributes.length;y++)p[t.attributes[y].name]=t.attributes[y].value;(d||f)&&(d&&(f&&d.__html==f.__html||d.__html===t.innerHTML)||(t.innerHTML=d&&d.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||x(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||x(e,i,t[i],n[i],r)}(t,h,p,i,u),d)n.__k=[];else if(y=n.props.children,w(t,Array.isArray(y)?y:[y],n,r,o,i&&"foreignObject"!==v,a,c,a?a[0]:r.__k&&m(r,0),u),null!=a)for(y=a.length;y--;)null!=a[y]&&_(a[y]);u||("value"in h&&void 0!==(y=h.value)&&(y!==t.value||"progress"===v&&!y)&&x(t,"value",y,p.value,!1),"checked"in h&&void 0!==(y=h.checked)&&y!==t.checked&&x(t,"checked",y,p.checked,!1))}return t}function N(e,n,r){try{"function"==typeof e?e(n):e.current=n}catch(e){t.__e(e,r)}}function L(e,n,r){var o,i;if(t.unmount&&t.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||N(o,null,n)),null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){t.__e(e,n)}o.base=o.__P=null}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&L(o[i],n,"function"!=typeof e.type);r||null==e.__e||_(e.__e),e.__e=e.__d=void 0}function P(e,t,n){return this.constructor(e,n)}e=c.slice,t={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},r=0,h.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=s({},this.state),"function"==typeof e&&(e=e(s({},n),this.props)),e&&s(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),y(this))},h.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),y(this))},h.prototype.render=p,o=[],i="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,g.__r=0;var D=n(379),A=n.n(D),R=n(795),F=n.n(R),M=n(569),j=n.n(M),U=n(565),W=n.n(U),H=n(216),B=n.n(H),$=n(589),z=n.n($),Z=n(759),q={};function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function V(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function J(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Q(e,t,n){return t&&J(e.prototype,t),n&&J(e,n),e}function K(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&X(e,t)}function X(e,t){return X=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},X(e,t)}function Y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=te(e);if(t){var o=te(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ee(this,n)}}function ee(e,t){if(t&&("object"===G(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function te(e){return te=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},te(e)}q.styleTagTransform=z(),q.setAttributes=W(),q.insert=j().bind(null,"head"),q.domAPI=F(),q.insertStyleElement=B(),A()(Z.Z,q),Z.Z&&Z.Z.locals&&Z.Z.locals;var ne={elems:[],log:[]},re={elemWidth:6,getElemWidth:function(e,t){return"MAP"==e.name?600/t.count:6},hues:{CORE:120,BEFORE_MAP_LOADED:40,AFTER_MAP_LOADED:210,SESSION:330,MAP:150},pickColor:function(e,t,n){var r=new ColorScheme;return r.from_hue(re.hues[e.name.replace(/INIT_/,"")]).scheme("analogic").add_complement(!0).distance(.5).variation(n>0?"pastel":"default"),"#"+r.colors()[4*(t-1)+n]}},oe=[],ie=function(){oe.map((function(e){return e()}))},ae=function(e,t){ne.log.push({type:e,str:t})};Array.prototype.last=function(){return this[this.length-1]};var le={startInitFunction:function(e){ne.elems.push({name:e.type,orders:[]}),ae(1,"Running ".concat(e.type," init functions")),ie()},startInitFunctionOrder:function(e){ne.elems.filter((function(t){return t.name==e.type})).map((function(t){return t.orders.push({idx:e.order,count:e.count,done:0})})),ae(2,"Running functions of order ".concat(e.order," (").concat(e.count," total)")),ie()},initFunctionInvoking:function(e){ae(3,"Invoking ".concat(e.name," ").concat(e.type," init (").concat(e.idx," of ").concat(e.count,")")),ie()},initFunctionInvoked:function(e){ne.elems.filter((function(t){return t.name==e.type})).map((function(e){return e.orders.last().done++})),ie()},endInitFunction:function(e){ae(1,"Done running ".concat(e.type," init functions")),ie()},startDataFileEntries:function(e){ne.elems.push({name:"MAP",orders:[{idx:1,count:e.count,done:0}]}),ae(1,"Loading map"),ie()},performMapLoadFunction:function(e){ne.elems.filter((function(e){return"MAP"==e.name})).map((function(e){return e.orders.last().done++})),ie()},onDataFileEntry:function(e){ae(3,"Loading ".concat(e.name)),ie()},endDataFileEntries:function(){ne.elems.filter((function(e){return"MAP"==e.name})).map((function(e){return e.orders.last().done=e.orders.last().count})),ae(1,"Done loading map"),ie()},onLogLine:function(e){ae(3,e.message),ie()}},ce=function(e){K(n,e);var t=Y(n);function n(){return V(this,n),t.apply(this,arguments)}return Q(n,[{key:"render",value:function(e){var t=e.value,n=e.type;return f("dd",{class:t.done==t.count?"done":"not-done",style:{width:t.count*re.getElemWidth(n,t)+"px"}},f("i",{style:{backgroundColor:re.pickColor(n,t.idx,1)}}),f("span",{style:{width:t.done*re.getElemWidth(n,t)+"px",backgroundColor:re.pickColor(n,t.idx,0)}}))}}]),n}(h),ue=function(e){K(n,e);var t=Y(n);function n(){return V(this,n),t.apply(this,arguments)}return Q(n,[{key:"render",value:function(e){return f("li",null,f("dl",{class:"orders-"+e.value.name.replace(/INIT_/,"").toLowerCase()},f("dt",null,e.value.name.replace(/INIT_/,"").replace(/_LOADED/,"")),e.value.orders.map((function(t){return f(ce,{type:e.value,value:t})}))))}}]),n}(h),se=function(e){K(n,e);var t=Y(n);function n(){return V(this,n),t.apply(this,arguments)}return Q(n,[{key:"render",value:function(e){return f("li",{class:"log-"+e.value.type},e.value.str)}}]),n}(h),_e=function(e){K(n,e);var t=Y(n);function n(){return V(this,n),t.call(this)}return Q(n,[{key:"componentDidMount",value:function(){var e=this;setInterval((function(){e.forceUpdate()}),50)}},{key:"render",value:function(e,t){return f("div",null,f("ul",{class:"progressList"},ne.elems.map((function(e){return f(ue,{value:e})}))),f("ul",{class:"log"},ne.log.slice(-100).map((function(e){return f(se,{value:e})})),f("li",null)))}}]),n}(h);if(window.addEventListener("message",(function(e){(le[e.data.eventName]||function(){})(e.data)})),function(n,r,o){var i,a,c;t.__&&t.__(n,r),a=(i="function"==typeof o)?null:o&&o.__k||r.__k,c=[],C(r,n=(!i&&o||r).__k=f(p,null,[n]),a||l,l,void 0!==r.ownerSVGElement,!i&&o?[o]:a?null:r.firstChild?e.call(r.childNodes):null,c,!i&&o?o:a?a.__e:r.firstChild,i),T(c,n)}(f(_e,null),document.querySelector("#root")),!window.invokeNative){for(var fe=function(e,t,n){return function(){return le.startInitFunctionOrder({type:e,order:t,count:n})}},de=function(e,t){return function(){le.initFunctionInvoking({type:e,name:t,idx:0,count}),le.initFunctionInvoked({type:e})}},pe=function(){return function(){return le.onDataFileEntry({name:"meow",isNew:!0})}},he=function(e,t){return function(){!function(e){return function(){return le.startInitFunction({type:e})}}(e)(),fe(e,1,t)()}},me=[],ve=0;ve<50;ve++)me.push(de("INIT_SESSION","meow".concat(ve)));var ye=[he("INIT_CORE",5),de("INIT_CORE","meow1"),de("INIT_CORE","meow2"),de("INIT_CORE","meow3"),de("INIT_CORE","meow4"),de("INIT_CORE","meow5"),fe("INIT_CORE",2,2),de("INIT_CORE","meow1"),de("INIT_CORE","meow2"),function(){return le.startDataFileEntries({count:6})},pe(),pe(),pe(),pe(),pe(),pe(),function(){return le.endDataFileEntries({})},he("INIT_SESSION",50)].concat(me);setInterval((function(){ye.length&&ye.shift()()}),0)}})()})();