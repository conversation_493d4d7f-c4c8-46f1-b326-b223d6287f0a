# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests that when values predicted but not proven int are used in a tower of additions, we don't eliminate the overflow check unsoundly.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(1, 2, {f:3}) is 6
PASS bar(2147483645, 2147483644, {f:9007199254740990}) is -8
PASS bar(2147483643, 2147483643, {f:18014398509481980}) is -16
PASS bar(2147483643, 2147483642, {f:36028797018963960}) is -16
PASS bar(2147483642, 2147483642, {f:36028797018963960}) is -16
PASS bar(2147483641, 2147483640, {f:144115188075855840}) is -32
PASS bar(2147483640, 2147483640, {f:144115188075855840}) is -64
PASS bar(2147483640, 2147483639, {f:288230376151711680}) is -64
PASS bar(2147483639, 2147483639, {f:288230376151711680}) is -64
PASS successfullyParsed is true

TEST COMPLETE

