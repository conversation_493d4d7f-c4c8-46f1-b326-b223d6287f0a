# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests that if you alias the arguments in a very small function, arguments simplification still works.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS foo(i, i + 1) is 1
PASS foo(i, i + 1) is 3
PASS foo(i, i + 1) is 5
PASS foo(i, i + 1) is 7
PASS foo(i, i + 1) is 9
PASS foo(i, i + 1) is 11
PASS foo(i, i + 1) is 13
PASS foo(i, i + 1) is 15
PASS foo(i, i + 1) is 17
PASS foo(i, i + 1) is 19
PASS foo(i, i + 1) is 21
PASS foo(i, i + 1) is 23
PASS foo(i, i + 1) is 25
PASS foo(i, i + 1) is 27
PASS foo(i, i + 1) is 29
PASS foo(i, i + 1) is 31
PASS foo(i, i + 1) is 33
PASS foo(i, i + 1) is 35
PASS foo(i, i + 1) is 37
PASS foo(i, i + 1) is 39
PASS foo(i, i + 1) is 41
PASS foo(i, i + 1) is 43
PASS foo(i, i + 1) is 45
PASS foo(i, i + 1) is 47
PASS foo(i, i + 1) is 49
PASS foo(i, i + 1) is 51
PASS foo(i, i + 1) is 53
PASS foo(i, i + 1) is 55
PASS foo(i, i + 1) is 57
PASS foo(i, i + 1) is 59
PASS foo(i, i + 1) is 61
PASS foo(i, i + 1) is 63
PASS foo(i, i + 1) is 65
PASS foo(i, i + 1) is 67
PASS foo(i, i + 1) is 69
PASS foo(i, i + 1) is 71
PASS foo(i, i + 1) is 73
PASS foo(i, i + 1) is 75
PASS foo(i, i + 1) is 77
PASS foo(i, i + 1) is 79
PASS foo(i, i + 1) is 81
PASS foo(i, i + 1) is 83
PASS foo(i, i + 1) is 85
PASS foo(i, i + 1) is 87
PASS foo(i, i + 1) is 89
PASS foo(i, i + 1) is 91
PASS foo(i, i + 1) is 93
PASS foo(i, i + 1) is 95
PASS foo(i, i + 1) is 97
PASS foo(i, i + 1) is 99
PASS foo(i, i + 1) is 101
PASS foo(i, i + 1) is 103
PASS foo(i, i + 1) is 105
PASS foo(i, i + 1) is 107
PASS foo(i, i + 1) is 109
PASS foo(i, i + 1) is 111
PASS foo(i, i + 1) is 113
PASS foo(i, i + 1) is 115
PASS foo(i, i + 1) is 117
PASS foo(i, i + 1) is 119
PASS foo(i, i + 1) is 121
PASS foo(i, i + 1) is 123
PASS foo(i, i + 1) is 125
PASS foo(i, i + 1) is 127
PASS foo(i, i + 1) is 129
PASS foo(i, i + 1) is 131
PASS foo(i, i + 1) is 133
PASS foo(i, i + 1) is 135
PASS foo(i, i + 1) is 137
PASS foo(i, i + 1) is 139
PASS foo(i, i + 1) is 141
PASS foo(i, i + 1) is 143
PASS foo(i, i + 1) is 145
PASS foo(i, i + 1) is 147
PASS foo(i, i + 1) is 149
PASS foo(i, i + 1) is 151
PASS foo(i, i + 1) is 153
PASS foo(i, i + 1) is 155
PASS foo(i, i + 1) is 157
PASS foo(i, i + 1) is 159
PASS foo(i, i + 1) is 161
PASS foo(i, i + 1) is 163
PASS foo(i, i + 1) is 165
PASS foo(i, i + 1) is 167
PASS foo(i, i + 1) is 169
PASS foo(i, i + 1) is 171
PASS foo(i, i + 1) is 173
PASS foo(i, i + 1) is 175
PASS foo(i, i + 1) is 177
PASS foo(i, i + 1) is 179
PASS foo(i, i + 1) is 181
PASS foo(i, i + 1) is 183
PASS foo(i, i + 1) is 185
PASS foo(i, i + 1) is 187
PASS foo(i, i + 1) is 189
PASS foo(i, i + 1) is 191
PASS foo(i, i + 1) is 193
PASS foo(i, i + 1) is 195
PASS foo(i, i + 1) is 197
PASS foo(i, i + 1) is 199
PASS foo(i, i + 1) is 201
PASS foo(i, i + 1) is 203
PASS foo(i, i + 1) is 205
PASS foo(i, i + 1) is 207
PASS foo(i, i + 1) is 209
PASS foo(i, i + 1) is 211
PASS foo(i, i + 1) is 213
PASS foo(i, i + 1) is 215
PASS foo(i, i + 1) is 217
PASS foo(i, i + 1) is 219
PASS foo(i, i + 1) is 221
PASS foo(i, i + 1) is 223
PASS foo(i, i + 1) is 225
PASS foo(i, i + 1) is 227
PASS foo(i, i + 1) is 229
PASS foo(i, i + 1) is 231
PASS foo(i, i + 1) is 233
PASS foo(i, i + 1) is 235
PASS foo(i, i + 1) is 237
PASS foo(i, i + 1) is 239
PASS foo(i, i + 1) is 241
PASS foo(i, i + 1) is 243
PASS foo(i, i + 1) is 245
PASS foo(i, i + 1) is 247
PASS foo(i, i + 1) is 249
PASS foo(i, i + 1) is 251
PASS foo(i, i + 1) is 253
PASS foo(i, i + 1) is 255
PASS foo(i, i + 1) is 257
PASS foo(i, i + 1) is 259
PASS foo(i, i + 1) is 261
PASS foo(i, i + 1) is 263
PASS foo(i, i + 1) is 265
PASS foo(i, i + 1) is 267
PASS foo(i, i + 1) is 269
PASS foo(i, i + 1) is 271
PASS foo(i, i + 1) is 273
PASS foo(i, i + 1) is 275
PASS foo(i, i + 1) is 277
PASS foo(i, i + 1) is 279
PASS foo(i, i + 1) is 281
PASS foo(i, i + 1) is 283
PASS foo(i, i + 1) is 285
PASS foo(i, i + 1) is 287
PASS foo(i, i + 1) is 289
PASS foo(i, i + 1) is 291
PASS foo(i, i + 1) is 293
PASS foo(i, i + 1) is 295
PASS foo(i, i + 1) is 297
PASS foo(i, i + 1) is 299
PASS foo(i, i + 1) is 301
PASS foo(i, i + 1) is 303
PASS foo(i, i + 1) is 305
PASS foo(i, i + 1) is 307
PASS foo(i, i + 1) is 309
PASS foo(i, i + 1) is 311
PASS foo(i, i + 1) is 313
PASS foo(i, i + 1) is 315
PASS foo(i, i + 1) is 317
PASS foo(i, i + 1) is 319
PASS foo(i, i + 1) is 321
PASS foo(i, i + 1) is 323
PASS foo(i, i + 1) is 325
PASS foo(i, i + 1) is 327
PASS foo(i, i + 1) is 329
PASS foo(i, i + 1) is 331
PASS foo(i, i + 1) is 333
PASS foo(i, i + 1) is 335
PASS foo(i, i + 1) is 337
PASS foo(i, i + 1) is 339
PASS foo(i, i + 1) is 341
PASS foo(i, i + 1) is 343
PASS foo(i, i + 1) is 345
PASS foo(i, i + 1) is 347
PASS foo(i, i + 1) is 349
PASS foo(i, i + 1) is 351
PASS foo(i, i + 1) is 353
PASS foo(i, i + 1) is 355
PASS foo(i, i + 1) is 357
PASS foo(i, i + 1) is 359
PASS foo(i, i + 1) is 361
PASS foo(i, i + 1) is 363
PASS foo(i, i + 1) is 365
PASS foo(i, i + 1) is 367
PASS foo(i, i + 1) is 369
PASS foo(i, i + 1) is 371
PASS foo(i, i + 1) is 373
PASS foo(i, i + 1) is 375
PASS foo(i, i + 1) is 377
PASS foo(i, i + 1) is 379
PASS foo(i, i + 1) is 381
PASS foo(i, i + 1) is 383
PASS foo(i, i + 1) is 385
PASS foo(i, i + 1) is 387
PASS foo(i, i + 1) is 389
PASS foo(i, i + 1) is 391
PASS foo(i, i + 1) is 393
PASS foo(i, i + 1) is 395
PASS foo(i, i + 1) is 397
PASS foo(i, i + 1) is 399
PASS successfullyParsed is true

TEST COMPLETE

