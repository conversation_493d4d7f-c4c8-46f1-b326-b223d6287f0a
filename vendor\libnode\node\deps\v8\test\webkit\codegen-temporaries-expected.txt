# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Tests whether bytecode codegen properly handles temporaries.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS a is true
PASS b is false
PASS assign_test1() is 'PASS'
PASS assign_test2() is 'PASS'
PASS assign_test3() is 'PASS'
PASS testObject4.test is 'PASS'
PASS testObject5.test is 'PASS'
PASS assign_test6() is 'PASS'
PASS assign_test7() is 'PASS'
PASS assign_test8() is 'PASS'
PASS assign_test9() is 'PASS'
PASS testObject10.test is 'PASS'
PASS assign_test11() is 'PASS'
PASS assign_test12() is 'PASS'
PASS assign_test13() is 'PASS'
PASS assign_test14() is 'PASS'
PASS assign_test15() is 'PASS'
PASS assign_test16() is 2
PASS a17 is 3
PASS assign_test18() is 3
PASS a19.b is 3
PASS assign_test20() is 3
PASS a21['b'] is 3
PASS assign_test22() is 3
PASS assign_test23() is 3
PASS assign_test24() is 3
PASS assign_test25() is 3
PASS assign_test26() is 3
PASS assign_test27() is 3
PASS assign_test28() is 3
PASS assign_test29() is 3
PASS assign_test30() is 'fooNaN'
PASS assign_test31() is 'PASS'
PASS bracket_test1() is -1
PASS bracket_test2() is 1
PASS bracket_test3() is 0
PASS bracket_test4() is 0
PASS bracket_test5() is 1
PASS bracket_test6() is 1
PASS mult_test1() is 2
PASS mult_test2() is 2
PASS mult_test3() is 2
PASS div_test1() is 0.5
PASS div_test2() is 0.5
PASS div_test3() is 0.5
PASS mod_test1() is 1
PASS mod_test2() is 1
PASS mod_test3() is 1
PASS add_test1() is 3
PASS add_test2() is 3
PASS add_test3() is 3
PASS sub_test1() is -1
PASS sub_test2() is -1
PASS sub_test3() is -1
PASS lshift_test1() is 4
PASS lshift_test2() is 4
PASS lshift_test3() is 4
PASS rshift_test1() is 1
PASS rshift_test2() is 1
PASS rshift_test3() is 1
PASS urshift_test1() is 1
PASS urshift_test2() is 1
PASS urshift_test3() is 1
PASS less_test1() is true
PASS less_test2() is true
PASS less_test3() is true
PASS greater_test1() is true
PASS greater_test2() is true
PASS greater_test3() is true
PASS lesseq_test1() is true
PASS lesseq_test2() is true
PASS lesseq_test3() is true
PASS greatereq_test1() is true
PASS greatereq_test2() is true
PASS greatereq_test3() is true
PASS instanceof_test1() is true
PASS instanceof_test2() is true
PASS instanceof_test3() is true
PASS in_test1() is true
PASS in_test2() is true
PASS in_test3() is true
PASS eq_test1() is false
PASS eq_test2() is false
PASS eq_test3() is false
PASS neq_test1() is true
PASS neq_test2() is true
PASS neq_test3() is true
PASS stricteq_test1() is false
PASS stricteq_test2() is false
PASS stricteq_test3() is false
PASS nstricteq_test1() is true
PASS nstricteq_test2() is true
PASS nstricteq_test3() is true
PASS bitand_test1() is 0
PASS bitand_test2() is 0
PASS bitand_test3() is 0
PASS bitor_test1() is 3
PASS bitor_test2() is 3
PASS bitor_test3() is 3
PASS bitxor_test1() is 3
PASS bitxor_test2() is 3
PASS bitxor_test3() is 3
PASS switch_test1() is true
PASS switch_test2() is true
PASS switch_test3() is true
PASS construct_test() is true
PASS [(0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), (0/0), ].length is 64
PASS successfullyParsed is true

TEST COMPLETE

