# Copyright 2013 the V8 project authors. All rights reserved.
# Copyright (C) 2005, 2006, 2007, 2008, 2009 Apple Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1.  Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
# 2.  Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in the
#     documentation and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY APPLE INC. AND ITS CONTRIBUTORS ``AS IS'' AND ANY
# EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL APPLE INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
# (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
# LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
# ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

This tests that inlining preserves basic function.arguments functionality when said functionality is used from outside of the code where inlining actually happened.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a0,b1,c2,a0,b1,c2"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a1,b2,c3,a1,b2,c3"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a2,b3,c4,a2,b3,c4"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a3,b4,c5,a3,b4,c5"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a4,b5,c6,a4,b5,c6"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a5,b6,c7,a5,b6,c7"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a6,b7,c8,a6,b7,c8"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a7,b8,c9,a7,b8,c9"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a8,b9,c10,a8,b9,c10"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a9,b10,c11,a9,b10,c11"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a10,b11,c12,a10,b11,c12"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a11,b12,c13,a11,b12,c13"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a12,b13,c14,a12,b13,c14"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a13,b14,c15,a13,b14,c15"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a14,b15,c16,a14,b15,c16"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a15,b16,c17,a15,b16,c17"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a16,b17,c18,a16,b17,c18"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a17,b18,c19,a17,b18,c19"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a18,b19,c20,a18,b19,c20"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a19,b20,c21,a19,b20,c21"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a20,b21,c22,a20,b21,c22"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a21,b22,c23,a21,b22,c23"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a22,b23,c24,a22,b23,c24"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a23,b24,c25,a23,b24,c25"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a24,b25,c26,a24,b25,c26"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a25,b26,c27,a25,b26,c27"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a26,b27,c28,a26,b27,c28"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a27,b28,c29,a27,b28,c29"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a28,b29,c30,a28,b29,c30"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a29,b30,c31,a29,b30,c31"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a30,b31,c32,a30,b31,c32"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a31,b32,c33,a31,b32,c33"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a32,b33,c34,a32,b33,c34"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a33,b34,c35,a33,b34,c35"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a34,b35,c36,a34,b35,c36"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a35,b36,c37,a35,b36,c37"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a36,b37,c38,a36,b37,c38"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a37,b38,c39,a37,b38,c39"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a38,b39,c40,a38,b39,c40"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a39,b40,c41,a39,b40,c41"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a40,b41,c42,a40,b41,c42"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a41,b42,c43,a41,b42,c43"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a42,b43,c44,a42,b43,c44"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a43,b44,c45,a43,b44,c45"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a44,b45,c46,a44,b45,c46"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a45,b46,c47,a45,b46,c47"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a46,b47,c48,a46,b47,c48"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a47,b48,c49,a47,b48,c49"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a48,b49,c50,a48,b49,c50"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a49,b50,c51,a49,b50,c51"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a50,b51,c52,a50,b51,c52"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a51,b52,c53,a51,b52,c53"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a52,b53,c54,a52,b53,c54"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a53,b54,c55,a53,b54,c55"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a54,b55,c56,a54,b55,c56"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a55,b56,c57,a55,b56,c57"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a56,b57,c58,a56,b57,c58"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a57,b58,c59,a57,b58,c59"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a58,b59,c60,a58,b59,c60"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a59,b60,c61,a59,b60,c61"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a60,b61,c62,a60,b61,c62"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a61,b62,c63,a61,b62,c63"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a62,b63,c64,a62,b63,c64"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a63,b64,c65,a63,b64,c65"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a64,b65,c66,a64,b65,c66"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a65,b66,c67,a65,b66,c67"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a66,b67,c68,a66,b67,c68"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a67,b68,c69,a67,b68,c69"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a68,b69,c70,a68,b69,c70"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a69,b70,c71,a69,b70,c71"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a70,b71,c72,a70,b71,c72"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a71,b72,c73,a71,b72,c73"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a72,b73,c74,a72,b73,c74"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a73,b74,c75,a73,b74,c75"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a74,b75,c76,a74,b75,c76"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a75,b76,c77,a75,b76,c77"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a76,b77,c78,a76,b77,c78"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a77,b78,c79,a77,b78,c79"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a78,b79,c80,a78,b79,c80"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a79,b80,c81,a79,b80,c81"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a80,b81,c82,a80,b81,c82"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a81,b82,c83,a81,b82,c83"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a82,b83,c84,a82,b83,c84"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a83,b84,c85,a83,b84,c85"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a84,b85,c86,a84,b85,c86"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a85,b86,c87,a85,b86,c87"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a86,b87,c88,a86,b87,c88"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a87,b88,c89,a87,b88,c89"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a88,b89,c90,a88,b89,c90"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a89,b90,c91,a89,b90,c91"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a90,b91,c92,a90,b91,c92"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a91,b92,c93,a91,b92,c93"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a92,b93,c94,a92,b93,c94"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a93,b94,c95,a93,b94,c95"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a94,b95,c96,a94,b95,c96"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a95,b96,c97,a95,b96,c97"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a96,b97,c98,a96,b97,c98"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a97,b98,c99,a97,b98,c99"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a98,b99,c100,a98,b99,c100"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a99,b100,c101,a99,b100,c101"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a100,b101,c102,a100,b101,c102"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a101,b102,c103,a101,b102,c103"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a102,b103,c104,a102,b103,c104"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a103,b104,c105,a103,b104,c105"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a104,b105,c106,a104,b105,c106"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a105,b106,c107,a105,b106,c107"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a106,b107,c108,a106,b107,c108"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a107,b108,c109,a107,b108,c109"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a108,b109,c110,a108,b109,c110"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a109,b110,c111,a109,b110,c111"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a110,b111,c112,a110,b111,c112"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a111,b112,c113,a111,b112,c113"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a112,b113,c114,a112,b113,c114"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a113,b114,c115,a113,b114,c115"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a114,b115,c116,a114,b115,c116"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a115,b116,c117,a115,b116,c117"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a116,b117,c118,a116,b117,c118"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a117,b118,c119,a117,b118,c119"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a118,b119,c120,a118,b119,c120"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a119,b120,c121,a119,b120,c121"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a120,b121,c122,a120,b121,c122"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a121,b122,c123,a121,b122,c123"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a122,b123,c124,a122,b123,c124"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a123,b124,c125,a123,b124,c125"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a124,b125,c126,a124,b125,c126"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a125,b126,c127,a125,b126,c127"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a126,b127,c128,a126,b127,c128"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a127,b128,c129,a127,b128,c129"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a128,b129,c130,a128,b129,c130"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a129,b130,c131,a129,b130,c131"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a130,b131,c132,a130,b131,c132"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a131,b132,c133,a131,b132,c133"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a132,b133,c134,a132,b133,c134"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a133,b134,c135,a133,b134,c135"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a134,b135,c136,a134,b135,c136"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a135,b136,c137,a135,b136,c137"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a136,b137,c138,a136,b137,c138"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a137,b138,c139,a137,b138,c139"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a138,b139,c140,a138,b139,c140"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a139,b140,c141,a139,b140,c141"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a140,b141,c142,a140,b141,c142"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a141,b142,c143,a141,b142,c143"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a142,b143,c144,a142,b143,c144"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a143,b144,c145,a143,b144,c145"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a144,b145,c146,a144,b145,c146"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a145,b146,c147,a145,b146,c147"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a146,b147,c148,a146,b147,c148"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a147,b148,c149,a147,b148,c149"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a148,b149,c150,a148,b149,c150"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a149,b150,c151,a149,b150,c151"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a150,b151,c152,a150,b151,c152"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a151,b152,c153,a151,b152,c153"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a152,b153,c154,a152,b153,c154"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a153,b154,c155,a153,b154,c155"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a154,b155,c156,a154,b155,c156"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a155,b156,c157,a155,b156,c157"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a156,b157,c158,a156,b157,c158"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a157,b158,c159,a157,b158,c159"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a158,b159,c160,a158,b159,c160"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a159,b160,c161,a159,b160,c161"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a160,b161,c162,a160,b161,c162"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a161,b162,c163,a161,b162,c163"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a162,b163,c164,a162,b163,c164"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a163,b164,c165,a163,b164,c165"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a164,b165,c166,a164,b165,c166"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a165,b166,c167,a165,b166,c167"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a166,b167,c168,a166,b167,c168"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a167,b168,c169,a167,b168,c169"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a168,b169,c170,a168,b169,c170"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a169,b170,c171,a169,b170,c171"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a170,b171,c172,a170,b171,c172"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a171,b172,c173,a171,b172,c173"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a172,b173,c174,a172,b173,c174"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a173,b174,c175,a173,b174,c175"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a174,b175,c176,a174,b175,c176"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a175,b176,c177,a175,b176,c177"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a176,b177,c178,a176,b177,c178"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a177,b178,c179,a177,b178,c179"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a178,b179,c180,a178,b179,c180"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a179,b180,c181,a179,b180,c181"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a180,b181,c182,a180,b181,c182"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a181,b182,c183,a181,b182,c183"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a182,b183,c184,a182,b183,c184"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a183,b184,c185,a183,b184,c185"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a184,b185,c186,a184,b185,c186"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a185,b186,c187,a185,b186,c187"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a186,b187,c188,a186,b187,c188"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a187,b188,c189,a187,b188,c189"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a188,b189,c190,a188,b189,c190"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a189,b190,c191,a189,b190,c191"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a190,b191,c192,a190,b191,c192"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a191,b192,c193,a191,b192,c193"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a192,b193,c194,a192,b193,c194"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a193,b194,c195,a193,b194,c195"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a194,b195,c196,a194,b195,c196"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a195,b196,c197,a195,b196,c197"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a196,b197,c198,a196,b197,c198"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a197,b198,c199,a197,b198,c199"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a198,b199,c200,a198,b199,c200"
PASS "" + baz("a" + __i, "b" + (__i + 1), "c" + (__i + 2)) is "a199,b200,c201,a199,b200,c201"
PASS successfullyParsed is true

TEST COMPLETE

