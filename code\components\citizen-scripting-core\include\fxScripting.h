/*
 * DO NOT EDIT.  THIS FILE IS GENERATED FROM ../../components/citizen-scripting-core/include/fxScripting.idl
 */

#ifndef __gen_fxScripting_h__
#define __gen_fxScripting_h__


#ifndef __gen_fxIBase_h__
#include "fxIBase.h"
#endif

/* For IDL files that don't want to include root IDL files. */
#ifndef NS_NO_VTABLE
#define NS_NO_VTABLE
#endif
#include "fxNativeContext.h"

/* starting interface:    fxIStream */
#define FXISTREAM_IID_STR "82ec2441-dbb4-4512-81e9-3a98ce9ffcab"

#define FXISTREAM_IID \
  {0x82ec2441, 0xdbb4, 0x4512, \
    { 0x81, 0xe9, 0x3a, 0x98, 0xce, 0x9f, 0xfc, 0xab }}

class NS_NO_VTABLE fxIStream : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(FXISTREAM_IID)

  /* void Read (in voidPtr data, in uint32_t size, out uint32_t bytesRead); */
  NS_IMETHOD Read(void *data, uint32_t size, uint32_t *bytesRead) = 0;

  /* void Write (in voidPtr data, in uint32_t size, out uint32_t bytesWritten); */
  NS_IMETHOD Write(void *data, uint32_t size, uint32_t *bytesWritten) = 0;

  /* void Seek (in int64_t offset, in int32_t origin, out uint64_t newPosition); */
  NS_IMETHOD Seek(int64_t offset, int32_t origin, uint64_t *newPosition) = 0;

  /* void GetLength (out uint64_t length); */
  NS_IMETHOD GetLength(uint64_t *length) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(fxIStream, FXISTREAM_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_FXISTREAM \
  NS_IMETHOD Read(void *data, uint32_t size, uint32_t *bytesRead) override; \
  NS_IMETHOD Write(void *data, uint32_t size, uint32_t *bytesWritten) override; \
  NS_IMETHOD Seek(int64_t offset, int32_t origin, uint64_t *newPosition) override; \
  NS_IMETHOD GetLength(uint64_t *length) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_FXISTREAM(_to) \
  NS_IMETHOD Read(void *data, uint32_t size, uint32_t *bytesRead) override { return _to Read(data, size, bytesRead); } \
  NS_IMETHOD Write(void *data, uint32_t size, uint32_t *bytesWritten) override { return _to Write(data, size, bytesWritten); } \
  NS_IMETHOD Seek(int64_t offset, int32_t origin, uint64_t *newPosition) override { return _to Seek(offset, origin, newPosition); } \
  NS_IMETHOD GetLength(uint64_t *length) override { return _to GetLength(length); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_FXISTREAM(_to) \
  NS_IMETHOD Read(void *data, uint32_t size, uint32_t *bytesRead) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Read(data, size, bytesRead); } \
  NS_IMETHOD Write(void *data, uint32_t size, uint32_t *bytesWritten) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Write(data, size, bytesWritten); } \
  NS_IMETHOD Seek(int64_t offset, int32_t origin, uint64_t *newPosition) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Seek(offset, origin, newPosition); } \
  NS_IMETHOD GetLength(uint64_t *length) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetLength(length); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class fxStream : public fxIStream
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_FXISTREAM

  fxStream();

private:
  ~fxStream();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(fxStream, fxIStream)

fxStream::fxStream()
{
  /* member initializers and constructor code */
}

fxStream::~fxStream()
{
  /* destructor code */
}

/* void Read (in voidPtr data, in uint32_t size, out uint32_t bytesRead); */
NS_IMETHODIMP fxStream::Read(void *data, uint32_t size, uint32_t *bytesRead)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void Write (in voidPtr data, in uint32_t size, out uint32_t bytesWritten); */
NS_IMETHODIMP fxStream::Write(void *data, uint32_t size, uint32_t *bytesWritten)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void Seek (in int64_t offset, in int32_t origin, out uint64_t newPosition); */
NS_IMETHODIMP fxStream::Seek(int64_t offset, int32_t origin, uint64_t *newPosition)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetLength (out uint64_t length); */
NS_IMETHODIMP fxStream::GetLength(uint64_t *length)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptBuffer */
#define ISCRIPTBUFFER_IID_STR "ad1b9d69-b984-4d30-8d33-bb1e6cf9e1ba"

#define ISCRIPTBUFFER_IID \
  {0xad1b9d69, 0xb984, 0x4d30, \
    { 0x8d, 0x33, 0xbb, 0x1e, 0x6c, 0xf9, 0xe1, 0xba }}

class NS_NO_VTABLE IScriptBuffer : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTBUFFER_IID)

  /* [notxpcom] charPtr GetBytes (); */
  NS_IMETHOD_(char *) GetBytes(void) = 0;

  /* [notxpcom] uint32_t GetLength (); */
  NS_IMETHOD_(uint32_t) GetLength(void) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptBuffer, ISCRIPTBUFFER_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTBUFFER \
  NS_IMETHOD_(char *) GetBytes(void) override; \
  NS_IMETHOD_(uint32_t) GetLength(void) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTBUFFER(_to) \
  NS_IMETHOD_(char *) GetBytes(void) override { return _to GetBytes(); } \
  NS_IMETHOD_(uint32_t) GetLength(void) override { return _to GetLength(); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTBUFFER(_to) \
  NS_IMETHOD_(char *) GetBytes(void) override; \
  NS_IMETHOD_(uint32_t) GetLength(void) override; 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptBuffer
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTBUFFER

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptBuffer)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* [notxpcom] charPtr GetBytes (); */
NS_IMETHODIMP_(char *) _MYCLASS_::GetBytes()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* [notxpcom] uint32_t GetLength (); */
NS_IMETHODIMP_(uint32_t) _MYCLASS_::GetLength()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptHost */
#define ISCRIPTHOST_IID_STR "8ffdc384-4767-4ea2-a935-3bfcad1db7bf"

#define ISCRIPTHOST_IID \
  {0x8ffdc384, 0x4767, 0x4ea2, \
    { 0xa9, 0x35, 0x3b, 0xfc, 0xad, 0x1d, 0xb7, 0xbf }}

class NS_NO_VTABLE IScriptHost : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTHOST_IID)

  /* void InvokeNative (inout NativeCtx context); */
  NS_IMETHOD InvokeNative(fxNativeContext & context) = 0;

  /* void OpenSystemFile (in charPtr fileName, out fxIStream stream); */
  NS_IMETHOD OpenSystemFile(char *fileName, fxIStream * *stream) = 0;

  /* void OpenHostFile (in charPtr fileName, out fxIStream stream); */
  NS_IMETHOD OpenHostFile(char *fileName, fxIStream * *stream) = 0;

  /* void CanonicalizeRef (in int32_t localRef, in int32_t instanceId, out charPtr refString); */
  NS_IMETHOD CanonicalizeRef(int32_t localRef, int32_t instanceId, char **refString) = 0;

  /* void ScriptTrace (in charPtr message); */
  NS_IMETHOD ScriptTrace(char *message) = 0;

  /* void SubmitBoundaryStart (in charPtr boundaryData, in int32_t boundarySize); */
  NS_IMETHOD SubmitBoundaryStart(char *boundaryData, int32_t boundarySize) = 0;

  /* void SubmitBoundaryEnd (in charPtr boundaryData, in int32_t boundarySize); */
  NS_IMETHOD SubmitBoundaryEnd(char *boundaryData, int32_t boundarySize) = 0;

  /* void GetLastErrorText (out charPtr errorString); */
  NS_IMETHOD GetLastErrorText(char **errorString) = 0;

  /* void InvokeFunctionReference (in charPtr refId, in charPtr argsSerialized, in uint32_t argsSize, out IScriptBuffer ret); */
  NS_IMETHOD InvokeFunctionReference(char *refId, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptHost, ISCRIPTHOST_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTHOST \
  NS_IMETHOD InvokeNative(fxNativeContext & context) override; \
  NS_IMETHOD OpenSystemFile(char *fileName, fxIStream * *stream) override; \
  NS_IMETHOD OpenHostFile(char *fileName, fxIStream * *stream) override; \
  NS_IMETHOD CanonicalizeRef(int32_t localRef, int32_t instanceId, char **refString) override; \
  NS_IMETHOD ScriptTrace(char *message) override; \
  NS_IMETHOD SubmitBoundaryStart(char *boundaryData, int32_t boundarySize) override; \
  NS_IMETHOD SubmitBoundaryEnd(char *boundaryData, int32_t boundarySize) override; \
  NS_IMETHOD GetLastErrorText(char **errorString) override; \
  NS_IMETHOD InvokeFunctionReference(char *refId, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTHOST(_to) \
  NS_IMETHOD InvokeNative(fxNativeContext & context) override { return _to InvokeNative(context); } \
  NS_IMETHOD OpenSystemFile(char *fileName, fxIStream * *stream) override { return _to OpenSystemFile(fileName, stream); } \
  NS_IMETHOD OpenHostFile(char *fileName, fxIStream * *stream) override { return _to OpenHostFile(fileName, stream); } \
  NS_IMETHOD CanonicalizeRef(int32_t localRef, int32_t instanceId, char **refString) override { return _to CanonicalizeRef(localRef, instanceId, refString); } \
  NS_IMETHOD ScriptTrace(char *message) override { return _to ScriptTrace(message); } \
  NS_IMETHOD SubmitBoundaryStart(char *boundaryData, int32_t boundarySize) override { return _to SubmitBoundaryStart(boundaryData, boundarySize); } \
  NS_IMETHOD SubmitBoundaryEnd(char *boundaryData, int32_t boundarySize) override { return _to SubmitBoundaryEnd(boundaryData, boundarySize); } \
  NS_IMETHOD GetLastErrorText(char **errorString) override { return _to GetLastErrorText(errorString); } \
  NS_IMETHOD InvokeFunctionReference(char *refId, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override { return _to InvokeFunctionReference(refId, argsSerialized, argsSize, ret); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTHOST(_to) \
  NS_IMETHOD InvokeNative(fxNativeContext & context) override { return !_to ? NS_ERROR_NULL_POINTER : _to->InvokeNative(context); } \
  NS_IMETHOD OpenSystemFile(char *fileName, fxIStream * *stream) override { return !_to ? NS_ERROR_NULL_POINTER : _to->OpenSystemFile(fileName, stream); } \
  NS_IMETHOD OpenHostFile(char *fileName, fxIStream * *stream) override { return !_to ? NS_ERROR_NULL_POINTER : _to->OpenHostFile(fileName, stream); } \
  NS_IMETHOD CanonicalizeRef(int32_t localRef, int32_t instanceId, char **refString) override { return !_to ? NS_ERROR_NULL_POINTER : _to->CanonicalizeRef(localRef, instanceId, refString); } \
  NS_IMETHOD ScriptTrace(char *message) override { return !_to ? NS_ERROR_NULL_POINTER : _to->ScriptTrace(message); } \
  NS_IMETHOD SubmitBoundaryStart(char *boundaryData, int32_t boundarySize) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SubmitBoundaryStart(boundaryData, boundarySize); } \
  NS_IMETHOD SubmitBoundaryEnd(char *boundaryData, int32_t boundarySize) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SubmitBoundaryEnd(boundaryData, boundarySize); } \
  NS_IMETHOD GetLastErrorText(char **errorString) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetLastErrorText(errorString); } \
  NS_IMETHOD InvokeFunctionReference(char *refId, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override { return !_to ? NS_ERROR_NULL_POINTER : _to->InvokeFunctionReference(refId, argsSerialized, argsSize, ret); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptHost
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTHOST

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptHost)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void InvokeNative (inout NativeCtx context); */
NS_IMETHODIMP _MYCLASS_::InvokeNative(fxNativeContext & context)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void OpenSystemFile (in charPtr fileName, out fxIStream stream); */
NS_IMETHODIMP _MYCLASS_::OpenSystemFile(char *fileName, fxIStream * *stream)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void OpenHostFile (in charPtr fileName, out fxIStream stream); */
NS_IMETHODIMP _MYCLASS_::OpenHostFile(char *fileName, fxIStream * *stream)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void CanonicalizeRef (in int32_t localRef, in int32_t instanceId, out charPtr refString); */
NS_IMETHODIMP _MYCLASS_::CanonicalizeRef(int32_t localRef, int32_t instanceId, char **refString)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void ScriptTrace (in charPtr message); */
NS_IMETHODIMP _MYCLASS_::ScriptTrace(char *message)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void SubmitBoundaryStart (in charPtr boundaryData, in int32_t boundarySize); */
NS_IMETHODIMP _MYCLASS_::SubmitBoundaryStart(char *boundaryData, int32_t boundarySize)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void SubmitBoundaryEnd (in charPtr boundaryData, in int32_t boundarySize); */
NS_IMETHODIMP _MYCLASS_::SubmitBoundaryEnd(char *boundaryData, int32_t boundarySize)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetLastErrorText (out charPtr errorString); */
NS_IMETHODIMP _MYCLASS_::GetLastErrorText(char **errorString)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void InvokeFunctionReference (in charPtr refId, in charPtr argsSerialized, in uint32_t argsSize, out IScriptBuffer ret); */
NS_IMETHODIMP _MYCLASS_::InvokeFunctionReference(char *refId, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptHostWithResourceData */
#define ISCRIPTHOSTWITHRESOURCEDATA_IID_STR "9568df2d-27c8-4b9e-b29d-48272c317084"

#define ISCRIPTHOSTWITHRESOURCEDATA_IID \
  {0x9568df2d, 0x27c8, 0x4b9e, \
    { 0xb2, 0x9d, 0x48, 0x27, 0x2c, 0x31, 0x70, 0x84 }}

class NS_NO_VTABLE IScriptHostWithResourceData : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTHOSTWITHRESOURCEDATA_IID)

  /* void GetResourceName (out charPtr resourceName); */
  NS_IMETHOD GetResourceName(char **resourceName) = 0;

  /* void GetNumResourceMetaData (in charPtr fieldName, out int32_t numFields); */
  NS_IMETHOD GetNumResourceMetaData(char *fieldName, int32_t *numFields) = 0;

  /* void GetResourceMetaData (in charPtr fieldName, in int32_t fieldIndex, out charPtr fieldValue); */
  NS_IMETHOD GetResourceMetaData(char *fieldName, int32_t fieldIndex, char **fieldValue) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptHostWithResourceData, ISCRIPTHOSTWITHRESOURCEDATA_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTHOSTWITHRESOURCEDATA \
  NS_IMETHOD GetResourceName(char **resourceName) override; \
  NS_IMETHOD GetNumResourceMetaData(char *fieldName, int32_t *numFields) override; \
  NS_IMETHOD GetResourceMetaData(char *fieldName, int32_t fieldIndex, char **fieldValue) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTHOSTWITHRESOURCEDATA(_to) \
  NS_IMETHOD GetResourceName(char **resourceName) override { return _to GetResourceName(resourceName); } \
  NS_IMETHOD GetNumResourceMetaData(char *fieldName, int32_t *numFields) override { return _to GetNumResourceMetaData(fieldName, numFields); } \
  NS_IMETHOD GetResourceMetaData(char *fieldName, int32_t fieldIndex, char **fieldValue) override { return _to GetResourceMetaData(fieldName, fieldIndex, fieldValue); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTHOSTWITHRESOURCEDATA(_to) \
  NS_IMETHOD GetResourceName(char **resourceName) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetResourceName(resourceName); } \
  NS_IMETHOD GetNumResourceMetaData(char *fieldName, int32_t *numFields) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetNumResourceMetaData(fieldName, numFields); } \
  NS_IMETHOD GetResourceMetaData(char *fieldName, int32_t fieldIndex, char **fieldValue) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetResourceMetaData(fieldName, fieldIndex, fieldValue); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptHostWithResourceData
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTHOSTWITHRESOURCEDATA

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptHostWithResourceData)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void GetResourceName (out charPtr resourceName); */
NS_IMETHODIMP _MYCLASS_::GetResourceName(char **resourceName)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetNumResourceMetaData (in charPtr fieldName, out int32_t numFields); */
NS_IMETHODIMP _MYCLASS_::GetNumResourceMetaData(char *fieldName, int32_t *numFields)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetResourceMetaData (in charPtr fieldName, in int32_t fieldIndex, out charPtr fieldValue); */
NS_IMETHODIMP _MYCLASS_::GetResourceMetaData(char *fieldName, int32_t fieldIndex, char **fieldValue)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptHostWithManifest */
#define ISCRIPTHOSTWITHMANIFEST_IID_STR "5e212027-3aad-46d1-97e0-b8bc5ef89e18"

#define ISCRIPTHOSTWITHMANIFEST_IID \
  {0x5e212027, 0x3aad, 0x46d1, \
    { 0x97, 0xe0, 0xb8, 0xbc, 0x5e, 0xf8, 0x9e, 0x18 }}

class NS_NO_VTABLE IScriptHostWithManifest : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTHOSTWITHMANIFEST_IID)

  /* boolean IsManifestVersionBetween (in fxIIDRef lowerBound, in fxIIDRef upperBound); */
  NS_IMETHOD IsManifestVersionBetween(const guid_t & lowerBound, const guid_t & upperBound, bool *_retval) = 0;

  /* boolean IsManifestVersionV2Between (in charPtr lowerBound, in charPtr upperBound); */
  NS_IMETHOD IsManifestVersionV2Between(char *lowerBound, char *upperBound, bool *_retval) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptHostWithManifest, ISCRIPTHOSTWITHMANIFEST_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTHOSTWITHMANIFEST \
  NS_IMETHOD IsManifestVersionBetween(const guid_t & lowerBound, const guid_t & upperBound, bool *_retval) override; \
  NS_IMETHOD IsManifestVersionV2Between(char *lowerBound, char *upperBound, bool *_retval) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTHOSTWITHMANIFEST(_to) \
  NS_IMETHOD IsManifestVersionBetween(const guid_t & lowerBound, const guid_t & upperBound, bool *_retval) override { return _to IsManifestVersionBetween(lowerBound, upperBound, _retval); } \
  NS_IMETHOD IsManifestVersionV2Between(char *lowerBound, char *upperBound, bool *_retval) override { return _to IsManifestVersionV2Between(lowerBound, upperBound, _retval); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTHOSTWITHMANIFEST(_to) \
  NS_IMETHOD IsManifestVersionBetween(const guid_t & lowerBound, const guid_t & upperBound, bool *_retval) override { return !_to ? NS_ERROR_NULL_POINTER : _to->IsManifestVersionBetween(lowerBound, upperBound, _retval); } \
  NS_IMETHOD IsManifestVersionV2Between(char *lowerBound, char *upperBound, bool *_retval) override { return !_to ? NS_ERROR_NULL_POINTER : _to->IsManifestVersionV2Between(lowerBound, upperBound, _retval); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptHostWithManifest
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTHOSTWITHMANIFEST

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptHostWithManifest)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* boolean IsManifestVersionBetween (in fxIIDRef lowerBound, in fxIIDRef upperBound); */
NS_IMETHODIMP _MYCLASS_::IsManifestVersionBetween(const guid_t & lowerBound, const guid_t & upperBound, bool *_retval)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* boolean IsManifestVersionV2Between (in charPtr lowerBound, in charPtr upperBound); */
NS_IMETHODIMP _MYCLASS_::IsManifestVersionV2Between(char *lowerBound, char *upperBound, bool *_retval)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptRuntime */
#define ISCRIPTRUNTIME_IID_STR "67b28af1-aaf9-4368-8296-f93afc7bde96"

#define ISCRIPTRUNTIME_IID \
  {0x67b28af1, 0xaaf9, 0x4368, \
    { 0x82, 0x96, 0xf9, 0x3a, 0xfc, 0x7b, 0xde, 0x96 }}

class NS_NO_VTABLE IScriptRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTRUNTIME_IID)

  /* void Create (in IScriptHost scriptHost); */
  NS_IMETHOD Create(IScriptHost *scriptHost) = 0;

  /* void Destroy (); */
  NS_IMETHOD Destroy(void) = 0;

  /* [notxpcom] voidPtr GetParentObject (); */
  NS_IMETHOD_(void *) GetParentObject(void) = 0;

  /* [notxpcom] void SetParentObject (in voidPtr obj); */
  NS_IMETHOD_(void) SetParentObject(void *obj) = 0;

  /* [notxpcom] int32_t GetInstanceId (); */
  NS_IMETHOD_(int32_t) GetInstanceId(void) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptRuntime, ISCRIPTRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTRUNTIME \
  NS_IMETHOD Create(IScriptHost *scriptHost) override; \
  NS_IMETHOD Destroy(void) override; \
  NS_IMETHOD_(void *) GetParentObject(void) override; \
  NS_IMETHOD_(void) SetParentObject(void *obj) override; \
  NS_IMETHOD_(int32_t) GetInstanceId(void) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTRUNTIME(_to) \
  NS_IMETHOD Create(IScriptHost *scriptHost) override { return _to Create(scriptHost); } \
  NS_IMETHOD Destroy(void) override { return _to Destroy(); } \
  NS_IMETHOD_(void *) GetParentObject(void) override { return _to GetParentObject(); } \
  NS_IMETHOD_(void) SetParentObject(void *obj) override { return _to SetParentObject(obj); } \
  NS_IMETHOD_(int32_t) GetInstanceId(void) override { return _to GetInstanceId(); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTRUNTIME(_to) \
  NS_IMETHOD Create(IScriptHost *scriptHost) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Create(scriptHost); } \
  NS_IMETHOD Destroy(void) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Destroy(); } \
  NS_IMETHOD_(void *) GetParentObject(void) override; \
  NS_IMETHOD_(void) SetParentObject(void *obj) override; \
  NS_IMETHOD_(int32_t) GetInstanceId(void) override; 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void Create (in IScriptHost scriptHost); */
NS_IMETHODIMP _MYCLASS_::Create(IScriptHost *scriptHost)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void Destroy (); */
NS_IMETHODIMP _MYCLASS_::Destroy()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* [notxpcom] voidPtr GetParentObject (); */
NS_IMETHODIMP_(void *) _MYCLASS_::GetParentObject()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* [notxpcom] void SetParentObject (in voidPtr obj); */
NS_IMETHODIMP_(void) _MYCLASS_::SetParentObject(void *obj)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* [notxpcom] int32_t GetInstanceId (); */
NS_IMETHODIMP_(int32_t) _MYCLASS_::GetInstanceId()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptRuntimeHandler */
#define ISCRIPTRUNTIMEHANDLER_IID_STR "4720a986-eaa6-4ecc-a31f-2ce2bbf569f7"

#define ISCRIPTRUNTIMEHANDLER_IID \
  {0x4720a986, 0xeaa6, 0x4ecc, \
    { 0xa3, 0x1f, 0x2c, 0xe2, 0xbb, 0xf5, 0x69, 0xf7 }}

class NS_NO_VTABLE IScriptRuntimeHandler : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTRUNTIMEHANDLER_IID)

  /* void PushRuntime (in IScriptRuntime runtime); */
  NS_IMETHOD PushRuntime(IScriptRuntime *runtime) = 0;

  /* void GetCurrentRuntime (out IScriptRuntime runtime); */
  NS_IMETHOD GetCurrentRuntime(IScriptRuntime * *runtime) = 0;

  /* void PopRuntime (in IScriptRuntime runtime); */
  NS_IMETHOD PopRuntime(IScriptRuntime *runtime) = 0;

  /* void GetInvokingRuntime (out IScriptRuntime runtime); */
  NS_IMETHOD GetInvokingRuntime(IScriptRuntime * *runtime) = 0;

  /* void TryPushRuntime (in IScriptRuntime runtime); */
  NS_IMETHOD TryPushRuntime(IScriptRuntime *runtime) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptRuntimeHandler, ISCRIPTRUNTIMEHANDLER_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTRUNTIMEHANDLER \
  NS_IMETHOD PushRuntime(IScriptRuntime *runtime) override; \
  NS_IMETHOD GetCurrentRuntime(IScriptRuntime * *runtime) override; \
  NS_IMETHOD PopRuntime(IScriptRuntime *runtime) override; \
  NS_IMETHOD GetInvokingRuntime(IScriptRuntime * *runtime) override; \
  NS_IMETHOD TryPushRuntime(IScriptRuntime *runtime) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTRUNTIMEHANDLER(_to) \
  NS_IMETHOD PushRuntime(IScriptRuntime *runtime) override { return _to PushRuntime(runtime); } \
  NS_IMETHOD GetCurrentRuntime(IScriptRuntime * *runtime) override { return _to GetCurrentRuntime(runtime); } \
  NS_IMETHOD PopRuntime(IScriptRuntime *runtime) override { return _to PopRuntime(runtime); } \
  NS_IMETHOD GetInvokingRuntime(IScriptRuntime * *runtime) override { return _to GetInvokingRuntime(runtime); } \
  NS_IMETHOD TryPushRuntime(IScriptRuntime *runtime) override { return _to TryPushRuntime(runtime); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTRUNTIMEHANDLER(_to) \
  NS_IMETHOD PushRuntime(IScriptRuntime *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->PushRuntime(runtime); } \
  NS_IMETHOD GetCurrentRuntime(IScriptRuntime * *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetCurrentRuntime(runtime); } \
  NS_IMETHOD PopRuntime(IScriptRuntime *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->PopRuntime(runtime); } \
  NS_IMETHOD GetInvokingRuntime(IScriptRuntime * *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetInvokingRuntime(runtime); } \
  NS_IMETHOD TryPushRuntime(IScriptRuntime *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->TryPushRuntime(runtime); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptRuntimeHandler
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTRUNTIMEHANDLER

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptRuntimeHandler)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void PushRuntime (in IScriptRuntime runtime); */
NS_IMETHODIMP _MYCLASS_::PushRuntime(IScriptRuntime *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetCurrentRuntime (out IScriptRuntime runtime); */
NS_IMETHODIMP _MYCLASS_::GetCurrentRuntime(IScriptRuntime * *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void PopRuntime (in IScriptRuntime runtime); */
NS_IMETHODIMP _MYCLASS_::PopRuntime(IScriptRuntime *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void GetInvokingRuntime (out IScriptRuntime runtime); */
NS_IMETHODIMP _MYCLASS_::GetInvokingRuntime(IScriptRuntime * *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void TryPushRuntime (in IScriptRuntime runtime); */
NS_IMETHODIMP _MYCLASS_::TryPushRuntime(IScriptRuntime *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptTickRuntime */
#define ISCRIPTTICKRUNTIME_IID_STR "91b203c7-f95a-4902-b463-722d55098366"

#define ISCRIPTTICKRUNTIME_IID \
  {0x91b203c7, 0xf95a, 0x4902, \
    { 0xb4, 0x63, 0x72, 0x2d, 0x55, 0x09, 0x83, 0x66 }}

class NS_NO_VTABLE IScriptTickRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTTICKRUNTIME_IID)

  /* void Tick (); */
  NS_IMETHOD Tick(void) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptTickRuntime, ISCRIPTTICKRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTTICKRUNTIME \
  NS_IMETHOD Tick(void) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTTICKRUNTIME(_to) \
  NS_IMETHOD Tick(void) override { return _to Tick(); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTTICKRUNTIME(_to) \
  NS_IMETHOD Tick(void) override { return !_to ? NS_ERROR_NULL_POINTER : _to->Tick(); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptTickRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTTICKRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptTickRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void Tick (); */
NS_IMETHODIMP _MYCLASS_::Tick()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptTickRuntimeWithBookmarks */
#define ISCRIPTTICKRUNTIMEWITHBOOKMARKS_IID_STR "195fb3bd-1a64-4ebd-a1cc-8052ed7eb0bd"

#define ISCRIPTTICKRUNTIMEWITHBOOKMARKS_IID \
  {0x195fb3bd, 0x1a64, 0x4ebd, \
    { 0xa1, 0xcc, 0x80, 0x52, 0xed, 0x7e, 0xb0, 0xbd }}

class NS_NO_VTABLE IScriptTickRuntimeWithBookmarks : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTTICKRUNTIMEWITHBOOKMARKS_IID)

  /* void TickBookmarks (in u64Ptr bookmarks, in int32_t numBookmarks); */
  NS_IMETHOD TickBookmarks(uint64_t *bookmarks, int32_t numBookmarks) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptTickRuntimeWithBookmarks, ISCRIPTTICKRUNTIMEWITHBOOKMARKS_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTTICKRUNTIMEWITHBOOKMARKS \
  NS_IMETHOD TickBookmarks(uint64_t *bookmarks, int32_t numBookmarks) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTTICKRUNTIMEWITHBOOKMARKS(_to) \
  NS_IMETHOD TickBookmarks(uint64_t *bookmarks, int32_t numBookmarks) override { return _to TickBookmarks(bookmarks, numBookmarks); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTTICKRUNTIMEWITHBOOKMARKS(_to) \
  NS_IMETHOD TickBookmarks(uint64_t *bookmarks, int32_t numBookmarks) override { return !_to ? NS_ERROR_NULL_POINTER : _to->TickBookmarks(bookmarks, numBookmarks); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptTickRuntimeWithBookmarks
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTTICKRUNTIMEWITHBOOKMARKS

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptTickRuntimeWithBookmarks)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void TickBookmarks (in u64Ptr bookmarks, in int32_t numBookmarks); */
NS_IMETHODIMP _MYCLASS_::TickBookmarks(uint64_t *bookmarks, int32_t numBookmarks)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptEventRuntime */
#define ISCRIPTEVENTRUNTIME_IID_STR "637140db-24e5-46bf-a8bd-08f2dbac519a"

#define ISCRIPTEVENTRUNTIME_IID \
  {0x637140db, 0x24e5, 0x46bf, \
    { 0xa8, 0xbd, 0x08, 0xf2, 0xdb, 0xac, 0x51, 0x9a }}

class NS_NO_VTABLE IScriptEventRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTEVENTRUNTIME_IID)

  /* void TriggerEvent (in charPtr eventName, in charPtr argsSerialized, in uint32_t serializedSize, in charPtr sourceId); */
  NS_IMETHOD TriggerEvent(char *eventName, char *argsSerialized, uint32_t serializedSize, char *sourceId) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptEventRuntime, ISCRIPTEVENTRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTEVENTRUNTIME \
  NS_IMETHOD TriggerEvent(char *eventName, char *argsSerialized, uint32_t serializedSize, char *sourceId) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTEVENTRUNTIME(_to) \
  NS_IMETHOD TriggerEvent(char *eventName, char *argsSerialized, uint32_t serializedSize, char *sourceId) override { return _to TriggerEvent(eventName, argsSerialized, serializedSize, sourceId); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTEVENTRUNTIME(_to) \
  NS_IMETHOD TriggerEvent(char *eventName, char *argsSerialized, uint32_t serializedSize, char *sourceId) override { return !_to ? NS_ERROR_NULL_POINTER : _to->TriggerEvent(eventName, argsSerialized, serializedSize, sourceId); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptEventRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTEVENTRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptEventRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void TriggerEvent (in charPtr eventName, in charPtr argsSerialized, in uint32_t serializedSize, in charPtr sourceId); */
NS_IMETHODIMP _MYCLASS_::TriggerEvent(char *eventName, char *argsSerialized, uint32_t serializedSize, char *sourceId)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptRefRuntime */
#define ISCRIPTREFRUNTIME_IID_STR "a2f1b24b-a29f-4121-8162-86901eca8097"

#define ISCRIPTREFRUNTIME_IID \
  {0xa2f1b24b, 0xa29f, 0x4121, \
    { 0x81, 0x62, 0x86, 0x90, 0x1e, 0xca, 0x80, 0x97 }}

class NS_NO_VTABLE IScriptRefRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTREFRUNTIME_IID)

  /* void CallRef (in int32_t refIdx, in charPtr argsSerialized, in uint32_t argsSize, out IScriptBuffer ret); */
  NS_IMETHOD CallRef(int32_t refIdx, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) = 0;

  /* void DuplicateRef (in int32_t refIdx, out int32_t newRefIdx); */
  NS_IMETHOD DuplicateRef(int32_t refIdx, int32_t *newRefIdx) = 0;

  /* void RemoveRef (in int32_t refIdx); */
  NS_IMETHOD RemoveRef(int32_t refIdx) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptRefRuntime, ISCRIPTREFRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTREFRUNTIME \
  NS_IMETHOD CallRef(int32_t refIdx, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override; \
  NS_IMETHOD DuplicateRef(int32_t refIdx, int32_t *newRefIdx) override; \
  NS_IMETHOD RemoveRef(int32_t refIdx) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTREFRUNTIME(_to) \
  NS_IMETHOD CallRef(int32_t refIdx, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override { return _to CallRef(refIdx, argsSerialized, argsSize, ret); } \
  NS_IMETHOD DuplicateRef(int32_t refIdx, int32_t *newRefIdx) override { return _to DuplicateRef(refIdx, newRefIdx); } \
  NS_IMETHOD RemoveRef(int32_t refIdx) override { return _to RemoveRef(refIdx); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTREFRUNTIME(_to) \
  NS_IMETHOD CallRef(int32_t refIdx, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret) override { return !_to ? NS_ERROR_NULL_POINTER : _to->CallRef(refIdx, argsSerialized, argsSize, ret); } \
  NS_IMETHOD DuplicateRef(int32_t refIdx, int32_t *newRefIdx) override { return !_to ? NS_ERROR_NULL_POINTER : _to->DuplicateRef(refIdx, newRefIdx); } \
  NS_IMETHOD RemoveRef(int32_t refIdx) override { return !_to ? NS_ERROR_NULL_POINTER : _to->RemoveRef(refIdx); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptRefRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTREFRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptRefRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void CallRef (in int32_t refIdx, in charPtr argsSerialized, in uint32_t argsSize, out IScriptBuffer ret); */
NS_IMETHODIMP _MYCLASS_::CallRef(int32_t refIdx, char *argsSerialized, uint32_t argsSize, IScriptBuffer * *ret)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void DuplicateRef (in int32_t refIdx, out int32_t newRefIdx); */
NS_IMETHODIMP _MYCLASS_::DuplicateRef(int32_t refIdx, int32_t *newRefIdx)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void RemoveRef (in int32_t refIdx); */
NS_IMETHODIMP _MYCLASS_::RemoveRef(int32_t refIdx)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptFileHandlingRuntime */
#define ISCRIPTFILEHANDLINGRUNTIME_IID_STR "567634c6-3bdd-4d0e-af39-7472aed479b7"

#define ISCRIPTFILEHANDLINGRUNTIME_IID \
  {0x567634c6, 0x3bdd, 0x4d0e, \
    { 0xaf, 0x39, 0x74, 0x72, 0xae, 0xd4, 0x79, 0xb7 }}

class NS_NO_VTABLE IScriptFileHandlingRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTFILEHANDLINGRUNTIME_IID)

  /* [notxpcom] int32_t HandlesFile (in charPtr scriptFile, in IScriptHostWithResourceData metadata); */
  NS_IMETHOD_(int32_t) HandlesFile(char *scriptFile, IScriptHostWithResourceData *metadata) = 0;

  /* void LoadFile (in charPtr scriptFile); */
  NS_IMETHOD LoadFile(char *scriptFile) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptFileHandlingRuntime, ISCRIPTFILEHANDLINGRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTFILEHANDLINGRUNTIME \
  NS_IMETHOD_(int32_t) HandlesFile(char *scriptFile, IScriptHostWithResourceData *metadata) override; \
  NS_IMETHOD LoadFile(char *scriptFile) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTFILEHANDLINGRUNTIME(_to) \
  NS_IMETHOD_(int32_t) HandlesFile(char *scriptFile, IScriptHostWithResourceData *metadata) override { return _to HandlesFile(scriptFile, metadata); } \
  NS_IMETHOD LoadFile(char *scriptFile) override { return _to LoadFile(scriptFile); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTFILEHANDLINGRUNTIME(_to) \
  NS_IMETHOD_(int32_t) HandlesFile(char *scriptFile, IScriptHostWithResourceData *metadata) override; \
  NS_IMETHOD LoadFile(char *scriptFile) override { return !_to ? NS_ERROR_NULL_POINTER : _to->LoadFile(scriptFile); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptFileHandlingRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTFILEHANDLINGRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptFileHandlingRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* [notxpcom] int32_t HandlesFile (in charPtr scriptFile, in IScriptHostWithResourceData metadata); */
NS_IMETHODIMP_(int32_t) _MYCLASS_::HandlesFile(char *scriptFile, IScriptHostWithResourceData *metadata)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void LoadFile (in charPtr scriptFile); */
NS_IMETHODIMP _MYCLASS_::LoadFile(char *scriptFile)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptStackWalkVisitor */
#define ISCRIPTSTACKWALKVISITOR_IID_STR "182caaf3-e33d-474b-a6af-33d59ff0e9ed"

#define ISCRIPTSTACKWALKVISITOR_IID \
  {0x182caaf3, 0xe33d, 0x474b, \
    { 0xa6, 0xaf, 0x33, 0xd5, 0x9f, 0xf0, 0xe9, 0xed }}

class NS_NO_VTABLE IScriptStackWalkVisitor : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTSTACKWALKVISITOR_IID)

  /* void SubmitStackFrame (in charPtr frameBlob, in uint32_t frameBlobSize); */
  NS_IMETHOD SubmitStackFrame(char *frameBlob, uint32_t frameBlobSize) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptStackWalkVisitor, ISCRIPTSTACKWALKVISITOR_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTSTACKWALKVISITOR \
  NS_IMETHOD SubmitStackFrame(char *frameBlob, uint32_t frameBlobSize) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTSTACKWALKVISITOR(_to) \
  NS_IMETHOD SubmitStackFrame(char *frameBlob, uint32_t frameBlobSize) override { return _to SubmitStackFrame(frameBlob, frameBlobSize); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTSTACKWALKVISITOR(_to) \
  NS_IMETHOD SubmitStackFrame(char *frameBlob, uint32_t frameBlobSize) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SubmitStackFrame(frameBlob, frameBlobSize); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptStackWalkVisitor
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTSTACKWALKVISITOR

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptStackWalkVisitor)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void SubmitStackFrame (in charPtr frameBlob, in uint32_t frameBlobSize); */
NS_IMETHODIMP _MYCLASS_::SubmitStackFrame(char *frameBlob, uint32_t frameBlobSize)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptStackWalkingRuntime */
#define ISCRIPTSTACKWALKINGRUNTIME_IID_STR "567d2fda-610c-4fa0-ae3e-4f700ae5ce56"

#define ISCRIPTSTACKWALKINGRUNTIME_IID \
  {0x567d2fda, 0x610c, 0x4fa0, \
    { 0xae, 0x3e, 0x4f, 0x70, 0x0a, 0xe5, 0xce, 0x56 }}

class NS_NO_VTABLE IScriptStackWalkingRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTSTACKWALKINGRUNTIME_IID)

  /* void WalkStack (in charPtr boundaryStart, in uint32_t boundaryStartLength, in charPtr boundaryEnd, in uint32_t boundaryEndLength, in IScriptStackWalkVisitor visitor); */
  NS_IMETHOD WalkStack(char *boundaryStart, uint32_t boundaryStartLength, char *boundaryEnd, uint32_t boundaryEndLength, IScriptStackWalkVisitor *visitor) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptStackWalkingRuntime, ISCRIPTSTACKWALKINGRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTSTACKWALKINGRUNTIME \
  NS_IMETHOD WalkStack(char *boundaryStart, uint32_t boundaryStartLength, char *boundaryEnd, uint32_t boundaryEndLength, IScriptStackWalkVisitor *visitor) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTSTACKWALKINGRUNTIME(_to) \
  NS_IMETHOD WalkStack(char *boundaryStart, uint32_t boundaryStartLength, char *boundaryEnd, uint32_t boundaryEndLength, IScriptStackWalkVisitor *visitor) override { return _to WalkStack(boundaryStart, boundaryStartLength, boundaryEnd, boundaryEndLength, visitor); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTSTACKWALKINGRUNTIME(_to) \
  NS_IMETHOD WalkStack(char *boundaryStart, uint32_t boundaryStartLength, char *boundaryEnd, uint32_t boundaryEndLength, IScriptStackWalkVisitor *visitor) override { return !_to ? NS_ERROR_NULL_POINTER : _to->WalkStack(boundaryStart, boundaryStartLength, boundaryEnd, boundaryEndLength, visitor); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptStackWalkingRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTSTACKWALKINGRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptStackWalkingRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void WalkStack (in charPtr boundaryStart, in uint32_t boundaryStartLength, in charPtr boundaryEnd, in uint32_t boundaryEndLength, in IScriptStackWalkVisitor visitor); */
NS_IMETHODIMP _MYCLASS_::WalkStack(char *boundaryStart, uint32_t boundaryStartLength, char *boundaryEnd, uint32_t boundaryEndLength, IScriptStackWalkVisitor *visitor)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptMemInfoRuntime */
#define ISCRIPTMEMINFORUNTIME_IID_STR "d98a35cf-d6ee-4b51-a1c3-99b70f4ec1e6"

#define ISCRIPTMEMINFORUNTIME_IID \
  {0xd98a35cf, 0xd6ee, 0x4b51, \
    { 0xa1, 0xc3, 0x99, 0xb7, 0x0f, 0x4e, 0xc1, 0xe6 }}

class NS_NO_VTABLE IScriptMemInfoRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTMEMINFORUNTIME_IID)

  /* void RequestMemoryUsage (); */
  NS_IMETHOD RequestMemoryUsage(void) = 0;

  /* int64_t GetMemoryUsage (); */
  NS_IMETHOD GetMemoryUsage(int64_t *_retval) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptMemInfoRuntime, ISCRIPTMEMINFORUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTMEMINFORUNTIME \
  NS_IMETHOD RequestMemoryUsage(void) override; \
  NS_IMETHOD GetMemoryUsage(int64_t *_retval) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTMEMINFORUNTIME(_to) \
  NS_IMETHOD RequestMemoryUsage(void) override { return _to RequestMemoryUsage(); } \
  NS_IMETHOD GetMemoryUsage(int64_t *_retval) override { return _to GetMemoryUsage(_retval); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTMEMINFORUNTIME(_to) \
  NS_IMETHOD RequestMemoryUsage(void) override { return !_to ? NS_ERROR_NULL_POINTER : _to->RequestMemoryUsage(); } \
  NS_IMETHOD GetMemoryUsage(int64_t *_retval) override { return !_to ? NS_ERROR_NULL_POINTER : _to->GetMemoryUsage(_retval); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptMemInfoRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTMEMINFORUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptMemInfoRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void RequestMemoryUsage (); */
NS_IMETHODIMP _MYCLASS_::RequestMemoryUsage()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* int64_t GetMemoryUsage (); */
NS_IMETHODIMP _MYCLASS_::GetMemoryUsage(int64_t *_retval)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptWarningRuntime */
#define ISCRIPTWARNINGRUNTIME_IID_STR "d72be411-5152-4474-917c-5361ac051181"

#define ISCRIPTWARNINGRUNTIME_IID \
  {0xd72be411, 0x5152, 0x4474, \
    { 0x91, 0x7c, 0x53, 0x61, 0xac, 0x05, 0x11, 0x81 }}

class NS_NO_VTABLE IScriptWarningRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTWARNINGRUNTIME_IID)

  /* void EmitWarning (in charPtr channel, in charPtr message); */
  NS_IMETHOD EmitWarning(char *channel, char *message) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptWarningRuntime, ISCRIPTWARNINGRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTWARNINGRUNTIME \
  NS_IMETHOD EmitWarning(char *channel, char *message) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTWARNINGRUNTIME(_to) \
  NS_IMETHOD EmitWarning(char *channel, char *message) override { return _to EmitWarning(channel, message); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTWARNINGRUNTIME(_to) \
  NS_IMETHOD EmitWarning(char *channel, char *message) override { return !_to ? NS_ERROR_NULL_POINTER : _to->EmitWarning(channel, message); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptWarningRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTWARNINGRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptWarningRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void EmitWarning (in charPtr channel, in charPtr message); */
NS_IMETHODIMP _MYCLASS_::EmitWarning(char *channel, char *message)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptProfiler */
#define ISCRIPTPROFILER_IID_STR "782a4496-2ae3-4c70-b54a-fad8fd8aeefd"

#define ISCRIPTPROFILER_IID \
  {0x782a4496, 0x2ae3, 0x4c70, \
    { 0xb5, 0x4a, 0xfa, 0xd8, 0xfd, 0x8a, 0xee, 0xfd }}

class NS_NO_VTABLE IScriptProfiler : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTPROFILER_IID)

  /* void SetupFxProfiler (in voidPtr obj, in int32_t resourceId); */
  NS_IMETHOD SetupFxProfiler(void *obj, int32_t resourceId) = 0;

  /* void ShutdownFxProfiler (); */
  NS_IMETHOD ShutdownFxProfiler(void) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptProfiler, ISCRIPTPROFILER_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTPROFILER \
  NS_IMETHOD SetupFxProfiler(void *obj, int32_t resourceId) override; \
  NS_IMETHOD ShutdownFxProfiler(void) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTPROFILER(_to) \
  NS_IMETHOD SetupFxProfiler(void *obj, int32_t resourceId) override { return _to SetupFxProfiler(obj, resourceId); } \
  NS_IMETHOD ShutdownFxProfiler(void) override { return _to ShutdownFxProfiler(); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTPROFILER(_to) \
  NS_IMETHOD SetupFxProfiler(void *obj, int32_t resourceId) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SetupFxProfiler(obj, resourceId); } \
  NS_IMETHOD ShutdownFxProfiler(void) override { return !_to ? NS_ERROR_NULL_POINTER : _to->ShutdownFxProfiler(); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptProfiler
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTPROFILER

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptProfiler)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void SetupFxProfiler (in voidPtr obj, in int32_t resourceId); */
NS_IMETHODIMP _MYCLASS_::SetupFxProfiler(void *obj, int32_t resourceId)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void ShutdownFxProfiler (); */
NS_IMETHODIMP _MYCLASS_::ShutdownFxProfiler()
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IDebugEventListener */
#define IDEBUGEVENTLISTENER_IID_STR "08e68541-a1c9-4f51-8737-3c77f8ab5b21"

#define IDEBUGEVENTLISTENER_IID \
  {0x08e68541, 0xa1c9, 0x4f51, \
    { 0x87, 0x37, 0x3c, 0x77, 0xf8, 0xab, 0x5b, 0x21 }}

class NS_NO_VTABLE IDebugEventListener : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(IDEBUGEVENTLISTENER_IID)

  /* void OnBreakpointsDefined (in int32_t scriptId, in charPtr breakpointJson); */
  NS_IMETHOD OnBreakpointsDefined(int32_t scriptId, char *breakpointJson) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IDebugEventListener, IDEBUGEVENTLISTENER_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_IDEBUGEVENTLISTENER \
  NS_IMETHOD OnBreakpointsDefined(int32_t scriptId, char *breakpointJson) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_IDEBUGEVENTLISTENER(_to) \
  NS_IMETHOD OnBreakpointsDefined(int32_t scriptId, char *breakpointJson) override { return _to OnBreakpointsDefined(scriptId, breakpointJson); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_IDEBUGEVENTLISTENER(_to) \
  NS_IMETHOD OnBreakpointsDefined(int32_t scriptId, char *breakpointJson) override { return !_to ? NS_ERROR_NULL_POINTER : _to->OnBreakpointsDefined(scriptId, breakpointJson); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IDebugEventListener
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_IDEBUGEVENTLISTENER

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IDebugEventListener)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void OnBreakpointsDefined (in int32_t scriptId, in charPtr breakpointJson); */
NS_IMETHODIMP _MYCLASS_::OnBreakpointsDefined(int32_t scriptId, char *breakpointJson)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptDebugRuntime */
#define ISCRIPTDEBUGRUNTIME_IID_STR "7eb9f56a-8ebe-4bb2-b467-a6f6c099597b"

#define ISCRIPTDEBUGRUNTIME_IID \
  {0x7eb9f56a, 0x8ebe, 0x4bb2, \
    { 0xb4, 0x67, 0xa6, 0xf6, 0xc0, 0x99, 0x59, 0x7b }}

class NS_NO_VTABLE IScriptDebugRuntime : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTDEBUGRUNTIME_IID)

  /* void SetDebugEventListener (in IDebugEventListener listener); */
  NS_IMETHOD SetDebugEventListener(IDebugEventListener *listener) = 0;

  /* void SetScriptIdentifier (in charPtr fileName, in int32_t scriptId); */
  NS_IMETHOD SetScriptIdentifier(char *fileName, int32_t scriptId) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptDebugRuntime, ISCRIPTDEBUGRUNTIME_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTDEBUGRUNTIME \
  NS_IMETHOD SetDebugEventListener(IDebugEventListener *listener) override; \
  NS_IMETHOD SetScriptIdentifier(char *fileName, int32_t scriptId) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTDEBUGRUNTIME(_to) \
  NS_IMETHOD SetDebugEventListener(IDebugEventListener *listener) override { return _to SetDebugEventListener(listener); } \
  NS_IMETHOD SetScriptIdentifier(char *fileName, int32_t scriptId) override { return _to SetScriptIdentifier(fileName, scriptId); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTDEBUGRUNTIME(_to) \
  NS_IMETHOD SetDebugEventListener(IDebugEventListener *listener) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SetDebugEventListener(listener); } \
  NS_IMETHOD SetScriptIdentifier(char *fileName, int32_t scriptId) override { return !_to ? NS_ERROR_NULL_POINTER : _to->SetScriptIdentifier(fileName, scriptId); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptDebugRuntime
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTDEBUGRUNTIME

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptDebugRuntime)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void SetDebugEventListener (in IDebugEventListener listener); */
NS_IMETHODIMP _MYCLASS_::SetDebugEventListener(IDebugEventListener *listener)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void SetScriptIdentifier (in charPtr fileName, in int32_t scriptId); */
NS_IMETHODIMP _MYCLASS_::SetScriptIdentifier(char *fileName, int32_t scriptId)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif


/* starting interface:    IScriptHostWithBookmarks */
#define ISCRIPTHOSTWITHBOOKMARKS_IID_STR "2a7e092d-6ce9-4b9d-ac4f-8da818bd0da4"

#define ISCRIPTHOSTWITHBOOKMARKS_IID \
  {0x2a7e092d, 0x6ce9, 0x4b9d, \
    { 0xac, 0x4f, 0x8d, 0xa8, 0x18, 0xbd, 0x0d, 0xa4 }}

class NS_NO_VTABLE IScriptHostWithBookmarks : public fxIBase {
 public:

  NS_DECLARE_STATIC_IID_ACCESSOR(ISCRIPTHOSTWITHBOOKMARKS_IID)

  /* void ScheduleBookmark (in IScriptTickRuntimeWithBookmarks runtime, in uint64_t bookmark, in int64_t deadline); */
  NS_IMETHOD ScheduleBookmark(IScriptTickRuntimeWithBookmarks *runtime, uint64_t bookmark, int64_t deadline) = 0;

  /* void RemoveBookmarks (in IScriptTickRuntimeWithBookmarks runtime); */
  NS_IMETHOD RemoveBookmarks(IScriptTickRuntimeWithBookmarks *runtime) = 0;

  /* void CreateBookmarks (in IScriptTickRuntimeWithBookmarks runtime); */
  NS_IMETHOD CreateBookmarks(IScriptTickRuntimeWithBookmarks *runtime) = 0;

};

  NS_DEFINE_STATIC_IID_ACCESSOR(IScriptHostWithBookmarks, ISCRIPTHOSTWITHBOOKMARKS_IID)

/* Use this macro when declaring classes that implement this interface. */
#define NS_DECL_ISCRIPTHOSTWITHBOOKMARKS \
  NS_IMETHOD ScheduleBookmark(IScriptTickRuntimeWithBookmarks *runtime, uint64_t bookmark, int64_t deadline) override; \
  NS_IMETHOD RemoveBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override; \
  NS_IMETHOD CreateBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override; 

/* Use this macro to declare functions that forward the behavior of this interface to another object. */
#define NS_FORWARD_ISCRIPTHOSTWITHBOOKMARKS(_to) \
  NS_IMETHOD ScheduleBookmark(IScriptTickRuntimeWithBookmarks *runtime, uint64_t bookmark, int64_t deadline) override { return _to ScheduleBookmark(runtime, bookmark, deadline); } \
  NS_IMETHOD RemoveBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override { return _to RemoveBookmarks(runtime); } \
  NS_IMETHOD CreateBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override { return _to CreateBookmarks(runtime); } 

/* Use this macro to declare functions that forward the behavior of this interface to another object in a safe way. */
#define NS_FORWARD_SAFE_ISCRIPTHOSTWITHBOOKMARKS(_to) \
  NS_IMETHOD ScheduleBookmark(IScriptTickRuntimeWithBookmarks *runtime, uint64_t bookmark, int64_t deadline) override { return !_to ? NS_ERROR_NULL_POINTER : _to->ScheduleBookmark(runtime, bookmark, deadline); } \
  NS_IMETHOD RemoveBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->RemoveBookmarks(runtime); } \
  NS_IMETHOD CreateBookmarks(IScriptTickRuntimeWithBookmarks *runtime) override { return !_to ? NS_ERROR_NULL_POINTER : _to->CreateBookmarks(runtime); } 

#if 0
/* Use the code below as a template for the implementation class for this interface. */

/* Header file */
class _MYCLASS_ : public IScriptHostWithBookmarks
{
public:
  NS_DECL_ISUPPORTS
  NS_DECL_ISCRIPTHOSTWITHBOOKMARKS

  _MYCLASS_();

private:
  ~_MYCLASS_();

protected:
  /* additional members */
};

/* Implementation file */
NS_IMPL_ISUPPORTS(_MYCLASS_, IScriptHostWithBookmarks)

_MYCLASS_::_MYCLASS_()
{
  /* member initializers and constructor code */
}

_MYCLASS_::~_MYCLASS_()
{
  /* destructor code */
}

/* void ScheduleBookmark (in IScriptTickRuntimeWithBookmarks runtime, in uint64_t bookmark, in int64_t deadline); */
NS_IMETHODIMP _MYCLASS_::ScheduleBookmark(IScriptTickRuntimeWithBookmarks *runtime, uint64_t bookmark, int64_t deadline)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void RemoveBookmarks (in IScriptTickRuntimeWithBookmarks runtime); */
NS_IMETHODIMP _MYCLASS_::RemoveBookmarks(IScriptTickRuntimeWithBookmarks *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* void CreateBookmarks (in IScriptTickRuntimeWithBookmarks runtime); */
NS_IMETHODIMP _MYCLASS_::CreateBookmarks(IScriptTickRuntimeWithBookmarks *runtime)
{
    return NS_ERROR_NOT_IMPLEMENTED;
}

/* End of implementation class template. */
#endif

#include "PushEnvironment.h"

#endif /* __gen_fxScripting_h__ */
