# Copyright 2016 the V8 project authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

[
[ALWAYS, {
  'skip-stack-guard-page': [PASS, ['((arch == ppc or arch == ppc64 or arch == s390 or arch == s390x) and simulator_run)', SKIP]],
  # Missing rebase in the proposal repository.
  'proposals/js-types/table': [FAIL],
  # "data" is supposed to fail for "data segment does not fit"; missing rebase
  # on https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/data': [FAIL],
  # "elem" is supposed to fail for "elements segment does not fit"; missing
  # rebase on https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/elem': [FAIL],
  # "imports" is supposed to fail for "multiple tables"; missing rebase on
  # https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/imports': [FAIL],
  # "linking" is supposed to fail for "elements segment does not fit"; missing
  # rebase on https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/linking': [FAIL],
  # "table" is supposed to fail for "multiple tables"; missing rebase on
  # https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/table': [FAIL],
  # "unreached-invalid" is supposed to fail for "type mismatch"; missing rebase
  # on https://github.com/WebAssembly/spec/commit/7fa2f20a.
  'proposals/memory64/unreached-invalid': [FAIL],

  'proposals/gc/imports': [FAIL],
  'proposals/extended-const/imports': [FAIL],
  'proposals/js-types/imports': [FAIL],
  'proposals/tail-call/imports': [FAIL],

  # TODO(wasm): Roll newest tests into "js-types" repository.
  'proposals/js-types/globals': [FAIL],

  # Tests that need to run sequentially (e.g. due to memory consumption).
  'simd_f32x4*': [PASS, HEAVY],
  'simd_f64x2*': [PASS, HEAVY],
  'f32*': [PASS, HEAVY],
  'f64*': [PASS, HEAVY],

  # The function-references spec tests haven't been updated to the final
  # binary encoding. We don't care, because the GC tests are up to date and
  # we'll ship both proposals together.
  'proposals/function-references/*': [SKIP],

  # These break with the GC proposal (because they check that certain modules
  # don't validate that become valid with GC). The GC proposal will update them
  # when it is merged into the main spec repository.
  'data': [FAIL],
  'proposals/extended-const/data': [FAIL],
  'proposals/multi-memory/data': [FAIL],

  # These break with the GC proposal (because they check that certain modules
  # don't validate that become valid with GC). The GC proposal will update them
  # when it is merged into the main spec repository.
  # With the GC proposal global.get is a constant instruction, independent on
  # the type of the global. In the main spec repository, only global.get of
  # imported globals are allowed.
  'elem': [FAIL],
  'proposals/extended-const/elem': [FAIL],
  'proposals/js-types/elem': [FAIL],
  'proposals/multi-memory/elem': [FAIL],
  'proposals/tail-call/elem': [FAIL],
  'proposals/exception-handling/elem': [FAIL],

  'global': [FAIL],
  'proposals/extended-const/global': [FAIL],

  # These tests need to be rebased on the multi-memory proposal once it's
  # merged into the main repository. They test that not more than one memory
  # can be declared ("memory" / "memory64") or imported ("imports"), or that
  # the byte for encoding the memory index is a single byte (and no LEB,
  # "binary").
  'binary': [FAIL],
  'imports': [FAIL],
  'memory': [FAIL],
  'proposals/extended-const/binary': [FAIL],
  'proposals/gc/binary': [FAIL],
  'proposals/js-types/binary': [FAIL],
  'proposals/memory64/binary': [FAIL],
  'proposals/memory64/memory64': [FAIL],
  'proposals/memory64/memory': [FAIL],
  'proposals/tail-call/binary': [FAIL],
  'proposals/exception-handling/binary': [FAIL],
  'proposals/exception-handling/imports': [FAIL],
}],  # ALWAYS

['arch == arm and not simulator_run', {
  # See https://crbug.com/v8/10938 denormals not handled correctly on ARM.
  'simd_f32x4': [PASS, FAIL],
  'simd_f32x4_arith': [PASS, FAIL],
  'simd_f32x4_cmp': [PASS, FAIL],
  # This test only has 1 problematic use of f32x4.min and f32x4.div, consider
  # removing it from upstream, then we can run this test.
  'simd_splat' : [PASS, FAIL],
  'simd_f32x4_pmin_pmax' : [PASS, FAIL],
}],  # arch == arm and not simulator_run

['arch == mips64el or arch == mips64', {
  # These tests fail because mips does not support the correct NaN bit patterns.
  'float_misc': [SKIP],
  'float_exprs': [SKIP],
  'f32': [SKIP],
  'f64': [SKIP],
  'f32_bitwise': [SKIP],
  'f64_bitwise': [SKIP],
  'proposals/reference-types/conversions':  [SKIP],
  'proposals/bulk-memory-operations/conversions': [SKIP],
  'proposals/js-types/f32': [SKIP],
  'proposals/js-types/f64': [SKIP],
  'proposals/js-types/f32_bitwise': [SKIP],
  'proposals/js-types/f64_bitwise': [SKIP],
  'proposals/js-types/float_exprs': [SKIP],
  'proposals/js-types/float_misc': [SKIP],
  'proposals/js-types/conversions': [SKIP],
  'proposals/bulk-memory-operations/f32': [SKIP],
  'proposals/bulk-memory-operations/f64': [SKIP],
  'proposals/reference-types/f32': [SKIP],
  'proposals/reference-types/f64': [SKIP],
  'proposals/bulk-memory-operations/float_misc': [SKIP],
  'proposals/reference-types/float_misc': [SKIP],
  'proposals/tail-call/f32': [SKIP],
  'proposals/tail-call/f32_bitwise': [SKIP],
  'proposals/tail-call/f64': [SKIP],
  'proposals/tail-call/f64_bitwise': [SKIP],
  'proposals/tail-call/float_exprs': [SKIP],
  'proposals/tail-call/float_misc': [SKIP],
  'proposals/tail-call/conversions': [SKIP],
}],  # 'arch == mips64el or arch == mips64'

['(arch == mips64el or arch == mips64) and not simulator_run', {
  # This test fail because mips does not support the correct NaN bit patterns.
  # But it doesn't fail in simulator.
  'conversions': [SKIP],
}],  # '(arch == mips64el or arch == mips64) and not simulator_run'

['(arch == mips64el or arch == loong64) and simulator_run', {
  # These tests need larger stack size on simulator.
  'skip-stack-guard-page': '--sim-stack-size=8192',
  'proposals/tail-call/skip-stack-guard-page': '--sim-stack-size=8192',
}],  # '(arch == mips64el or arch == loong64) and simulator_run'

['arch == riscv64', {
   # These tests need larger stack size on simulator.
   'skip-stack-guard-page': '--sim-stack-size=8192',
   'proposals/tail-call/skip-stack-guard-page': '--sim-stack-size=8192',
}],  # 'arch == riscv64'



['arch == riscv32', {
   # These tests need larger stack size on simulator.
   'skip-stack-guard-page': '--sim-stack-size=8192',
   'proposals/tail-call/skip-stack-guard-page': '--sim-stack-size=8192',

   'func': ['variant == stress', SKIP],
}],  # 'arch == riscv32'

['arch == ppc or arch == ppc64', {
  # These tests fail because ppc float min and max doesn't convert sNaN to qNaN.
  'f32': [SKIP],
  'f64': [SKIP],
  'proposals/js-types/f32': [SKIP],
  'proposals/js-types/f64': [SKIP],
  'proposals/bulk-memory-operations/f32': [SKIP],
  'proposals/bulk-memory-operations/f64': [SKIP],
  'proposals/reference-types/f32': [SKIP],
  'proposals/reference-types/f64': [SKIP],
  'proposals/tail-call/f32': [SKIP],
  'proposals/tail-call/f64': [SKIP],
  # This test fails because ppc float to double doesn't convert sNaN to qNaN.
  'conversions': [SKIP],
  'proposals/js-types/conversions': [SKIP],
  'proposals/bulk-memory-operations/conversions': [SKIP],
  'proposals/reference-types/conversions':  [SKIP],
  'proposals/tail-call/conversions':  [SKIP],
}],  # 'arch == ppc or arch == ppc64'

['arch == s390 or arch == s390x', {
  # These tests fail because s390 float min and max doesn't convert sNaN to qNaN.
  'f32': [SKIP],
  'f64': [SKIP],
  'proposals/js-types/f32': [SKIP],
  'proposals/js-types/f64': [SKIP],
  'proposals/bulk-memory-operations/f32': [SKIP],
  'proposals/bulk-memory-operations/f64': [SKIP],
  'proposals/reference-types/f32': [SKIP],
  'proposals/reference-types/f64': [SKIP],
  'proposals/tail-call/f32': [SKIP],
  'proposals/tail-call/f64': [SKIP],
}],  # 'arch == s390 or arch == s390x'

##############################################################################
# TODO(v8:7777): Change this once wasm is supported in jitless mode.
['not has_webassembly or variant == jitless', {
  '*': [SKIP],
}],  # not has_webassembly or variant == jitless

##############################################################################
['variant == stress_snapshot', {
  '*': [SKIP],  # only relevant for mjsunit tests.
}],  # variant == stress_snapshot

##############################################################################
['no_simd_hardware == True', {
  'linking': [SKIP],
  'simd*': [SKIP],
  'proposals/js-types/linking': [SKIP],
  'proposals/multi-memory/simd_memory-multi': [SKIP],
  'proposals/tail-call/simd_lane': [SKIP],
  'proposals/memory64/simd_address': [SKIP],
}],  # no_simd_hardware == True

##############################################################################
['variant == stress', {
  # Spec tests are executing long enough even without stress mode.
  # As stress mode is unlikely to flush out bugs, skip the tests there.
  '*': [SKIP],
}],  # variant == stress

##############################################################################
# Skip tests that require a large amount of virtual address space (inside the
# sandbox if that is enabled) if tsan is enabled.
['tsan == True', {
  'memory_copy': [SKIP],
}],  # tsan == True

]
