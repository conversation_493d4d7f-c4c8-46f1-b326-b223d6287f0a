#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: no
test function name: f

---
snippet: "
  function f(a) { a.name = \"val\"; }
  f({name : \"test\"})
"
frame size: 0
parameter count: 2
bytecode array length: 8
bytecodes: [
  /*   16 S> */ B(LdaConstant), U8(0),
  /*   23 E> */ B(SetNamedProperty), R(arg0), U8(1), U8(0),
                B(LdaUndefined),
  /*   32 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a) { a[\"key\"] = \"val\"; }
  f({key : \"test\"})
"
frame size: 0
parameter count: 2
bytecode array length: 8
bytecodes: [
  /*   16 S> */ B(LdaConstant), U8(0),
  /*   25 E> */ B(SetNamedProperty), R(arg0), U8(1), U8(0),
                B(LdaUndefined),
  /*   34 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["key"],
]
handlers: [
]

---
snippet: "
  function f(a) { a[100] = \"val\"; }
  f({100 : \"test\"})
"
frame size: 2
parameter count: 2
bytecode array length: 11
bytecodes: [
  /*   16 S> */ B(LdaSmi), I8(100),
                B(Star1),
                B(LdaConstant), U8(0),
  /*   23 E> */ B(SetKeyedProperty), R(arg0), R(1), U8(0),
                B(LdaUndefined),
  /*   32 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  function f(a, b) { a[b] = \"val\"; }
  f({arg : \"test\"}, \"arg\")
"
frame size: 0
parameter count: 3
bytecode array length: 8
bytecodes: [
  /*   19 S> */ B(LdaConstant), U8(0),
  /*   24 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(0),
                B(LdaUndefined),
  /*   33 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  function f(a) { a.name = a[-124]; }
  f({\"-124\" : \"test\", name : 123 })
"
frame size: 0
parameter count: 2
bytecode array length: 11
bytecodes: [
  /*   16 S> */ B(LdaSmi), I8(-124),
  /*   26 E> */ B(GetKeyedProperty), R(arg0), U8(0),
  /*   23 E> */ B(SetNamedProperty), R(arg0), U8(0), U8(2),
                B(LdaUndefined),
  /*   34 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a) { \"use strict\"; a.name = \"val\"; }
  f({name : \"test\"})
"
frame size: 0
parameter count: 2
bytecode array length: 8
bytecodes: [
  /*   30 S> */ B(LdaConstant), U8(0),
  /*   37 E> */ B(SetNamedProperty), R(arg0), U8(1), U8(0),
                B(LdaUndefined),
  /*   46 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
]
handlers: [
]

---
snippet: "
  function f(a, b) { \"use strict\"; a[b] = \"val\"; }
  f({arg : \"test\"}, \"arg\")
"
frame size: 0
parameter count: 3
bytecode array length: 8
bytecodes: [
  /*   33 S> */ B(LdaConstant), U8(0),
  /*   38 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(0),
                B(LdaUndefined),
  /*   47 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["val"],
]
handlers: [
]

---
snippet: "
  function f(a) {
    a.name = 1;
    var b = {};
    b.name128;
    b.name129;
    b.name130;
    b.name131;
    b.name132;
    b.name133;
    b.name134;
    b.name135;
    b.name136;
    b.name137;
    b.name138;
    b.name139;
    b.name140;
    b.name141;
    b.name142;
    b.name143;
    b.name144;
    b.name145;
    b.name146;
    b.name147;
    b.name148;
    b.name149;
    b.name150;
    b.name151;
    b.name152;
    b.name153;
    b.name154;
    b.name155;
    b.name156;
    b.name157;
    b.name158;
    b.name159;
    b.name160;
    b.name161;
    b.name162;
    b.name163;
    b.name164;
    b.name165;
    b.name166;
    b.name167;
    b.name168;
    b.name169;
    b.name170;
    b.name171;
    b.name172;
    b.name173;
    b.name174;
    b.name175;
    b.name176;
    b.name177;
    b.name178;
    b.name179;
    b.name180;
    b.name181;
    b.name182;
    b.name183;
    b.name184;
    b.name185;
    b.name186;
    b.name187;
    b.name188;
    b.name189;
    b.name190;
    b.name191;
    b.name192;
    b.name193;
    b.name194;
    b.name195;
    b.name196;
    b.name197;
    b.name198;
    b.name199;
    b.name200;
    b.name201;
    b.name202;
    b.name203;
    b.name204;
    b.name205;
    b.name206;
    b.name207;
    b.name208;
    b.name209;
    b.name210;
    b.name211;
    b.name212;
    b.name213;
    b.name214;
    b.name215;
    b.name216;
    b.name217;
    b.name218;
    b.name219;
    b.name220;
    b.name221;
    b.name222;
    b.name223;
    b.name224;
    b.name225;
    b.name226;
    b.name227;
    b.name228;
    b.name229;
    b.name230;
    b.name231;
    b.name232;
    b.name233;
    b.name234;
    b.name235;
    b.name236;
    b.name237;
    b.name238;
    b.name239;
    b.name240;
    b.name241;
    b.name242;
    b.name243;
    b.name244;
    b.name245;
    b.name246;
    b.name247;
    b.name248;
    b.name249;
    b.name250;
    b.name251;
    b.name252;
    b.name253;
    b.name254;
    b.name255;
    a.name = 2;
  }
  f({name : \"test\"})
"
frame size: 1
parameter count: 2
bytecode array length: 532
bytecodes: [
  /*   18 S> */ B(LdaSmi), I8(1),
  /*   25 E> */ B(SetNamedProperty), R(arg0), U8(0), U8(0),
  /*   40 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   48 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   61 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   74 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*   87 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*  100 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  113 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  126 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  139 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  152 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  165 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  178 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  191 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  204 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  217 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  230 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  243 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  256 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  269 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  282 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  295 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  308 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  321 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  334 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  347 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  360 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  373 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  386 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  399 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  412 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  425 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  438 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  451 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  464 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  477 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  490 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  503 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  516 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  529 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  542 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  555 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  568 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  581 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  594 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  607 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  620 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  633 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  646 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  659 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  672 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  685 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  698 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  711 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  724 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  737 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  750 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  763 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  776 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  789 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  802 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  815 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  828 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  841 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  854 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  867 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  880 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  893 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  906 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  919 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  932 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  945 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  958 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  971 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /*  984 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /*  997 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /* 1010 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /* 1023 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /* 1036 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /* 1049 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /* 1062 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /* 1075 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /* 1088 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1101 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1114 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1127 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1140 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1153 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1166 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1179 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1192 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1205 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1218 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1231 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1244 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1257 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1270 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1283 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1296 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1309 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1322 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1335 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1348 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1361 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1374 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1387 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1400 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1413 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1426 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1439 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1452 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1465 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1478 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1491 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1504 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1517 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1530 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1543 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1556 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1569 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1582 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1595 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1608 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1621 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1634 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1647 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1660 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1673 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1686 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1699 S> */ B(Wide), B(GetNamedProperty), R16(0), U16(128), U16(256),
  /* 1710 S> */ B(LdaSmi), I8(2),
  /* 1717 E> */ B(SetNamedProperty), R(arg0), U8(0), U8(0),
                B(LdaUndefined),
  /* 1722 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name128"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name129"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name130"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name131"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name132"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name133"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name134"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name135"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name136"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name137"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name138"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name139"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name140"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name141"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name142"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name143"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name144"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name145"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name146"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name147"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name148"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name149"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name150"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name151"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name152"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name153"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name154"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name155"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name156"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name157"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name158"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name159"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name160"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name161"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name162"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name163"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name164"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name165"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name166"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name167"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name168"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name169"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name170"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name171"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name172"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name173"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name174"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name175"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name176"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name177"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name178"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name179"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name180"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name181"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name182"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name183"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name184"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name185"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name186"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name187"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name188"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name189"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name190"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name191"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name192"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name193"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name194"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name195"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name196"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name197"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name198"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name199"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name200"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name201"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name202"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name203"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name204"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name205"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name206"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name207"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name208"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name209"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name210"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name211"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name212"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name213"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name214"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name215"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name216"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name217"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name218"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name219"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name220"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name221"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name222"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name223"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name224"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name225"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name226"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name227"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name228"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name229"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name230"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name231"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name232"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name233"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name234"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name235"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name236"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name237"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name238"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name239"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name240"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name241"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name242"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name243"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name244"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name245"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name246"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name247"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name248"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name249"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name250"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name251"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name252"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name253"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name254"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name255"],
]
handlers: [
]

---
snippet: "
  function f(a) {
   'use strict';
    a.name = 1;
    var b = {};
    b.name256;
    b.name257;
    b.name258;
    b.name259;
    b.name260;
    b.name261;
    b.name262;
    b.name263;
    b.name264;
    b.name265;
    b.name266;
    b.name267;
    b.name268;
    b.name269;
    b.name270;
    b.name271;
    b.name272;
    b.name273;
    b.name274;
    b.name275;
    b.name276;
    b.name277;
    b.name278;
    b.name279;
    b.name280;
    b.name281;
    b.name282;
    b.name283;
    b.name284;
    b.name285;
    b.name286;
    b.name287;
    b.name288;
    b.name289;
    b.name290;
    b.name291;
    b.name292;
    b.name293;
    b.name294;
    b.name295;
    b.name296;
    b.name297;
    b.name298;
    b.name299;
    b.name300;
    b.name301;
    b.name302;
    b.name303;
    b.name304;
    b.name305;
    b.name306;
    b.name307;
    b.name308;
    b.name309;
    b.name310;
    b.name311;
    b.name312;
    b.name313;
    b.name314;
    b.name315;
    b.name316;
    b.name317;
    b.name318;
    b.name319;
    b.name320;
    b.name321;
    b.name322;
    b.name323;
    b.name324;
    b.name325;
    b.name326;
    b.name327;
    b.name328;
    b.name329;
    b.name330;
    b.name331;
    b.name332;
    b.name333;
    b.name334;
    b.name335;
    b.name336;
    b.name337;
    b.name338;
    b.name339;
    b.name340;
    b.name341;
    b.name342;
    b.name343;
    b.name344;
    b.name345;
    b.name346;
    b.name347;
    b.name348;
    b.name349;
    b.name350;
    b.name351;
    b.name352;
    b.name353;
    b.name354;
    b.name355;
    b.name356;
    b.name357;
    b.name358;
    b.name359;
    b.name360;
    b.name361;
    b.name362;
    b.name363;
    b.name364;
    b.name365;
    b.name366;
    b.name367;
    b.name368;
    b.name369;
    b.name370;
    b.name371;
    b.name372;
    b.name373;
    b.name374;
    b.name375;
    b.name376;
    b.name377;
    b.name378;
    b.name379;
    b.name380;
    b.name381;
    b.name382;
    b.name383;
    a.name = 2;
  }
  f({name : \"test\"})
"
frame size: 1
parameter count: 2
bytecode array length: 532
bytecodes: [
  /*   33 S> */ B(LdaSmi), I8(1),
  /*   40 E> */ B(SetNamedProperty), R(arg0), U8(0), U8(0),
  /*   55 S> */ B(CreateEmptyObjectLiteral),
                B(Star0),
  /*   63 S> */ B(GetNamedProperty), R(0), U8(1), U8(2),
  /*   76 S> */ B(GetNamedProperty), R(0), U8(2), U8(4),
  /*   89 S> */ B(GetNamedProperty), R(0), U8(3), U8(6),
  /*  102 S> */ B(GetNamedProperty), R(0), U8(4), U8(8),
  /*  115 S> */ B(GetNamedProperty), R(0), U8(5), U8(10),
  /*  128 S> */ B(GetNamedProperty), R(0), U8(6), U8(12),
  /*  141 S> */ B(GetNamedProperty), R(0), U8(7), U8(14),
  /*  154 S> */ B(GetNamedProperty), R(0), U8(8), U8(16),
  /*  167 S> */ B(GetNamedProperty), R(0), U8(9), U8(18),
  /*  180 S> */ B(GetNamedProperty), R(0), U8(10), U8(20),
  /*  193 S> */ B(GetNamedProperty), R(0), U8(11), U8(22),
  /*  206 S> */ B(GetNamedProperty), R(0), U8(12), U8(24),
  /*  219 S> */ B(GetNamedProperty), R(0), U8(13), U8(26),
  /*  232 S> */ B(GetNamedProperty), R(0), U8(14), U8(28),
  /*  245 S> */ B(GetNamedProperty), R(0), U8(15), U8(30),
  /*  258 S> */ B(GetNamedProperty), R(0), U8(16), U8(32),
  /*  271 S> */ B(GetNamedProperty), R(0), U8(17), U8(34),
  /*  284 S> */ B(GetNamedProperty), R(0), U8(18), U8(36),
  /*  297 S> */ B(GetNamedProperty), R(0), U8(19), U8(38),
  /*  310 S> */ B(GetNamedProperty), R(0), U8(20), U8(40),
  /*  323 S> */ B(GetNamedProperty), R(0), U8(21), U8(42),
  /*  336 S> */ B(GetNamedProperty), R(0), U8(22), U8(44),
  /*  349 S> */ B(GetNamedProperty), R(0), U8(23), U8(46),
  /*  362 S> */ B(GetNamedProperty), R(0), U8(24), U8(48),
  /*  375 S> */ B(GetNamedProperty), R(0), U8(25), U8(50),
  /*  388 S> */ B(GetNamedProperty), R(0), U8(26), U8(52),
  /*  401 S> */ B(GetNamedProperty), R(0), U8(27), U8(54),
  /*  414 S> */ B(GetNamedProperty), R(0), U8(28), U8(56),
  /*  427 S> */ B(GetNamedProperty), R(0), U8(29), U8(58),
  /*  440 S> */ B(GetNamedProperty), R(0), U8(30), U8(60),
  /*  453 S> */ B(GetNamedProperty), R(0), U8(31), U8(62),
  /*  466 S> */ B(GetNamedProperty), R(0), U8(32), U8(64),
  /*  479 S> */ B(GetNamedProperty), R(0), U8(33), U8(66),
  /*  492 S> */ B(GetNamedProperty), R(0), U8(34), U8(68),
  /*  505 S> */ B(GetNamedProperty), R(0), U8(35), U8(70),
  /*  518 S> */ B(GetNamedProperty), R(0), U8(36), U8(72),
  /*  531 S> */ B(GetNamedProperty), R(0), U8(37), U8(74),
  /*  544 S> */ B(GetNamedProperty), R(0), U8(38), U8(76),
  /*  557 S> */ B(GetNamedProperty), R(0), U8(39), U8(78),
  /*  570 S> */ B(GetNamedProperty), R(0), U8(40), U8(80),
  /*  583 S> */ B(GetNamedProperty), R(0), U8(41), U8(82),
  /*  596 S> */ B(GetNamedProperty), R(0), U8(42), U8(84),
  /*  609 S> */ B(GetNamedProperty), R(0), U8(43), U8(86),
  /*  622 S> */ B(GetNamedProperty), R(0), U8(44), U8(88),
  /*  635 S> */ B(GetNamedProperty), R(0), U8(45), U8(90),
  /*  648 S> */ B(GetNamedProperty), R(0), U8(46), U8(92),
  /*  661 S> */ B(GetNamedProperty), R(0), U8(47), U8(94),
  /*  674 S> */ B(GetNamedProperty), R(0), U8(48), U8(96),
  /*  687 S> */ B(GetNamedProperty), R(0), U8(49), U8(98),
  /*  700 S> */ B(GetNamedProperty), R(0), U8(50), U8(100),
  /*  713 S> */ B(GetNamedProperty), R(0), U8(51), U8(102),
  /*  726 S> */ B(GetNamedProperty), R(0), U8(52), U8(104),
  /*  739 S> */ B(GetNamedProperty), R(0), U8(53), U8(106),
  /*  752 S> */ B(GetNamedProperty), R(0), U8(54), U8(108),
  /*  765 S> */ B(GetNamedProperty), R(0), U8(55), U8(110),
  /*  778 S> */ B(GetNamedProperty), R(0), U8(56), U8(112),
  /*  791 S> */ B(GetNamedProperty), R(0), U8(57), U8(114),
  /*  804 S> */ B(GetNamedProperty), R(0), U8(58), U8(116),
  /*  817 S> */ B(GetNamedProperty), R(0), U8(59), U8(118),
  /*  830 S> */ B(GetNamedProperty), R(0), U8(60), U8(120),
  /*  843 S> */ B(GetNamedProperty), R(0), U8(61), U8(122),
  /*  856 S> */ B(GetNamedProperty), R(0), U8(62), U8(124),
  /*  869 S> */ B(GetNamedProperty), R(0), U8(63), U8(126),
  /*  882 S> */ B(GetNamedProperty), R(0), U8(64), U8(128),
  /*  895 S> */ B(GetNamedProperty), R(0), U8(65), U8(130),
  /*  908 S> */ B(GetNamedProperty), R(0), U8(66), U8(132),
  /*  921 S> */ B(GetNamedProperty), R(0), U8(67), U8(134),
  /*  934 S> */ B(GetNamedProperty), R(0), U8(68), U8(136),
  /*  947 S> */ B(GetNamedProperty), R(0), U8(69), U8(138),
  /*  960 S> */ B(GetNamedProperty), R(0), U8(70), U8(140),
  /*  973 S> */ B(GetNamedProperty), R(0), U8(71), U8(142),
  /*  986 S> */ B(GetNamedProperty), R(0), U8(72), U8(144),
  /*  999 S> */ B(GetNamedProperty), R(0), U8(73), U8(146),
  /* 1012 S> */ B(GetNamedProperty), R(0), U8(74), U8(148),
  /* 1025 S> */ B(GetNamedProperty), R(0), U8(75), U8(150),
  /* 1038 S> */ B(GetNamedProperty), R(0), U8(76), U8(152),
  /* 1051 S> */ B(GetNamedProperty), R(0), U8(77), U8(154),
  /* 1064 S> */ B(GetNamedProperty), R(0), U8(78), U8(156),
  /* 1077 S> */ B(GetNamedProperty), R(0), U8(79), U8(158),
  /* 1090 S> */ B(GetNamedProperty), R(0), U8(80), U8(160),
  /* 1103 S> */ B(GetNamedProperty), R(0), U8(81), U8(162),
  /* 1116 S> */ B(GetNamedProperty), R(0), U8(82), U8(164),
  /* 1129 S> */ B(GetNamedProperty), R(0), U8(83), U8(166),
  /* 1142 S> */ B(GetNamedProperty), R(0), U8(84), U8(168),
  /* 1155 S> */ B(GetNamedProperty), R(0), U8(85), U8(170),
  /* 1168 S> */ B(GetNamedProperty), R(0), U8(86), U8(172),
  /* 1181 S> */ B(GetNamedProperty), R(0), U8(87), U8(174),
  /* 1194 S> */ B(GetNamedProperty), R(0), U8(88), U8(176),
  /* 1207 S> */ B(GetNamedProperty), R(0), U8(89), U8(178),
  /* 1220 S> */ B(GetNamedProperty), R(0), U8(90), U8(180),
  /* 1233 S> */ B(GetNamedProperty), R(0), U8(91), U8(182),
  /* 1246 S> */ B(GetNamedProperty), R(0), U8(92), U8(184),
  /* 1259 S> */ B(GetNamedProperty), R(0), U8(93), U8(186),
  /* 1272 S> */ B(GetNamedProperty), R(0), U8(94), U8(188),
  /* 1285 S> */ B(GetNamedProperty), R(0), U8(95), U8(190),
  /* 1298 S> */ B(GetNamedProperty), R(0), U8(96), U8(192),
  /* 1311 S> */ B(GetNamedProperty), R(0), U8(97), U8(194),
  /* 1324 S> */ B(GetNamedProperty), R(0), U8(98), U8(196),
  /* 1337 S> */ B(GetNamedProperty), R(0), U8(99), U8(198),
  /* 1350 S> */ B(GetNamedProperty), R(0), U8(100), U8(200),
  /* 1363 S> */ B(GetNamedProperty), R(0), U8(101), U8(202),
  /* 1376 S> */ B(GetNamedProperty), R(0), U8(102), U8(204),
  /* 1389 S> */ B(GetNamedProperty), R(0), U8(103), U8(206),
  /* 1402 S> */ B(GetNamedProperty), R(0), U8(104), U8(208),
  /* 1415 S> */ B(GetNamedProperty), R(0), U8(105), U8(210),
  /* 1428 S> */ B(GetNamedProperty), R(0), U8(106), U8(212),
  /* 1441 S> */ B(GetNamedProperty), R(0), U8(107), U8(214),
  /* 1454 S> */ B(GetNamedProperty), R(0), U8(108), U8(216),
  /* 1467 S> */ B(GetNamedProperty), R(0), U8(109), U8(218),
  /* 1480 S> */ B(GetNamedProperty), R(0), U8(110), U8(220),
  /* 1493 S> */ B(GetNamedProperty), R(0), U8(111), U8(222),
  /* 1506 S> */ B(GetNamedProperty), R(0), U8(112), U8(224),
  /* 1519 S> */ B(GetNamedProperty), R(0), U8(113), U8(226),
  /* 1532 S> */ B(GetNamedProperty), R(0), U8(114), U8(228),
  /* 1545 S> */ B(GetNamedProperty), R(0), U8(115), U8(230),
  /* 1558 S> */ B(GetNamedProperty), R(0), U8(116), U8(232),
  /* 1571 S> */ B(GetNamedProperty), R(0), U8(117), U8(234),
  /* 1584 S> */ B(GetNamedProperty), R(0), U8(118), U8(236),
  /* 1597 S> */ B(GetNamedProperty), R(0), U8(119), U8(238),
  /* 1610 S> */ B(GetNamedProperty), R(0), U8(120), U8(240),
  /* 1623 S> */ B(GetNamedProperty), R(0), U8(121), U8(242),
  /* 1636 S> */ B(GetNamedProperty), R(0), U8(122), U8(244),
  /* 1649 S> */ B(GetNamedProperty), R(0), U8(123), U8(246),
  /* 1662 S> */ B(GetNamedProperty), R(0), U8(124), U8(248),
  /* 1675 S> */ B(GetNamedProperty), R(0), U8(125), U8(250),
  /* 1688 S> */ B(GetNamedProperty), R(0), U8(126), U8(252),
  /* 1701 S> */ B(GetNamedProperty), R(0), U8(127), U8(254),
  /* 1714 S> */ B(Wide), B(GetNamedProperty), R16(0), U16(128), U16(256),
  /* 1725 S> */ B(LdaSmi), I8(2),
  /* 1732 E> */ B(SetNamedProperty), R(arg0), U8(0), U8(0),
                B(LdaUndefined),
  /* 1737 S> */ B(Return),
]
constant pool: [
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name256"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name257"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name258"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name259"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name260"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name261"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name262"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name263"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name264"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name265"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name266"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name267"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name268"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name269"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name270"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name271"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name272"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name273"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name274"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name275"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name276"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name277"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name278"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name279"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name280"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name281"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name282"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name283"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name284"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name285"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name286"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name287"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name288"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name289"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name290"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name291"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name292"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name293"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name294"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name295"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name296"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name297"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name298"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name299"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name300"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name301"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name302"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name303"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name304"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name305"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name306"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name307"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name308"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name309"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name310"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name311"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name312"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name313"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name314"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name315"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name316"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name317"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name318"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name319"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name320"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name321"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name322"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name323"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name324"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name325"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name326"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name327"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name328"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name329"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name330"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name331"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name332"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name333"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name334"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name335"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name336"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name337"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name338"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name339"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name340"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name341"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name342"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name343"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name344"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name345"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name346"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name347"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name348"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name349"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name350"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name351"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name352"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name353"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name354"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name355"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name356"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name357"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name358"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name359"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name360"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name361"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name362"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name363"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name364"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name365"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name366"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name367"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name368"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name369"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name370"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name371"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name372"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name373"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name374"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name375"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name376"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name377"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name378"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name379"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name380"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name381"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name382"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["name383"],
]
handlers: [
]

---
snippet: "
  function f(a, b) {
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 2;
  }
  f({name : \"test\"})
"
frame size: 0
parameter count: 3
bytecode array length: 780
bytecodes: [
  /*   21 S> */ B(LdaSmi), I8(1),
  /*   26 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(0),
  /*   33 S> */ B(LdaSmi), I8(1),
  /*   38 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(2),
  /*   45 S> */ B(LdaSmi), I8(1),
  /*   50 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(4),
  /*   57 S> */ B(LdaSmi), I8(1),
  /*   62 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(6),
  /*   69 S> */ B(LdaSmi), I8(1),
  /*   74 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(8),
  /*   81 S> */ B(LdaSmi), I8(1),
  /*   86 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(10),
  /*   93 S> */ B(LdaSmi), I8(1),
  /*   98 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(12),
  /*  105 S> */ B(LdaSmi), I8(1),
  /*  110 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(14),
  /*  117 S> */ B(LdaSmi), I8(1),
  /*  122 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(16),
  /*  129 S> */ B(LdaSmi), I8(1),
  /*  134 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(18),
  /*  141 S> */ B(LdaSmi), I8(1),
  /*  146 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(20),
  /*  153 S> */ B(LdaSmi), I8(1),
  /*  158 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(22),
  /*  165 S> */ B(LdaSmi), I8(1),
  /*  170 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(24),
  /*  177 S> */ B(LdaSmi), I8(1),
  /*  182 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(26),
  /*  189 S> */ B(LdaSmi), I8(1),
  /*  194 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(28),
  /*  201 S> */ B(LdaSmi), I8(1),
  /*  206 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(30),
  /*  213 S> */ B(LdaSmi), I8(1),
  /*  218 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(32),
  /*  225 S> */ B(LdaSmi), I8(1),
  /*  230 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(34),
  /*  237 S> */ B(LdaSmi), I8(1),
  /*  242 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(36),
  /*  249 S> */ B(LdaSmi), I8(1),
  /*  254 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(38),
  /*  261 S> */ B(LdaSmi), I8(1),
  /*  266 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(40),
  /*  273 S> */ B(LdaSmi), I8(1),
  /*  278 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(42),
  /*  285 S> */ B(LdaSmi), I8(1),
  /*  290 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(44),
  /*  297 S> */ B(LdaSmi), I8(1),
  /*  302 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(46),
  /*  309 S> */ B(LdaSmi), I8(1),
  /*  314 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(48),
  /*  321 S> */ B(LdaSmi), I8(1),
  /*  326 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(50),
  /*  333 S> */ B(LdaSmi), I8(1),
  /*  338 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(52),
  /*  345 S> */ B(LdaSmi), I8(1),
  /*  350 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(54),
  /*  357 S> */ B(LdaSmi), I8(1),
  /*  362 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(56),
  /*  369 S> */ B(LdaSmi), I8(1),
  /*  374 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(58),
  /*  381 S> */ B(LdaSmi), I8(1),
  /*  386 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(60),
  /*  393 S> */ B(LdaSmi), I8(1),
  /*  398 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(62),
  /*  405 S> */ B(LdaSmi), I8(1),
  /*  410 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(64),
  /*  417 S> */ B(LdaSmi), I8(1),
  /*  422 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(66),
  /*  429 S> */ B(LdaSmi), I8(1),
  /*  434 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(68),
  /*  441 S> */ B(LdaSmi), I8(1),
  /*  446 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(70),
  /*  453 S> */ B(LdaSmi), I8(1),
  /*  458 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(72),
  /*  465 S> */ B(LdaSmi), I8(1),
  /*  470 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(74),
  /*  477 S> */ B(LdaSmi), I8(1),
  /*  482 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(76),
  /*  489 S> */ B(LdaSmi), I8(1),
  /*  494 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(78),
  /*  501 S> */ B(LdaSmi), I8(1),
  /*  506 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(80),
  /*  513 S> */ B(LdaSmi), I8(1),
  /*  518 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(82),
  /*  525 S> */ B(LdaSmi), I8(1),
  /*  530 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(84),
  /*  537 S> */ B(LdaSmi), I8(1),
  /*  542 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(86),
  /*  549 S> */ B(LdaSmi), I8(1),
  /*  554 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(88),
  /*  561 S> */ B(LdaSmi), I8(1),
  /*  566 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(90),
  /*  573 S> */ B(LdaSmi), I8(1),
  /*  578 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(92),
  /*  585 S> */ B(LdaSmi), I8(1),
  /*  590 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(94),
  /*  597 S> */ B(LdaSmi), I8(1),
  /*  602 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(96),
  /*  609 S> */ B(LdaSmi), I8(1),
  /*  614 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(98),
  /*  621 S> */ B(LdaSmi), I8(1),
  /*  626 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(100),
  /*  633 S> */ B(LdaSmi), I8(1),
  /*  638 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(102),
  /*  645 S> */ B(LdaSmi), I8(1),
  /*  650 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(104),
  /*  657 S> */ B(LdaSmi), I8(1),
  /*  662 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(106),
  /*  669 S> */ B(LdaSmi), I8(1),
  /*  674 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(108),
  /*  681 S> */ B(LdaSmi), I8(1),
  /*  686 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(110),
  /*  693 S> */ B(LdaSmi), I8(1),
  /*  698 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(112),
  /*  705 S> */ B(LdaSmi), I8(1),
  /*  710 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(114),
  /*  717 S> */ B(LdaSmi), I8(1),
  /*  722 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(116),
  /*  729 S> */ B(LdaSmi), I8(1),
  /*  734 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(118),
  /*  741 S> */ B(LdaSmi), I8(1),
  /*  746 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(120),
  /*  753 S> */ B(LdaSmi), I8(1),
  /*  758 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(122),
  /*  765 S> */ B(LdaSmi), I8(1),
  /*  770 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(124),
  /*  777 S> */ B(LdaSmi), I8(1),
  /*  782 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(126),
  /*  789 S> */ B(LdaSmi), I8(1),
  /*  794 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(128),
  /*  801 S> */ B(LdaSmi), I8(1),
  /*  806 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(130),
  /*  813 S> */ B(LdaSmi), I8(1),
  /*  818 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(132),
  /*  825 S> */ B(LdaSmi), I8(1),
  /*  830 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(134),
  /*  837 S> */ B(LdaSmi), I8(1),
  /*  842 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(136),
  /*  849 S> */ B(LdaSmi), I8(1),
  /*  854 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(138),
  /*  861 S> */ B(LdaSmi), I8(1),
  /*  866 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(140),
  /*  873 S> */ B(LdaSmi), I8(1),
  /*  878 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(142),
  /*  885 S> */ B(LdaSmi), I8(1),
  /*  890 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(144),
  /*  897 S> */ B(LdaSmi), I8(1),
  /*  902 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(146),
  /*  909 S> */ B(LdaSmi), I8(1),
  /*  914 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(148),
  /*  921 S> */ B(LdaSmi), I8(1),
  /*  926 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(150),
  /*  933 S> */ B(LdaSmi), I8(1),
  /*  938 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(152),
  /*  945 S> */ B(LdaSmi), I8(1),
  /*  950 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(154),
  /*  957 S> */ B(LdaSmi), I8(1),
  /*  962 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(156),
  /*  969 S> */ B(LdaSmi), I8(1),
  /*  974 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(158),
  /*  981 S> */ B(LdaSmi), I8(1),
  /*  986 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(160),
  /*  993 S> */ B(LdaSmi), I8(1),
  /*  998 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(162),
  /* 1005 S> */ B(LdaSmi), I8(1),
  /* 1010 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(164),
  /* 1017 S> */ B(LdaSmi), I8(1),
  /* 1022 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(166),
  /* 1029 S> */ B(LdaSmi), I8(1),
  /* 1034 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(168),
  /* 1041 S> */ B(LdaSmi), I8(1),
  /* 1046 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(170),
  /* 1053 S> */ B(LdaSmi), I8(1),
  /* 1058 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(172),
  /* 1065 S> */ B(LdaSmi), I8(1),
  /* 1070 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(174),
  /* 1077 S> */ B(LdaSmi), I8(1),
  /* 1082 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(176),
  /* 1089 S> */ B(LdaSmi), I8(1),
  /* 1094 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(178),
  /* 1101 S> */ B(LdaSmi), I8(1),
  /* 1106 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(180),
  /* 1113 S> */ B(LdaSmi), I8(1),
  /* 1118 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(182),
  /* 1125 S> */ B(LdaSmi), I8(1),
  /* 1130 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(184),
  /* 1137 S> */ B(LdaSmi), I8(1),
  /* 1142 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(186),
  /* 1149 S> */ B(LdaSmi), I8(1),
  /* 1154 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(188),
  /* 1161 S> */ B(LdaSmi), I8(1),
  /* 1166 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(190),
  /* 1173 S> */ B(LdaSmi), I8(1),
  /* 1178 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(192),
  /* 1185 S> */ B(LdaSmi), I8(1),
  /* 1190 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(194),
  /* 1197 S> */ B(LdaSmi), I8(1),
  /* 1202 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(196),
  /* 1209 S> */ B(LdaSmi), I8(1),
  /* 1214 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(198),
  /* 1221 S> */ B(LdaSmi), I8(1),
  /* 1226 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(200),
  /* 1233 S> */ B(LdaSmi), I8(1),
  /* 1238 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(202),
  /* 1245 S> */ B(LdaSmi), I8(1),
  /* 1250 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(204),
  /* 1257 S> */ B(LdaSmi), I8(1),
  /* 1262 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(206),
  /* 1269 S> */ B(LdaSmi), I8(1),
  /* 1274 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(208),
  /* 1281 S> */ B(LdaSmi), I8(1),
  /* 1286 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(210),
  /* 1293 S> */ B(LdaSmi), I8(1),
  /* 1298 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(212),
  /* 1305 S> */ B(LdaSmi), I8(1),
  /* 1310 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(214),
  /* 1317 S> */ B(LdaSmi), I8(1),
  /* 1322 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(216),
  /* 1329 S> */ B(LdaSmi), I8(1),
  /* 1334 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(218),
  /* 1341 S> */ B(LdaSmi), I8(1),
  /* 1346 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(220),
  /* 1353 S> */ B(LdaSmi), I8(1),
  /* 1358 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(222),
  /* 1365 S> */ B(LdaSmi), I8(1),
  /* 1370 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(224),
  /* 1377 S> */ B(LdaSmi), I8(1),
  /* 1382 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(226),
  /* 1389 S> */ B(LdaSmi), I8(1),
  /* 1394 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(228),
  /* 1401 S> */ B(LdaSmi), I8(1),
  /* 1406 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(230),
  /* 1413 S> */ B(LdaSmi), I8(1),
  /* 1418 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(232),
  /* 1425 S> */ B(LdaSmi), I8(1),
  /* 1430 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(234),
  /* 1437 S> */ B(LdaSmi), I8(1),
  /* 1442 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(236),
  /* 1449 S> */ B(LdaSmi), I8(1),
  /* 1454 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(238),
  /* 1461 S> */ B(LdaSmi), I8(1),
  /* 1466 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(240),
  /* 1473 S> */ B(LdaSmi), I8(1),
  /* 1478 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(242),
  /* 1485 S> */ B(LdaSmi), I8(1),
  /* 1490 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(244),
  /* 1497 S> */ B(LdaSmi), I8(1),
  /* 1502 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(246),
  /* 1509 S> */ B(LdaSmi), I8(1),
  /* 1514 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(248),
  /* 1521 S> */ B(LdaSmi), I8(1),
  /* 1526 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(250),
  /* 1533 S> */ B(LdaSmi), I8(1),
  /* 1538 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(252),
  /* 1545 S> */ B(LdaSmi), I8(1),
  /* 1550 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(254),
  /* 1557 S> */ B(LdaSmi), I8(2),
  /* 1562 E> */ B(Wide), B(SetKeyedProperty), R16(arg0), R16(arg1), U16(256),
                B(LdaUndefined),
  /* 1567 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

---
snippet: "
  function f(a, b) {
    'use strict';
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 1;
    a[b] = 2;
  }
  f({name : \"test\"})
"
frame size: 0
parameter count: 3
bytecode array length: 780
bytecodes: [
  /*   37 S> */ B(LdaSmi), I8(1),
  /*   42 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(0),
  /*   49 S> */ B(LdaSmi), I8(1),
  /*   54 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(2),
  /*   61 S> */ B(LdaSmi), I8(1),
  /*   66 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(4),
  /*   73 S> */ B(LdaSmi), I8(1),
  /*   78 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(6),
  /*   85 S> */ B(LdaSmi), I8(1),
  /*   90 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(8),
  /*   97 S> */ B(LdaSmi), I8(1),
  /*  102 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(10),
  /*  109 S> */ B(LdaSmi), I8(1),
  /*  114 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(12),
  /*  121 S> */ B(LdaSmi), I8(1),
  /*  126 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(14),
  /*  133 S> */ B(LdaSmi), I8(1),
  /*  138 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(16),
  /*  145 S> */ B(LdaSmi), I8(1),
  /*  150 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(18),
  /*  157 S> */ B(LdaSmi), I8(1),
  /*  162 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(20),
  /*  169 S> */ B(LdaSmi), I8(1),
  /*  174 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(22),
  /*  181 S> */ B(LdaSmi), I8(1),
  /*  186 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(24),
  /*  193 S> */ B(LdaSmi), I8(1),
  /*  198 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(26),
  /*  205 S> */ B(LdaSmi), I8(1),
  /*  210 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(28),
  /*  217 S> */ B(LdaSmi), I8(1),
  /*  222 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(30),
  /*  229 S> */ B(LdaSmi), I8(1),
  /*  234 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(32),
  /*  241 S> */ B(LdaSmi), I8(1),
  /*  246 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(34),
  /*  253 S> */ B(LdaSmi), I8(1),
  /*  258 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(36),
  /*  265 S> */ B(LdaSmi), I8(1),
  /*  270 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(38),
  /*  277 S> */ B(LdaSmi), I8(1),
  /*  282 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(40),
  /*  289 S> */ B(LdaSmi), I8(1),
  /*  294 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(42),
  /*  301 S> */ B(LdaSmi), I8(1),
  /*  306 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(44),
  /*  313 S> */ B(LdaSmi), I8(1),
  /*  318 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(46),
  /*  325 S> */ B(LdaSmi), I8(1),
  /*  330 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(48),
  /*  337 S> */ B(LdaSmi), I8(1),
  /*  342 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(50),
  /*  349 S> */ B(LdaSmi), I8(1),
  /*  354 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(52),
  /*  361 S> */ B(LdaSmi), I8(1),
  /*  366 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(54),
  /*  373 S> */ B(LdaSmi), I8(1),
  /*  378 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(56),
  /*  385 S> */ B(LdaSmi), I8(1),
  /*  390 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(58),
  /*  397 S> */ B(LdaSmi), I8(1),
  /*  402 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(60),
  /*  409 S> */ B(LdaSmi), I8(1),
  /*  414 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(62),
  /*  421 S> */ B(LdaSmi), I8(1),
  /*  426 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(64),
  /*  433 S> */ B(LdaSmi), I8(1),
  /*  438 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(66),
  /*  445 S> */ B(LdaSmi), I8(1),
  /*  450 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(68),
  /*  457 S> */ B(LdaSmi), I8(1),
  /*  462 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(70),
  /*  469 S> */ B(LdaSmi), I8(1),
  /*  474 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(72),
  /*  481 S> */ B(LdaSmi), I8(1),
  /*  486 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(74),
  /*  493 S> */ B(LdaSmi), I8(1),
  /*  498 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(76),
  /*  505 S> */ B(LdaSmi), I8(1),
  /*  510 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(78),
  /*  517 S> */ B(LdaSmi), I8(1),
  /*  522 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(80),
  /*  529 S> */ B(LdaSmi), I8(1),
  /*  534 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(82),
  /*  541 S> */ B(LdaSmi), I8(1),
  /*  546 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(84),
  /*  553 S> */ B(LdaSmi), I8(1),
  /*  558 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(86),
  /*  565 S> */ B(LdaSmi), I8(1),
  /*  570 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(88),
  /*  577 S> */ B(LdaSmi), I8(1),
  /*  582 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(90),
  /*  589 S> */ B(LdaSmi), I8(1),
  /*  594 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(92),
  /*  601 S> */ B(LdaSmi), I8(1),
  /*  606 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(94),
  /*  613 S> */ B(LdaSmi), I8(1),
  /*  618 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(96),
  /*  625 S> */ B(LdaSmi), I8(1),
  /*  630 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(98),
  /*  637 S> */ B(LdaSmi), I8(1),
  /*  642 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(100),
  /*  649 S> */ B(LdaSmi), I8(1),
  /*  654 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(102),
  /*  661 S> */ B(LdaSmi), I8(1),
  /*  666 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(104),
  /*  673 S> */ B(LdaSmi), I8(1),
  /*  678 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(106),
  /*  685 S> */ B(LdaSmi), I8(1),
  /*  690 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(108),
  /*  697 S> */ B(LdaSmi), I8(1),
  /*  702 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(110),
  /*  709 S> */ B(LdaSmi), I8(1),
  /*  714 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(112),
  /*  721 S> */ B(LdaSmi), I8(1),
  /*  726 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(114),
  /*  733 S> */ B(LdaSmi), I8(1),
  /*  738 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(116),
  /*  745 S> */ B(LdaSmi), I8(1),
  /*  750 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(118),
  /*  757 S> */ B(LdaSmi), I8(1),
  /*  762 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(120),
  /*  769 S> */ B(LdaSmi), I8(1),
  /*  774 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(122),
  /*  781 S> */ B(LdaSmi), I8(1),
  /*  786 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(124),
  /*  793 S> */ B(LdaSmi), I8(1),
  /*  798 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(126),
  /*  805 S> */ B(LdaSmi), I8(1),
  /*  810 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(128),
  /*  817 S> */ B(LdaSmi), I8(1),
  /*  822 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(130),
  /*  829 S> */ B(LdaSmi), I8(1),
  /*  834 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(132),
  /*  841 S> */ B(LdaSmi), I8(1),
  /*  846 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(134),
  /*  853 S> */ B(LdaSmi), I8(1),
  /*  858 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(136),
  /*  865 S> */ B(LdaSmi), I8(1),
  /*  870 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(138),
  /*  877 S> */ B(LdaSmi), I8(1),
  /*  882 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(140),
  /*  889 S> */ B(LdaSmi), I8(1),
  /*  894 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(142),
  /*  901 S> */ B(LdaSmi), I8(1),
  /*  906 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(144),
  /*  913 S> */ B(LdaSmi), I8(1),
  /*  918 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(146),
  /*  925 S> */ B(LdaSmi), I8(1),
  /*  930 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(148),
  /*  937 S> */ B(LdaSmi), I8(1),
  /*  942 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(150),
  /*  949 S> */ B(LdaSmi), I8(1),
  /*  954 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(152),
  /*  961 S> */ B(LdaSmi), I8(1),
  /*  966 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(154),
  /*  973 S> */ B(LdaSmi), I8(1),
  /*  978 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(156),
  /*  985 S> */ B(LdaSmi), I8(1),
  /*  990 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(158),
  /*  997 S> */ B(LdaSmi), I8(1),
  /* 1002 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(160),
  /* 1009 S> */ B(LdaSmi), I8(1),
  /* 1014 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(162),
  /* 1021 S> */ B(LdaSmi), I8(1),
  /* 1026 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(164),
  /* 1033 S> */ B(LdaSmi), I8(1),
  /* 1038 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(166),
  /* 1045 S> */ B(LdaSmi), I8(1),
  /* 1050 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(168),
  /* 1057 S> */ B(LdaSmi), I8(1),
  /* 1062 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(170),
  /* 1069 S> */ B(LdaSmi), I8(1),
  /* 1074 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(172),
  /* 1081 S> */ B(LdaSmi), I8(1),
  /* 1086 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(174),
  /* 1093 S> */ B(LdaSmi), I8(1),
  /* 1098 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(176),
  /* 1105 S> */ B(LdaSmi), I8(1),
  /* 1110 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(178),
  /* 1117 S> */ B(LdaSmi), I8(1),
  /* 1122 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(180),
  /* 1129 S> */ B(LdaSmi), I8(1),
  /* 1134 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(182),
  /* 1141 S> */ B(LdaSmi), I8(1),
  /* 1146 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(184),
  /* 1153 S> */ B(LdaSmi), I8(1),
  /* 1158 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(186),
  /* 1165 S> */ B(LdaSmi), I8(1),
  /* 1170 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(188),
  /* 1177 S> */ B(LdaSmi), I8(1),
  /* 1182 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(190),
  /* 1189 S> */ B(LdaSmi), I8(1),
  /* 1194 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(192),
  /* 1201 S> */ B(LdaSmi), I8(1),
  /* 1206 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(194),
  /* 1213 S> */ B(LdaSmi), I8(1),
  /* 1218 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(196),
  /* 1225 S> */ B(LdaSmi), I8(1),
  /* 1230 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(198),
  /* 1237 S> */ B(LdaSmi), I8(1),
  /* 1242 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(200),
  /* 1249 S> */ B(LdaSmi), I8(1),
  /* 1254 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(202),
  /* 1261 S> */ B(LdaSmi), I8(1),
  /* 1266 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(204),
  /* 1273 S> */ B(LdaSmi), I8(1),
  /* 1278 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(206),
  /* 1285 S> */ B(LdaSmi), I8(1),
  /* 1290 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(208),
  /* 1297 S> */ B(LdaSmi), I8(1),
  /* 1302 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(210),
  /* 1309 S> */ B(LdaSmi), I8(1),
  /* 1314 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(212),
  /* 1321 S> */ B(LdaSmi), I8(1),
  /* 1326 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(214),
  /* 1333 S> */ B(LdaSmi), I8(1),
  /* 1338 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(216),
  /* 1345 S> */ B(LdaSmi), I8(1),
  /* 1350 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(218),
  /* 1357 S> */ B(LdaSmi), I8(1),
  /* 1362 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(220),
  /* 1369 S> */ B(LdaSmi), I8(1),
  /* 1374 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(222),
  /* 1381 S> */ B(LdaSmi), I8(1),
  /* 1386 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(224),
  /* 1393 S> */ B(LdaSmi), I8(1),
  /* 1398 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(226),
  /* 1405 S> */ B(LdaSmi), I8(1),
  /* 1410 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(228),
  /* 1417 S> */ B(LdaSmi), I8(1),
  /* 1422 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(230),
  /* 1429 S> */ B(LdaSmi), I8(1),
  /* 1434 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(232),
  /* 1441 S> */ B(LdaSmi), I8(1),
  /* 1446 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(234),
  /* 1453 S> */ B(LdaSmi), I8(1),
  /* 1458 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(236),
  /* 1465 S> */ B(LdaSmi), I8(1),
  /* 1470 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(238),
  /* 1477 S> */ B(LdaSmi), I8(1),
  /* 1482 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(240),
  /* 1489 S> */ B(LdaSmi), I8(1),
  /* 1494 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(242),
  /* 1501 S> */ B(LdaSmi), I8(1),
  /* 1506 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(244),
  /* 1513 S> */ B(LdaSmi), I8(1),
  /* 1518 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(246),
  /* 1525 S> */ B(LdaSmi), I8(1),
  /* 1530 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(248),
  /* 1537 S> */ B(LdaSmi), I8(1),
  /* 1542 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(250),
  /* 1549 S> */ B(LdaSmi), I8(1),
  /* 1554 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(252),
  /* 1561 S> */ B(LdaSmi), I8(1),
  /* 1566 E> */ B(SetKeyedProperty), R(arg0), R(arg1), U8(254),
  /* 1573 S> */ B(LdaSmi), I8(2),
  /* 1578 E> */ B(Wide), B(SetKeyedProperty), R16(arg0), R16(arg1), U16(256),
                B(LdaUndefined),
  /* 1583 S> */ B(Return),
]
constant pool: [
]
handlers: [
]

