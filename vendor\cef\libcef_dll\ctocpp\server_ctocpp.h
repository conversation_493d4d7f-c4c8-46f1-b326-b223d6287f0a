// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=ed13956e5941bbb0885224ef57016cf7f34d389c$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_SERVER_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_SERVER_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_server_capi.h"
#include "include/cef_server.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefServerCToCpp
    : public CefCToCppRefCounted<CefServerCToCpp, CefServer, cef_server_t> {
 public:
  CefServerCToCpp();
  virtual ~CefServerCToCpp();

  // CefServer methods.
  CefRefPtr<CefTaskRunner> GetTaskRunner() override;
  void Shutdown() override;
  bool IsRunning() override;
  CefString GetAddress() override;
  bool HasConnection() override;
  bool IsValidConnection(int connection_id) override;
  void SendHttp200Response(int connection_id,
                           const CefString& content_type,
                           const void* data,
                           size_t data_size) override;
  void SendHttp404Response(int connection_id) override;
  void SendHttp500Response(int connection_id,
                           const CefString& error_message) override;
  void SendHttpResponse(int connection_id,
                        int response_code,
                        const CefString& content_type,
                        int64 content_length,
                        const HeaderMap& extra_headers) override;
  void SendRawData(int connection_id,
                   const void* data,
                   size_t data_size) override;
  void CloseConnection(int connection_id) override;
  void SendWebSocketMessage(int connection_id,
                            const void* data,
                            size_t data_size) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_SERVER_CTOCPP_H_
