// Copyright (c) 2022 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=acb14970579704b71425d23b2bb7468f782a1e17$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_FILE_DIALOG_CALLBACK_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_FILE_DIALOG_CALLBACK_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include <vector>
#include "include/capi/cef_dialog_handler_capi.h"
#include "include/cef_dialog_handler.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefFileDialogCallbackCToCpp
    : public CefCToCppRefCounted<CefFileDialogCallbackCToCpp,
                                 CefFileDialogCallback,
                                 cef_file_dialog_callback_t> {
 public:
  CefFileDialogCallbackCToCpp();
  virtual ~CefFileDialogCallbackCToCpp();

  // CefFileDialogCallback methods.
  void Continue(const std::vector<CefString>& file_paths) override;
  void Cancel() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_FILE_DIALOG_CALLBACK_CTOCPP_H_
