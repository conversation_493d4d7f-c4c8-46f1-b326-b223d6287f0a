#
# Autogenerated by generate-bytecode-expectations.
#

---
wrap: yes

---
snippet: "
  class A { constructor(...args) { this.args = args; } }
  new A(...[1, 2, 3]);
"
frame size: 6
parameter count: 1
bytecode array length: 41
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
  /*   89 S> */ B(CreateArrayLiteral), U8(3), U8(0), U8(37),
                B(Star2),
                B(Ldar), R(0),
  /*   89 E> */ B(ConstructWithSpread), R(0), R(2), U8(1), U8(1),
                B(LdaUndefined),
  /*  110 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  ARRAY_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  class A { constructor(...args) { this.args = args; } }
  new A(0, ...[1, 2, 3]);
"
frame size: 6
parameter count: 1
bytecode array length: 43
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
  /*   89 S> */ B(LdaZero),
                B(Star2),
                B(CreateArrayLiteral), U8(3), U8(0), U8(37),
                B(Star3),
                B(Ldar), R(0),
  /*   89 E> */ B(ConstructWithSpread), R(0), R(2), U8(2), U8(1),
                B(LdaUndefined),
  /*  113 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  ARRAY_BOILERPLATE_DESCRIPTION_TYPE,
]
handlers: [
]

---
snippet: "
  class A { constructor(...args) { this.args = args; } }
  new A(0, ...[1, 2, 3], 4);
"
frame size: 7
parameter count: 1
bytecode array length: 100
bytecodes: [
  /*   30 E> */ B(CreateBlockContext), U8(0),
                B(PushContext), R(1),
                B(LdaTheHole),
                B(Star5),
                B(CreateClosure), U8(2), U8(0), U8(2),
                B(Star2),
                B(LdaConstant), U8(1),
                B(Star3),
                B(Mov), R(2), R(4),
                B(CallRuntime), U16(Runtime::kDefineClass), R(3), U8(3),
                B(PopContext), R(1),
                B(Mov), R(4), R(0),
  /*   89 S> */ B(CreateArrayLiteral), U8(3), U8(0), U8(37),
                B(Star2),
                B(LdaSmi), I8(1),
                B(Star3),
  /*  101 E> */ B(CreateArrayLiteral), U8(4), U8(1), U8(37),
                B(Star6),
  /*  101 E> */ B(GetIterator), R(6), U8(2), U8(4),
                B(Star5),
                B(GetNamedProperty), R(5), U8(5), U8(6),
                B(Star4),
                B(Mov), R(0), R(1),
                B(CallProperty0), R(4), R(5), U8(15),
                B(Star6),
                B(JumpIfJSReceiver), U8(7),
                B(CallRuntime), U16(Runtime::kThrowIteratorResultNotAnObject), R(6), U8(1),
                B(GetNamedProperty), R(6), U8(6), U8(17),
                B(JumpIfToBooleanTrue), U8(19),
                B(GetNamedProperty), R(6), U8(7), U8(8),
                B(StaInArrayLiteral), R(2), R(3), U8(13),
                B(Ldar), R(3),
                B(Inc), U8(12),
                B(Star3),
                B(JumpLoop), U8(31), I8(0), U8(19),
                B(LdaSmi), I8(4),
                B(StaInArrayLiteral), R(2), R(3), U8(13),
  /*   89 E> */ B(CallJSRuntime), U8(%reflect_construct), R(1), U8(2),
                B(LdaUndefined),
  /*  116 S> */ B(Return),
]
constant pool: [
  SCOPE_INFO_TYPE,
  CLASS_BOILERPLATE_TYPE,
  SHARED_FUNCTION_INFO_TYPE,
  ARRAY_BOILERPLATE_DESCRIPTION_TYPE,
  ARRAY_BOILERPLATE_DESCRIPTION_TYPE,
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["next"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["done"],
  INTERNALIZED_ONE_BYTE_STRING_TYPE ["value"],
]
handlers: [
]

